#include "active_configuration.h"
#include "basestation-config.pb.h"
#include <algorithm>
#include <pb_decode.h>
#include <pb_encode.h>
#include <string.h>
#include <string>

#ifdef __EMSCRIPTEN__
#include <emscripten/bind.h>
#include <emscripten/emscripten.h>
#include <emscripten/val.h>
#endif

#include "message_handler.h"

// Helper function to format action target based on oneof discriminator
const char* format_action_target(const Action* action, char* buffer, size_t buffer_size) {
    if (action->which_target == Action_light_id_tag) {
        snprintf(buffer, buffer_size, "Light ID %u", action->target.light_id);
    } else if (action->which_target == Action_somfy_shade_id_tag) {
        snprintf(buffer, buffer_size, "Somfy Shade ID %u", action->target.somfy_shade_id);
    } else {
        snprintf(buffer, buffer_size, "Unknown Target");
    }
    return buffer;
}

// Helper function to format just the target ID based on oneof discriminator
const char* format_action_target_id(const Action* action, char* buffer, size_t buffer_size) {
    if (action->which_target == Action_light_id_tag) {
        snprintf(buffer, buffer_size, "Light ID: %u", action->target.light_id);
    } else if (action->which_target == Action_somfy_shade_id_tag) {
        snprintf(buffer, buffer_size, "Somfy Shade ID: %u", action->target.somfy_shade_id);
    } else {
        snprintf(buffer, buffer_size, "Unknown Target ID");
    }
    return buffer;
}

// Helper function to set action target to light ID
static inline void set_action_target_light(Action* action, uint8_t light_id) {
    action->which_target = Action_light_id_tag;
    action->target.light_id = light_id;
}

// Helper function to set action target to somfy shade ID
static inline void set_action_target_shade(Action* action, uint8_t shade_id) {
    action->which_target = Action_somfy_shade_id_tag;
    action->target.somfy_shade_id = shade_id;
}

ActiveConfiguration g_active_configuration = ActiveConfiguration_init_zero;
char g_qr_code[13] = {0}; // Global QR code storage

ActiveConfiguration *get_active_configuration() {
    return &g_active_configuration;
}

// for emscripten binding, don't use this in C++ code, since it allocates a lot of memory on the stack
bool set_active_configuration_from_protobuf(const pb_byte_t *data, size_t size) {
    // Initialize the global configuration before decoding
    g_active_configuration = ActiveConfiguration_init_zero;

    pb_istream_t stream = pb_istream_from_buffer(data, size);

    bool status = pb_decode(&stream, ActiveConfiguration_fields, &g_active_configuration);

    if (!status) {
        printf("Failed to decode active configuration: %s\n", PB_GET_ERROR(&stream));
        return false;
    }

    return true;
}

bool apply_configuration_update(const BasestationUpdateMessage *message) {
    // Copy the config from the message
    memcpy(&g_active_configuration.config, &message->config, sizeof(BasestationConfig));
    g_active_configuration.has_config = true;
    
    // Note: Node QR mappings are part of the config and will be automatically replaced
    // when the new config is copied above. No additional cleanup needed for QR mappings.

    // Update light states, preserving existing ones that match the config
    uint8_t old_lights_count = g_active_configuration.state.lights_count;
    g_active_configuration.state.lights_count = g_active_configuration.config.lights_count;
    g_active_configuration.has_state = true;

    for (uint8_t i = 0; i < g_active_configuration.state.lights_count; i++) {
        LightState *light_state = &g_active_configuration.state.lights[i];
        LightConfig *light_config = &g_active_configuration.config.lights[i];

        // Check if this light already exists in current state
        bool found = false;
        for (uint8_t j = 0; j < old_lights_count; j++) {
            if (g_active_configuration.state.lights[j].id == light_config->id) {
                // Light exists, just update the dim speed which may have changed
                g_active_configuration.state.lights[j].dim_speed_msec = light_config->dim_speed_msec;
                if (i != j) {
                    // Copy to new position if needed
                    *light_state = g_active_configuration.state.lights[j];
                }
                found = true;
                break;
            }
        }

        // Initialize new lights
        if (!found) {
            light_state->id = light_config->id;
            light_state->brightness = 0;
            light_state->target_value = 0;
            light_state->dim_speed_msec = light_config->dim_speed_msec;
            light_state->last_modified_time = get_time_in_ms();
            light_state->last_brightness_before_action = 0;
        }
    }

    // Clean up button states for nodes that are no longer configured
    // Button states should only exist for configured nodes
    uint8_t old_button_count = g_active_configuration.state.buttons_count;
    uint8_t new_button_count = 0;
    
    // First, collect all node IDs from the new configuration
    uint8_t configured_node_ids[50]; // Temporary array to store configured node IDs
    uint8_t configured_node_count = 0;
    const uint8_t MAX_NODE_IDS = sizeof(configured_node_ids) / sizeof(configured_node_ids[0]);

    // Add CAN node IDs from canbo configs
    for (uint8_t i = 0; i < g_active_configuration.config.canbo_configs_count; i++) {
        if (configured_node_count >= MAX_NODE_IDS) {
            printf("[ ACTIVE CONFIGURATION ] Warning: Too many configured nodes, some will be ignored\n");
            break;
        }
        configured_node_ids[configured_node_count++] = g_active_configuration.config.canbo_configs[i].node_id;
    }

    // Add RF node IDs from rf reed configs
    for (uint8_t i = 0; i < g_active_configuration.config.rf_reed_configs_count; i++) {
        if (configured_node_count >= MAX_NODE_IDS) {
            printf("[ ACTIVE CONFIGURATION ] Skipping remaining RF reed configs\n");
            break;
        }
        configured_node_ids[configured_node_count++] = g_active_configuration.config.rf_reed_configs[i].node_id;
    }

    // Add RF node IDs from light fixtures
    for (uint8_t i = 0; i < g_active_configuration.config.lights_count; i++) {
        for (uint8_t j = 0; j < g_active_configuration.config.lights[i].fixtures_count; j++) {
            if (g_active_configuration.config.lights[i].fixtures[j].type == LightConfig_FixtureConfig_FixtureType_RF) {
                configured_node_ids[configured_node_count++] = g_active_configuration.config.lights[i].fixtures[j].config.rf.node_id;
            }
        }
    }
    
    // Add RF node IDs from rf dimmer configs
    for (uint8_t i = 0; i < g_active_configuration.config.rf_dimmer_configs_count; i++) {
        if (configured_node_count >= MAX_NODE_IDS) {
            printf("[ ACTIVE CONFIGURATION ] Skipping remaining RF dimmer configs\n");
            break;
        }
        configured_node_ids[configured_node_count++] = g_active_configuration.config.rf_dimmer_configs[i].node_id;
    }
    
    // Add RF node IDs from rf presence configs
    for (uint8_t i = 0; i < g_active_configuration.config.rf_presence_configs_count; i++) {
        if (configured_node_count >= MAX_NODE_IDS) {
            printf("[ ACTIVE CONFIGURATION ] Skipping remaining RF presence configs\n");
            break;
        }
        configured_node_ids[configured_node_count++] = g_active_configuration.config.rf_presence_configs[i].node_id;
    }

    // Clean up button states for nodes that are no longer configured
    for (uint8_t i = 0; i < old_button_count; i++) {
        bool is_configured = false;
        for (uint8_t j = 0; j < configured_node_count; j++) {
            if (g_active_configuration.state.buttons[i].node_id == configured_node_ids[j]) {
                is_configured = true;
                break;
            }
        }
        
        if (is_configured) {
            // Keep this button state, move to new position if needed
            if (new_button_count != i) {
                g_active_configuration.state.buttons[new_button_count] = g_active_configuration.state.buttons[i];
            }
            new_button_count++;
        } else {
            printf("[ ACTIVE CONFIGURATION ] Removing button state for unconfigured node %d\n", 
                   g_active_configuration.state.buttons[i].node_id);
        }
    }
    g_active_configuration.state.buttons_count = new_button_count;
    
    // Clean up PIR states for nodes that are no longer configured
    uint8_t old_pir_count = g_active_configuration.state.pirs_count;
    uint8_t new_pir_count = 0;
    
    for (uint8_t i = 0; i < old_pir_count; i++) {
        bool is_configured = false;
        for (uint8_t j = 0; j < configured_node_count; j++) {
            if (g_active_configuration.state.pirs[i].node_id == configured_node_ids[j]) {
                is_configured = true;
                break;
            }
        }
        
        if (is_configured) {
            // Keep this PIR state, move to new position if needed
            if (new_pir_count != i) {
                g_active_configuration.state.pirs[new_pir_count] = g_active_configuration.state.pirs[i];
            }
            new_pir_count++;
        } else {
            printf("[ ACTIVE CONFIGURATION ] Removing PIR state for unconfigured node %d\n", 
                   g_active_configuration.state.pirs[i].node_id);
        }
    }
    g_active_configuration.state.pirs_count = new_pir_count;

    // Preserve existing provisioning states for configured devices
    uint8_t old_provisioned_count = g_active_configuration.state.provisioned_devices_count;
    uint8_t new_provisioned_count = 0;

    // First pass: preserve existing states for configured devices
    for (uint8_t i = 0; i < old_provisioned_count; i++) {
        bool is_configured = false;
        for (uint8_t j = 0; j < configured_node_count; j++) {
            if (g_active_configuration.state.provisioned_devices[i].node_id == configured_node_ids[j]) {
                is_configured = true;
                break;
            }
        }

        if (is_configured) {
            // Move to new position if needed
            if (new_provisioned_count != i) {
                g_active_configuration.state.provisioned_devices[new_provisioned_count] = g_active_configuration.state.provisioned_devices[i];
            }
            new_provisioned_count++;
        }
    }

    // Update the count
    g_active_configuration.state.provisioned_devices_count = new_provisioned_count;

    // Second pass: initialize provisioning states for new devices that don't have existing states
    for (uint8_t i = 0; i < configured_node_count; i++) {
        bool has_existing_state = false;
        for (uint8_t j = 0; j < g_active_configuration.state.provisioned_devices_count; j++) {
            if (g_active_configuration.state.provisioned_devices[j].node_id == configured_node_ids[i]) {
                has_existing_state = true;
                break;
            }
        }

        if (!has_existing_state) {
            // Create new provisioning state for this device
            ProvisioningState *new_state = &g_active_configuration.state.provisioned_devices[g_active_configuration.state.provisioned_devices_count];
            new_state->node_id = configured_node_ids[i];
            new_state->is_provisioned = false;
            new_state->last_seen_time = 0;
            new_state->error_code = ProvisioningState_ProvisioningErrorCode_NONE;
            g_active_configuration.state.provisioned_devices_count++;
        }
    }

    // Update RF reed states, preserving existing ones that match the config
    uint8_t old_reeds_count = g_active_configuration.state.reeds_count;
    g_active_configuration.state.reeds_count = g_active_configuration.config.rf_reed_configs_count;

    for (uint8_t i = 0; i < g_active_configuration.state.reeds_count; i++) {
        RFReedState *reed_state = &g_active_configuration.state.reeds[i];
        RFReedSensorConfig *reed_config = &g_active_configuration.config.rf_reed_configs[i];

        // Check if this reed already exists in current state
        bool found = false;
        for (uint8_t j = 0; j < old_reeds_count; j++) {
            if (g_active_configuration.state.reeds[j].node_id == reed_config->node_id) {
                // Reed exists, preserve its state
                if (i != j) {
                    // Copy to new position if needed
                    *reed_state = g_active_configuration.state.reeds[j];
                }
                found = true;
                break;
            }
        }

        // Initialize new reed states
        if (!found) {
            reed_state->node_id = reed_config->node_id;
            reed_state->sensor_status = RFReedState_Status_UNKNOWN;
            reed_state->last_modified_time = get_time_in_ms();
            reed_state->battery_voltage = 0.0;
        }
    }
    
    // Update RF presence states, preserving existing ones that match the config
    uint8_t old_presences_count = g_active_configuration.state.presences_count;
    g_active_configuration.state.presences_count = g_active_configuration.config.rf_presence_configs_count;

    for (uint8_t i = 0; i < g_active_configuration.state.presences_count; i++) {
        RFPresenceState *presence_state = &g_active_configuration.state.presences[i];
        RFPresenceSensorConfig *presence_config = &g_active_configuration.config.rf_presence_configs[i];

        // Check if this presence sensor already exists in current state
        bool found = false;
        for (uint8_t j = 0; j < old_presences_count; j++) {
            if (g_active_configuration.state.presences[j].node_id == presence_config->node_id) {
                // Presence exists, preserve its state
                if (i != j) {
                    // Copy to new position if needed
                    *presence_state = g_active_configuration.state.presences[j];
                }
                found = true;
                break;
            }
        }

        // Initialize new presence states
        if (!found) {
            presence_state->node_id = presence_config->node_id;
            presence_state->sensor_status = RFPresenceState_Status_UNKNOWN;
            presence_state->last_modified_time = get_time_in_ms();
            presence_state->battery_voltage = 0.0;
        }
    }

    return true;
}

bool decode_update_message(const pb_byte_t *data, size_t size, BasestationUpdateMessage *message) {
    *message = BasestationUpdateMessage_init_zero;
    pb_istream_t stream = pb_istream_from_buffer(data, size);
    if (!pb_decode(&stream, BasestationUpdateMessage_fields, message)) {
        printf("[ ACTIVE CONFIGURATION ] Failed to decode: %s\n", PB_GET_ERROR(&stream));
        return false;
    }
    return true;
}

bool validate_qr_code(const char *received, const char *expected) {
    if (!expected || strlen(expected) == 0) {
        printf("[ ACTIVE CONFIGURATION ] No expected QR code provided\n");
        return false;
    }
    if (strcmp(received, expected) != 0) {
        printf("[ ACTIVE CONFIGURATION ] QR mismatch: '%s' vs '%s'\n", received, expected);
        return false;
    }
    return true;
}

bool validate_and_apply_update_message_protobuf(const pb_byte_t *data, size_t size, const char *expected_qr_code, BasestationUpdateMessage *message) {
    if (!message) {
        printf("[ ACTIVE CONFIGURATION ] No message buffer provided\n");
        return false;
    }
    if (!decode_update_message(data, size, message)) {
        return false;
    }
    if (!validate_qr_code(message->qr_code, expected_qr_code)) {
        return false;
    }
    return apply_configuration_update(message);
}

bool set_active_configuration_from_protobuf_emscripten(std::string data) {
    const char *data_chars = data.c_str();
    return set_active_configuration_from_protobuf(reinterpret_cast<const uint8_t *>(data_chars), data.size());
}

void set_debug_configuration() {
    g_active_configuration = ActiveConfiguration_init_zero;

    static LightConfig_FixtureConfig_DMXConfig_LightParams wledParams = {0.0f, 1.0f, 1.5f, 0.0f, 1.0f, 1.5f};

    g_active_configuration.config = BasestationConfig_init_zero;

    g_active_configuration.config.id = 1;
    strncpy(g_active_configuration.config.version, "000014", sizeof(g_active_configuration.config.version) - 1);
    g_active_configuration.config.version[sizeof(g_active_configuration.config.version) - 1] = '\0';
    g_active_configuration.has_config = true;
    g_active_configuration.has_state = true;

    uint32_t now = get_time_in_ms();

    LightConfig *largeLED = &g_active_configuration.config.lights[0];
    largeLED->id = 1;
    largeLED->dim_speed_msec = 1000;

    LightConfig *smallLED = &g_active_configuration.config.lights[1];
    smallLED->id = 2;
    smallLED->dim_speed_msec = 1000;

    LightConfig *canboLight = &g_active_configuration.config.lights[2];
    canboLight->id = 3;
    canboLight->dim_speed_msec = 1000;

    LightConfig *rfLight = &g_active_configuration.config.lights[3];
    rfLight->id = 4;
    rfLight->dim_speed_msec = 1000;

    // LightConfig_FixtureConfig *wledStripRight = &lightConfig->fixtures[0];
    // wledStripRight->min_brightness = 0;
    // wledStripRight->max_brightness = 50;
    // wledStripRight->type = LightConfig_FixtureConfig_FixtureType_DMX;  // Set the type field
    // wledStripRight->which_config = LightConfig_FixtureConfig_dmx_tag;
    // wledStripRight->config.dmx.type = LightConfig_FixtureConfig_DMXConfig_FixtureTypeConfig_TUNABLE_WHITE;
    // wledStripRight->config.dmx.channels_count = 1;
    // wledStripRight->config.dmx.channels[0] = 4;
    // wledStripRight->config.dmx.params = wledParams;
    // wledStripRight->config.dmx.has_params = true;
    LightConfig_FixtureConfig *light1 = &largeLED->fixtures[0];
    light1->min_brightness = 0;
    light1->max_brightness = 100;
    light1->type = LightConfig_FixtureConfig_FixtureType_DMX; // Set the type field
    light1->which_config = LightConfig_FixtureConfig_dmx_tag;
    light1->config.dmx.type = LightConfig_FixtureConfig_DMXConfig_FixtureTypeConfig_ELV;
    light1->config.dmx.channels_count = 1;
    light1->config.dmx.channels[0] = 5;
    light1->config.dmx.has_params = false;

    LightConfig_FixtureConfig *light2 = &smallLED->fixtures[0];
    light2->min_brightness = 0;
    light2->max_brightness = 100;
    light2->type = LightConfig_FixtureConfig_FixtureType_DMX; // Set the type field
    light2->which_config = LightConfig_FixtureConfig_dmx_tag;
    light2->config.dmx.type = LightConfig_FixtureConfig_DMXConfig_FixtureTypeConfig_ELV;
    light2->config.dmx.channels_count = 1;
    light2->config.dmx.channels[0] = 4;
    light2->config.dmx.has_params = false;

    LightConfig_FixtureConfig *zeroToTenLight = &canboLight->fixtures[0];
    zeroToTenLight->min_brightness = 0;
    zeroToTenLight->max_brightness = 100;
    zeroToTenLight->type = LightConfig_FixtureConfig_FixtureType_ZERO_TO_TEN_VOLT; // Set the type field
    zeroToTenLight->which_config = LightConfig_FixtureConfig_zero_to_ten_volt_tag;
    zeroToTenLight->config.zero_to_ten_volt.node_id = 51;
    zeroToTenLight->config.zero_to_ten_volt.use_relay = true;
    zeroToTenLight->config.zero_to_ten_volt.type = LightConfig_FixtureConfig_ZeroToTenVoltConfig_Type_SOURCING;
    zeroToTenLight->config.zero_to_ten_volt.out_connector_id = 1;

    LightConfig_FixtureConfig *rfLightFixture = &rfLight->fixtures[0];
    rfLightFixture->min_brightness = 0;
    rfLightFixture->max_brightness = 100;
    rfLightFixture->type = LightConfig_FixtureConfig_FixtureType_RF; // Set the type field
    rfLightFixture->which_config = LightConfig_FixtureConfig_rf_tag;
    rfLightFixture->config.rf.node_id = 42;
    rfLightFixture->config.rf.type = LightConfig_FixtureConfig_RFConfig_Type_DIMMER;

    // LightConfig_FixtureConfig *zeroToTenLight = &lightConfig->fixtures[2];
    // zeroToTenLight->type = LightConfig_FixtureConfig_FixtureType_ZERO_TO_TEN_VOLT;  // Set the type field
    // zeroToTenLight->which_config = LightConfig_FixtureConfig_zero_to_ten_volt_tag;
    // zeroToTenLight->config.zero_to_ten_volt.node_id = 50;
    // zeroToTenLight->config.zero_to_ten_volt.use_relay = true;
    // zeroToTenLight->config.zero_to_ten_volt.type = LightConfig_FixtureConfig_ZeroToTenVoltConfig_Type_SOURCING;
    // zeroToTenLight->config.zero_to_ten_volt.out_connector_id = 1;
    // zeroToTenLight->min_brightness = 0;
    // zeroToTenLight->max_brightness = 100;

    largeLED->fixtures_count = 1;
    smallLED->fixtures_count = 1;
    canboLight->fixtures_count = 1;
    rfLight->fixtures_count = 1;

    g_active_configuration.config.lights_count = 4;

    LightState *largeLEDLightState = &g_active_configuration.state.lights[0];
    largeLEDLightState->id = 1;
    largeLEDLightState->brightness = 0;
    largeLEDLightState->target_value = 0;
    largeLEDLightState->dim_speed_msec = 1000;
    largeLEDLightState->last_modified_time = now;
    largeLEDLightState->last_brightness_before_action = 0;

    LightState *smallLEDLightState = &g_active_configuration.state.lights[1];
    smallLEDLightState->id = 2;
    smallLEDLightState->brightness = 0;
    smallLEDLightState->target_value = 0;
    smallLEDLightState->dim_speed_msec = 1000;
    smallLEDLightState->last_modified_time = now;
    smallLEDLightState->last_brightness_before_action = 0;

    LightState *zeroToTenState = &g_active_configuration.state.lights[2];
    zeroToTenState->id = 3;
    zeroToTenState->brightness = 0;
    zeroToTenState->target_value = 0;
    zeroToTenState->dim_speed_msec = 1000;
    zeroToTenState->last_modified_time = now;
    zeroToTenState->last_brightness_before_action = 0;

    LightState *rfLightState = &g_active_configuration.state.lights[3];
    rfLightState->id = 4;
    rfLightState->brightness = 0;
    rfLightState->target_value = 0;
    rfLightState->dim_speed_msec = 1000;
    rfLightState->last_modified_time = now;
    rfLightState->last_brightness_before_action = 0;

    g_active_configuration.state.lights_count = 4;

    // qr code mapping
    BasestationConfig_NodeQRMapping *canboDevice = &g_active_configuration.config.node_qr_mappings[0];
    strncpy(canboDevice->qr_code, "SC1ABC00011", sizeof(canboDevice->qr_code) - 1);
    canboDevice->qr_code[sizeof(canboDevice->qr_code) - 1] = '\0';
    canboDevice->type = BasestationConfig_NodeQRMapping_DeviceType_CAN;
    canboDevice->node_id = 50;

    BasestationConfig_NodeQRMapping *rfReedDevice = &g_active_configuration.config.node_qr_mappings[1];
    strncpy(rfReedDevice->qr_code, "KW3223005845", sizeof(rfReedDevice->qr_code) - 1);
    rfReedDevice->qr_code[sizeof(rfReedDevice->qr_code) - 1] = '\0';
    rfReedDevice->type = BasestationConfig_NodeQRMapping_DeviceType_RF;
    rfReedDevice->node_id = 41;

    BasestationConfig_NodeQRMapping *rfDimmerDevice = &g_active_configuration.config.node_qr_mappings[2];
    strncpy(rfDimmerDevice->qr_code, "KD2237001057", sizeof(rfDimmerDevice->qr_code) - 1);
    rfDimmerDevice->qr_code[sizeof(rfDimmerDevice->qr_code) - 1] = '\0';
    rfDimmerDevice->type = BasestationConfig_NodeQRMapping_DeviceType_RF;
    rfDimmerDevice->node_id = 42;

    BasestationConfig_NodeQRMapping *canbo1Device = &g_active_configuration.config.node_qr_mappings[3];
    strncpy(canbo1Device->qr_code, "SC1ABC0001", sizeof(canbo1Device->qr_code) - 1);
    canbo1Device->qr_code[sizeof(canbo1Device->qr_code) - 1] = '\0';
    canbo1Device->type = BasestationConfig_NodeQRMapping_DeviceType_CAN;
    canbo1Device->node_id = 51;

    g_active_configuration.config.node_qr_mappings_count = 4;

    // Create list of actions that can be used
    // Actions for light 1
    Action *action1 = &g_active_configuration.config.actions[0];
    action1->id = 1;
    set_action_target_light(action1, 1);
    action1->dim_speed_msec = 1000;
    action1->target_brightness = 60.0;
    action1->on_brightness = 60.0;
    action1->off_brightness = 0.0;

    Action *action2 = &g_active_configuration.config.actions[1];
    action2->id = 2;
    set_action_target_light(action2, 1);
    action2->dim_speed_msec = 1000;
    action2->target_brightness = 0.0;
    action2->on_brightness = 60.0;
    action2->off_brightness = 0.0;

    Action *action3 = &g_active_configuration.config.actions[2];
    action3->id = 3;
    set_action_target_light(action3, 1);
    action3->dim_speed_msec = 4000;
    action3->target_brightness = 100.0;
    action3->on_brightness = 100.0;
    action3->off_brightness = 0.0;

    Action *action4 = &g_active_configuration.config.actions[3];
    action4->id = 4;
    set_action_target_light(action4, 1);
    action4->dim_speed_msec = 4000;
    action4->target_brightness = 0.0;
    action4->on_brightness = 100.0;
    action4->off_brightness = 0.0;

    // Actions for light 2
    Action *action5 = &g_active_configuration.config.actions[4];
    action5->id = 5;
    set_action_target_light(action5, 2);
    action5->dim_speed_msec = 1000;
    action5->target_brightness = 60.0;
    action5->on_brightness = 60.0;
    action5->off_brightness = 0.0;

    Action *action6 = &g_active_configuration.config.actions[5];
    action6->id = 6;
    set_action_target_light(action6, 2);
    action6->dim_speed_msec = 1000;
    action6->target_brightness = 0.0;
    action6->on_brightness = 60.0;
    action6->off_brightness = 0.0;

    Action *action7 = &g_active_configuration.config.actions[6];
    action7->id = 7;
    set_action_target_light(action7, 2);
    action7->dim_speed_msec = 4000;
    action7->target_brightness = 100.0;
    action7->on_brightness = 100.0;
    action7->off_brightness = 0.0;

    Action *action8 = &g_active_configuration.config.actions[7];
    action8->id = 8;
    set_action_target_light(action8, 2);
    action8->dim_speed_msec = 4000;
    action8->target_brightness = 0.0;
    action8->on_brightness = 100.0;
    action8->off_brightness = 0.0;

    // Actions for light 3
    Action *action9 = &g_active_configuration.config.actions[8];
    action9->id = 9;
    set_action_target_light(action9, 3);
    action9->dim_speed_msec = 1000;
    action9->target_brightness = 60.0;
    action9->on_brightness = 60.0;
    action9->off_brightness = 0.0;

    Action *action10 = &g_active_configuration.config.actions[9];
    action10->id = 10;
    set_action_target_light(action10, 3);
    action10->dim_speed_msec = 1000;
    action10->target_brightness = 0.0;
    action10->on_brightness = 60.0;
    action10->off_brightness = 0.0;

    Action *action11 = &g_active_configuration.config.actions[10];
    action11->id = 11;
    set_action_target_light(action11, 3);
    action11->dim_speed_msec = 4000;
    action11->target_brightness = 100.0;
    action11->on_brightness = 100.0;
    action11->off_brightness = 0.0;

    Action *action12 = &g_active_configuration.config.actions[11];
    action12->id = 12;
    set_action_target_light(action12, 3);
    action12->dim_speed_msec = 4000;
    action12->target_brightness = 0.0;
    action12->on_brightness = 100.0;
    action12->off_brightness = 0.0;

    // Actions for RF reed sensor
    Action *action13 = &g_active_configuration.config.actions[12];
    action13->id = 13;
    set_action_target_light(action13, 1);
    action13->dim_speed_msec = 2000;
    action13->target_brightness = 50.0;
    action13->activate_delay_msec = 0;

    Action *action14 = &g_active_configuration.config.actions[13];
    action14->id = 14;
    set_action_target_light(action14, 1);
    action14->dim_speed_msec = 8000;
    action14->target_brightness = 0.0;
    action14->activate_delay_msec = 3000;

    // Actions for RF dimmer
    Action *action15 = &g_active_configuration.config.actions[14];
    action15->id = 15;
    set_action_target_light(action15, 1);
    action15->dim_speed_msec = 5000;
    action15->target_brightness = 0.0;
    action15->on_brightness = 100.0;
    action15->off_brightness = 0.0;

    Action *action16 = &g_active_configuration.config.actions[15];
    action16->id = 16;
    set_action_target_light(action16, 1);
    action16->dim_speed_msec = 2000;
    action16->target_brightness = 0.0;
    action16->on_brightness = 50.0;
    action16->off_brightness = 0.0;

    Action *action17 = &g_active_configuration.config.actions[16];
    action17->id = 17;
    set_action_target_light(action17, 1);
    action17->dim_speed_msec = 2000;
    action17->target_brightness = 0.0;
    action17->on_brightness = 10.0;
    action17->off_brightness = 0.0;

    Action *action18 = &g_active_configuration.config.actions[17];
    action18->id = 18;
    set_action_target_light(action18, 1);
    action18->dim_speed_msec = 3000;
    action18->target_brightness = 100.0;

    g_active_configuration.config.actions_count = 18;

    // canbo setup
    CanboConfig *canboConfig = &g_active_configuration.config.canbo_configs[0];
    canboConfig->node_id = 50;
    canboConfig->two_pin_inputs_count = 0;

    canboConfig->three_pin_inputs[0].connector_id = 1;
    canboConfig->three_pin_inputs[0].type = CanboConfig_ThreePinInput_ConnectorType_TOGGLE;
    canboConfig->three_pin_inputs[0].which_config = CanboConfig_ThreePinInput_toggle_tag; // Set the oneof field
    canboConfig->three_pin_inputs[0].config.toggle.up_click[0] = 1;
    canboConfig->three_pin_inputs[0].config.toggle.up_click_count = 1;
    canboConfig->three_pin_inputs[0].config.toggle.down_click[0] = 2;
    canboConfig->three_pin_inputs[0].config.toggle.down_click_count = 1;
    canboConfig->three_pin_inputs[0].config.toggle.up_hold[0] = 3;
    canboConfig->three_pin_inputs[0].config.toggle.up_hold_count = 1;
    canboConfig->three_pin_inputs[0].config.toggle.down_hold[0] = 4;
    canboConfig->three_pin_inputs[0].config.toggle.down_hold_count = 1;

    canboConfig->three_pin_inputs[1].connector_id = 3;
    canboConfig->three_pin_inputs[1].type = CanboConfig_ThreePinInput_ConnectorType_TOGGLE;
    canboConfig->three_pin_inputs[1].which_config = CanboConfig_ThreePinInput_toggle_tag; // Set the oneof field
    canboConfig->three_pin_inputs[1].config.toggle.up_click[0] = 5;
    canboConfig->three_pin_inputs[1].config.toggle.up_click_count = 1;
    canboConfig->three_pin_inputs[1].config.toggle.down_click[0] = 6;
    canboConfig->three_pin_inputs[1].config.toggle.down_click_count = 1;
    canboConfig->three_pin_inputs[1].config.toggle.up_hold[0] = 7;
    canboConfig->three_pin_inputs[1].config.toggle.up_hold_count = 1;
    canboConfig->three_pin_inputs[1].config.toggle.down_hold[0] = 8;
    canboConfig->three_pin_inputs[1].config.toggle.down_hold_count = 1;

    // Add a momentary button configuration for testing
    // canboConfig->three_pin_inputs[1].connector_id = 2;
    // canboConfig->three_pin_inputs[1].type = CanboConfig_ThreePinInput_ConnectorType_MOMENTARY;
    // canboConfig->three_pin_inputs[1].which_config = CanboConfig_ThreePinInput_momentary_tag;  // Set the oneof field
    // canboConfig->three_pin_inputs[1].config.momentary.up_click[0].dim_speed_msec = 1000;
    // canboConfig->three_pin_inputs[1].config.momentary.up_click[0].light_id = 1;
    // canboConfig->three_pin_inputs[1].config.momentary.up_click[0].target_brightness = 25.0;  // Initial target
    // canboConfig->three_pin_inputs[1].config.momentary.up_click[0].on_brightness = 25.0;
    // canboConfig->three_pin_inputs[1].config.momentary.up_click[0].off_brightness = 0.0;
    // canboConfig->three_pin_inputs[1].config.momentary.up_click_count = 1;
    // canboConfig->three_pin_inputs[1].config.momentary.up_hold[0].dim_speed_msec = 4000;
    // canboConfig->three_pin_inputs[1].config.momentary.up_hold[0].light_id = 1;
    // canboConfig->three_pin_inputs[1].config.momentary.up_hold[0].target_brightness = 100.0;
    // canboConfig->three_pin_inputs[1].config.momentary.up_hold_count = 1;

    canboConfig->three_pin_inputs_count = 2;

    CanboConfig *canboConfig1 = &g_active_configuration.config.canbo_configs[1];
    canboConfig1->node_id = 51;
    canboConfig1->two_pin_inputs_count = 0;
    canboConfig1->three_pin_inputs_count = 1;

    canboConfig1->three_pin_inputs[0].connector_id = 1;
    canboConfig1->three_pin_inputs[0].type = CanboConfig_ThreePinInput_ConnectorType_TOGGLE;
    canboConfig1->three_pin_inputs[0].which_config = CanboConfig_ThreePinInput_toggle_tag; // Set the oneof field
    canboConfig1->three_pin_inputs[0].config.toggle.up_click[0] = 9;
    canboConfig1->three_pin_inputs[0].config.toggle.up_click_count = 1;
    canboConfig1->three_pin_inputs[0].config.toggle.down_click[0] = 10;
    canboConfig1->three_pin_inputs[0].config.toggle.down_click_count = 1;
    canboConfig1->three_pin_inputs[0].config.toggle.up_hold[0] = 11;
    canboConfig1->three_pin_inputs[0].config.toggle.up_hold_count = 1;
    canboConfig1->three_pin_inputs[0].config.toggle.down_hold[0] = 12;
    canboConfig1->three_pin_inputs[0].config.toggle.down_hold_count = 1;

    g_active_configuration.config.canbo_configs_count = 2;

    // rf reed config
    RFReedSensorConfig *rfReedConfig = &g_active_configuration.config.rf_reed_configs[0];
    rfReedConfig->node_id = 41;
    rfReedConfig->door_open[0] = 13;
    rfReedConfig->door_open_count = 1;
    rfReedConfig->door_close[0] = 14;
    rfReedConfig->door_close_count = 1;

    g_active_configuration.config.rf_reed_configs_count = 1;

    // rf dimmer config
    RFDimmerConfig *rfDimmerConfig = &g_active_configuration.config.rf_dimmer_configs[0];
    rfDimmerConfig->node_id = 42;
    rfDimmerConfig->up_button_click[0] = 15;
    rfDimmerConfig->up_button_click_count = 1;
    rfDimmerConfig->middle_button_click[0] = 16;
    rfDimmerConfig->middle_button_click_count = 1;
    rfDimmerConfig->down_button_click[0] = 17;
    rfDimmerConfig->down_button_click_count = 1;
    rfDimmerConfig->middle_button_hold[0] = 18;
    rfDimmerConfig->middle_button_hold_count = 1;

    g_active_configuration.config.rf_dimmer_configs_count = 1;

    // setup rf
    g_active_configuration.config.rf_config.channel = 15;
    g_active_configuration.config.rf_config.network = 40;
    g_active_configuration.config.has_rf_config = true;

    // setup wifi
    BasestationConfig_WifiConfig *wifiConfig = &g_active_configuration.config.wifi_config;
    strncpy(wifiConfig->ssid, "Good Machine", sizeof(wifiConfig->ssid) - 1);
    wifiConfig->ssid[sizeof(wifiConfig->ssid) - 1] = '\0';
    strncpy(wifiConfig->password, "1500trains", sizeof(wifiConfig->password) - 1);
    wifiConfig->password[sizeof(wifiConfig->password) - 1] = '\0';
    g_active_configuration.config.has_wifi_config = true;

    // provisioning (remove this if you want to test provisioning) --- RF REED
    ProvisioningState *provisioning_state = get_provisioned_device_state_from_node_id(41);
    provisioning_state->is_provisioned = false;
    provisioning_state->last_seen_time = get_time_in_ms();
    provisioning_state->error_code = ProvisioningState_ProvisioningErrorCode_NONE;

    // provisioning (remove this if you want to test provisioning) --- DIMMER
    ProvisioningState *rf_dimmer_provisioning_state = get_provisioned_device_state_from_node_id(42);
    rf_dimmer_provisioning_state->is_provisioned = false;
    rf_dimmer_provisioning_state->last_seen_time = get_time_in_ms();
    rf_dimmer_provisioning_state->error_code = ProvisioningState_ProvisioningErrorCode_NONE;

    // provisioning (remove this if you want to test provisioning) --- CANBO
    ProvisioningState *canbo_provisioning_state = get_provisioned_device_state_from_node_id(50);
    canbo_provisioning_state->is_provisioned = true;
    canbo_provisioning_state->last_seen_time = get_time_in_ms();
    canbo_provisioning_state->error_code = ProvisioningState_ProvisioningErrorCode_NONE;

    // provisioning (remove this if you want to test provisioning) --- CANBO 1
    ProvisioningState *canbo1_provisioning_state = get_provisioned_device_state_from_node_id(51);
    canbo1_provisioning_state->is_provisioned = true;
    canbo1_provisioning_state->last_seen_time = get_time_in_ms();
    canbo1_provisioning_state->error_code = ProvisioningState_ProvisioningErrorCode_NONE;
}

ProvisioningState *get_provisioned_device_state_from_node_id(uint8_t ucNodeId) {
    for (uint8_t i = 0; i < g_active_configuration.state.provisioned_devices_count; i++) {
        if (g_active_configuration.state.provisioned_devices[i].node_id == ucNodeId) {
            return &g_active_configuration.state.provisioned_devices[i];
        }
    }
    // create a new provisioning state
    ProvisioningState *provisioning_state = &g_active_configuration.state.provisioned_devices[g_active_configuration.state.provisioned_devices_count];
    provisioning_state->node_id = ucNodeId;
    provisioning_state->is_provisioned = false;
    provisioning_state->last_seen_time = 0;
    provisioning_state->error_code = ProvisioningState_ProvisioningErrorCode_NONE;
    g_active_configuration.state.provisioned_devices_count++;
    return provisioning_state;
}

bool has_provisioned_device_state_for_node_id(uint8_t ucNodeId) {
    for (uint8_t i = 0; i < g_active_configuration.state.provisioned_devices_count; i++) {
        if (g_active_configuration.state.provisioned_devices[i].node_id == ucNodeId) {
            return true;
        }
    }
    return false;
}

CanboConfig *get_canbo_config_from_node_id(uint8_t ucNodeId) {
    for (uint8_t i = 0; i < g_active_configuration.config.canbo_configs_count; i++) {
        if (g_active_configuration.config.canbo_configs[i].node_id == ucNodeId) {
            return &g_active_configuration.config.canbo_configs[i];
        }
    }
    return nullptr;
}

RFDimmerConfig *get_dimmer_config_from_node_id(uint8_t ucNodeId) {
    for (uint8_t i = 0; i < g_active_configuration.config.rf_dimmer_configs_count; i++) {
        if (g_active_configuration.config.rf_dimmer_configs[i].node_id == ucNodeId) {
            return &g_active_configuration.config.rf_dimmer_configs[i];
        }
    }
    return nullptr;
}

LightConfig *get_light_config_from_id(uint8_t light_id) {
    for (uint8_t i = 0; i < g_active_configuration.config.lights_count; i++) {
        if (g_active_configuration.config.lights[i].id == light_id) {
            return &g_active_configuration.config.lights[i];
        }
    }
    return nullptr;
}

LightState *get_light_state_from_id(uint8_t light_id) {
    for (uint8_t i = 0; i < g_active_configuration.state.lights_count; i++) {
        if (g_active_configuration.state.lights[i].id == light_id) {
            return &g_active_configuration.state.lights[i];
        }
    }
    return nullptr;
}

RFReedState *get_rf_reed_state_from_node_id(uint8_t ucNodeId) {
    for (uint8_t i = 0; i < g_active_configuration.state.reeds_count; i++) {
        if (g_active_configuration.state.reeds[i].node_id == ucNodeId) {
            return &g_active_configuration.state.reeds[i];
        }
    }
    // create a new rf reed state
    RFReedState *rf_reed_state = &g_active_configuration.state.reeds[g_active_configuration.state.reeds_count];
    rf_reed_state->node_id = ucNodeId;
    rf_reed_state->sensor_status = RFReedState_Status_UNKNOWN;
    rf_reed_state->last_modified_time = get_time_in_ms();
    rf_reed_state->battery_voltage = 0.0;
    g_active_configuration.state.reeds_count++;
    return rf_reed_state;
}

ButtonState *get_button_state_from_node_id(uint8_t ucNodeId) {
    for (uint8_t i = 0; i < g_active_configuration.state.buttons_count; i++) {
        if (g_active_configuration.state.buttons[i].node_id == ucNodeId) {
            return &g_active_configuration.state.buttons[i];
        }
    }
    // create a new button state
    ButtonState *button_state = &g_active_configuration.state.buttons[g_active_configuration.state.buttons_count];
    button_state->node_id = ucNodeId;
    button_state->current_state = ButtonState_State_BUTTON_STATE_RELEASED;
    button_state->last_modified_time = get_time_in_ms();
    g_active_configuration.state.buttons_count++;
    return button_state;
}

void pretty_print_active_configuration() {
    printf("\n=== Active Configuration ===\n");

    // Print Basestation Config
    printf("\nBasestation Config:\n");
    // printf("  Name: %s\n", g_active_configuration.config.name);
    printf("  Version: %s\n", g_active_configuration.config.version);
    // printf("  Room ID: %s\n", g_active_configuration.config.room_id);
    printf("  Node ID: %u\n", g_active_configuration.config.id);

    // Print RF Config if present
    if (g_active_configuration.config.has_rf_config) {
        printf("\n  RF Config:\n");
        printf("    Channel: %u\n", g_active_configuration.config.rf_config.channel);
        printf("    Network: %u\n", g_active_configuration.config.rf_config.network);
        printf("    Basestation ID: %u\n", g_active_configuration.config.id);
    }

    // print wifi config if present
    if (g_active_configuration.config.has_wifi_config) {
        printf("\n  WiFi Config:\n");
        printf("    SSID: %s\n", g_active_configuration.config.wifi_config.ssid);
        printf("    Password: %s\n", g_active_configuration.config.wifi_config.password);
    }

    // print dhcp config if present
    if (g_active_configuration.config.has_dhcp_config) {
        printf("\n  DHCP Config:\n");
        printf("    Static IP: %s\n", g_active_configuration.config.dhcp_config.static_ip ? "true" : "false");
        printf("    IP Address: %s\n", g_active_configuration.config.dhcp_config.ip_address);
        printf("    Subnet Mask: %s\n", g_active_configuration.config.dhcp_config.subnet_mask);
        printf("    Gateway: %s\n", g_active_configuration.config.dhcp_config.gateway);
    }

    // print mac config
    if (g_active_configuration.config.has_mac_config) {
        printf("\n  MAC Config:\n");
        printf("    MAC Address: %s\n", g_active_configuration.config.mac_config.mac_address);
    }

    // Print Lights
    printf("\n  Lights (%u):\n", g_active_configuration.config.lights_count);
    for (size_t i = 0; i < g_active_configuration.config.lights_count; i++) {
        const LightConfig &light = g_active_configuration.config.lights[i];
        printf("    Light %zu: ID %u\n", i, light.id);
        printf("      Dim Speed: %u msec\n", light.dim_speed_msec);
        printf("      Fixtures (%u):\n", light.fixtures_count);

        for (size_t j = 0; j < light.fixtures_count; j++) {
            const LightConfig_FixtureConfig &fixture = light.fixtures[j];
            printf("        Fixture %zu:\n", j);
            // printf("          UUID: %s\n", fixture.uuid);
            printf("          Brightness Range: %.02f-%.02f\n", fixture.min_brightness, fixture.max_brightness);

            // Print fixture specific config
            if (fixture.type == LightConfig_FixtureConfig_FixtureType_DMX) {
                printf("          Type: %d\n", fixture.config.dmx.type);
                printf("          DMX Channels (%u):\n", fixture.config.dmx.channels_count);
                for (size_t k = 0; k < fixture.config.dmx.channels_count; k++) {
                    printf("            Channel %zu: %u\n", k, fixture.config.dmx.channels[k]);
                }
                if (fixture.config.dmx.has_rgb) {
                    printf("          RGB: R=%u G=%u B=%u\n",
                           fixture.config.dmx.rgb.red,
                           fixture.config.dmx.rgb.green,
                           fixture.config.dmx.rgb.blue);
                }
            } else if (fixture.type == LightConfig_FixtureConfig_FixtureType_ZERO_TO_TEN_VOLT) {
                if (fixture.config.zero_to_ten_volt.type == LightConfig_FixtureConfig_ZeroToTenVoltConfig_Type_SOURCING) {
                    printf("          Type: Sourcing\n");
                } else if (fixture.config.zero_to_ten_volt.type == LightConfig_FixtureConfig_ZeroToTenVoltConfig_Type_SINKING) {
                    printf("          Type: Sinking\n");
                } else {
                    printf("          Type: Disabled\n");
                }
                printf("          Node ID: %d\n", fixture.config.zero_to_ten_volt.node_id);
                printf("          Use Relay: %d\n", fixture.config.zero_to_ten_volt.use_relay);
                printf("          Out Connector ID: %u\n", fixture.config.zero_to_ten_volt.out_connector_id);
            } else if (fixture.type == LightConfig_FixtureConfig_FixtureType_RELAY) {
                printf("          Type: RELAY\n");
            }
        }
    }

    // Print Canbo Configs
    printf("\n  Canbo Configs (%u):\n", g_active_configuration.config.canbo_configs_count);
    for (size_t i = 0; i < g_active_configuration.config.canbo_configs_count; i++) {
        const CanboConfig &canbo = g_active_configuration.config.canbo_configs[i];
        printf("    Canbo %zu: Node ID %u\n", i, canbo.node_id);
        printf("      Toggle Inputs: %u\n", canbo.three_pin_inputs_count);
        for (size_t j = 0; j < canbo.three_pin_inputs_count; j++) {
            const CanboConfig_ThreePinInput &three_pin_input = canbo.three_pin_inputs[j];
            printf("        Three Pin Input %zu: Connector ID %u\n", j, three_pin_input.connector_id);
            printf("          Type: %s\n", three_pin_input.type == CanboConfig_ThreePinInput_ConnectorType_TOGGLE ? "TOGGLE" : "MOMENTARY");

            if (three_pin_input.type == CanboConfig_ThreePinInput_ConnectorType_TOGGLE) {
                // Print Up Press Actions
                printf("          Up Press Actions (%u):\n", three_pin_input.config.toggle.up_press_count);
                for (size_t k = 0; k < three_pin_input.config.toggle.up_press_count; k++) {
                    uint32_t action_id = three_pin_input.config.toggle.up_press[k];
                    printf("            Action %zu: ID %u\n", k, action_id);
                    // Find and print action details
                    for (size_t a = 0; a < g_active_configuration.config.actions_count; a++) {
                        if (g_active_configuration.config.actions[a].id == action_id) {
                            Action *action = &g_active_configuration.config.actions[a];
                            char target_buffer[32];
                            printf("              %s, Target Brightness %.2f, Dim Speed %u ms, Delay %u ms\n",
                                   format_action_target(action, target_buffer, sizeof(target_buffer)), 
                                   action->target_brightness, action->dim_speed_msec, action->delay_in_msec);
                            break;
                        }
                    }
                }

                // Print Down Press Actions
                printf("          Down Press Actions (%u):\n", three_pin_input.config.toggle.down_press_count);
                for (size_t k = 0; k < three_pin_input.config.toggle.down_press_count; k++) {
                    uint32_t action_id = three_pin_input.config.toggle.down_press[k];
                    printf("            Action %zu: ID %u\n", k, action_id);
                    // Find and print action details
                    for (size_t a = 0; a < g_active_configuration.config.actions_count; a++) {
                        if (g_active_configuration.config.actions[a].id == action_id) {
                            Action *action = &g_active_configuration.config.actions[a];
                            char target_buffer[32];
                            printf("              %s, Target Brightness %.2f, Dim Speed %u ms, Delay %u ms\n",
                                   format_action_target(action, target_buffer, sizeof(target_buffer)), 
                                   action->target_brightness, action->dim_speed_msec, action->delay_in_msec);
                            break;
                        }
                    }
                }

                // Print Up Release Actions
                printf("          Up Release Actions (%u):\n", three_pin_input.config.toggle.up_release_count);
                for (size_t k = 0; k < three_pin_input.config.toggle.up_release_count; k++) {
                    uint32_t action_id = three_pin_input.config.toggle.up_release[k];
                    printf("            Action %zu: ID %u\n", k, action_id);
                    // Find and print action details
                    for (size_t a = 0; a < g_active_configuration.config.actions_count; a++) {
                        if (g_active_configuration.config.actions[a].id == action_id) {
                            Action *action = &g_active_configuration.config.actions[a];
                            char target_buffer[32];
                            printf("              %s, Target Brightness %.2f, Dim Speed %u ms, Delay %u ms\n",
                                   format_action_target(action, target_buffer, sizeof(target_buffer)), 
                                   action->target_brightness, action->dim_speed_msec, action->delay_in_msec);
                            break;
                        }
                    }
                }

                // Print Down Release Actions
                printf("          Down Release Actions (%u):\n", three_pin_input.config.toggle.down_release_count);
                for (size_t k = 0; k < three_pin_input.config.toggle.down_release_count; k++) {
                    uint32_t action_id = three_pin_input.config.toggle.down_release[k];
                    printf("            Action %zu: ID %u\n", k, action_id);
                    // Find and print action details
                    for (size_t a = 0; a < g_active_configuration.config.actions_count; a++) {
                        if (g_active_configuration.config.actions[a].id == action_id) {
                            Action *action = &g_active_configuration.config.actions[a];
                            char target_buffer[32];
                            printf("              %s, Target Brightness %.2f, Dim Speed %u ms, Delay %u ms\n",
                                   format_action_target(action, target_buffer, sizeof(target_buffer)), 
                                   action->target_brightness, action->dim_speed_msec, action->delay_in_msec);
                            break;
                        }
                    }
                }

                // Print Up Click Actions
                printf("          Up Click Actions (%u):\n", three_pin_input.config.toggle.up_click_count);
                for (size_t k = 0; k < three_pin_input.config.toggle.up_click_count; k++) {
                    uint32_t action_id = three_pin_input.config.toggle.up_click[k];
                    printf("            Action %zu: ID %u\n", k, action_id);
                    // Find and print action details
                    for (size_t a = 0; a < g_active_configuration.config.actions_count; a++) {
                        if (g_active_configuration.config.actions[a].id == action_id) {
                            Action *action = &g_active_configuration.config.actions[a];
                            char target_buffer[32];
                            printf("              %s, Target Brightness %.2f, Dim Speed %u ms, Delay %u ms\n",
                                   format_action_target(action, target_buffer, sizeof(target_buffer)), 
                                   action->target_brightness, action->dim_speed_msec, action->delay_in_msec);
                            break;
                        }
                    }
                }

                // Print Down Click Actions
                printf("          Down Click Actions (%u):\n", three_pin_input.config.toggle.down_click_count);
                for (size_t k = 0; k < three_pin_input.config.toggle.down_click_count; k++) {
                    uint32_t action_id = three_pin_input.config.toggle.down_click[k];
                    printf("            Action %zu: ID %u\n", k, action_id);
                    // Find and print action details
                    for (size_t a = 0; a < g_active_configuration.config.actions_count; a++) {
                        if (g_active_configuration.config.actions[a].id == action_id) {
                            Action *action = &g_active_configuration.config.actions[a];
                            char target_buffer[32];
                            printf("              %s, Target Brightness %.2f, Dim Speed %u ms, Delay %u ms\n",
                                   format_action_target(action, target_buffer, sizeof(target_buffer)), 
                                   action->target_brightness, action->dim_speed_msec, action->delay_in_msec);
                            break;
                        }
                    }
                }

                // Print Up Hold Actions
                printf("          Up Hold Actions (%u):\n", three_pin_input.config.toggle.up_hold_count);
                for (size_t k = 0; k < three_pin_input.config.toggle.up_hold_count; k++) {
                    uint32_t action_id = three_pin_input.config.toggle.up_hold[k];
                    printf("            Action %zu: ID %u\n", k, action_id);
                    // Find and print action details
                    for (size_t a = 0; a < g_active_configuration.config.actions_count; a++) {
                        if (g_active_configuration.config.actions[a].id == action_id) {
                            Action *action = &g_active_configuration.config.actions[a];
                            char target_buffer[32];
                            printf("              %s, Target Brightness %.2f, Dim Speed %u ms, Delay %u ms\n",
                                   format_action_target(action, target_buffer, sizeof(target_buffer)), 
                                   action->target_brightness, action->dim_speed_msec, action->delay_in_msec);
                            break;
                        }
                    }
                }

                // Print Down Hold Actions
                printf("          Down Hold Actions (%u):\n", three_pin_input.config.toggle.down_hold_count);
                for (size_t k = 0; k < three_pin_input.config.toggle.down_hold_count; k++) {
                    uint32_t action_id = three_pin_input.config.toggle.down_hold[k];
                    printf("            Action %zu: ID %u\n", k, action_id);
                    // Find and print action details
                    for (size_t a = 0; a < g_active_configuration.config.actions_count; a++) {
                        if (g_active_configuration.config.actions[a].id == action_id) {
                            Action *action = &g_active_configuration.config.actions[a];
                            char target_buffer[32];
                            printf("              %s, Target Brightness %.2f, Dim Speed %u ms, Delay %u ms\n",
                                   format_action_target(action, target_buffer, sizeof(target_buffer)), 
                                   action->target_brightness, action->dim_speed_msec, action->delay_in_msec);
                            break;
                        }
                    }
                }

                // Print Up Hold Release Actions
                printf("          Up Hold Release Actions (%u):\n", three_pin_input.config.toggle.up_hold_release_count);
                for (size_t k = 0; k < three_pin_input.config.toggle.up_hold_release_count; k++) {
                    uint32_t action_id = three_pin_input.config.toggle.up_hold_release[k];
                    printf("            Action %zu: ID %u\n", k, action_id);
                    // Find and print action details
                    for (size_t a = 0; a < g_active_configuration.config.actions_count; a++) {
                        if (g_active_configuration.config.actions[a].id == action_id) {
                            Action *action = &g_active_configuration.config.actions[a];
                            char target_buffer[32];
                            printf("              %s, Target Brightness %.2f, Dim Speed %u ms, Delay %u ms\n",
                                   format_action_target(action, target_buffer, sizeof(target_buffer)), 
                                   action->target_brightness, action->dim_speed_msec, action->delay_in_msec);
                            break;
                        }
                    }
                }

                // Print Down Hold Release Actions
                printf("          Down Hold Release Actions (%u):\n", three_pin_input.config.toggle.down_hold_release_count);
                for (size_t k = 0; k < three_pin_input.config.toggle.down_hold_release_count; k++) {
                    uint32_t action_id = three_pin_input.config.toggle.down_hold_release[k];
                    printf("            Action %zu: ID %u\n", k, action_id);
                    // Find and print action details
                    for (size_t a = 0; a < g_active_configuration.config.actions_count; a++) {
                        if (g_active_configuration.config.actions[a].id == action_id) {
                            Action *action = &g_active_configuration.config.actions[a];
                            char target_buffer[32];
                            printf("              %s, Target Brightness %.2f, Dim Speed %u ms, Delay %u ms\n",
                                   format_action_target(action, target_buffer, sizeof(target_buffer)), 
                                   action->target_brightness, action->dim_speed_msec, action->delay_in_msec);
                            break;
                        }
                    }
                }
            } else if (three_pin_input.type == CanboConfig_ThreePinInput_ConnectorType_MOMENTARY) {
                // Print Up Press Actions
                printf("          Up Press Actions (%u):\n", three_pin_input.config.momentary.up_press_count);
                for (size_t k = 0; k < three_pin_input.config.momentary.up_press_count; k++) {
                    uint32_t action_id = three_pin_input.config.momentary.up_press[k];
                    printf("            Action %zu: ID %u\n", k, action_id);
                    // Find and print action details
                    for (size_t a = 0; a < g_active_configuration.config.actions_count; a++) {
                        if (g_active_configuration.config.actions[a].id == action_id) {
                            Action *action = &g_active_configuration.config.actions[a];
                            char target_buffer[32];
                            printf("              %s, Target Brightness %.2f, Dim Speed %u ms, Delay %u ms\n",
                                   format_action_target(action, target_buffer, sizeof(target_buffer)), 
                                   action->target_brightness, action->dim_speed_msec, action->delay_in_msec);
                            break;
                        }
                    }
                }

                // Print Up Release Actions
                printf("          Up Release Actions (%u):\n", three_pin_input.config.momentary.up_release_count);
                for (size_t k = 0; k < three_pin_input.config.momentary.up_release_count; k++) {
                    uint32_t action_id = three_pin_input.config.momentary.up_release[k];
                    printf("            Action %zu: ID %u\n", k, action_id);
                    // Find and print action details
                    for (size_t a = 0; a < g_active_configuration.config.actions_count; a++) {
                        if (g_active_configuration.config.actions[a].id == action_id) {
                            Action *action = &g_active_configuration.config.actions[a];
                            char target_buffer[32];
                            printf("              %s, Target Brightness %.2f, Dim Speed %u ms, Delay %u ms\n",
                                   format_action_target(action, target_buffer, sizeof(target_buffer)), 
                                   action->target_brightness, action->dim_speed_msec, action->delay_in_msec);
                            break;
                        }
                    }
                }

                // Print Up Click Actions
                printf("          Up Click Actions (%u):\n", three_pin_input.config.momentary.up_click_count);
                for (size_t k = 0; k < three_pin_input.config.momentary.up_click_count; k++) {
                    uint32_t action_id = three_pin_input.config.momentary.up_click[k];
                    printf("            Action %zu: ID %u\n", k, action_id);
                    // Find and print action details
                    for (size_t a = 0; a < g_active_configuration.config.actions_count; a++) {
                        if (g_active_configuration.config.actions[a].id == action_id) {
                            Action *action = &g_active_configuration.config.actions[a];
                            char target_buffer[32];
                            printf("              %s, Target Brightness %.2f, Dim Speed %u ms, Delay %u ms\n",
                                   format_action_target(action, target_buffer, sizeof(target_buffer)), 
                                   action->target_brightness, action->dim_speed_msec, action->delay_in_msec);
                            break;
                        }
                    }
                }

                // Print Up Hold Actions
                printf("          Up Hold Actions (%u):\n", three_pin_input.config.momentary.up_hold_count);
                for (size_t k = 0; k < three_pin_input.config.momentary.up_hold_count; k++) {
                    uint32_t action_id = three_pin_input.config.momentary.up_hold[k];
                    printf("            Action %zu: ID %u\n", k, action_id);
                    // Find and print action details
                    for (size_t a = 0; a < g_active_configuration.config.actions_count; a++) {
                        if (g_active_configuration.config.actions[a].id == action_id) {
                            Action *action = &g_active_configuration.config.actions[a];
                            char target_buffer[32];
                            printf("              %s, Target Brightness %.2f, Dim Speed %u ms, Delay %u ms\n",
                                   format_action_target(action, target_buffer, sizeof(target_buffer)), 
                                   action->target_brightness, action->dim_speed_msec, action->delay_in_msec);
                            break;
                        }
                    }
                }

                // Print Up Hold Release Actions
                printf("          Up Hold Release Actions (%u):\n", three_pin_input.config.momentary.up_hold_release_count);
                for (size_t k = 0; k < three_pin_input.config.momentary.up_hold_release_count; k++) {
                    uint32_t action_id = three_pin_input.config.momentary.up_hold_release[k];
                    printf("            Action %zu: ID %u\n", k, action_id);
                    // Find and print action details
                    for (size_t a = 0; a < g_active_configuration.config.actions_count; a++) {
                        if (g_active_configuration.config.actions[a].id == action_id) {
                            Action *action = &g_active_configuration.config.actions[a];
                            char target_buffer[32];
                            printf("              %s, Target Brightness %.2f, Dim Speed %u ms, Delay %u ms\n",
                                   format_action_target(action, target_buffer, sizeof(target_buffer)), 
                                   action->target_brightness, action->dim_speed_msec, action->delay_in_msec);
                            break;
                        }
                    }
                }
            }
        }
        for (size_t j = 0; j < canbo.two_pin_inputs_count; j++) {
            const CanboConfig_TwoPinInput &two_pin_input = canbo.two_pin_inputs[j];
            printf("        Two Pin Input %zu: Connector ID %u\n", j, two_pin_input.connector_id);
            printf("          Type: %d\n", two_pin_input.type);
        }

        // Print ADC Input config
        if (canbo.has_adc_inputs) {
            const CanboConfig_ADCInput &adc = canbo.adc_inputs;
            printf("        ADC Input: Connector ID %u\n", adc.connector_id);
            printf("          Type: ");
            switch (adc.type) {
                case CanboConfig_ADCInput_ConnectorType_KNOB:
                    printf("KNOB\n");
                    if (adc.which_config == CanboConfig_ADCInput_knob_tag) {
                        printf("          On Turn Actions (%u):\n", adc.config.knob.on_turn_count);
                        for (size_t k = 0; k < adc.config.knob.on_turn_count; k++) {
                            printf("            Action ID: %u\n", adc.config.knob.on_turn[k]);
                        }
                    }
                    break;
                case CanboConfig_ADCInput_ConnectorType_THERMOSTAT:
                    printf("THERMOSTAT\n");
                    if (adc.which_config == CanboConfig_ADCInput_thermostat_tag) {
                        printf("          Thermostat Actions (%u):\n", adc.config.thermostat.thermostat_action_count);
                        for (size_t k = 0; k < adc.config.thermostat.thermostat_action_count; k++) {
                            printf("            Action ID: %u\n", adc.config.thermostat.thermostat_action[k]);
                        }
                    }
                    break;
                case CanboConfig_ADCInput_ConnectorType_PIR:
                    printf("PIR\n");
                    if (adc.which_config == CanboConfig_ADCInput_pir_tag) {
                        printf("          On Activate Actions (%u):\n", adc.config.pir.on_activate_count);
                        for (size_t k = 0; k < adc.config.pir.on_activate_count; k++) {
                            printf("            Action ID: %u\n", adc.config.pir.on_activate[k]);
                        }
                        printf("          On Deactivate Actions (%u):\n", adc.config.pir.on_deactivate_count);
                        for (size_t k = 0; k < adc.config.pir.on_deactivate_count; k++) {
                            printf("            Action ID: %u\n", adc.config.pir.on_deactivate[k]);
                        }
                    }
                    break;
                default:
                    printf("UNKNOWN\n");
                    break;
            }
        }
    }

    // Print RF Reed Configs
    printf("\n  RF Reed Configs (%u):\n", g_active_configuration.config.rf_reed_configs_count);
    for (size_t i = 0; i < g_active_configuration.config.rf_reed_configs_count; i++) {
        const RFReedSensorConfig &reed = g_active_configuration.config.rf_reed_configs[i];
        printf("    Reed %zu: Node ID %u\n", i, reed.node_id);
        printf("      Door Close Actions (%u):\n", reed.door_close_count);
        for (size_t j = 0; j < reed.door_close_count; j++) {
            uint32_t action_id = reed.door_close[j];
            printf("        Action %zu: ID %u\n", j, action_id);
            // Find and print action details
            for (size_t a = 0; a < g_active_configuration.config.actions_count; a++) {
                if (g_active_configuration.config.actions[a].id == action_id) {
                    Action *action = &g_active_configuration.config.actions[a];
                    char target_buffer[32];
                    printf("          %s\n", format_action_target_id(action, target_buffer, sizeof(target_buffer)));
                    printf("          Target Brightness: %.2f\n", action->target_brightness);
                    printf("          Dim Speed: %u msec\n", action->dim_speed_msec);
                    printf("          Activate Delay: %u msec\n", action->activate_delay_msec);
                    break;
                }
            }
        }
        printf("      Door Open Actions (%u):\n", reed.door_open_count);
        for (size_t j = 0; j < reed.door_open_count; j++) {
            uint32_t action_id = reed.door_open[j];
            printf("        Action %zu: ID %u\n", j, action_id);
            // Find and print action details
            for (size_t a = 0; a < g_active_configuration.config.actions_count; a++) {
                if (g_active_configuration.config.actions[a].id == action_id) {
                    Action *action = &g_active_configuration.config.actions[a];
                    char target_buffer[32];
                    printf("          %s\n", format_action_target_id(action, target_buffer, sizeof(target_buffer)));
                    printf("          Target Brightness: %.2f\n", action->target_brightness);
                    printf("          Dim Speed: %u msec\n", action->dim_speed_msec);
                    printf("          Activate Delay: %u msec\n", action->activate_delay_msec);
                    break;
                }
            }
        }
    }

    // Print RF Dimmer Configs
    printf("\n  RF Dimmer Configs (%u):\n", g_active_configuration.config.rf_dimmer_configs_count);
    for (size_t i = 0; i < g_active_configuration.config.rf_dimmer_configs_count; i++) {
        const RFDimmerConfig &dimmer = g_active_configuration.config.rf_dimmer_configs[i];
        printf("    RF Dimmer %zu: Node ID %u\n", i, dimmer.node_id);

        // Print Up Button Click Actions
        if (dimmer.up_button_click_count > 0) {
            printf("      Up Button Click Actions (%u):\n", dimmer.up_button_click_count);
            for (size_t j = 0; j < dimmer.up_button_click_count; j++) {
                uint32_t action_id = dimmer.up_button_click[j];
                printf("        Action %zu: ID %u\n", j, action_id);
                // Find and print action details
                for (size_t a = 0; a < g_active_configuration.config.actions_count; a++) {
                    if (g_active_configuration.config.actions[a].id == action_id) {
                        Action *action = &g_active_configuration.config.actions[a];
                        char target_buffer[32];
                        printf("          %s, Target %.1f%%, On %.1f%%, Off %.1f%%, Dim %u ms\n",
                               format_action_target(action, target_buffer, sizeof(target_buffer)),
                               action->target_brightness, action->on_brightness, action->off_brightness, action->dim_speed_msec);
                        break;
                    }
                }
            }
        }

        // Print Middle Button Click Actions
        if (dimmer.middle_button_click_count > 0) {
            printf("      Middle Button Click Actions (%u):\n", dimmer.middle_button_click_count);
            for (size_t j = 0; j < dimmer.middle_button_click_count; j++) {
                uint32_t action_id = dimmer.middle_button_click[j];
                printf("        Action %zu: ID %u\n", j, action_id);
                // Find and print action details
                for (size_t a = 0; a < g_active_configuration.config.actions_count; a++) {
                    if (g_active_configuration.config.actions[a].id == action_id) {
                        Action *action = &g_active_configuration.config.actions[a];
                        char target_buffer[32];
                        printf("          %s, Target %.1f%%, On %.1f%%, Off %.1f%%, Dim %u ms\n",
                               format_action_target(action, target_buffer, sizeof(target_buffer)),
                               action->target_brightness, action->on_brightness, action->off_brightness, action->dim_speed_msec);
                        break;
                    }
                }
            }
        }

        // Print Down Button Click Actions
        if (dimmer.down_button_click_count > 0) {
            printf("      Down Button Click Actions (%u):\n", dimmer.down_button_click_count);
            for (size_t j = 0; j < dimmer.down_button_click_count; j++) {
                uint32_t action_id = dimmer.down_button_click[j];
                printf("        Action %zu: ID %u\n", j, action_id);
                // Find and print action details
                for (size_t a = 0; a < g_active_configuration.config.actions_count; a++) {
                    if (g_active_configuration.config.actions[a].id == action_id) {
                        Action *action = &g_active_configuration.config.actions[a];
                        char target_buffer[32];
                        printf("          %s, Target %.1f%%, On %.1f%%, Off %.1f%%, Dim %u ms\n",
                               format_action_target(action, target_buffer, sizeof(target_buffer)),
                               action->target_brightness, action->on_brightness, action->off_brightness, action->dim_speed_msec);
                        break;
                    }
                }
            }
        }

        // Print Up Button Hold Actions
        if (dimmer.up_button_hold_count > 0) {
            printf("      Up Button Hold Actions (%u):\n", dimmer.up_button_hold_count);
            for (size_t j = 0; j < dimmer.up_button_hold_count; j++) {
                uint32_t action_id = dimmer.up_button_hold[j];
                printf("        Action %zu: ID %u\n", j, action_id);
                // Find and print action details
                for (size_t a = 0; a < g_active_configuration.config.actions_count; a++) {
                    if (g_active_configuration.config.actions[a].id == action_id) {
                        Action *action = &g_active_configuration.config.actions[a];
                        char target_buffer[32];
                        printf("          %s, Target %.1f%%, On %.1f%%, Off %.1f%%, Dim %u ms\n",
                               format_action_target(action, target_buffer, sizeof(target_buffer)),
                               action->target_brightness, action->on_brightness, action->off_brightness, action->dim_speed_msec);
                        break;
                    }
                }
            }
        }

        // Print Middle Button Hold Actions
        if (dimmer.middle_button_hold_count > 0) {
            printf("      Middle Button Hold Actions (%u):\n", dimmer.middle_button_hold_count);
            for (size_t j = 0; j < dimmer.middle_button_hold_count; j++) {
                uint32_t action_id = dimmer.middle_button_hold[j];
                printf("        Action %zu: ID %u\n", j, action_id);
                // Find and print action details
                for (size_t a = 0; a < g_active_configuration.config.actions_count; a++) {
                    if (g_active_configuration.config.actions[a].id == action_id) {
                        Action *action = &g_active_configuration.config.actions[a];
                        char target_buffer[32];
                        printf("          %s, Target %.1f%%, On %.1f%%, Off %.1f%%, Dim %u ms\n",
                               format_action_target(action, target_buffer, sizeof(target_buffer)),
                               action->target_brightness, action->on_brightness, action->off_brightness, action->dim_speed_msec);
                        break;
                    }
                }
            }
        }

        // Print Down Button Hold Actions
        if (dimmer.down_button_hold_count > 0) {
            printf("      Down Button Hold Actions (%u):\n", dimmer.down_button_hold_count);
            for (size_t j = 0; j < dimmer.down_button_hold_count; j++) {
                uint32_t action_id = dimmer.down_button_hold[j];
                printf("        Action %zu: ID %u\n", j, action_id);
                // Find and print action details
                for (size_t a = 0; a < g_active_configuration.config.actions_count; a++) {
                    if (g_active_configuration.config.actions[a].id == action_id) {
                        Action *action = &g_active_configuration.config.actions[a];
                        char target_buffer[32];
                        printf("          %s, Target %.1f%%, On %.1f%%, Off %.1f%%, Dim %u ms\n",
                               format_action_target(action, target_buffer, sizeof(target_buffer)),
                               action->target_brightness, action->on_brightness, action->off_brightness, action->dim_speed_msec);
                        break;
                    }
                }
            }
        }
    }

    // Print Node QR Mappings
    printf("\n  Node QR Mappings (%u):\n", g_active_configuration.config.node_qr_mappings_count);
    for (size_t i = 0; i < g_active_configuration.config.node_qr_mappings_count; i++) {
        const BasestationConfig_NodeQRMapping &mapping = g_active_configuration.config.node_qr_mappings[i];
        printf("    Mapping %zu:\n", i);
        printf("      QR Code: %s\n", mapping.qr_code);
        printf("      Node ID: %u\n", mapping.node_id);
        printf("      Type: %d\n", mapping.type);
    }

    // Print Light States
    printf("\nLight States (%u):\n", g_active_configuration.state.lights_count);
    for (size_t i = 0; i < g_active_configuration.state.lights_count; i++) {
        const LightState &state = g_active_configuration.state.lights[i];
        printf("  Light %zu:\n", i);
        printf("    ID: %u\n", state.id);
        printf("    Brightness: %.2f\n", state.brightness);
        printf("    Target Brightness: %.2f\n", state.target_value);
        printf("    Dim Speed: %lu msec\n", state.dim_speed_msec);
        printf("    Last Modified: %llu\n", (unsigned long long)state.last_modified_time);
        printf("    Active After: %llu\n", (unsigned long long)state.active_after_time);
    }

    // Print Button States
    printf("\nButton States (%u):\n", g_active_configuration.state.buttons_count);
    for (size_t i = 0; i < g_active_configuration.state.buttons_count; i++) {
        const ButtonState &state = g_active_configuration.state.buttons[i];
        printf("  Button %zu:\n", i);
        printf("    Node ID: %u\n", state.node_id);
        printf("    Current State: %d\n", state.current_state);
        printf("    Last Modified: %llu\n", (unsigned long long)state.last_modified_time);
    }

    // Print Device Provisioning Status
    printf("\nDevice Provisioning Status (%u):\n", g_active_configuration.state.provisioned_devices_count);
    for (size_t i = 0; i < g_active_configuration.state.provisioned_devices_count; i++) {
        const ProvisioningState &state = g_active_configuration.state.provisioned_devices[i];
        printf("  Device %zu:\n", i);
        printf("    Node ID: %u\n", state.node_id);
        printf("    Is Provisioned: %s\n", state.is_provisioned ? "true" : "false");
        printf("    Error Code: %d\n", state.error_code);
        printf("    Last Seen: %llu\n", (unsigned long long)state.last_seen_time);
        printf("    RSSI: %d\n", state.rssi);
    }

    // Print RF Reed States
    printf("\nRF Reed States (%u):\n", g_active_configuration.state.reeds_count);
    for (size_t i = 0; i < g_active_configuration.state.reeds_count; i++) {
        const RFReedState &state = g_active_configuration.state.reeds[i];
        printf("  Reed %zu:\n", i);
        printf("    Node ID: %u\n", state.node_id);
        printf("    Sensor Status: %d\n", state.sensor_status);
        printf("    Last Modified: %llu\n", (unsigned long long)state.last_modified_time);
        printf("    Battery Voltage: %.2f\n", state.battery_voltage);
    }

    printf("\n=== End Configuration ===\n");
}

void update_light_states(uint32_t current_time_ms) {
    for (size_t i = 0; i < g_active_configuration.state.lights_count; i++) {
        LightState *lightState = &g_active_configuration.state.lights[i];

        if (lightState->active_after_time > current_time_ms) {
            // not active yet
            lightState->last_modified_time = current_time_ms;
            continue;
        }

        const uint32_t dim = lightState->dim_speed_msec ? lightState->dim_speed_msec : 1;
        float delta = (100.0f / dim) * (current_time_ms - lightState->last_modified_time);

        // Calculate new brightness based on target and current values
        float newBrightness = lightState->brightness;
        if (lightState->target_value < lightState->brightness) {
            newBrightness = std::max(lightState->target_value, lightState->brightness - delta);
        } else if (lightState->target_value > lightState->brightness) {
            newBrightness = std::min(lightState->target_value, lightState->brightness + delta);
        }

        if (newBrightness != lightState->brightness) {
            lightState->brightness = newBrightness;
            lightState->last_modified_time = current_time_ms;
            lightState->is_transitioning = true;
        } else if (lightState->is_transitioning) {
            lightState->is_transitioning = false;
            lightState->last_modified_time = current_time_ms;
            lightState->last_transition_stop_reason = LightState_TransitionStopReason_TRANSITION_STOP_TARGET_REACHED;
        }
    }
}

#ifdef __EMSCRIPTEN__

// Make buffer size large enough to support bigger room configurations
uint8_t configuration_protobuf_buffer[16384];
size_t configuration_protobuf_buffer_length;

emscripten::val get_active_configuration_emscripten() {
    pb_ostream_t stream = pb_ostream_from_buffer(configuration_protobuf_buffer, sizeof(configuration_protobuf_buffer));
    bool status = pb_encode(&stream, ActiveConfiguration_fields, &g_active_configuration);
    configuration_protobuf_buffer_length = stream.bytes_written;
    if (!status) {
        printf("Failed to encode active configuration: %s\n", PB_GET_ERROR(&stream));
        return emscripten::val(false);
    }

    return emscripten::val(emscripten::typed_memory_view(configuration_protobuf_buffer_length, configuration_protobuf_buffer));
}

void process_all_lights_emscripten() {
    uint32_t now = get_time_in_ms();
    update_light_states(now);
}

EMSCRIPTEN_BINDINGS(active_configuration) {
    emscripten::function("setActiveConfigurationFromProtobuf", &set_active_configuration_from_protobuf_emscripten);
    emscripten::function("getActiveConfiguration", &get_active_configuration_emscripten);
    emscripten::function("setDebugConfiguration", &set_debug_configuration);
    emscripten::function("prettyPrintActiveConfiguration", &pretty_print_active_configuration);
    emscripten::function("processAllLights", &process_all_lights_emscripten);
}
#endif
