#include "message_handler.h"
#include "active_configuration.h"
#include "basestation-config.pb.h"
#include "events.h"
#include <cmath>
#include <pb_decode.h>
#include <pb_encode.h>
#include <stdio.h>
#include <inttypes.h>
#include <string>

#ifndef __EMSCRIPTEN__
  // Include RTC functions for basestation builds
  extern "C" {
  bool rtc_get_current_unix_time(uint32_t *unix_time);
  }
#endif

#define DEBUG_MESSAGE_HANDLER 1
#define CLICK_TIME_THRESHOLD 500

  // Global callback for sending CAN messages and RF messages
  static void (*g_send_can_message_callback)(const uint8_t *data, uint32_t len) = nullptr;
  static void (*g_send_rf_message_callback)(const uint8_t *data, uint32_t len) = nullptr;

  void register_send_can_message_callback(void (*callback)(const uint8_t *data, uint32_t len)) {
      g_send_can_message_callback = callback;
#ifdef DEBUG_MESSAGE_HANDLER
      printf("CAN message callback registered\n");
#endif
  }

  void register_send_rf_message_callback(void (*callback)(const uint8_t *data, uint32_t len)) {
      g_send_rf_message_callback = callback;
#ifdef DEBUG_MESSAGE_HANDLER
      printf("RF message callback registered\n");
#endif
  }

  // Delayed Action System for PIR and other timed actions
  #define MAX_DELAYED_ACTIONS 10
  
  struct DelayedAction {
      uint16_t action_id;
      uint32_t execute_time;  // When to execute this action
      bool is_active;         // Whether this slot is in use
      uint8_t owner_node_id;  // Which node scheduled this (disambiguates shared actions)
  };
  
  static DelayedAction g_delayed_actions[MAX_DELAYED_ACTIONS] = {0};

  // Helper function to find CanboConfig by node_id
  CanboConfig *find_canbo_config_by_node_id(uint8_t node_id) {
      for (uint8_t i = 0; i < g_active_configuration.config.canbo_configs_count; i++) {
#ifdef DEBUG_MESSAGE_HANDLER
          printf("  Canbo config %d: Node ID %d\n", i, g_active_configuration.config.canbo_configs[i].node_id);
#endif
          if (g_active_configuration.config.canbo_configs[i].node_id == node_id) {
              return &g_active_configuration.config.canbo_configs[i];
          }
      }
      return nullptr;
  }

  RFReedSensorConfig *find_rf_reed_sensor_config_by_node_id(uint8_t node_id) {
      for (uint8_t i = 0; i < g_active_configuration.config.rf_reed_configs_count; i++) {
#ifdef DEBUG_MESSAGE_HANDLER
          printf("  RF Reed sensor config %d: Node ID %d\n", i, g_active_configuration.config.rf_reed_configs[i].node_id);
#endif
          if (g_active_configuration.config.rf_reed_configs[i].node_id == node_id) {
              return &g_active_configuration.config.rf_reed_configs[i];
          }
      }
      return nullptr;
  }

  RFReedState *find_rf_reed_state_by_node_id(uint8_t node_id) {
      for (uint8_t i = 0; i < g_active_configuration.state.reeds_count; i++) {
#ifdef DEBUG_MESSAGE_HANDLER
          printf("  RF Reed state %d: Node ID %d\n", i, g_active_configuration.state.reeds[i].node_id);
#endif
          if (g_active_configuration.state.reeds[i].node_id == node_id) {
              return &g_active_configuration.state.reeds[i];
          }
      }
      return nullptr;
  }

  // Helper function to find momentary input by connector_id
  CanboConfig_ThreePinInput *find_momentary_input_by_connector_id(CanboConfig *canbo_config, uint8_t connector_id) {
      // TODO: should the search be the same in this case?
      for (uint8_t i = 0; i < canbo_config->three_pin_inputs_count; i++) {
          if (canbo_config->three_pin_inputs[i].connector_id == connector_id) {
              return &canbo_config->three_pin_inputs[i];
          }
      }
      return nullptr;
  }

  // Helper function to find toggle input by connector_id
  CanboConfig_ThreePinInput *find_toggle_input_by_connector_id(CanboConfig *canbo_config, uint8_t connector_id) {
      for (uint8_t i = 0; i < canbo_config->three_pin_inputs_count; i++) {
          if (canbo_config->three_pin_inputs[i].connector_id == connector_id) {
              return &canbo_config->three_pin_inputs[i];
          }
      }
      return nullptr;
  }

  // Helper function to find or create button state
  ButtonState *find_or_create_button_state(uint8_t node_id, uint8_t connector_id) {
      ButtonState *button_state = nullptr;

      // Find existing button state for this node_id and connector_id combination
      for (uint8_t i = 0; i < g_active_configuration.state.buttons_count; i++) {
          if (g_active_configuration.state.buttons[i].node_id == node_id &&
              g_active_configuration.state.buttons[i].connector_id == connector_id) {
              button_state = &g_active_configuration.state.buttons[i];
              break;
          }
      }

      // Create new button state if not found
      if (!button_state && g_active_configuration.state.buttons_count < 10) {
          button_state = &g_active_configuration.state.buttons[g_active_configuration.state.buttons_count++];
          button_state->node_id = node_id;
          button_state->connector_id = connector_id;
          button_state->current_state = ButtonState_State_BUTTON_STATE_RELEASED;
          button_state->last_modified_time = get_time_in_ms();
      }

      return button_state;
  }

  // Helper function to find light state by light_id
  LightState *find_light_state_by_id(uint8_t light_id) {
      for (uint8_t j = 0; j < g_active_configuration.state.lights_count; j++) {
          if (g_active_configuration.state.lights[j].id == light_id) {
              return &g_active_configuration.state.lights[j];
          }
      }
      return nullptr;
  }

  // Helper function to find or create PIR state
  PIRState *find_or_create_pir_state(uint8_t node_id) {
      PIRState *pir_state = nullptr;

      // Find existing PIR state for this node_id
      for (uint8_t i = 0; i < g_active_configuration.state.pirs_count; i++) {
          if (g_active_configuration.state.pirs[i].node_id == node_id) {
              pir_state = &g_active_configuration.state.pirs[i];
              break;
          }
      }

      // Create new PIR state if not found and we have space
      if (!pir_state && g_active_configuration.state.pirs_count < 10) {
          pir_state = &g_active_configuration.state.pirs[g_active_configuration.state.pirs_count++];
          pir_state->node_id = node_id;
          pir_state->is_activated = false;
          pir_state->last_activated_time = get_time_in_ms();
          pir_state->deactivate_after_time = 0;
      }

      return pir_state;
  }

  // Helper function to find or create provisioning state by node_id
  ProvisioningState *find_or_create_provisioning_state_by_node_id(uint8_t node_id) {
      ProvisioningState *provisioning_state = nullptr;

      // Find existing provisioning state
      for (uint8_t i = 0; i < g_active_configuration.state.provisioned_devices_count; i++) {
          if (g_active_configuration.state.provisioned_devices[i].node_id == node_id) {
              provisioning_state = &g_active_configuration.state.provisioned_devices[i];
              break;
          }
      }

      // Create new provisioning state if not found
      if (!provisioning_state && g_active_configuration.state.provisioned_devices_count < 10) {
          provisioning_state = &g_active_configuration.state.provisioned_devices[g_active_configuration.state.provisioned_devices_count++];
          provisioning_state->node_id = node_id;
          provisioning_state->is_provisioned = false;
          provisioning_state->error_code = ProvisioningState_ProvisioningErrorCode_NONE;
      }

      return provisioning_state;
  }

  LightConfig *find_light_config_by_id(uint8_t light_id) {
      for (uint8_t i = 0; i < g_active_configuration.config.lights_count; i++) {
          if (g_active_configuration.config.lights[i].id == light_id) {
              return &g_active_configuration.config.lights[i];
          }
      }
      return nullptr;
  }

  void apply_light_state_change(LightState *light_state, float target_value, uint32_t dim_speed_msec, uint32_t activate_delay_msec) {
      uint32_t now = get_time_in_ms();
      light_state->target_value = target_value;
      light_state->dim_speed_msec = dim_speed_msec;
      light_state->last_modified_time = now + activate_delay_msec;
      light_state->active_after_time = now + activate_delay_msec + 1; // + 1 to always trigger a state change

      // printf("apply_light_state_change: light_state->id: %d, light_state->target_value: %f, light_state->dim_speed_msec: %d, activate_delay_msec: %d\n", light_state->id, light_state->target_value, light_state->dim_speed_msec, activate_delay_msec);
  }

  // Helper function to find zero to ten volt config by node id
  LightConfig_FixtureConfig_ZeroToTenVoltConfig *find_canbo_zero_to_ten_volt_config_by_node(uint8_t node_id, float *light_min_brightness, float *light_max_brightness) {
      for (uint8_t i = 0; i < g_active_configuration.config.lights_count; i++) {
          for (uint8_t j = 0; j < g_active_configuration.config.lights[i].fixtures_count; j++) {
              if (g_active_configuration.config.lights[i].fixtures[j].type == LightConfig_FixtureConfig_FixtureType_ZERO_TO_TEN_VOLT) {
                  *light_min_brightness = g_active_configuration.config.lights[i].fixtures[j].min_brightness;
                  *light_max_brightness = g_active_configuration.config.lights[i].fixtures[j].max_brightness;
                  return &g_active_configuration.config.lights[i].fixtures[j].config.zero_to_ten_volt;
              }
          }
      }

      return nullptr;
  }

  // Helper function to find action by ID
  Action *find_action_by_id(uint16_t action_id) {
      for (uint8_t i = 0; i < g_active_configuration.config.actions_count; i++) {
          if (g_active_configuration.config.actions[i].id == action_id) {
              return &g_active_configuration.config.actions[i];
          }
      }
      return nullptr;
  }

  // Forward declaration
  void schedule_delayed_action(const Action *action, uint32_t delay_ms, uint32_t current_time, uint8_t owner_node_id);

  // Helper function to apply light actions
  void apply_canbo_light_actions(uint16_t *action_ids, uint8_t action_count, bool stop_at_current = false, bool is_delayed_execution = false) {
    uint32_t now = get_time_in_ms();
    
    for (uint8_t i = 0; i < action_count; i++) {
        Action *action = find_action_by_id(action_ids[i]);
        if (!action) {
#ifdef DEBUG_MESSAGE_HANDLER
            printf("    WARNING: Action ID %d not found\n", action_ids[i]);
#endif
            continue;
        }
#ifdef DEBUG_MESSAGE_HANDLER
        if (action->which_target == Action_light_id_tag) {
            printf("    Action %d: Light ID %d, Target Brightness %.2f, Dim Speed %d ms, Delay %d ms\n",
                   i, action->target.light_id, action->target_brightness, action->dim_speed_msec, action->delay_in_msec);
        } else if (action->which_target == Action_somfy_shade_id_tag) {
            printf("    Action %d: Somfy Shade ID %d, Target Brightness %.2f, Dim Speed %d ms, Delay %d ms\n",
                   i, action->target.somfy_shade_id, action->target_brightness, action->dim_speed_msec, action->delay_in_msec);
        }
#endif

        // If action has a delay, schedule it for later execution (but not if this IS the delayed execution)
        if (action->delay_in_msec > 0 && !stop_at_current && !is_delayed_execution) {
            schedule_delayed_action(action, action->delay_in_msec, now, 0);
            continue;
        }

        // Check if this action targets a light or a shade
        if (action->which_target == Action_light_id_tag) {
            LightState *light_state = find_light_state_by_id(action->target.light_id);
            if (light_state) {
                if (stop_at_current) {
                    // Stop dimming at current brightness
                    apply_light_state_change(light_state, light_state->brightness, action->dim_speed_msec, 0);
                    light_state->last_transition_stop_reason = LightState_TransitionStopReason_TRANSITION_STOP_HOLD_RELEASE;
                } else {
                    // Set to target brightness with synchronization delay
                    uint32_t total_delay = action->activate_delay_msec;
                    apply_light_state_change(light_state, action->target_brightness, action->dim_speed_msec, 0);
                    light_state->last_transition_stop_reason = LightState_TransitionStopReason_TRANSITION_STOP_CLICK_COMMAND;
                }

#ifdef DEBUG_MESSAGE_HANDLER
                printf("    [Dimming] Current light state: Brightness %.2f, Target Value %.2f, Dim Speed %d ms\n",
                       light_state->brightness, light_state->target_value, light_state->dim_speed_msec);
#endif
            }
        } else if (action->which_target == Action_somfy_shade_id_tag) {
            // TODO: Handle Somfy shade actions
#ifdef DEBUG_MESSAGE_HANDLER
            printf("    Somfy shade action: shade_id=%d, target_brightness=%.2f, dim_speed_msec=%d\n",
                   action->target.somfy_shade_id, action->target_brightness, action->dim_speed_msec);
#endif
        }
    }
}

// Helper function to apply RF dimmer actions
void apply_rf_dimmer_actions(uint16_t *action_ids, uint8_t action_count, bool stop_at_current = false) {
    uint32_t now = get_time_in_ms();
    
    for (uint8_t i = 0; i < action_count; i++) {
        Action *action = find_action_by_id(action_ids[i]);
        if (!action) {
#ifdef DEBUG_MESSAGE_HANDLER
            printf("    WARNING: Action ID %d not found\n", action_ids[i]);
#endif
            continue;
        }
#ifdef DEBUG_MESSAGE_HANDLER
        if (action->which_target == Action_light_id_tag) {
            printf("    RF Action %d: Light ID %d, Target Brightness %.2f, Dim Speed %d ms, Delay %d ms\n",
                   i, action->target.light_id, action->target_brightness, action->dim_speed_msec, action->delay_in_msec);
        } else if (action->which_target == Action_somfy_shade_id_tag) {
            printf("    RF Action %d: Somfy Shade ID %d, Target Brightness %.2f, Dim Speed %d ms, Delay %d ms\n",
                   i, action->target.somfy_shade_id, action->target_brightness, action->dim_speed_msec, action->delay_in_msec);
        }
#endif

        // If action has a delay, schedule it for later execution
        if (action->delay_in_msec > 0 && !stop_at_current) {
            schedule_delayed_action(action, action->delay_in_msec, now, 0);
            continue;
        }

        // Check if this action targets a light or a shade
        if (action->which_target == Action_light_id_tag) {
            LightState *light_state = find_light_state_by_id(action->target.light_id);
            if (light_state) {
                if (stop_at_current) {
                    // Stop dimming at current brightness
                    apply_light_state_change(light_state, light_state->brightness, action->dim_speed_msec, 0);
                    light_state->last_transition_stop_reason = LightState_TransitionStopReason_TRANSITION_STOP_HOLD_RELEASE;
                } else {
                    // Set to target brightness
                    apply_light_state_change(light_state, action->target_brightness, action->dim_speed_msec, 0);
                    light_state->last_transition_stop_reason = LightState_TransitionStopReason_TRANSITION_STOP_CLICK_COMMAND;
                }

#ifdef DEBUG_MESSAGE_HANDLER
                printf("    [Dimming] Current light state: Brightness %.2f, Target Value %.2f, Dim Speed %d ms\n",
                       light_state->brightness, light_state->target_value, light_state->dim_speed_msec);
#endif
            }
        } else if (action->which_target == Action_somfy_shade_id_tag) {
            // TODO: Handle Somfy shade actions
#ifdef DEBUG_MESSAGE_HANDLER
            printf("    Somfy shade action: shade_id=%d, target_brightness=%.2f, dim_speed_msec=%d\n",
                   action->target.somfy_shade_id, action->target_brightness, action->dim_speed_msec);
#endif
        }
    }
}

// Delayed Action System Functions
void schedule_delayed_action(const Action *action, uint32_t delay_ms, uint32_t current_time, uint8_t owner_node_id) {
    if (!action) return;
    
    // Find an empty slot
    for (uint8_t i = 0; i < MAX_DELAYED_ACTIONS; i++) {
        if (!g_delayed_actions[i].is_active) {
            g_delayed_actions[i].action_id = action->id;
            g_delayed_actions[i].execute_time = current_time + delay_ms;
            g_delayed_actions[i].is_active = true;
            g_delayed_actions[i].owner_node_id = owner_node_id;
#ifdef DEBUG_MESSAGE_HANDLER
            printf("Scheduled delayed action %" PRIu16 " to execute at %" PRIu32 " (in %" PRIu32 " ms)\n", 
                   action->id, g_delayed_actions[i].execute_time, delay_ms);
#endif
            return;
        }
    }
#ifdef DEBUG_MESSAGE_HANDLER
    printf("WARNING: No free slots for delayed action %d\n", action->id);
#endif
}

void cancel_delayed_actions_by_action_id(uint16_t action_id) {
    for (uint8_t i = 0; i < MAX_DELAYED_ACTIONS; i++) {
        if (g_delayed_actions[i].is_active && g_delayed_actions[i].action_id == action_id) {
            g_delayed_actions[i].is_active = false;
#ifdef DEBUG_MESSAGE_HANDLER
            printf("Cancelled delayed action %d\n", action_id);
#endif
        }
    }
}

void cancel_all_pir_delayed_actions(uint8_t owner_node_id, const CanboConfig_ADCInput *pir_input) {
    if (!pir_input) return;
    
    // Cancel all pending deactivation actions for this PIR
    for (uint8_t i = 0; i < pir_input->config.pir.on_deactivate_count; i++) {
        Action *action = find_action_by_id(pir_input->config.pir.on_deactivate[i]);
        if (action) {
            for (uint8_t j = 0; j < MAX_DELAYED_ACTIONS; j++) {
                if (g_delayed_actions[j].is_active &&
                    g_delayed_actions[j].action_id == action->id &&
                    g_delayed_actions[j].owner_node_id == owner_node_id) {
                    g_delayed_actions[j].is_active = false;
                }
            }
        }
    }
}

void cancel_all_pir_activation_actions(uint8_t owner_node_id, const CanboConfig_ADCInput *pir_input) {
    if (!pir_input) return;
    
    // Cancel all pending activation actions for this PIR
    for (uint8_t i = 0; i < pir_input->config.pir.on_activate_count; i++) {
        Action *action = find_action_by_id(pir_input->config.pir.on_activate[i]);
        if (action) {
            cancel_delayed_actions_by_action_id(action->id);
        }
    }
}

void process_delayed_actions() {
    uint32_t now = get_time_in_ms();
    
    for (uint8_t i = 0; i < MAX_DELAYED_ACTIONS; i++) {
        if (g_delayed_actions[i].is_active && (int32_t)(now - g_delayed_actions[i].execute_time) >= 0) {
            // Time to execute this delayed action
            Action *action = find_action_by_id(g_delayed_actions[i].action_id);
            if (action) {
#ifdef DEBUG_MESSAGE_HANDLER
                printf("Executing delayed action %" PRIu16 "\n", action->id);
#endif
                // Execute the single action (with is_delayed_execution=true to prevent re-scheduling)
                uint16_t action_id = action->id;
                apply_canbo_light_actions(&action_id, 1, false, true);
            }
            
            // Mark this slot as free
            g_delayed_actions[i].is_active = false;
        }
    }
}

// Helper function to convert MomentaryButtonCommand_State to ButtonState_State
ButtonState_State convert_momentary_state_to_button_state(MomentaryButtonCommand_State momentary_state) {
    switch (momentary_state) {
    case MomentaryButtonCommand_State_Pressed:
        return ButtonState_State_BUTTON_STATE_UP_PRESSED;
    case MomentaryButtonCommand_State_Released:
        return ButtonState_State_BUTTON_STATE_RELEASED;
    default:
        return ButtonState_State_BUTTON_STATE_RELEASED;
    }
}

// Helper function to handle momentary button press
void handle_canbo_momentary_button_press(ButtonState *button_state, CanboConfig_ThreePinInput *three_pin_input,
                                         MomentaryButtonCommand_State button_state_enum, bool is_up) {
    button_state->current_state = convert_momentary_state_to_button_state(button_state_enum);
    button_state->last_modified_time = get_time_in_ms();

    // Trigger onPress actions
#ifdef DEBUG_MESSAGE_HANDLER
    if (three_pin_input->config.momentary.up_press_count > 0) {
        printf("  Triggering %d onPress action(s)\n", three_pin_input->config.momentary.up_press_count);
    }
#endif
    apply_canbo_light_actions(three_pin_input->config.momentary.up_press, three_pin_input->config.momentary.up_press_count);

    // Save the current brightness before starting the hold action
    for (uint8_t i = 0; i < three_pin_input->config.momentary.up_hold_count; i++) {
        Action *action = find_action_by_id(three_pin_input->config.momentary.up_hold[i]);
        if (!action) {
#ifdef DEBUG_MESSAGE_HANDLER
            printf("    WARNING: Action ID %d not found\n", three_pin_input->config.momentary.up_hold[i]);
#endif
            continue;
        }
        // Only save brightness for light actions
        if (action->which_target == Action_light_id_tag) {
            LightState *light_state = find_light_state_by_id(action->target.light_id);
            if (light_state) {
                light_state->last_brightness_before_action = light_state->brightness;
#ifdef DEBUG_MESSAGE_HANDLER
                printf("  Saved brightness before hold action for light %d: %.2f\n",
                       light_state->id, light_state->last_brightness_before_action);
#endif
            }
        } else if (action->which_target == Action_somfy_shade_id_tag) {
            // TODO: Handle Somfy shade target percentage saving
#ifdef DEBUG_MESSAGE_HANDLER
            printf("  Somfy shade action%d\n", action->target.somfy_shade_id);
#endif
        }
    }

    // Start dimming action
#ifdef DEBUG_MESSAGE_HANDLER
    if (three_pin_input->config.momentary.up_hold_count > 0) {
        printf("  Triggering %d onHold action(s)\n", three_pin_input->config.momentary.up_hold_count);
    }
#endif
    apply_canbo_light_actions(three_pin_input->config.momentary.up_hold, three_pin_input->config.momentary.up_hold_count);
}

// Helper function to handle momentary button release
void handle_momentary_button_release(ButtonState *button_state, CanboConfig_ThreePinInput *three_pin_input) {
#ifdef DEBUG_MESSAGE_HANDLER
    printf("  Released button pressed\n");
#endif

    uint32_t now = get_time_in_ms();
    uint32_t time_since_last_action = now - button_state->last_modified_time;

#ifdef DEBUG_MESSAGE_HANDLER
    printf("  Time since last button action: %d ms\n", time_since_last_action);
#endif

    // Store the current button state before we change it
    ButtonState_State previous_state = button_state->current_state;

    // Trigger onRelease actions
    if (previous_state == ButtonState_State_BUTTON_STATE_UP_PRESSED) {
#ifdef DEBUG_MESSAGE_HANDLER
        if (three_pin_input->config.momentary.up_release_count > 0) {
            printf("  Triggering %d onRelease action(s)\n", three_pin_input->config.momentary.up_release_count);
        }
#endif
        apply_canbo_light_actions(three_pin_input->config.momentary.up_release, three_pin_input->config.momentary.up_release_count);
    }

    // If the button was pressed for less than threshold, it's a click
    if (time_since_last_action < CLICK_TIME_THRESHOLD) {
#ifdef DEBUG_MESSAGE_HANDLER
        printf("  Released button pressed [click]\n");
#endif

        button_state->current_state = ButtonState_State_BUTTON_STATE_RELEASED;
        button_state->last_modified_time = get_time_in_ms();

        if (previous_state == ButtonState_State_BUTTON_STATE_UP_PRESSED) {
#ifdef DEBUG_MESSAGE_HANDLER
            printf("  up click\n");
            if (three_pin_input->config.momentary.up_click_count > 0) {
                printf("  Triggering %d onClick action(s)\n", three_pin_input->config.momentary.up_click_count);
            }
#endif
            // Apply the same logic as RF buttons: if at target brightness, turn off; otherwise set to target
            for (uint8_t i = 0; i < three_pin_input->config.momentary.up_click_count; i++) {
                Action *action = find_action_by_id(three_pin_input->config.momentary.up_click[i]);
                if (!action) {
#ifdef DEBUG_MESSAGE_HANDLER
                    printf("    WARNING: Action ID %d not found\n", three_pin_input->config.momentary.up_click[i]);
#endif
                    continue;
                }
                // Check if this action targets a light or a shade
                if (action->which_target == Action_light_id_tag) {
                    LightState *light_state = find_light_state_by_id(action->target.light_id);
                    if (light_state) {
                        // Use the brightness from before the hold action started for toggle decision
                        float brightness_before = light_state->last_brightness_before_action;

                        // If light is on (not at off_brightness), turn it off
                        // Otherwise, turn it to this button's on_brightness
                        float target;
                        if (fabsf(brightness_before - action->off_brightness) > 1.0f) {
                            // Light is on (not at off brightness), so turn it off
                            target = action->off_brightness;
                        } else {
                            // Light is off, so turn it to this button's brightness
                            target = action->on_brightness;
                        }

#ifdef DEBUG_MESSAGE_HANDLER
                        printf("    Canbo momentary: brightness_before=%.2f, current=%.2f, on=%.2f, off=%.2f, new_target=%.2f\n",
                               brightness_before, light_state->brightness, action->on_brightness, action->off_brightness, target);
#endif
                        apply_light_state_change(light_state, target, action->dim_speed_msec, 0);
                        light_state->last_transition_stop_reason = LightState_TransitionStopReason_TRANSITION_STOP_CLICK_COMMAND;
                    }
                } else if (action->which_target == Action_somfy_shade_id_tag) {
                    // TODO: Handle Somfy shade actions
#ifdef DEBUG_MESSAGE_HANDLER
                    printf("    Somfy shade momentary action: shade_id=%d, target_brightness=%.2f, dim_speed_msec=%d\n",
                           action->target.somfy_shade_id, action->target_brightness, action->dim_speed_msec);
#endif
                }
            }
        }
    } else {
#ifdef DEBUG_MESSAGE_HANDLER
        printf("  Released button pressed [dimming]\n");
#endif
        // Stop the dimming action
        button_state->current_state = ButtonState_State_BUTTON_STATE_RELEASED;
        button_state->last_modified_time = get_time_in_ms();

        if (previous_state == ButtonState_State_BUTTON_STATE_UP_PRESSED) {
#ifdef DEBUG_MESSAGE_HANDLER
            if (three_pin_input->config.momentary.up_hold_release_count > 0) {
                printf("  Triggering %d onHoldRelease action(s)\n", three_pin_input->config.momentary.up_hold_release_count);
            }
            printf("  Stopping hold action\n");
#endif
            // Trigger onHoldRelease actions
            apply_canbo_light_actions(three_pin_input->config.momentary.up_hold_release, three_pin_input->config.momentary.up_hold_release_count);
            // Stop the hold action
            apply_canbo_light_actions(three_pin_input->config.momentary.up_hold, three_pin_input->config.momentary.up_hold_count, true);
        }
    }
}

// Helper function to convert ToggleButtonCommand_State to ButtonState_State
ButtonState_State convert_toggle_state_to_button_state(ToggleButtonCommand_State toggle_state) {
    switch (toggle_state) {
    case ToggleButtonCommand_State_Up:
        return ButtonState_State_BUTTON_STATE_UP_PRESSED;
    case ToggleButtonCommand_State_Down:
        return ButtonState_State_BUTTON_STATE_DOWN_PRESSED;
    case ToggleButtonCommand_State_Released:
        return ButtonState_State_BUTTON_STATE_RELEASED;
    default:
        return ButtonState_State_BUTTON_STATE_RELEASED;
    }
}

// Helper function to handle toggle button press (up or down)
void handle_canbo_toggle_button_press(ButtonState *button_state, CanboConfig_ThreePinInput *three_pin_input,
                                      ToggleButtonCommand_State button_state_enum, bool is_up) {
#ifdef DEBUG_MESSAGE_HANDLER
    printf("  %s button pressed\n", is_up ? "Up" : "Down");
#endif

    button_state->current_state = convert_toggle_state_to_button_state(button_state_enum);
    button_state->last_modified_time = get_time_in_ms();

    // Trigger onPress actions
    if (is_up) {
#ifdef DEBUG_MESSAGE_HANDLER
        if (three_pin_input->config.toggle.up_press_count > 0) {
            printf("  Triggering %d onUpPress action(s)\n", three_pin_input->config.toggle.up_press_count);
        }
#endif
        apply_canbo_light_actions(three_pin_input->config.toggle.up_press, three_pin_input->config.toggle.up_press_count);
    } else {
#ifdef DEBUG_MESSAGE_HANDLER
        if (three_pin_input->config.toggle.down_press_count > 0) {
            printf("  Triggering %d onDownPress action(s)\n", three_pin_input->config.toggle.down_press_count);
        }
#endif
        apply_canbo_light_actions(three_pin_input->config.toggle.down_press, three_pin_input->config.toggle.down_press_count);
    }

    // Start dimming action
    if (is_up) {
#ifdef DEBUG_MESSAGE_HANDLER
        if (three_pin_input->config.toggle.up_hold_count > 0) {
            printf("  Triggering %d onUpHold action(s)\n", three_pin_input->config.toggle.up_hold_count);
        }
#endif
        apply_canbo_light_actions(three_pin_input->config.toggle.up_hold, three_pin_input->config.toggle.up_hold_count);
    } else {
#ifdef DEBUG_MESSAGE_HANDLER
        if (three_pin_input->config.toggle.down_hold_count > 0) {
            printf("  Triggering %d onDownHold action(s)\n", three_pin_input->config.toggle.down_hold_count);
        }
#endif
        apply_canbo_light_actions(three_pin_input->config.toggle.down_hold, three_pin_input->config.toggle.down_hold_count);
    }
}

// Helper function to handle toggle button release
void handle_toggle_button_release(ButtonState *button_state, CanboConfig_ThreePinInput *three_pin_input) {
#ifdef DEBUG_MESSAGE_HANDLER
    printf("  Released button pressed\n");
#endif

    uint32_t now = get_time_in_ms();
    uint32_t time_since_last_action = now - button_state->last_modified_time;

#ifdef DEBUG_MESSAGE_HANDLER
    printf("  Time since last button action: %d ms\n", time_since_last_action);
#endif

    // Store the current button state before we change it
    ButtonState_State previous_state = button_state->current_state;

    // Trigger onRelease actions
    if (previous_state == ButtonState_State_BUTTON_STATE_UP_PRESSED) {
#ifdef DEBUG_MESSAGE_HANDLER
        if (three_pin_input->config.toggle.up_release_count > 0) {
            printf("  Triggering %d onUpRelease action(s)\n", three_pin_input->config.toggle.up_release_count);
        }
#endif
        apply_canbo_light_actions(three_pin_input->config.toggle.up_release, three_pin_input->config.toggle.up_release_count);
    } else if (previous_state == ButtonState_State_BUTTON_STATE_DOWN_PRESSED) {
#ifdef DEBUG_MESSAGE_HANDLER
        if (three_pin_input->config.toggle.down_release_count > 0) {
            printf("  Triggering %d onDownRelease action(s)\n", three_pin_input->config.toggle.down_release_count);
        }
#endif
        apply_canbo_light_actions(three_pin_input->config.toggle.down_release, three_pin_input->config.toggle.down_release_count);
    }

    // If the button was pressed for less than threshold, it's a click
    if (time_since_last_action < CLICK_TIME_THRESHOLD) {
#ifdef DEBUG_MESSAGE_HANDLER
        printf("  Released button pressed [click]\n");
#endif

        button_state->current_state = ButtonState_State_BUTTON_STATE_RELEASED;
        button_state->last_modified_time = get_time_in_ms();

        if (previous_state == ButtonState_State_BUTTON_STATE_UP_PRESSED) {
#ifdef DEBUG_MESSAGE_HANDLER
            printf("  up click\n");
            if (three_pin_input->config.toggle.up_click_count > 0) {
                printf("  Triggering %d onUpClick action(s)\n", three_pin_input->config.toggle.up_click_count);
            }
#endif
            apply_canbo_light_actions(three_pin_input->config.toggle.up_click, three_pin_input->config.toggle.up_click_count);
        } else if (previous_state == ButtonState_State_BUTTON_STATE_DOWN_PRESSED) {
#ifdef DEBUG_MESSAGE_HANDLER
            printf("  down click\n");
            if (three_pin_input->config.toggle.down_click_count > 0) {
                printf("  Triggering %d onDownClick action(s)\n", three_pin_input->config.toggle.down_click_count);
            }
#endif
            apply_canbo_light_actions(three_pin_input->config.toggle.down_click, three_pin_input->config.toggle.down_click_count);
        }
    } else {
#ifdef DEBUG_MESSAGE_HANDLER
        printf("  Released button pressed [dimming]\n");
#endif
        // Stop the dimming action
        button_state->current_state = ButtonState_State_BUTTON_STATE_RELEASED;
        button_state->last_modified_time = get_time_in_ms();

        if (previous_state == ButtonState_State_BUTTON_STATE_UP_PRESSED) {
#ifdef DEBUG_MESSAGE_HANDLER
            printf("  up dimming\n");
            if (three_pin_input->config.toggle.up_hold_release_count > 0) {
                printf("  Triggering %d onUpHoldRelease action(s)\n", three_pin_input->config.toggle.up_hold_release_count);
            }
            printf("  Stopping up hold action\n");
#endif
            // Trigger onHoldRelease actions
            apply_canbo_light_actions(three_pin_input->config.toggle.up_hold_release, three_pin_input->config.toggle.up_hold_release_count);
            // Stop the hold action
            apply_canbo_light_actions(three_pin_input->config.toggle.up_hold, three_pin_input->config.toggle.up_hold_count, true);
        } else if (previous_state == ButtonState_State_BUTTON_STATE_DOWN_PRESSED) {
#ifdef DEBUG_MESSAGE_HANDLER
            printf("  down dimming\n");
            if (three_pin_input->config.toggle.down_hold_release_count > 0) {
                printf("  Triggering %d onDownHoldRelease action(s)\n", three_pin_input->config.toggle.down_hold_release_count);
            }
            printf("  Stopping down hold action\n");
#endif
            // Trigger onHoldRelease actions
            apply_canbo_light_actions(three_pin_input->config.toggle.down_hold_release, three_pin_input->config.toggle.down_hold_release_count);
            // Stop the hold action
            apply_canbo_light_actions(three_pin_input->config.toggle.down_hold, three_pin_input->config.toggle.down_hold_count, true);
        }
    }
}

// Helper function to handle ping command
void handle_canbo_ping_command(const uint8_t *data, uint32_t len) {
#ifndef __EMSCRIPTEN__
    // Capture start time for processing delay measurement
    uint32_t start_time_us = time_us_32();
#endif

#ifdef DEBUG_MESSAGE_HANDLER
    printf("Received ping command\n");
#endif

    PingCommand command = PingCommand_init_zero;
    pb_istream_t stream = pb_istream_from_buffer(data + 1, len - 1);
    bool status = pb_decode(&stream, PingCommand_fields, &command);

    if (!status) {
#ifdef DEBUG_MESSAGE_HANDLER
        printf("Failed to decode ping command: %s\n", PB_GET_ERROR(&stream));
#endif
        return;
    }

#ifdef DEBUG_MESSAGE_HANDLER
    printf("  QR Code: %s\n", command.qr_code);
    printf("  Is Provisioned: %s\n", command.is_provisioned ? "Yes" : "No");
#endif

    // Send time sync response regardless of provisioning status
#ifndef __EMSCRIPTEN__
    // Only send time sync on real hardware, not in emulator
    if (g_send_can_message_callback != nullptr) {
        TimeSyncResponse time_sync = TimeSyncResponse_init_zero;

        // Set QR code from incoming ping command for validation
        strncpy(time_sync.qr_code, command.qr_code, sizeof(time_sync.qr_code) - 1);
        time_sync.qr_code[sizeof(time_sync.qr_code) - 1] = '\0';

        // Get current Unix timestamp from RTC
        uint32_t unix_time = 0;
        if (rtc_get_current_unix_time(&unix_time)) {
            time_sync.unix_timestamp = unix_time;
        } else {
            // If RTC read fails, use a placeholder or skip
            printf("Warning: Failed to read RTC for time sync\n");
        }

        // Echo back the millis_since_boot for latency calculation
        time_sync.echo_millis = command.millis_since_boot;

        // Calculate processing delay for accurate latency measurement
#ifndef __EMSCRIPTEN__
        uint32_t end_time_us = time_us_32();
        time_sync.processing_delay_us = end_time_us - start_time_us;
#endif

        // Encode and send the time sync response
        uint8_t buffer[TimeSyncResponse_size + 1];
        buffer[0] = static_cast<uint8_t>(MessageType::MESSAGE_TIME_SYNC_RESPONSE);
        pb_ostream_t stream = pb_ostream_from_buffer(buffer + 1, sizeof(buffer) - 1);
        if (pb_encode(&stream, TimeSyncResponse_fields, &time_sync)) {
            g_send_can_message_callback(buffer, stream.bytes_written + 1);
#ifdef DEBUG_MESSAGE_HANDLER
            printf("  Sent time sync response: unix=%u, echo_millis=%u, processing_delay_us=%u\n",
                   time_sync.unix_timestamp, time_sync.echo_millis, time_sync.processing_delay_us);
#endif
        }
    }
#endif

    if (command.is_provisioned && strncmp(command.version, g_active_configuration.config.version, sizeof(command.version)) == 0) {
        printf("  Provisioned and version matches\n");
        
        // Find node ID from QR code to update provisioning state
        uint8_t node_id = 0;
        for (size_t i = 0; i < g_active_configuration.config.node_qr_mappings_count; i++) {
            const BasestationConfig_NodeQRMapping &mapping = g_active_configuration.config.node_qr_mappings[i];
            if (strncmp(mapping.qr_code, command.qr_code, sizeof(mapping.qr_code)) == 0) {
                node_id = mapping.node_id;
                break;
            }
        }
        
        if (node_id != 0) {
            // Update provisioning state for already-provisioned device
            ProvisioningState *provisioning_state = find_or_create_provisioning_state_by_node_id(node_id);
            provisioning_state->is_provisioned = true;
            provisioning_state->last_seen_time = get_time_in_ms();
            provisioning_state->error_code = ProvisioningState_ProvisioningErrorCode_NONE;
        }
        
        return;
    }

    printf("  Version: %s\n", command.version);
    printf("  Active Version: %s\n", g_active_configuration.config.version);

    // find the node id from the qr code
    uint8_t node_id = 0;
    for (size_t i = 0; i < g_active_configuration.config.node_qr_mappings_count; i++) {
        const BasestationConfig_NodeQRMapping &mapping = g_active_configuration.config.node_qr_mappings[i];
        if (strncmp(mapping.qr_code, command.qr_code, sizeof(mapping.qr_code)) == 0) {
            node_id = mapping.node_id;
            break;
        }
    }
    if (node_id == 0) {
#ifdef DEBUG_MESSAGE_HANDLER
        printf("  No node ID found for QR code: %s\n", command.qr_code);
#endif
        return;
    }

    // find the provisioning state for this node id
    ProvisioningState *provisioning_state = find_or_create_provisioning_state_by_node_id(node_id);
    provisioning_state->last_seen_time = get_time_in_ms();

    // find canbo config for this node id
    CanboConfig *canbo_config = find_canbo_config_by_node_id(node_id);
    if (!canbo_config) {
#ifdef DEBUG_MESSAGE_HANDLER
        printf("  No Canbo config found for node ID %d\n", node_id);
#endif
        provisioning_state->error_code = ProvisioningState_ProvisioningErrorCode_NO_CANBO_CONFIG;
        provisioning_state->is_provisioned = false;
        return;
    }

    // create a canbo provisioning command
    CanboProvisioningCommand provisioning_command = CanboProvisioningCommand_init_zero;
    provisioning_command.node_id = node_id;
    strncpy(provisioning_command.version, g_active_configuration.config.version, sizeof(provisioning_command.version));
    provisioning_command.version[sizeof(provisioning_command.version) - 1] = '\0';
    strncpy(provisioning_command.qr_code, command.qr_code, sizeof(provisioning_command.qr_code));
    provisioning_command.qr_code[sizeof(provisioning_command.qr_code) - 1] = '\0';

    provisioning_command.three_pin_inputs_count = 0;
    // Add 3-pin inputs
    for (uint8_t i = 0; i < canbo_config->three_pin_inputs_count; i++) {
        if (provisioning_command.three_pin_inputs_count >= 4) {
            break;
        }
        provisioning_command.three_pin_inputs[provisioning_command.three_pin_inputs_count].connector_id = canbo_config->three_pin_inputs[i].connector_id;
        bool recognized = false;
        if (canbo_config->three_pin_inputs[i].type == CanboConfig_ThreePinInput_ConnectorType_TOGGLE) {
            provisioning_command.three_pin_inputs[provisioning_command.three_pin_inputs_count].connector_type = CanboProvisioningCommand_ThreePinInput_ConnectorType_TOGGLE;
            recognized = true;
        } else if (canbo_config->three_pin_inputs[i].type == CanboConfig_ThreePinInput_ConnectorType_MOMENTARY) {
            provisioning_command.three_pin_inputs[provisioning_command.three_pin_inputs_count].connector_type = CanboProvisioningCommand_ThreePinInput_ConnectorType_MOMENTARY;
            recognized = true;
        }
        if (recognized) {
            provisioning_command.three_pin_inputs_count++;
        }
    }

    provisioning_command.two_pin_inputs_count = 0;
    // Add 2-pin inputs (MOMENTARY, DOOR only)
    for (uint8_t i = 0; i < canbo_config->two_pin_inputs_count; i++) {
        if (provisioning_command.two_pin_inputs_count >= 2) {
            break;
        }
        provisioning_command.two_pin_inputs[provisioning_command.two_pin_inputs_count].connector_id = canbo_config->two_pin_inputs[i].connector_id;
        if (canbo_config->two_pin_inputs[i].type == CanboConfig_TwoPinInput_ConnectorType_MOMENTARY) {
            provisioning_command.two_pin_inputs[provisioning_command.two_pin_inputs_count].connector_type = CanboProvisioningCommand_TwoPinInput_ConnectorType_MOMENTARY;
            provisioning_command.two_pin_inputs_count++;
        } else if (canbo_config->two_pin_inputs[i].type == CanboConfig_TwoPinInput_ConnectorType_DOOR_SENSOR) {
            provisioning_command.two_pin_inputs[provisioning_command.two_pin_inputs_count].connector_type = CanboProvisioningCommand_TwoPinInput_ConnectorType_DOOR_SENSOR;
            provisioning_command.two_pin_inputs_count++;
        }
    }

    // Add ADC input (KNOB, THERMOSTAT, PIR) - single object
    if (canbo_config->has_adc_inputs) {
        provisioning_command.has_adc_inputs = true;
        provisioning_command.adc_inputs.connector_id = canbo_config->adc_inputs.connector_id;
        if (canbo_config->adc_inputs.type == CanboConfig_ADCInput_ConnectorType_KNOB) {
            provisioning_command.adc_inputs.connector_type = CanboProvisioningCommand_ADCInput_ConnectorType_KNOB;
        } else if (canbo_config->adc_inputs.type == CanboConfig_ADCInput_ConnectorType_THERMOSTAT) {
            provisioning_command.adc_inputs.connector_type = CanboProvisioningCommand_ADCInput_ConnectorType_THERMOSTAT;
        } else if (canbo_config->adc_inputs.type == CanboConfig_ADCInput_ConnectorType_PIR) {
            provisioning_command.adc_inputs.connector_type = CanboProvisioningCommand_ADCInput_ConnectorType_PIR;
        }
    } else {
        provisioning_command.has_adc_inputs = false;
    }

    // Add outputs (relays) from canbo config
    provisioning_command.outputs_count = 0;
    for (uint8_t i = 0; i < canbo_config->outputs_count && provisioning_command.outputs_count < 4; i++) {
        provisioning_command.outputs[provisioning_command.outputs_count].connector_id = canbo_config->outputs[i].connector_id;
        provisioning_command.outputs[provisioning_command.outputs_count].connector_type = CanboProvisioningCommand_Output_ConnectorType_Relay;
        provisioning_command.outputs_count++;
#ifdef DEBUG_MESSAGE_HANDLER
        printf("  Added output: Connector ID %d, Type RELAY\n", canbo_config->outputs[i].connector_id);
#endif
    }

    // Add 0-10V dimming connection
    LightConfig_FixtureConfig_ZeroToTenVoltConfig *zero_to_ten_volt_config =
        find_canbo_zero_to_ten_volt_config_by_node(node_id, &provisioning_command.zero_to_ten_volt_config.min_brightness, &provisioning_command.zero_to_ten_volt_config.max_brightness);

    if (!zero_to_ten_volt_config) {
        provisioning_command.has_zero_to_ten_volt_config = false;
    } else {
        provisioning_command.has_zero_to_ten_volt_config = true;

        if (zero_to_ten_volt_config->type == LightConfig_FixtureConfig_ZeroToTenVoltConfig_Type_SOURCING) {
            provisioning_command.zero_to_ten_volt_config.type = CanboProvisioningCommand_ZeroToTenVoltConfig_Type_SOURCING;
        } else if (zero_to_ten_volt_config->type == LightConfig_FixtureConfig_ZeroToTenVoltConfig_Type_SINKING) {
            provisioning_command.zero_to_ten_volt_config.type = CanboProvisioningCommand_ZeroToTenVoltConfig_Type_SINKING;
        }
        provisioning_command.zero_to_ten_volt_config.use_relay = zero_to_ten_volt_config->use_relay;
        provisioning_command.zero_to_ten_volt_config.relay_connector_id = zero_to_ten_volt_config->out_connector_id;
    }
    
    // Setup kleverness connector
    provisioning_command.has_kleverness_connector = false;

    // Add current Unix timestamp for time sync
#ifndef __EMSCRIPTEN__
    uint32_t unix_time = 0;
    if (rtc_get_current_unix_time(&unix_time)) {
        provisioning_command.unix_timestamp = unix_time;
    }
#endif

    // Send the provisioning command
    int ret = send_provisioning_command(node_id, &provisioning_command);

    if (ret == 0) {
#ifdef DEBUG_MESSAGE_HANDLER
        printf("  Provisioning command sent successfully to node %d\n", node_id);
#endif
        provisioning_state->is_provisioned = true;
        provisioning_state->error_code = ProvisioningState_ProvisioningErrorCode_NONE;
    } else {
#ifdef DEBUG_MESSAGE_HANDLER
        printf("  Failed to send provisioning command to node %d\n", node_id);
#endif
        provisioning_state->is_provisioned = false;
        provisioning_state->error_code = ProvisioningState_ProvisioningErrorCode_COULD_NOT_SEND_PROVISIONING_COMMAND;
    }
}

// Helper function to handle momentary button command
void handle_canbo_momentary_button_command(const uint8_t *data, uint32_t len, uint8_t node_id) {
#ifdef DEBUG_MESSAGE_HANDLER
    printf("Received momentary button command\n");
#endif

    MomentaryButtonCommand command = MomentaryButtonCommand_init_zero;
    pb_istream_t stream = pb_istream_from_buffer(data + 1, len - 1);
    bool status = pb_decode(&stream, MomentaryButtonCommand_fields, &command);

    if (!status) {
#ifdef DEBUG_MESSAGE_HANDLER
        printf("Failed to decode momentary button command: %s\n", PB_GET_ERROR(&stream));
#endif
        return;
    }

#ifdef DEBUG_MESSAGE_HANDLER
    printf("  Connector ID: %d\n", command.connector_id);
    printf("  State: %d\n", command.state);
#endif

    // Find the Canbo device configuration for this node ID
    CanboConfig *canbo_config = find_canbo_config_by_node_id(node_id);

    if (!canbo_config) {
#ifdef DEBUG_MESSAGE_HANDLER
        printf("  No Canbo config found for node ID %d (count: %d)\n", node_id, g_active_configuration.config.canbo_configs_count);
#endif
        return;
    }

#ifdef DEBUG_MESSAGE_HANDLER
    printf("  Found Canbo config for node ID %d\n", node_id);
#endif

    // Find the momentary input configuration for this connector ID
    CanboConfig_ThreePinInput *three_pin_input = find_momentary_input_by_connector_id(canbo_config, command.connector_id);

    if (!three_pin_input) {
#ifdef DEBUG_MESSAGE_HANDLER
        printf("  No momentary input found for connector ID %d\n", command.connector_id);
#endif
        return;
    }

#ifdef DEBUG_MESSAGE_HANDLER
    printf("  Found momentary input for connector ID %d\n", command.connector_id);
#endif

    // Find or create button state for this node and connector
    ButtonState *button_state = find_or_create_button_state(node_id, command.connector_id);

    if (command.state == MomentaryButtonCommand_State_Pressed) {
        handle_canbo_momentary_button_press(button_state, three_pin_input, command.state, true);
    } else if (command.state == MomentaryButtonCommand_State_Released) {
        handle_momentary_button_release(button_state, three_pin_input);
    }
}

// Helper function to handle toggle button command
void handle_canbo_toggle_button_command(const uint8_t *data, uint32_t len, uint8_t node_id) {
#ifdef DEBUG_MESSAGE_HANDLER
    printf("Received toggle button command\n");
#endif

    ToggleButtonCommand command = ToggleButtonCommand_init_zero;
    pb_istream_t stream = pb_istream_from_buffer(data + 1, len - 1);
    bool status = pb_decode(&stream, ToggleButtonCommand_fields, &command);

    if (!status) {
#ifdef DEBUG_MESSAGE_HANDLER
        printf("Failed to decode toggle button command: %s\n", PB_GET_ERROR(&stream));
#endif
        return;
    }

#ifdef DEBUG_MESSAGE_HANDLER
    printf("  Connector ID: %d\n", command.connector_id);
    printf("  State: %d\n", command.state);
    printf("  Timestamp: %d\n", command.timestamp);
#endif

    // Find the Canbo device configuration for this node ID
    CanboConfig *canbo_config = find_canbo_config_by_node_id(node_id);

    if (!canbo_config) {
#ifdef DEBUG_MESSAGE_HANDLER
        printf("  No Canbo config found for node ID %d (count: %d)\n", node_id, g_active_configuration.config.canbo_configs_count);
#endif
        return;
    }

#ifdef DEBUG_MESSAGE_HANDLER
    printf("  Found Canbo config for node ID %d\n", node_id);
#endif

    // Find the toggle input configuration for this connector ID
    CanboConfig_ThreePinInput *three_pin_input = find_toggle_input_by_connector_id(canbo_config, command.connector_id);

    if (!three_pin_input) {
#ifdef DEBUG_MESSAGE_HANDLER
        printf("  No toggle input found for connector ID %d\n", command.connector_id);
#endif
        return;
    }

#ifdef DEBUG_MESSAGE_HANDLER
    printf("  Found toggle input for connector ID %d\n", command.connector_id);
#endif

    // Find or create button state for this node and connector
    ButtonState *button_state = find_or_create_button_state(node_id, command.connector_id);

    if (command.state == ToggleButtonCommand_State_Up) {
        handle_canbo_toggle_button_press(button_state, three_pin_input, command.state, true);
    } else if (command.state == ToggleButtonCommand_State_Down) {
        handle_canbo_toggle_button_press(button_state, three_pin_input, command.state, false);
    } else if (command.state == ToggleButtonCommand_State_Released) {
        handle_toggle_button_release(button_state, three_pin_input);
    }
}

void handle_rf_momentary_button_command(const uint8_t *data, uint32_t len, uint8_t node_id) {
#ifdef DEBUG_MESSAGE_HANDLER
    printf("Received RF button momentary command\n");
#endif
    MomentaryButtonCommand command = MomentaryButtonCommand_init_zero;
    pb_istream_t stream = pb_istream_from_buffer(data + 1, len - 1);
    bool status = pb_decode(&stream, MomentaryButtonCommand_fields, &command);
    if (!status) {
#ifdef DEBUG_MESSAGE_HANDLER
        printf("Failed to decode RF momentary button command: %s\n", PB_GET_ERROR(&stream));
#endif
        return;
    }

#ifdef DEBUG_MESSAGE_HANDLER
    printf("  Connector ID: %d\n", command.connector_id);
    printf("  State: %d\n", command.state);
    printf("  Timestamp: %d\n", command.timestamp);
#endif

    // Find the RF dimmer configuration for this node ID
    RFDimmerConfig *rf_dimmer_config = get_dimmer_config_from_node_id(node_id);

    if (!rf_dimmer_config) {
#ifdef DEBUG_MESSAGE_HANDLER
        printf("  No RF dimmer config found for node ID %d\n", node_id);
#endif
        return;
    }

#ifdef DEBUG_MESSAGE_HANDLER
    printf("  Found RF dimmer config for node ID %d\n", node_id);
#endif

    if (command.state == MomentaryButtonCommand_State_Pressed) {
        // Find or create button state for this node and the specific button
        ButtonState *button_state = find_or_create_button_state(node_id, command.connector_id);

        // Save the current brightness before any action for all affected lights
        // Simple approach: save brightness for all lights since we typically only have a few
        for (uint8_t i = 0; i < g_active_configuration.state.lights_count; i++) {
            g_active_configuration.state.lights[i].last_brightness_before_action =
                g_active_configuration.state.lights[i].brightness;
#ifdef DEBUG_MESSAGE_HANDLER
            printf("  RF: Saved brightness before action for light %d: %.2f\n",
                   g_active_configuration.state.lights[i].id,
                   g_active_configuration.state.lights[i].last_brightness_before_action);
#endif
        }

        // Store which button was pressed (0 = up, 1 = middle, 2 = down)
        button_state->current_state = ButtonState_State_BUTTON_STATE_UP_PRESSED;
        button_state->last_modified_time = get_time_in_ms();

        // Start hold action based on which button was pressed
        if (command.connector_id == 0 && rf_dimmer_config->up_button_hold_count > 0) {
#ifdef DEBUG_MESSAGE_HANDLER
            printf("  Starting up button hold action\n");
#endif
            apply_rf_dimmer_actions(rf_dimmer_config->up_button_hold, rf_dimmer_config->up_button_hold_count);
        } else if (command.connector_id == 1 && rf_dimmer_config->middle_button_hold_count > 0) {
#ifdef DEBUG_MESSAGE_HANDLER
            printf("  Starting middle button hold action\n");
#endif
            apply_rf_dimmer_actions(rf_dimmer_config->middle_button_hold, rf_dimmer_config->middle_button_hold_count);
        } else if (command.connector_id == 2 && rf_dimmer_config->down_button_hold_count > 0) {
#ifdef DEBUG_MESSAGE_HANDLER
            printf("  Starting down button hold action\n");
#endif
            apply_rf_dimmer_actions(rf_dimmer_config->down_button_hold, rf_dimmer_config->down_button_hold_count);
        }

    } else if (command.state == MomentaryButtonCommand_State_Released) {
#ifdef DEBUG_MESSAGE_HANDLER
        printf("  Released button pressed\n");
#endif

        uint32_t now = get_time_in_ms();

        // Find which button was pressed for this node (RF doesn't send button ID on release)
        // We need to check all button states for this node to find which one is pressed
        uint8_t pressed_connector_id = 255;
        ButtonState *pressed_button_state = nullptr;

        for (uint8_t i = 0; i < g_active_configuration.state.buttons_count; i++) {
            if (g_active_configuration.state.buttons[i].node_id == node_id &&
                g_active_configuration.state.buttons[i].current_state != ButtonState_State_BUTTON_STATE_RELEASED) {
                pressed_connector_id = g_active_configuration.state.buttons[i].connector_id;
                pressed_button_state = &g_active_configuration.state.buttons[i];
                break;
            }
        }

        if (pressed_button_state) {
            uint32_t time_since_last_action = now - pressed_button_state->last_modified_time;

#ifdef DEBUG_MESSAGE_HANDLER
            printf("  Found pressed button: connector_id=%d, time since press: %d ms\n",
                   pressed_connector_id, time_since_last_action);
#endif

            pressed_button_state->current_state = ButtonState_State_BUTTON_STATE_RELEASED;
            pressed_button_state->last_modified_time = now;

            // Check if this was a click or hold release
            if (time_since_last_action < CLICK_TIME_THRESHOLD) {
#ifdef DEBUG_MESSAGE_HANDLER
                printf("  Released button pressed [click]\n");
#endif
                // Continue to process click actions below
            } else {
#ifdef DEBUG_MESSAGE_HANDLER
                printf("  Released button pressed [dimming]\n");
#endif
                // Stop the dimming action at current brightness
                if (pressed_connector_id == 0 && rf_dimmer_config->up_button_hold_count > 0) {
                    apply_rf_dimmer_actions(rf_dimmer_config->up_button_hold, rf_dimmer_config->up_button_hold_count, true);
                } else if (pressed_connector_id == 1 && rf_dimmer_config->middle_button_hold_count > 0) {
                    apply_rf_dimmer_actions(rf_dimmer_config->middle_button_hold, rf_dimmer_config->middle_button_hold_count, true);
                } else if (pressed_connector_id == 2 && rf_dimmer_config->down_button_hold_count > 0) {
                    apply_rf_dimmer_actions(rf_dimmer_config->down_button_hold, rf_dimmer_config->down_button_hold_count, true);
                }
                return; // Don't process click actions for hold release
            }
        } else {
#ifdef DEBUG_MESSAGE_HANDLER
            printf("  WARNING: No pressed button found for node %d on release!\n", node_id);
#endif
            return; // Can't process click without knowing which button
        }

        // Apply the appropriate action based on which button was pressed
        // connector_id: 0 = up, 1 = middle, 2 = down
        if (pressed_connector_id == 0 && rf_dimmer_config->up_button_click_count > 0) {
#ifdef DEBUG_MESSAGE_HANDLER
            printf("  Up button click\n");
#endif
            // Apply up button click actions
            for (uint8_t i = 0; i < rf_dimmer_config->up_button_click_count; i++) {
                Action *action = find_action_by_id(rf_dimmer_config->up_button_click[i]);
                if (!action) {
#ifdef DEBUG_MESSAGE_HANDLER
                    printf("    WARNING: Action ID %d not found\n", rf_dimmer_config->up_button_click[i]);
#endif
                    continue;
                }
                // Check if this action targets a light or a shade
                if (action->which_target == Action_light_id_tag) {
                    LightState *light_state = find_light_state_by_id(action->target.light_id);
                    if (light_state) {
                        // Use the brightness from before any action for toggle decision
                        float brightness_before = light_state->last_brightness_before_action;

                        // For RF dimmers: if already at this button's brightness, turn off
                        // Otherwise always go to this button's brightness
                        float brightness_diff = fabsf(brightness_before - action->on_brightness);
                        float target = (brightness_diff < 1.0f) ? action->off_brightness : action->on_brightness;

#ifdef DEBUG_MESSAGE_HANDLER
                        printf("    RF: brightness_before=%.2f, current=%.2f, on=%.2f, off=%.2f, diff=%.2f, new_target=%.2f\n",
                               brightness_before, light_state->brightness, action->on_brightness, action->off_brightness,
                               brightness_diff, target);
#endif
                        apply_light_state_change(light_state, target, action->dim_speed_msec, 0);
                        light_state->last_transition_stop_reason = LightState_TransitionStopReason_TRANSITION_STOP_CLICK_COMMAND;
                    }
                } else if (action->which_target == Action_somfy_shade_id_tag) {
                    // TODO: Handle Somfy shade actions
#ifdef DEBUG_MESSAGE_HANDLER
                    printf("    Somfy shade RF up button action: shade_id=%d, target_brightness=%.2f, dim_speed_msec=%d\n",
                           action->target.somfy_shade_id, action->target_brightness, action->dim_speed_msec);
#endif
                }
            }
        } else if (pressed_connector_id == 1 && rf_dimmer_config->middle_button_click_count > 0) {
#ifdef DEBUG_MESSAGE_HANDLER
            printf("  Middle button click\n");
#endif
            // Apply middle button click actions
            for (uint8_t i = 0; i < rf_dimmer_config->middle_button_click_count; i++) {
                Action *action = find_action_by_id(rf_dimmer_config->middle_button_click[i]);
                if (!action) {
#ifdef DEBUG_MESSAGE_HANDLER
                    printf("    WARNING: Action ID %d not found\n", rf_dimmer_config->middle_button_click[i]);
#endif
                    continue;
                }
                // Check if this action targets a light or a shade
                if (action->which_target == Action_light_id_tag) {
                    LightState *light_state = find_light_state_by_id(action->target.light_id);
                    if (light_state) {
                        // Use the brightness from before any action for toggle decision
                        float brightness_before = light_state->last_brightness_before_action;

                        // For RF dimmers: if already at this button's brightness, turn off
                        // Otherwise always go to this button's brightness
                        float brightness_diff = fabsf(brightness_before - action->on_brightness);
                        float target = (brightness_diff < 1.0f) ? action->off_brightness : action->on_brightness;

#ifdef DEBUG_MESSAGE_HANDLER
                        printf("    RF: brightness_before=%.2f, current=%.2f, on=%.2f, off=%.2f, diff=%.2f, new_target=%.2f\n",
                               brightness_before, light_state->brightness, action->on_brightness, action->off_brightness,
                               brightness_diff, target);
#endif
                        apply_light_state_change(light_state, target, action->dim_speed_msec, 0);
                        light_state->last_transition_stop_reason = LightState_TransitionStopReason_TRANSITION_STOP_CLICK_COMMAND;
                    }
                } else if (action->which_target == Action_somfy_shade_id_tag) {
                    // TODO: Handle Somfy shade actions
#ifdef DEBUG_MESSAGE_HANDLER
                    printf("    Somfy shade RF middle button action: shade_id=%d, target_brightness=%.2f, dim_speed_msec=%d\n",
                           action->target.somfy_shade_id, action->target_brightness, action->dim_speed_msec);
#endif
                }
            }
        } else if (pressed_connector_id == 2 && rf_dimmer_config->down_button_click_count > 0) {
#ifdef DEBUG_MESSAGE_HANDLER
            printf("  Down button click\n");
#endif
            // Apply down button click actions
            for (uint8_t i = 0; i < rf_dimmer_config->down_button_click_count; i++) {
                Action *action = find_action_by_id(rf_dimmer_config->down_button_click[i]);
                if (!action) {
#ifdef DEBUG_MESSAGE_HANDLER
                    printf("    WARNING: Action ID %d not found\n", rf_dimmer_config->down_button_click[i]);
#endif
                    continue;
                }
                // Check if this action targets a light or a shade
                if (action->which_target == Action_light_id_tag) {
                    LightState *light_state = find_light_state_by_id(action->target.light_id);
                    if (light_state) {
                        // Use the brightness from before any action for toggle decision
                        float brightness_before = light_state->last_brightness_before_action;

                        // For RF dimmers: if already at this button's brightness, turn off
                        // Otherwise always go to this button's brightness
                        float brightness_diff = fabsf(brightness_before - action->on_brightness);
                        float target = (brightness_diff < 1.0f) ? action->off_brightness : action->on_brightness;

#ifdef DEBUG_MESSAGE_HANDLER
                        printf("    RF: brightness_before=%.2f, current=%.2f, on=%.2f, off=%.2f, diff=%.2f, new_target=%.2f\n",
                               brightness_before, light_state->brightness, action->on_brightness, action->off_brightness,
                               brightness_diff, target);
#endif
                        apply_light_state_change(light_state, target, action->dim_speed_msec, 0);
                        light_state->last_transition_stop_reason = LightState_TransitionStopReason_TRANSITION_STOP_CLICK_COMMAND;
                    }
                } else if (action->which_target == Action_somfy_shade_id_tag) {
                    // TODO: Handle Somfy shade actions
#ifdef DEBUG_MESSAGE_HANDLER
                    printf("    Somfy shade RF down button action: shade_id=%d, target_brightness=%.2f, dim_speed_msec=%d\n",
                           action->target.somfy_shade_id, action->target_brightness, action->dim_speed_msec);
#endif
                }
            }
        }
    }
}

// Helper function to handle door sensor command
void handle_rf_door_sensor_command(const uint8_t *data, uint32_t len, uint8_t node_id) {
#ifdef DEBUG_MESSAGE_HANDLER
    printf("Received door sensor command\n");
#endif

    RFDoorSensorCommand command = RFDoorSensorCommand_init_zero;
    pb_istream_t stream = pb_istream_from_buffer(data + 1, len - 1);
    bool status = pb_decode(&stream, RFDoorSensorCommand_fields, &command);

    if (!status) {
#ifdef DEBUG_MESSAGE_HANDLER
        printf("Failed to decode door sensor command: %s\n", PB_GET_ERROR(&stream));
#endif
        return;
    }

#ifdef DEBUG_MESSAGE_HANDLER
    printf("  Node ID: %d\n", command.node_id);
    printf("  State: %s\n", command.state == RFDoorSensorCommand_State_Closed ? "Closed" : "Opened");
#endif

    // Find the RF Reed state for this node ID
    RFReedState *rf_reed_state = find_rf_reed_state_by_node_id(node_id);

    if (!rf_reed_state) {
#ifdef DEBUG_MESSAGE_HANDLER
        printf("  No RF Reed state found for node ID %d\n", node_id);
#endif
        return;
    }

    RFReedSensorConfig *rf_reed_sensor_config = find_rf_reed_sensor_config_by_node_id(node_id);
    if (!rf_reed_sensor_config) {
#ifdef DEBUG_MESSAGE_HANDLER
        printf("  No RF Reed sensor config found for node ID %d\n", node_id);
#endif
        return;
    }

    if (command.state == RFDoorSensorCommand_State_Closed) {
        for (uint8_t i = 0; i < rf_reed_sensor_config->door_close_count; i++) {
            Action *action = find_action_by_id(rf_reed_sensor_config->door_close[i]);
            if (!action) {
#ifdef DEBUG_MESSAGE_HANDLER
                printf("    WARNING: Action ID %d not found\n", rf_reed_sensor_config->door_close[i]);
#endif
                continue;
            }
            // Check if this action targets a light or a shade
            if (action->which_target == Action_light_id_tag) {
                LightState *light_state = find_light_state_by_id(action->target.light_id);
                if (light_state) {
                    apply_light_state_change(light_state, action->target_brightness, action->dim_speed_msec, action->activate_delay_msec);
                    light_state->last_transition_stop_reason = LightState_TransitionStopReason_TRANSITION_STOP_CLICK_COMMAND;
                }
            } else if (action->which_target == Action_somfy_shade_id_tag) {
                // TODO: Handle Somfy shade actions
#ifdef DEBUG_MESSAGE_HANDLER
                printf("    Somfy shade door close action: shade_id=%d, target_brightness=%.2f, dim_speed_msec=%d\n",
                       action->target.somfy_shade_id, action->target_brightness, action->dim_speed_msec);
#endif
            }
        }
    } else if (command.state == RFDoorSensorCommand_State_Opened) {
        for (uint8_t i = 0; i < rf_reed_sensor_config->door_open_count; i++) {
            Action *action = find_action_by_id(rf_reed_sensor_config->door_open[i]);
            if (!action) {
#ifdef DEBUG_MESSAGE_HANDLER
                printf("    WARNING: Action ID %d not found\n", rf_reed_sensor_config->door_open[i]);
#endif
                continue;
            }
            // Check if this action targets a light or a shade
            if (action->which_target == Action_light_id_tag) {
                LightState *light_state = find_light_state_by_id(action->target.light_id);
                if (light_state) {
                    apply_light_state_change(light_state, action->target_brightness, action->dim_speed_msec, action->activate_delay_msec);
                    light_state->last_transition_stop_reason = LightState_TransitionStopReason_TRANSITION_STOP_CLICK_COMMAND;
                }
            } else if (action->which_target == Action_somfy_shade_id_tag) {
                // TODO: Handle Somfy shade actions
#ifdef DEBUG_MESSAGE_HANDLER
                printf("    Somfy shade door open action: shade_id=%d, target_brightness=%.2f, dim_speed_msec=%d\n",
                       action->target.somfy_shade_id, action->target_brightness, action->dim_speed_msec);
#endif
            }
        }
    }
}

// Function to send provisioning command to a CAN device
int send_provisioning_command(uint8_t node_id, const CanboProvisioningCommand *command) {
    // Encode the provisioning command
    uint8_t data[256] = {0};
    data[0] = static_cast<uint8_t>(MessageType::MESSAGE_CANBO_PROVISIONING);

    pb_ostream_t stream = pb_ostream_from_buffer(data + 1, sizeof(data) - 1);
    bool status = pb_encode(&stream, CanboProvisioningCommand_fields, command);

    if (!status) {
#ifdef DEBUG_MESSAGE_HANDLER
        printf("Failed to encode provisioning command for node %d\n", node_id);
#endif
        return -1;
    }

#ifdef DEBUG_MESSAGE_HANDLER
    printf("Sending provisioning command to node %d (size: %zu bytes)\n", node_id, stream.bytes_written + 1);
#endif

    // Send the command via the registered callback
    if (g_send_can_message_callback != nullptr) {
        g_send_can_message_callback(data, stream.bytes_written + 1);
        return 0; // Success
    } else {
#ifdef DEBUG_MESSAGE_HANDLER
        printf("No CAN message callback registered, cannot send provisioning command\n");
#endif
        return -1; // No callback registered
    }
}

uint32_t get_time_in_ms() {
#ifdef __EMSCRIPTEN__
    return emscripten_get_now();
#else
    return time_us_32() / 1000;
#endif
}

void handle_command(const uint8_t *data, uint32_t len, uint8_t node_id) {
    if (len < 1) {
        printf("[ERROR] Invalid message length: %d\r\n", len);
        return;
    }

    MessageType command_type = static_cast<MessageType>(data[0]);

    switch (command_type) {
    case MessageType::MESSAGE_CANBO_PING:
        handle_canbo_ping_command(data, len);
        break;

    case MessageType::MESSAGE_CANBO_TOGGLE_BUTTON:
        handle_canbo_toggle_button_command(data, len, node_id);
        break;

    case MessageType::MESSAGE_CANBO_MOMENTARY_BUTTON:
        handle_canbo_momentary_button_command(data, len, node_id);
        break;

    case MessageType::MESSAGE_RF_DOOR_SENSOR:
        handle_rf_door_sensor_command(data, len, node_id);
        break;

    case MessageType::MESSAGE_RF_MOMENTARY_BUTTON:
        handle_rf_momentary_button_command(data, len, node_id);
        break;

    case MessageType::MESSAGE_PIR_VALUE: {
        // Decode PIRCommand
        PIRCommand pir = PIRCommand_init_zero;
        pb_istream_t stream = pb_istream_from_buffer(data + 1, len - 1);
        bool ok = pb_decode(&stream, PIRCommand_fields, &pir);
        if (!ok) {
            printf("[ERROR] PIR decode failed: %s\r\n", PB_GET_ERROR(&stream));
            break;
        }

        // Process both motion detected and motion stopped messages

        CanboConfig *cfg = find_canbo_config_by_node_id(node_id);
        if (!cfg) {
            printf("[ERROR] PIR: No Canbo config for node %u\r\n", node_id);
            break;
        }

        // Look for PIR configuration in ADC input
        CanboConfig_ADCInput *pir_input = nullptr;
        if (cfg->has_adc_inputs &&
            cfg->adc_inputs.type == CanboConfig_ADCInput_ConnectorType_PIR) {
            pir_input = &cfg->adc_inputs;
        }
        if (!pir_input) {
            printf("[ERROR] PIR: No matching ADC PIR config found\r\n");
            break;
        }

        // Find or create PIR state for this node
        PIRState *pir_state = find_or_create_pir_state(node_id);
        if (!pir_state) {
            printf("[ERROR] PIR: Could not create PIR state for node %u\r\n", node_id);
            break;
        }

        uint32_t now = get_time_in_ms();

        if (pir.is_motion_detected) {
            // Motion detected - cancel any pending deactivation actions, schedule activation if needed
            if (!pir_state->is_activated) {
                // PIR is transitioning from inactive to active
#ifdef DEBUG_MESSAGE_HANDLER
                printf("PIR: Node %u activated (motion detected), scheduling activation actions with individual delays\n", node_id);
#endif
                pir_state->is_activated = true;
                pir_state->last_activated_time = now;

                // Schedule each activation action with its individual activate_delay_msec
                for (uint8_t i = 0; i < pir_input->config.pir.on_activate_count; i++) {
                    Action *action = find_action_by_id(pir_input->config.pir.on_activate[i]);
                    if (action) {
                        uint32_t action_delay = action->activate_delay_msec; // Use 0 for immediate if not specified
#ifdef DEBUG_MESSAGE_HANDLER
                        if (action->which_target == Action_light_id_tag) {
                            printf("PIR: Scheduling activation action %d for light %d with %d ms delay\n", 
                                   action->id, action->target.light_id, action_delay);
                        } else if (action->which_target == Action_somfy_shade_id_tag) {
                            printf("PIR: Scheduling activation action %d for shade %d with %d ms delay\n", 
                                   action->id, action->target.somfy_shade_id, action_delay);
                        } else {
                            printf("PIR: Scheduling activation action %d for unknown target type %zu with %d ms delay\n", 
                                   action->id, action->which_target, action_delay);
                        }
#endif
                        // Schedule this individual activation action with its delay
                        schedule_delayed_action(action, action_delay, now, node_id);
                    }
                }
            } else {
                // PIR already active - motion continues, cancel any pending deactivation actions
#ifdef DEBUG_MESSAGE_HANDLER
                printf("PIR: Node %u motion continues, cancelling pending deactivations\n", node_id);
#endif
            }
            
            // Cancel any pending deactivation actions
            cancel_all_pir_delayed_actions(node_id, pir_input);
            pir_state->deactivate_after_time = 0;
            
        } else {
            // Motion stopped - cancel any pending activation actions and schedule deactivation actions
            if (pir_state->is_activated) {
#ifdef DEBUG_MESSAGE_HANDLER
                printf("PIR: Node %u motion stopped, cancelling pending activations and scheduling deactivation actions with individual delays\n", node_id);
#endif
                
                // Cancel any pending activation actions (in case they haven't executed yet)
                cancel_all_pir_activation_actions(node_id, pir_input);
                
                // Mark PIR as deactivated immediately (no global timer needed)
                pir_state->is_activated = false;
                pir_state->deactivate_after_time = 0;
                
                // Schedule each deactivation action with its individual activate_delay_msec
                for (uint8_t i = 0; i < pir_input->config.pir.on_deactivate_count; i++) {
                    Action *action = find_action_by_id(pir_input->config.pir.on_deactivate[i]);
                    if (action) {
                        uint32_t action_delay = action->activate_delay_msec > 0 ? action->activate_delay_msec : 5000; // Default 5 second delay
#ifdef DEBUG_MESSAGE_HANDLER
                        if (action->which_target == Action_light_id_tag) {
                            printf("PIR: Scheduling deactivation action %d for light %d with %d ms delay\n", 
                                   action->id, action->target.light_id, action_delay);
                        } else if (action->which_target == Action_somfy_shade_id_tag) {
                            printf("PIR: Scheduling deactivation action %d for shade %d with %d ms delay\n", 
                                   action->id, action->target.somfy_shade_id, action_delay);
                        } else {
                            printf("PIR: Scheduling deactivation action %d for unknown target type %zu with %d ms delay\n", 
                                   action->id, action->which_target, action_delay);
                        }
#endif
                        // Schedule this individual action with its delay
                        schedule_delayed_action(action, action_delay, now, node_id);
                    }
                }
            } else {
                // PIR was already inactive - cancel any pending activation actions that might still be running
#ifdef DEBUG_MESSAGE_HANDLER
                printf("PIR: Node %u motion stopped, PIR already inactive, cancelling any pending activations\n", node_id);
#endif
                cancel_all_pir_activation_actions(node_id, pir_input);
            }
        }
        break;
    }

    default:
        // Unknown command type; ignore or log
        break;
    }
}

// Process PIR states - now replaced by process_delayed_actions()
void process_pir_states() {
    // Process any delayed actions (including PIR deactivation actions)
    process_delayed_actions();
}

#ifdef __EMSCRIPTEN__
void handle_command_emscripten(std::string data, uint8_t node_id) {
    const char *data_chars = data.c_str();
    handle_command(reinterpret_cast<const uint8_t *>(data_chars), data.size(), node_id);
}

using namespace emscripten;
EMSCRIPTEN_BINDINGS(message_handler) {
    function("handleCommand", &handle_command_emscripten);
}
#endif
