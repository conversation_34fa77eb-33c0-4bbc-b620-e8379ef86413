// Compile me into c with
// python lib/nanopb-0.4.9.1-macosx-x86/generator/nanopb_generator.py basestation-config.proto

syntax = "proto3";

import "nanopb.proto";

/**
* Basestation Main Schema (config + state + metadata)
*/

message ActiveConfiguration {
  BasestationConfig config = 1;
  BasestationState state = 2;
}

/**
* Basestation Update Message
*/

message BasestationUpdateMessage {
  string qr_code = 1 [(nanopb).max_length = 12];
  BasestationConfig config = 2;
}

/**
* Basestation Configuration
*/

message BasestationConfig {
  // Unique identifier for this basestation on the network
  uint32 id = 1 [(nanopb).int_size = IS_8];
  // Version number incremented on each config change
  string version = 2 [(nanopb).max_length = 8];
  // Configuration for radio-frequency devices
  RFConfig rf_config = 3;
  WifiConfig wifi_config = 4;
  MACConfig mac_config = 5;
  DHCPConfig dhcp_config = 6;
  // WebSocket server address for cloud connectivity
  string server_address = 7 [(nanopb).max_length = 128];

  // Configurations for the inputs
  repeated CanboConfig canbo_configs = 8 [(nanopb).max_count = 20];
  repeated RFReedSensorConfig rf_reed_configs = 9 [(nanopb).max_count = 10];
  repeated RFPresenceSensorConfig rf_presence_configs = 10 [(nanopb).max_count = 10];
  repeated RFDimmerConfig rf_dimmer_configs = 11 [(nanopb).max_count = 10];
  // Configurations for the lights
  repeated LightConfig lights = 12 [(nanopb).max_count = 20];
  // Configurations for the somfy shades
  repeated SomfyShadesConfig somfy_shades = 13 [(nanopb).max_count = 16];
  // Maps CAN and RF devices to their QR code
  repeated NodeQRMapping node_qr_mappings = 14 [(nanopb).max_count = 20];

  // Actions referenced by configs (shared here to reduce config size)
  repeated Action actions = 15 [(nanopb).max_count = 500];
  
  message RFConfig {
    uint32 channel = 1 [(nanopb).int_size = IS_8];
    uint32 network = 2 [(nanopb).int_size = IS_16];
  }

  message WifiConfig {
    string ssid = 1 [(nanopb).max_length = 32];
    string password = 2 [(nanopb).max_length = 32];
  }

  message MACConfig{
      bool use_mac_address = 1;
      string mac_address = 2 [(nanopb).max_length = 17];
  }

  message DHCPConfig{
      bool static_ip = 1;
      string ip_address = 2 [(nanopb).max_length = 15];
      string subnet_mask = 3 [(nanopb).max_length = 15];
      string gateway = 4 [(nanopb).max_length = 15];
      string dns_server = 5 [(nanopb).max_length = 15];
  }

  message NodeQRMapping {
    // The QR code string value for this device
    string qr_code = 1 [(nanopb).max_length = 12];
    // Either the CAN or the RF node ID
    uint32 node_id = 2 [(nanopb).int_size = IS_8];
    DeviceType type = 3;

    enum DeviceType {
      CAN = 0;
      RF = 1;
    }
  }
}

/**
* Action that can be triggered by nodes to control lights.
*/
message Action {
  uint32 id = 1 [(nanopb).int_size = IS_16];
  oneof target {
    uint32 light_id = 2 [(nanopb).int_size = IS_8];
    uint32 somfy_shade_id = 3 [(nanopb).int_size = IS_8];
  }
  uint32 dim_speed_msec = 4 [(nanopb).int_size = IS_32];
  float target_brightness = 5;

  // Optional fields for different action types
  // Note: we may use a oneof here for even smaller messages
  float on_brightness = 6;
  float off_brightness = 7;
  uint32 delay_in_msec = 8 [(nanopb).int_size = IS_32];
  uint32 activate_delay_msec = 9 [(nanopb).int_size = IS_32];
}

message RFDimmerConfig {
  uint32 node_id = 1 [(nanopb).int_size = IS_8];

  // Refer to relevant Action IDs
  repeated uint32 middle_button_click = 2 [(nanopb).max_count = 5, (nanopb).int_size = IS_16];
  repeated uint32 up_button_click = 3 [(nanopb).max_count = 5, (nanopb).int_size = IS_16];
  repeated uint32 down_button_click = 4 [(nanopb).max_count = 5, (nanopb).int_size = IS_16];
  repeated uint32 middle_button_hold = 5 [(nanopb).max_count = 5, (nanopb).int_size = IS_16];
  repeated uint32 up_button_hold = 6 [(nanopb).max_count = 5, (nanopb).int_size = IS_16];
  repeated uint32 down_button_hold = 7 [(nanopb).max_count = 5, (nanopb).int_size = IS_16];
}

message RFReedSensorConfig {
  uint32 node_id = 1 [(nanopb).int_size = IS_8];

  // Refer to relevant Action IDs
  repeated uint32 door_close = 2 [(nanopb).max_count = 5, (nanopb).int_size = IS_16];
  repeated uint32 door_open = 3 [(nanopb).max_count = 5, (nanopb).int_size = IS_16];
}

message RFPresenceSensorConfig {
  uint32 node_id = 1 [(nanopb).int_size = IS_8];

  repeated uint32 on_activate = 2 [(nanopb).max_count = 5, (nanopb).int_size = IS_16];
  repeated uint32 on_deactivate = 3 [(nanopb).max_count = 5, (nanopb).int_size = IS_16];
}

message CanboConfig {
  uint32 node_id = 1 [(nanopb).int_size = IS_8];

  repeated ThreePinInput three_pin_inputs = 2 [(nanopb).max_count = 4];
  repeated TwoPinInput two_pin_inputs = 3 [(nanopb).max_count = 2];
  ADCInput adc_inputs = 4;
  repeated Output outputs = 5 [(nanopb).max_count = 4];

  message ThreePinInput {
    uint32 connector_id = 1 [(nanopb).int_size = IS_8];
    ConnectorType type = 2;

    enum ConnectorType {
      TOGGLE = 0;
      MOMENTARY = 1;
    }

    oneof config {
      ToggleConfig toggle = 3;
      MomentaryConfig momentary = 4;
    }

    message ToggleConfig {
      // Refer to relevant Action IDs
      repeated uint32 up_click = 1 [(nanopb).max_count = 5, (nanopb).int_size = IS_16];
      repeated uint32 up_hold = 2 [(nanopb).max_count = 5, (nanopb).int_size = IS_16];
      repeated uint32 down_click = 3 [(nanopb).max_count = 5, (nanopb).int_size = IS_16];
      repeated uint32 down_hold = 4 [(nanopb).max_count = 5, (nanopb).int_size = IS_16];
      repeated uint32 up_press = 5 [(nanopb).max_count = 5, (nanopb).int_size = IS_16];
      repeated uint32 up_release = 6 [(nanopb).max_count = 5, (nanopb).int_size = IS_16];
      repeated uint32 down_press = 7 [(nanopb).max_count = 5, (nanopb).int_size = IS_16];
      repeated uint32 down_release = 8 [(nanopb).max_count = 5, (nanopb).int_size = IS_16];
      repeated uint32 up_hold_release = 9 [(nanopb).max_count = 5, (nanopb).int_size = IS_16];
      repeated uint32 down_hold_release = 10 [(nanopb).max_count = 5, (nanopb).int_size = IS_16];
    }

    message MomentaryConfig {
      // Refer to relevant Action IDs
      repeated uint32 up_click = 1 [(nanopb).max_count = 5, (nanopb).int_size = IS_16];
      repeated uint32 up_hold = 2 [(nanopb).max_count = 5, (nanopb).int_size = IS_16];
      repeated uint32 up_press = 3 [(nanopb).max_count = 5, (nanopb).int_size = IS_16];
      repeated uint32 up_release = 4 [(nanopb).max_count = 5, (nanopb).int_size = IS_16];
      repeated uint32 up_hold_release = 5 [(nanopb).max_count = 5, (nanopb).int_size = IS_16];
    }
  }

  message TwoPinInput {
    uint32 connector_id = 1 [(nanopb).int_size = IS_8];
    ConnectorType type = 2;

    enum ConnectorType {
      MOMENTARY = 0;
      DOOR_SENSOR = 1;
    }

    oneof config {
      MomentaryConfig momentary = 3;
      DoorSensorConfig door_sensor = 4;
    }

    message MomentaryConfig {
      // Refer to relevant Action IDs
      repeated uint32 up_click = 1 [(nanopb).max_count = 5, (nanopb).int_size = IS_16];
      repeated uint32 up_hold = 2 [(nanopb).max_count = 5, (nanopb).int_size = IS_16];
      repeated uint32 up_press = 3 [(nanopb).max_count = 5, (nanopb).int_size = IS_16];
      repeated uint32 up_release = 4 [(nanopb).max_count = 5, (nanopb).int_size = IS_16];
      repeated uint32 up_hold_release = 5 [(nanopb).max_count = 5, (nanopb).int_size = IS_16];
    }

    message DoorSensorConfig {
      // Refer to relevant Action IDs
      repeated uint32 on_open = 1 [(nanopb).max_count = 5, (nanopb).int_size = IS_16];
      repeated uint32 on_close = 2 [(nanopb).max_count = 5, (nanopb).int_size = IS_16];
    }
  }

  message ADCInput {
    uint32 connector_id = 1 [(nanopb).int_size = IS_8];
    ConnectorType type = 2;

    enum ConnectorType {
      KNOB = 0;
      THERMOSTAT = 1;
      PIR = 2;
    }

    oneof config {
      KnobConfig knob = 3;
      ThermostatConfig thermostat = 4;
      PIRConfig pir = 5;
    }

    message KnobConfig {
      // Refer to relevant Action IDs
      repeated uint32 on_turn = 1 [(nanopb).max_count = 5, (nanopb).int_size = IS_16];
    }

    message ThermostatConfig {
      // Refer to relevant Action IDs
      repeated uint32 thermostat_action = 1 [(nanopb).max_count = 5, (nanopb).int_size = IS_16];
    }

    message PIRConfig {
      // Refer to relevant Action IDs
      repeated uint32 on_activate = 1 [(nanopb).max_count = 5, (nanopb).int_size = IS_16];
      repeated uint32 on_deactivate = 2 [(nanopb).max_count = 5, (nanopb).int_size = IS_16];
    }
  }

  message Output {
    uint32 connector_id = 1 [(nanopb).int_size = IS_8];
    ConnectorType connector_type = 2;

    enum ConnectorType {
      RELAY = 0;
    }
  }
}

message LightConfig {
  uint32 id = 1 [(nanopb).int_size = IS_8];
  // How fast the light dims in milliseconds
  uint32 dim_speed_msec = 2 [(nanopb).int_size = IS_32];
  // Configurations for the fixtures
  repeated FixtureConfig fixtures = 3 [(nanopb).max_count = 5];

  message FixtureConfig {
    // Min and max brightness of the fixture
    float min_brightness = 1;
    float max_brightness = 2;

    // Configuration for the fixture
    FixtureType type = 3;

    enum FixtureType {
      DMX = 0;
      RF = 1;
      ZERO_TO_TEN_VOLT = 2;
      RELAY = 3;
    }

    oneof config {
      DMXConfig dmx = 4;
      RFConfig rf = 5;
      ZeroToTenVoltConfig zero_to_ten_volt = 6;
      RelayConfig relay = 7;
      // Analog0To10VConfig analog_0_10 = 7;
    }

    message DMXConfig {
      LightParams params = 1;
      RGBConfig rgb = 2;
      repeated uint32 channels = 3 [(nanopb).max_count = 10, (nanopb).int_size = IS_8];
      FixtureTypeConfig type = 4;

      enum FixtureTypeConfig {
        D4 = 0;
        TUNABLE_WHITE = 1;
        ELV = 2;
        DF_12 = 3;
        EST = 4;
        RGB_STRIP = 5;
      }

      message LightParams {
        float min1 = 1;
        float max1 = 2;
        float gamma1 = 3;
        float min2 = 4;
        float max2 = 5;
        float gamma2 = 6;
      }

      message RGBConfig {
        uint32 red = 1 [(nanopb).int_size = IS_8];
        uint32 green = 2 [(nanopb).int_size = IS_8];
        uint32 blue = 3 [(nanopb).int_size = IS_8];
      }
    }

    message RFConfig {
      Type type = 1;
      uint32 node_id = 2 [(nanopb).int_size = IS_8];

      enum Type {
        DIMMER = 0;
        SWITCH = 1;
      }
    }

    message ZeroToTenVoltConfig {
      Type type = 1;
      uint32 node_id = 2 [(nanopb).int_size = IS_8];
      bool use_relay = 3;
      uint32 out_connector_id = 4 [(nanopb).int_size = IS_8];

      enum Type {
        SOURCING = 0;
        SINKING = 1;
      }
    }

    message RelayConfig {
      uint32 node_id = 1 [(nanopb).int_size = IS_8];
      uint32 out_connector_id = 2 [(nanopb).int_size = IS_8];
    }

    // message Analog0To10VConfig {
    //   repeated uint32 channels = 1 [(nanopb).max_count = 10, (nanopb).int_size = IS_8];
    // }
  }
}


// Need to map the id used in the configurator to the id assigned by the basestation during pairing
message SomfyShadesConfig {
  uint32 internal_id = 1 [(nanopb).int_size = IS_8];
  uint32 device_id = 2 [(nanopb).int_size = IS_8];
}

/**
* Basestation State
*/

message BasestationState {
  repeated LightState lights = 1 [(nanopb).max_count = 10];
  repeated ButtonState buttons = 2 [(nanopb).max_count = 30];
  repeated ProvisioningState provisioned_devices = 3 [(nanopb).max_count = 10];
  repeated RFReedState reeds = 4 [(nanopb).max_count = 10];
  repeated RFPresenceState presences = 5 [(nanopb).max_count = 10];
  repeated PIRState pirs = 6 [(nanopb).max_count = 10];
}

message RFReedState {
    uint32 node_id = 1 [(nanopb).int_size = IS_8];
    Status sensor_status = 2;
    uint64 last_modified_time = 3;
    float battery_voltage = 4;

    enum Status {
      UNKNOWN = 0;
      OPEN = 1;
      CLOSED = 2;
    }
}

message RFPresenceState {
    uint32 node_id = 1 [(nanopb).int_size = IS_8];
    Status sensor_status = 2;
    uint64 last_modified_time = 3;
    float battery_voltage = 4;

    enum Status {
      UNKNOWN = 0;
      ACTIVATED = 1;
      DEACTIVATED = 2;
    }
} 

message ProvisioningState {
  uint32 node_id = 1 [(nanopb).int_size = IS_8];
  bool is_provisioned = 2;
  ProvisioningErrorCode error_code = 3;
  uint64 last_seen_time = 4;
  int32 rssi = 5 [(nanopb).int_size = IS_8];

  enum ProvisioningErrorCode {
    NONE = 0;
    NOT_FOUND = 1;
    NO_CANBO_CONFIG = 2;
    COULD_NOT_SEND_PROVISIONING_COMMAND = 3;
    NO_REED_CONFIG = 4;
    NO_PRESENCE_CONFIG = 5;
  }
}

message LightState {
  uint32 id = 1 [(nanopb).int_size = IS_8];
  float brightness = 2;
  float target_value = 3;
  uint32 dim_speed_msec = 4 [(nanopb).int_size = IS_32];
  uint64 last_modified_time = 5;
  uint64 active_after_time = 6;
  bool is_transitioning = 7;
  float last_brightness_before_action = 8;
  TransitionStopReason last_transition_stop_reason = 9;

  enum TransitionStopReason {
    TRANSITION_STOP_UNKNOWN = 0;
    TRANSITION_STOP_HOLD_RELEASE = 1;
    TRANSITION_STOP_CLICK_COMMAND = 2;
    TRANSITION_STOP_TARGET_REACHED = 3;
  }
}

message ButtonState {
  uint32 node_id = 1 [(nanopb).int_size = IS_8];
  uint32 connector_id = 2 [(nanopb).int_size = IS_8];
  State current_state = 3;
  uint64 last_modified_time = 4;

  enum State {
    BUTTON_STATE_RELEASED = 0;
    BUTTON_STATE_UP_PRESSED = 1;
    BUTTON_STATE_DOWN_PRESSED = 2;
  }
}

message PIRState {
  uint32 node_id = 1 [(nanopb).int_size = IS_8];
  bool is_activated = 2;
  uint64 last_activated_time = 3;
  uint64 deactivate_after_time = 4;
}

message ESPStatus {
  Status status = 1;

  enum Status {
    UNKNOWN = 0;
    CONNECTING = 1;
    CONNECTED = 2;
    WEBSOCKET_CONNECTED = 3;
    WEBSOCKET_DISCONNECTED = 4;
    WEBSOCKET_CONNECTION_FAILED = 5;
    DISCONNECTED = 6;
    WIFI_ERROR = 7;
    DHCP_ERROR = 8;
  }
}

message ESPUpdateConfig {
  MACConfig mac_config = 1;
  DHCPConfig dhcp_config = 2;
  WifiConfig wifi_config = 3;
  string qr_code = 4 [(nanopb).max_length = 12];
  string server_address = 5 [(nanopb).max_length = 128];
  string config_version = 6 [(nanopb).max_length = 8];

  message MACConfig{
      bool use_mac_address = 1;
      string mac_address = 2 [(nanopb).max_length = 17];
  }

  message DHCPConfig{
      bool static_ip = 1;
      string ip_address = 2 [(nanopb).max_length = 15];
      string subnet_mask = 3 [(nanopb).max_length = 15];
      string gateway = 4 [(nanopb).max_length = 15];
      string dns_server = 5 [(nanopb).max_length = 15];
  }

  message WifiConfig {
      string ssid = 1 [(nanopb).max_length = 32];
      string password = 2 [(nanopb).max_length = 32];
  }
}