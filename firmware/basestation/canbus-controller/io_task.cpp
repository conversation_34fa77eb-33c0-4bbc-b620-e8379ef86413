#include "io_task.h"
#include "watchdog_manager.h"

// C standard libraries
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <math.h>

// Global Instances/Variables
TaskHandle_t xInputPollingTaskHandle;
TaskHandle_t xInputKnobPollingTaskHandle;
TaskHandle_t xPWMDimmerTaskHandle;

// Local Instances/Variables
IO3PinParams_t threeInput[ NUM_INPUT_3_PIN ] = INPUT_3_PIN_DEFAULT_PARAMS();
IO2PinParams_t twoInput[ NUM_INPUT_2_PIN ] = INPUT_2_PIN_DEFAULT_PARAMS();
IO2PinParams_t twoOut[ NUM_OUTPUT_2_PIN ] = OUTPUT_2_PIN_DEFAULT_PARAMS();
KnobParams_t knob_param = KNOB_DEFAULT_PARAMS();
PWMParams_t pwm_param = PWM_DEFAULT_PARAMS();

static float fDimTarget = 0.0f;
static uint32_t uiDimTimeMs = 0;
static float fCurrentDimLevel = 0.0f;  // Track current dimming level for stop functionality

void vInputPollingTask( void * pvParameters ){
    CanboProvisioningCommand last_configuration = CanboProvisioningCommand_init_zero;
    CanboProvisioningCommand current_configuration = CanboProvisioningCommand_init_zero;
    bool add_3_pin[sizeof(last_configuration.three_pin_inputs) / sizeof(*last_configuration.three_pin_inputs)];
    bool remove_3_pin[sizeof(last_configuration.three_pin_inputs) / sizeof(*last_configuration.three_pin_inputs)];
    bool add_2_pin[sizeof(last_configuration.two_pin_inputs) / sizeof(*last_configuration.two_pin_inputs)];
    bool remove_2_pin[sizeof(last_configuration.two_pin_inputs) / sizeof(*last_configuration.two_pin_inputs)];
    // Fetch data from flash on how the device was configured
    // Allow CAN task to bring up queues before polling IO
    vTaskDelay( pdMS_TO_TICKS( 500 ) );
    //
    printf( "[START] Input polling task started\r\n" );
    // Pending RELEASE injection for rapid toggle transitions (per 3-pin connector)
    typedef struct {
        bool active;
        uint32_t due_time_ms;
        ToggleButtonCommand_State next_state; // Up or Down to send after RELEASE
    } PendingToggleRelease;

    static PendingToggleRelease pending_release[ NUM_INPUT_3_PIN ] = { 0 };

    for( ;; ) {
        // Direct read - no semaphore needed
        get_active_configuration(&current_configuration);
        // reconfigure 3 pin inputs
        if ( current_configuration.three_pin_inputs_count != last_configuration.three_pin_inputs_count
                || !same_three_pin_input_configs(current_configuration.three_pin_inputs, last_configuration.three_pin_inputs, sizeof(current_configuration.three_pin_inputs)) ) {

            #ifdef DEBUG_IO
            printf("[IO] 3 Pin Inputs Configuration Changed\r\n");
            #endif

            memset(add_3_pin, true, sizeof(add_3_pin));
            memset(remove_3_pin, true, sizeof(remove_3_pin));

            for ( size_t i = 0; i < sizeof(current_configuration.three_pin_inputs) / sizeof(*current_configuration.three_pin_inputs); ++i ) {
                CanboProvisioningCommand_ThreePinInput active_in = current_configuration.three_pin_inputs[i];
                for ( size_t j = 0; j < sizeof(last_configuration.three_pin_inputs) / sizeof(*last_configuration.three_pin_inputs); ++j ) {
                    CanboProvisioningCommand_ThreePinInput last_in = last_configuration.three_pin_inputs[j];

                    if (active_in.connector_id == last_in.connector_id) {
                        add_3_pin[i] = false;
                        remove_3_pin[j] = false;
                        break;
                    }
                }
            }

            for ( size_t i = 0; i < sizeof(current_configuration.three_pin_inputs) / sizeof(*current_configuration.three_pin_inputs); ++i ) {
                if ( add_3_pin[i] && current_configuration.three_pin_inputs[i].connector_id != 0 ) {
                    #ifdef DEBUG_IO
                    printf("[IO] Adding 3 Pin Input with Connector ID: %d\r\n", current_configuration.three_pin_inputs[i].connector_id);
                    #endif

                    vInitIO(threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio0.gpio_pin, true);
                    vInitIO(threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio1.gpio_pin, true);
                    threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio0.gpio_state = true;
                    threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio1.gpio_state = true;
                    threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio_state = 1;
                }
                if ( remove_3_pin[i] && last_configuration.three_pin_inputs[i].connector_id != 0 ) {
                    #ifdef DEBUG_IO
                    printf("[IO] Removing 3 Pin Input with Connector ID: %d\r\n", last_configuration.three_pin_inputs[i].connector_id);
                    #endif

                    vDeInitIO(threeInput[last_configuration.three_pin_inputs[i].connector_id - 1].gpio0.gpio_pin);
                    vDeInitIO(threeInput[last_configuration.three_pin_inputs[i].connector_id - 1].gpio1.gpio_pin);
                }
            }

            last_configuration.three_pin_inputs_count = current_configuration.three_pin_inputs_count;
            memcpy(last_configuration.three_pin_inputs, current_configuration.three_pin_inputs, sizeof(current_configuration.three_pin_inputs));
        }
        // reconfigure 2 pin inputs
        if ( current_configuration.two_pin_inputs_count != last_configuration.two_pin_inputs_count
                || !same_two_pin_input_configs(current_configuration.two_pin_inputs, last_configuration.two_pin_inputs, sizeof(current_configuration.two_pin_inputs)) ) {

            #ifdef DEBUG_IO
            printf("[IO] 2 Pin Inputs Configuration Changed\r\n");
            #endif

            memset(add_2_pin, true, sizeof(add_2_pin));
            memset(remove_2_pin, true, sizeof(remove_2_pin));

            for ( size_t i = 0; i < sizeof(current_configuration.two_pin_inputs) / sizeof(*current_configuration.two_pin_inputs); ++i ) {
                CanboProvisioningCommand_TwoPinInput active_in = current_configuration.two_pin_inputs[i];
                for ( size_t j = 0; j < sizeof(last_configuration.two_pin_inputs) / sizeof(*last_configuration.two_pin_inputs); ++j ) {
                    CanboProvisioningCommand_TwoPinInput last_in = last_configuration.two_pin_inputs[j];

                    if (active_in.connector_id == last_in.connector_id) {
                        add_2_pin[i] = false;
                        remove_2_pin[j] = false;
                        break;
                    }
                }
            }

            for ( size_t i = 0; i < sizeof(current_configuration.two_pin_inputs) / sizeof(*current_configuration.two_pin_inputs); ++i ) {
                if ( add_2_pin[i] && current_configuration.two_pin_inputs[i].connector_id != 0 ) {
                    #ifdef DEBUG_IO
                    printf("[IO] Adding 2 Pin Input with Connector ID: %d\r\n", current_configuration.two_pin_inputs[i].connector_id);
                    #endif

                    vInitIO(twoInput[current_configuration.two_pin_inputs[i].connector_id - 1].gpio_pin, true);
                    twoInput[current_configuration.two_pin_inputs[i].connector_id - 1].gpio_state = false;
                }
                if ( remove_2_pin[i] && last_configuration.two_pin_inputs[i].connector_id != 0 ) {
                    #ifdef DEBUG_IO
                    printf("[IO] Removing 2 Pin Input with Connector ID: %d\r\n", current_configuration.two_pin_inputs[i].connector_id);
                    #endif

                    vDeInitIO(twoInput[last_configuration.two_pin_inputs[i].connector_id - 1].gpio_pin);
                }
            }

            last_configuration.two_pin_inputs_count = current_configuration.two_pin_inputs_count;
            memcpy(last_configuration.two_pin_inputs, current_configuration.two_pin_inputs, sizeof(current_configuration.two_pin_inputs));
        }
        
        // reconfigure relay outputs
        static bool relays_initialized[NUM_OUTPUT_2_PIN] = {false, false, false, false};
        for (size_t i = 0; i < current_configuration.outputs_count && i < NUM_OUTPUT_2_PIN; ++i) {
            if (current_configuration.outputs[i].connector_type == CanboProvisioningCommand_Output_ConnectorType_Relay) {
                uint32_t relay_id = current_configuration.outputs[i].connector_id;
                if (relay_id > 0 && relay_id <= NUM_OUTPUT_2_PIN) {
                    if (!relays_initialized[relay_id - 1]) {
                        // Initialize relay as output
                        vInitIO(twoOut[relay_id - 1].gpio_pin, false);  // false = output
                        gpio_put(twoOut[relay_id - 1].gpio_pin, false); // Start with relay off
                        twoOut[relay_id - 1].gpio_state = false;
                        relays_initialized[relay_id - 1] = true;
                        
                        #ifdef DEBUG_IO
                        printf("[IO] Relay output initialized on connector ID: %d, GPIO: %d\r\n", relay_id, twoOut[relay_id - 1].gpio_pin);
                        #endif
                    }
                }
            }
        }
        
        // poll 3 pin inputs
        uint32_t now_ms = time_us_32() / 1000;
        for( size_t i = 0; i < current_configuration.three_pin_inputs_count; ++i ) {
            uint8_t last_pos = threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio_state;
            uint8_t last_gpio0_state = threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio0.gpio_state;
            uint8_t last_gpio1_state = threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio1.gpio_state;

            // "Up GPIO"
            threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio0.gpio_state = \
                        !gpio_get( threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio0.gpio_pin );
            // "Down GPIO"
            threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio1.gpio_state = \
                        !gpio_get( threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio1.gpio_pin );

            // "Up State"
            if ( threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio0.gpio_state
                        && !threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio1.gpio_state ) {
                threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio_state = 0;
            }
            // "Down State"
            else if ( !threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio0.gpio_state
                        && threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio1.gpio_state ) {
                threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio_state = 2;
            }
            // "Released State"
            else{
                threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio_state = 1;
            }

            // Handle state changes based on connector type
            if (threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio_state != last_pos) {
                switch (current_configuration.three_pin_inputs[i].connector_type) {
                    case CanboProvisioningCommand_ThreePinInput_ConnectorType_TOGGLE: {
                        uint8_t current_state = threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio_state;
                        
                        // Handle direct UP -> DOWN or DOWN -> UP transitions
                        // We need to inject a RELEASE in between for proper basestation handling
                        if ((last_pos == 0 && current_state == 2) || (last_pos == 2 && current_state == 0)) {
                            #ifdef DEBUG_IO_TOGGLE
                            printf("[IO] TOGGLE RAPID %s->%s: Injecting RELEASE\r\n", 
                                   last_pos == 0 ? "UP" : "DOWN", 
                                   current_state == 0 ? "UP" : "DOWN");
                            #endif
                            // Schedule RELEASE followed by the new state after 2ms (non-blocking)
                            size_t idx = current_configuration.three_pin_inputs[i].connector_id - 1;
                            pending_release[idx].active = true;
                            pending_release[idx].due_time_ms = now_ms + 2;
                            pending_release[idx].next_state = (current_state == 0)
                                ? ToggleButtonCommand_State_Up
                                : ToggleButtonCommand_State_Down;
                            // Do not send the current state immediately; it will be sent after RELEASE
                            // Skip further handling for this connector in this iteration
                        }
                        
                        // Send the current state
                        if (pending_release[current_configuration.three_pin_inputs[i].connector_id - 1].active) {
                            // Defer sending current state; it will be sent with the scheduled RELEASE
                        }
                        else if (current_state == 0 && last_pos != 0) {
                            #ifdef DEBUG_IO_TOGGLE
                            printf("[IO] TOGGLE UP\r\n");
                            #endif
                            send_toggle_button_state((current_configuration.three_pin_inputs[i].connector_id), ToggleButtonCommand_State_Up);
                        }
                        else if (current_state == 2 && last_pos != 2) {
                            #ifdef DEBUG_IO_TOGGLE
                            printf("[IO] TOGGLE DOWN\r\n");
                            #endif
                            send_toggle_button_state((current_configuration.three_pin_inputs[i].connector_id), ToggleButtonCommand_State_Down);
                        }
                        else if (current_state == 1 && last_pos != 1) {
                            #ifdef DEBUG_IO_TOGGLE
                            printf("[IO] TOGGLE RELEASE\r\n");
                            #endif
                            send_toggle_button_state((current_configuration.three_pin_inputs[i].connector_id), ToggleButtonCommand_State_Released);
                        }
                        break;
                    }

                    case CanboProvisioningCommand_ThreePinInput_ConnectorType_MOMENTARY:
                        // Handle "up" button
                        if (threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio0.gpio_state != last_gpio0_state) {
                            if (threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio0.gpio_state) {
                                #ifdef DEBUG_IO_3_MOMENTARY
                                printf("[IO] UP MOMENTARY PRESSED\r\n");
                                #endif
                                send_momentary_button_state(current_configuration.three_pin_inputs[i].connector_id,
                                    MomentaryButtonCommand_State_Pressed);
                            } else {
                                #ifdef DEBUG_IO_3_MOMENTARY
                                printf("[IO] UP MOMENTARY RELEASED\r\n");
                                #endif
                                send_momentary_button_state(current_configuration.three_pin_inputs[i].connector_id,
                                    MomentaryButtonCommand_State_Released);
                            }
                        }

                        // Handle "down" button
                        if (threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio1.gpio_state != last_gpio1_state) {
                            if (threeInput[current_configuration.three_pin_inputs[i].connector_id - 1].gpio1.gpio_state) {
                                #ifdef DEBUG_IO_3_MOMENTARY
                                printf("[IO] DOWN MOMENTARY PRESSED\r\n");
                                #endif
                                send_momentary_button_state(current_configuration.three_pin_inputs[i].connector_id,
                                    MomentaryButtonCommand_State_Pressed);
                            } else {
                                #ifdef DEBUG_IO_3_MOMENTARY
                                printf("[IO] DOWN MOMENTARY RELEASED\r\n");
                                #endif
                                send_momentary_button_state(current_configuration.three_pin_inputs[i].connector_id,
                                    MomentaryButtonCommand_State_Released);
                            }
                        }
                        break;

                    default:
                        printf("[Error] Unknown three-pin connector type: %d\r\n", current_configuration.three_pin_inputs[i].connector_type);
                        break;
                }
            }
        }

        // Process any pending RELEASE injections that have reached due time
        for ( size_t pid = 0; pid < NUM_INPUT_3_PIN; ++pid ) {
            if ( pending_release[pid].active && now_ms >= pending_release[pid].due_time_ms ) {
                uint32_t connector_id = pid + 1;
                #ifdef DEBUG_IO_TOGGLE
                printf("[IO] TOGGLE RELEASE (deferred) for connector %u\r\n", connector_id);
                #endif
                send_toggle_button_state(connector_id, ToggleButtonCommand_State_Released);
                // Immediately send the next intended state
                send_toggle_button_state(connector_id, pending_release[pid].next_state);
                pending_release[pid].active = false;
            }
        }
        // poll 2 pin inputs
        for( size_t i = 0; i < current_configuration.two_pin_inputs_count; ++i ) {
            uint8_t last_pos = twoInput[current_configuration.two_pin_inputs[i].connector_id - 1].gpio_state;

            twoInput[current_configuration.two_pin_inputs[i].connector_id - 1].gpio_state = \
                        !gpio_get( twoInput[current_configuration.two_pin_inputs[i].connector_id - 1].gpio_pin );

            // Handle state changes based on connector type
            if ( twoInput[current_configuration.two_pin_inputs[i].connector_id - 1].gpio_state != last_pos ) {
                switch (current_configuration.two_pin_inputs[i].connector_type) {
                    case CanboProvisioningCommand_TwoPinInput_ConnectorType_MOMENTARY:
                        // Rising edge - button pressed
                        if ( twoInput[current_configuration.two_pin_inputs[i].connector_id - 1].gpio_state && !last_pos ) {
                            #ifdef DEBUG_IO_2_MOMENTARY
                            printf("[IO] MOMENTARY PRESSED\r\n");
                            #endif
                            send_momentary_button_state((current_configuration.two_pin_inputs[i].connector_id), MomentaryButtonCommand_State_Pressed);
                        }
                        // Falling edge - button released
                        else if ( !twoInput[current_configuration.two_pin_inputs[i].connector_id - 1].gpio_state && last_pos ) {
                            #ifdef DEBUG_IO_2_MOMENTARY
                            printf("[IO] MOMENTARY RELEASED\r\n");
                            #endif
                            send_momentary_button_state((current_configuration.two_pin_inputs[i].connector_id), MomentaryButtonCommand_State_Released);
                        }
                        break;

                    case CanboProvisioningCommand_TwoPinInput_ConnectorType_DOOR_SENSOR:
                        // Rising edge - door opened
                        if ( twoInput[current_configuration.two_pin_inputs[i].connector_id - 1].gpio_state && !last_pos ) {
                            #ifdef DEBUG_IO_DOOR_SENSOR
                            printf("[IO] DOOR OPENED\r\n");
                            #endif
                            send_door_sensor_state((current_configuration.two_pin_inputs[i].connector_id), DoorSensorCommand_State_Opened);
                        }
                        // Falling edge - door closed
                        else if ( !twoInput[current_configuration.two_pin_inputs[i].connector_id - 1].gpio_state && last_pos ) {
                            #ifdef DEBUG_IO_DOOR_SENSOR
                            printf("[IO] DOOR CLOSED\r\n");
                            #endif
                            send_door_sensor_state((current_configuration.two_pin_inputs[i].connector_id), DoorSensorCommand_State_Closed);
                        }
                        break;

                    default:
                        printf("[ERROR] Unknown two-pin connector type: %d\r\n", current_configuration.two_pin_inputs[i].connector_type);
                        break;
                }
            }
        }

        watchdog_pet_self();
        vTaskDelay( pdMS_TO_TICKS( 10 ) );
    }
}

static bool handle_pir_sensor(float normalized, float last_normalized_value, 
                              bool *movement_detected, bool last_movement_detected,
                              uint32_t current_time, uint32_t *movement_start_time,
                              const float MOVEMENT_THRESHOLD, const uint32_t STABLE_TIMEOUT_MS) {
    // PIR sensor uses level-based detection, not change-based
    // Hardware ADC: LOW voltage when motion detected, HIGH when no motion (covered)
    // Protocol: Send LOW when motion detected, HIGH when no motion
    // Timer logic: suspend during motion, start during no motion
    
    // Use level-based detection: if normalized ADC value is below threshold, motion detected
    // When PIR detects motion, ADC reads low voltage; when covered (no motion), ADC reads high
    const float PIR_MOTION_THRESHOLD = 0.5f;  // 50% of full scale - adjust as needed
    bool is_motion_detected = (normalized < PIR_MOTION_THRESHOLD);
    
    // Calculate change for debugging purposes
    float normalized_change = 0.0f;
    if (last_normalized_value >= 0.0f) {
        normalized_change = fabs(normalized - last_normalized_value);
    }

#ifdef DEBUG_ADC_POLLING
    printf("[ADC] PIR Debug: norm=%.4f, threshold=%.4f, motion_detected=%d, last_norm=%.4f, change=%.4f\r\n", 
           normalized, PIR_MOTION_THRESHOLD, is_motion_detected, last_normalized_value, normalized_change);
#endif

    // Update movement state - timer suspended during motion, started during no motion
    if (is_motion_detected && !(*movement_detected)) {
        // Motion started - suspend timer, set to active state
        *movement_detected = true;  // Motion detected state
        *movement_start_time = current_time;  // Reset timer (suspended during motion)
#ifdef DEBUG_ADC_POLLING
        printf("[ADC] PIR Motion Started - timer suspended (ADC=%.3f < %.3f)\r\n", normalized, PIR_MOTION_THRESHOLD);
#endif
    } else if (is_motion_detected && *movement_detected) {
        // Continued motion - keep timer suspended
        *movement_start_time = current_time;  // Keep resetting timer while motion continues
#ifdef DEBUG_ADC_POLLING
        printf("[ADC] PIR Motion Continues - timer remains suspended (ADC=%.3f)\r\n", normalized);
#endif
    } else if (!is_motion_detected && *movement_detected) {
        // No motion detected but still in motion state - check if delay timer has expired
        // Note: movement_start_time was last updated when motion was detected, so this
        // calculates time since the last motion activity
        uint32_t time_since_last_motion = current_time - *movement_start_time;
        if (time_since_last_motion >= STABLE_TIMEOUT_MS) {
            *movement_detected = false;  // No motion state after delay
#ifdef DEBUG_ADC_POLLING
            printf("[ADC] PIR Motion Stopped after %d ms since last motion (ADC=%.3f >= %.3f)\r\n", 
                   time_since_last_motion, normalized, PIR_MOTION_THRESHOLD);
#endif
        }
#ifdef DEBUG_ADC_POLLING
        else {
            printf("[ADC] PIR No motion detected, waiting %d/%d ms (ADC=%.3f >= %.3f)\r\n", 
                   time_since_last_motion, STABLE_TIMEOUT_MS, normalized, PIR_MOTION_THRESHOLD);
        }
#endif
    }

    // Return true if we should send (state transition occurred)
    bool should_send = (*movement_detected != last_movement_detected);
    
    if (should_send) {
        // Send the actual motion state: true = motion detected, false = no motion
        send_pir_value(*movement_detected);  // Send actual state without inversion
#ifdef DEBUG_ADC_POLLING
        printf("[ADC] PIR SENT: %s (motion=%s)\r\n", *movement_detected ? "MOTION_DETECTED" : "NO_MOTION", *movement_detected ? "true" : "false");
#endif
    }
    
    return should_send;
}

static bool handle_analog_sensor(CanboProvisioningCommand_ADCInput_ConnectorType connector_type,
                                 float processed_value, float filtered_processed_value,
                                 float last_sent_value, float *last_stable_sent_value,
                                 bool *movement_detected, bool *value_latched, bool *just_latched,
                                 uint32_t *stable_count, uint32_t current_time, uint32_t *movement_start_time,
                                 uint32_t *latch_start_time, float normalized_change, 
                                 const float MOVEMENT_THRESHOLD, const float ABSOLUTE_CHANGE_THRESHOLD,
                                 const uint32_t STABLE_TIMEOUT_MS, const uint32_t MOVEMENT_TIMEOUT_MS,
                                 const uint32_t LATCH_TIMEOUT_MS, const uint32_t MIN_STABLE_COUNT) {
    
    bool is_movement = normalized_change > MOVEMENT_THRESHOLD;
    float filtered_sent_value_change = fabs(filtered_processed_value - last_sent_value);
    bool should_send = false;

    // Movement/Stable/Latch Logic for analog sensors
    if (*movement_detected && !is_movement) {
        uint32_t time_since_movement = current_time - *movement_start_time;
        if (time_since_movement >= STABLE_TIMEOUT_MS) {
            *movement_detected = false;
            *stable_count = 0;
            *movement_start_time = current_time;
        }
    }

    if (is_movement && !(*movement_detected)) {
        *movement_detected = true;
        *movement_start_time = current_time;
        *stable_count = 0;
        *value_latched = false;
        if (filtered_sent_value_change > ABSOLUTE_CHANGE_THRESHOLD) {
            should_send = true;
        }
    } else if (is_movement && *movement_detected && *value_latched) {
        *value_latched = false;
        *stable_count = 0;
        *movement_start_time = current_time;
        if (filtered_sent_value_change > ABSOLUTE_CHANGE_THRESHOLD) {
            should_send = true;
        }
    } else if (*movement_detected && is_movement) {
        uint32_t time_since_movement = current_time - *movement_start_time;
        if (time_since_movement >= MOVEMENT_TIMEOUT_MS) {
            if (filtered_sent_value_change > ABSOLUTE_CHANGE_THRESHOLD) {
                should_send = true;
                *movement_start_time = current_time;
            }
        }
    } else if (!(*movement_detected) && !is_movement) {
        if (!(*value_latched)) {
            uint32_t time_since_movement = current_time - *movement_start_time;
            if (time_since_movement >= LATCH_TIMEOUT_MS) {
                *value_latched = true;
                *latch_start_time = current_time;
                *just_latched = true;
            }
        }
        (*stable_count)++;
        
        if (*just_latched || (*value_latched && filtered_sent_value_change > ABSOLUTE_CHANGE_THRESHOLD)) {
            should_send = true;
        }
    }

    // Send if conditions are met
    bool can_send = should_send && filtered_sent_value_change > ABSOLUTE_CHANGE_THRESHOLD && *stable_count >= MIN_STABLE_COUNT;
    
    if (can_send) {
        float value_to_send = *value_latched ? filtered_processed_value : processed_value;
        
        switch (connector_type) {
            case CanboProvisioningCommand_ADCInput_ConnectorType_KNOB:
                send_knob_value(value_to_send);
#ifdef DEBUG_ADC_POLLING
                printf("[ADC] Knob value: %.3f\r\n", value_to_send);
#endif
                break;
            case CanboProvisioningCommand_ADCInput_ConnectorType_THERMOSTAT:
                send_thermostat_value(value_to_send);
#ifdef DEBUG_ADC_POLLING
                printf("[ADC] Thermostat value: %.1f°C\r\n", value_to_send);
#endif
                break;
            default:
                break;
        }
        
        if (*value_latched) {
            *last_stable_sent_value = value_to_send;
        }
    }
    
    return can_send;
}

void vInputKnobPollingTask( void * pvParameters ){
    // ============================================================================
    // ADC INPUT POLLING TASK
    // ============================================================================
    // Purpose: Polls ADC inputs (knobs/thermostats/PIR) and sends CAN commands based on configuration
    //
    // Variables:
    // - current_configuration: Current device configuration
    // - last_configuration: Previous configuration for change detection
    // - adc_initialized: Flag indicating if ADC has been initialized
    // - last_adc_value: Previous ADC value for change detection
    // - last_sent_value: Last value that was actually sent via CAN
    // - movement_detected: Flag indicating if knob is currently being turned
    // - movement_start_time: Timestamp when movement was first detected
    // - stable_count: Counter for stable readings at final position
    // - iir_filtered_value: IIR filtered value for stable state averaging
    // - iir_alpha: IIR filter coefficient (0.0-1.0, higher = more responsive)
    // - fConversionFactor: ADC conversion factor (3.3V / 4095)
    // - adc_pin: Current ADC pin being used
    // - adc_channel: Current ADC channel being used
    
    CanboProvisioningCommand current_configuration = CanboProvisioningCommand_init_zero;
    CanboProvisioningCommand last_configuration = CanboProvisioningCommand_init_zero;
    bool adc_initialized = false;
    float last_adc_value = 0.0f;
    float last_sent_value = 0.0f;
    float last_stable_sent_value = 0.0f;  // Track last value sent during stable state
    float last_filtered_value = 0.0f;      // Track last filtered value for change detection
    float last_normalized_value = -1.0f;    // Track last normalized value for threshold detection (initialize to invalid value)
    bool movement_detected = false;
    uint32_t movement_start_time = 0;
    uint32_t stable_count = 0;
    bool value_latched = false;  // Track if we've latched to filtered value
    uint32_t latch_start_time = 0;  // Track when we started waiting for latch
    bool last_movement_detected = false;  // Track previous movement state for PIR
    float iir_filtered_value = 0.0f;
    bool iir_initialized = false;  // Track if IIR filter has been initialized
    const float iir_alpha = 0.1f; // IIR filter coefficient (10% new value, 90% previous) - very conservative filtering
    const float fConversionFactor = ( 3.3f / ( ( 1 << 12 ) - 1 ) );
    uint32_t debug_counter = 0;  // Counter for debug prints

    // Thresholds for different phases
    const float MOVEMENT_THRESHOLD = 0.05f;       // 5% - conservative for movement detection
    const float MIN_SEND_THRESHOLD = 0.05f;       // 5% - minimum change required to send a value
    const float ABSOLUTE_CHANGE_THRESHOLD = 0.005f; // 0.005 absolute change threshold (works for any range)
    const uint32_t STABLE_TIMEOUT_MS = 500;      // 500ms of stable readings before sending final value
    const uint32_t MOVEMENT_TIMEOUT_MS = 100;    // 100ms between movement updates
    const uint32_t LATCH_TIMEOUT_MS = 3000;      // 3000ms (3 seconds) before latching to filtered value
    const uint32_t MIN_STABLE_COUNT = 1;         // Require 1 stable reading before sending

    vTaskDelay( pdMS_TO_TICKS( 250 ) );

    printf( "[START] ADC polling task started\r\n" );

    for( ;; ){
        // Get current configuration to check for ADC settings
        get_active_configuration(&current_configuration);
        
        // Pet watchdog early in loop before potentially long ADC operations
        watchdog_pet_self();
        
        // Check if ADC is configured
        if ( !current_configuration.has_adc_inputs ) {
#ifdef DEBUG_ADC_POLLING_DEEP
            printf("[ADC] No ADC inputs configured\r\n");
#endif

            if ( adc_initialized ) {
                // Deinitialize ADC if it was previously initialized
                vDeInitIO(knob_param.pin);
                adc_initialized = false;
                last_adc_value = 0.0f;
                last_sent_value = 0.0f;
                last_stable_sent_value = 0.0f;
                last_filtered_value = 0.0f;
                movement_detected = false;
                stable_count = 0;
                value_latched = false;
                latch_start_time = 0;
                iir_filtered_value = 0.0f;
                movement_start_time = 0;
                last_movement_detected = false;

#ifdef DEBUG_ADC_POLLING_DEEP
                printf("[ADC] ADC deinitialized\r\n");
#endif
            }

            watchdog_pet_self(); // Pet watchdog before delay when no ADC configured
            vTaskDelay( pdMS_TO_TICKS( 100 ) );
            continue;
        }

        // Initialize ADC if not already done
        if ( !adc_initialized ) {
            if ( adc_initialized ) {
                // Deinitialize previous ADC setup
                vDeInitIO(knob_param.pin);
                adc_initialized = false;
            }
            // Reset ALL state variables for new configuration or range type
            last_adc_value = 0.0f;
            last_sent_value = 0.0f;
            last_stable_sent_value = 0.0f;
            last_filtered_value = 0.0f;
            last_normalized_value = -1.0f;
            movement_detected = false;
            stable_count = 0;
            value_latched = false;
            latch_start_time = 0;
            iir_filtered_value = 0.0f;
            iir_initialized = false;
            movement_start_time = 0;
            last_movement_detected = false;

            // Initialize ADC with default knob parameters
            vInitADC( knob_param.pin, knob_param.channel );
            adc_initialized = true;
#ifdef DEBUG_ADC_POLLING_DEEP
            printf("[ADC] ADC initialized on pin %d, channel %d\r\n", knob_param.pin, knob_param.channel);
            const char *adc_type_dbg = "UNKNOWN";
            switch (current_configuration.adc_inputs.connector_type) {
                case CanboProvisioningCommand_ADCInput_ConnectorType_KNOB: adc_type_dbg = "KNOB"; break;
                case CanboProvisioningCommand_ADCInput_ConnectorType_THERMOSTAT: adc_type_dbg = "THERMOSTAT"; break;
                case CanboProvisioningCommand_ADCInput_ConnectorType_PIR: adc_type_dbg = "PIR"; break;
                default: break;
            }
            printf("[ADC] ADC Type: %s\r\n", adc_type_dbg);
            if (current_configuration.adc_inputs.connector_type == CanboProvisioningCommand_ADCInput_ConnectorType_KNOB) {
                printf("[ADC] ADC Min=%.2f, Max=%.2f\r\n",
                        current_configuration.adc_inputs.range.min_max.min, 
                        current_configuration.adc_inputs.range.min_max.max);
            } else if (current_configuration.adc_inputs.connector_type == CanboProvisioningCommand_ADCInput_ConnectorType_THERMOSTAT) {
                printf("[ADC] ADC Offset=%.2f, Scaling=%.2f\r\n",
                        current_configuration.adc_inputs.range.offset_scaling.offset, 
                        current_configuration.adc_inputs.range.offset_scaling.scaling_factor);
            } else if (current_configuration.adc_inputs.connector_type == CanboProvisioningCommand_ADCInput_ConnectorType_PIR) {
                printf("[ADC] PIR configured with standard movement detection\r\n");
            }
#endif
        }

        // --- Always use min/max formula for ADC scaling ---
        float min = 0.0f;
        float max = 1.0f;
        // Use connector_type to determine which range type to use
        if (current_configuration.adc_inputs.which_range == CanboProvisioningCommand_ADCInput_min_max_tag) {
            min = current_configuration.adc_inputs.range.min_max.min;
            max = current_configuration.adc_inputs.range.min_max.max;
        } else if (current_configuration.adc_inputs.which_range == CanboProvisioningCommand_ADCInput_offset_scaling_tag) {
            min = current_configuration.adc_inputs.range.offset_scaling.offset - 0.5f * current_configuration.adc_inputs.range.offset_scaling.scaling_factor;
            max = current_configuration.adc_inputs.range.offset_scaling.offset + 0.5f * current_configuration.adc_inputs.range.offset_scaling.scaling_factor;
        }
        // Read ADC value
        uint16_t usRead = adc_read();
        float fConversion = (float)usRead * fConversionFactor;
        float normalized = fConversion / 3.3f;
        float processed_value = min + normalized * (max - min);
        
#ifdef DEBUG_ADC_POLLING
        debug_counter++;
        if (debug_counter % 100 == 0) {
            printf("[ADC] Value: %.3f (message #%d)\r\n", processed_value, debug_counter);
        }
#endif

        // Update IIR filter on raw voltage values (before scaling/offset)
        if (!iir_initialized) {
            iir_filtered_value = fConversion;
            iir_initialized = true;
        } else {
            iir_filtered_value = (iir_alpha * fConversion) + ((1.0f - iir_alpha) * iir_filtered_value);
        }
        float filtered_normalized = iir_filtered_value / 3.3f;
        float filtered_processed_value = min + filtered_normalized * (max - min);

        // Get current time for timing-based logic
        uint32_t current_time = time_us_32() / 1000;
        if (movement_start_time == 0) {
            movement_start_time = current_time;
        }

        // Handle sensor processing based on type
        bool message_sent = false;
        float normalized_change = 0.0f;
        
        // Calculate normalized change for all sensor types
        if (last_normalized_value >= 0.0f) {
            normalized_change = fabs(normalized - last_normalized_value);
        }

        if (current_configuration.adc_inputs.connector_type == CanboProvisioningCommand_ADCInput_ConnectorType_PIR) {
            // PIR sensor: simple state transition logic
            message_sent = handle_pir_sensor(normalized, last_normalized_value, 
                                           &movement_detected, last_movement_detected,
                                           current_time, &movement_start_time,
                                           MOVEMENT_THRESHOLD, STABLE_TIMEOUT_MS);
        } else {
            // Analog sensors (knob/thermostat): complex change-based logic
            bool just_latched = false;
            message_sent = handle_analog_sensor(current_configuration.adc_inputs.connector_type,
                                              processed_value, filtered_processed_value,
                                              last_sent_value, &last_stable_sent_value,
                                              &movement_detected, &value_latched, &just_latched,
                                              &stable_count, current_time, &movement_start_time,
                                              &latch_start_time, normalized_change,
                                              MOVEMENT_THRESHOLD, ABSOLUTE_CHANGE_THRESHOLD,
                                              STABLE_TIMEOUT_MS, MOVEMENT_TIMEOUT_MS,
                                              LATCH_TIMEOUT_MS, MIN_STABLE_COUNT);
        }

        // Update last_sent_value if a message was sent
        if (message_sent) {
            if (current_configuration.adc_inputs.connector_type == CanboProvisioningCommand_ADCInput_ConnectorType_PIR) {
                last_sent_value = movement_detected ? 1.0f : 0.0f;
            } else {
                last_sent_value = value_latched ? filtered_processed_value : processed_value;
            }
        }

        // Update tracking variables
        last_adc_value = processed_value;
        last_filtered_value = iir_filtered_value;
        last_normalized_value = normalized;
        last_movement_detected = movement_detected;
        last_configuration = current_configuration;

        watchdog_pet_self();
        vTaskDelay( pdMS_TO_TICKS( 10 ) );
    }
}

void vPWMDimmerTask( void * pvParameters ){
    // ============================================================================
    // PWM DIMMER TASK
    // ============================================================================
    // Purpose: Controls PWM output for dimming applications with time-based transitions
    // Priority: Second highest (configMAX_PRIORITIES - 2) for smooth dimming control
    //
    // Variables:
    // - fCurrentDimLevel: Current dimming level (0.0 to 1.0) - global for stop functionality
    // - fDimTarget: Target dimming level to reach
    // - uiDimTimeMs: Time in milliseconds to complete the dimming transition
    // - dim_start_time: Timestamp when dimming transition started
    // - dim_start_level: Dimming level when transition started
    // - dim_in_progress: Flag indicating if a dimming transition is active
    // - relay_initialized: Flag indicating if relay has been initialized
    // - relay_connector_id: The connector ID for the relay (from zero_to_ten_volt_config)
    // - min_brightness: Minimum brightness level (0.0 to 1.0) from config
    // - max_brightness: Maximum brightness level (0.0 to 1.0) from config
    
    CanboProvisioningCommand current_configuration = CanboProvisioningCommand_init_zero;
    uint32_t dim_start_time = 0;
    float dim_start_level = 0.0f;
    bool dim_in_progress = false;
    bool relay_initialized = false;
    uint32_t relay_connector_id = 0;
    bool last_relay_state = false;
    float min_brightness = 0.0f;
    float max_brightness = 1.0f;

    vTaskDelay( pdMS_TO_TICKS( 250 ) );

    vPWMSetup();

    printf( "[START] PWM dimmer task started\r\n" );

    for( ;; ){
        // Get current configuration to check for relay settings
        get_active_configuration(&current_configuration);
        
        if ( !current_configuration.has_zero_to_ten_volt_config ) {
            #ifdef DEBUG_PWM_DIMMING_DEEP
            printf("[PWM] No zero to ten volt config found\r\n");
            #endif

            if ( relay_initialized ) {
                gpio_put(twoOut[relay_connector_id - 1].gpio_pin, false);
                twoOut[relay_connector_id - 1].gpio_state = false;
                last_relay_state = false;
                vDeInitIO(twoOut[relay_connector_id - 1].gpio_pin);
                relay_initialized = false;
                relay_connector_id = 0;
            }

            // Stop dimming
            pwm_param.duty_cycle = 0;
            pwm_set_chan_level( pwm_param.slice, pwm_param.channel, pwm_param.duty_cycle );

            watchdog_pet_self(); // Pet watchdog before delay when no PWM config
            vTaskDelay( pdMS_TO_TICKS( 100 ) );
            continue;
        }

        // Get min and max brightness constraints
        min_brightness = current_configuration.zero_to_ten_volt_config.min_brightness;
        max_brightness = current_configuration.zero_to_ten_volt_config.max_brightness;

        #ifdef DEBUG_PWM_DIMMING
        printf("[PWM] Min brightness: %.2f, Max brightness: %.2f\r\n", current_configuration.zero_to_ten_volt_config.min_brightness, current_configuration.zero_to_ten_volt_config.max_brightness);
        #endif

        // Clamp brightness constraints to valid range (0-100)
        min_brightness = (min_brightness < 0.0f) ? 0.0f : min_brightness;
        min_brightness = (min_brightness > 100.0f) ? 100.0f : min_brightness;
        max_brightness = (max_brightness < 0.0f) ? 0.0f : max_brightness;
        max_brightness = (max_brightness > 100.0f) ? 100.0f : max_brightness;
        
        // Ensure min <= max
        if (min_brightness > max_brightness) {
            min_brightness = 0.0f;
            max_brightness = 100.0f;
        }
        
        // Convert min/max brightness from 0-100 range to 0-1 range for internal calculations
        float min_brightness_01 = min_brightness / 100.0f;
        float max_brightness_01 = max_brightness / 100.0f;

        #ifdef DEBUG_PWM_DIMMING
        printf("[PWM] Min brightness 01: %.2f, Max brightness 01: %.2f\r\n", min_brightness_01, max_brightness_01);
        #endif

        // Check relay configuration and handle relay control
        bool relay_enabled = current_configuration.zero_to_ten_volt_config.use_relay;
        uint32_t new_relay_connector_id = current_configuration.zero_to_ten_volt_config.relay_connector_id;

        // If relay is disabled or out of bounds in config, disable relay control
        if (relay_initialized && (!relay_enabled || new_relay_connector_id <= 0 || new_relay_connector_id > NUM_OUTPUT_2_PIN)) {
            // Turn off relay if it was previously on
            if (last_relay_state) {
                gpio_put(twoOut[relay_connector_id - 1].gpio_pin, false);
                twoOut[relay_connector_id - 1].gpio_state = false;
                last_relay_state = false;

                #ifdef DEBUG_PWM_RELAY
                printf("[PWM] Relay %d turned OFF (relay disabled in config)\r\n", relay_connector_id);
                #endif
            }

            // Reset relay state
            vDeInitIO(twoOut[relay_connector_id - 1].gpio_pin);
            relay_initialized = false;
            relay_connector_id = 0;
        }
        // Initialize relay if not already done and relay is configured
        else if (!relay_initialized && relay_enabled && new_relay_connector_id > 0 && new_relay_connector_id <= NUM_OUTPUT_2_PIN) {
            
            // Initialize the relay pin as output
            if ( current_configuration.outputs[new_relay_connector_id - 1].connector_type == CanboProvisioningCommand_Output_ConnectorType_Relay ) {
                vInitIO(twoOut[new_relay_connector_id - 1].gpio_pin, false); // false = output
                gpio_put(twoOut[new_relay_connector_id - 1].gpio_pin, false); // Start with relay off
                twoOut[new_relay_connector_id - 1].gpio_state = false; // Start with relay off
                relay_initialized = true;
                relay_connector_id = new_relay_connector_id;
                
                #ifdef DEBUG_PWM_RELAY
                printf("[PWM] Relay initialized on connector ID: %d\r\n", new_relay_connector_id);
                #endif
            } else {
                #ifdef DEBUG_PWM_RELAY
                printf("[PWM] Invalid output of type: %d\r\n", current_configuration.outputs[new_relay_connector_id - 1].connector_type);
                #endif
            }
        }

        // Get current time in milliseconds
        uint32_t current_time = time_us_32() / 1000;
        
        // Check if we need to start a new dimming transition
        if (fCurrentDimLevel != fDimTarget && !dim_in_progress) {
            // Clamp fDimTarget to 0.0-1.0 range to prevent out-of-bounds lerp
            float clamped_target = (fDimTarget < 0.0f) ? 0.0f : fDimTarget;
            clamped_target = (clamped_target > 1.0f) ? 1.0f : clamped_target;

            #ifdef DEBUG_PWM_DIMMING
            printf("[PWM] Original target: %.2f, clamped target: %.2f (min: %.2f, max: %.2f)\r\n", 
                   fDimTarget, clamped_target, min_brightness_01, max_brightness_01);
            #endif
            
            // Only start transition if the clamped target is different from current level
            if (fCurrentDimLevel != clamped_target) {
                // Start a new dimming transition
                dim_start_time = current_time;
                dim_start_level = fCurrentDimLevel;
                dim_in_progress = true;
                fDimTarget = clamped_target; // Update target to clamped value

                #ifdef DEBUG_PWM_DIMMING
                printf("[PWM] Starting dim transition: %d%% -> %d%% over %d ms\r\n",
                       (uint32_t)lround(dim_start_level * 100.0f), 
                       (uint32_t)lround(fDimTarget * 100.0f), uiDimTimeMs);
                #endif
            }
        }

        // Calculate dimming progress if transition is in progress
        if (dim_in_progress && uiDimTimeMs > 0) {
            // Check if target has been changed (indicating a stop command)
            if (fDimTarget == fCurrentDimLevel) {
                // Target matches current level - transition is complete
                dim_in_progress = false;

                #ifdef DEBUG_PWM_DIMMING
                printf("[PWM] Dim transition stopped at current level: %d%%\r\n", 
                       (uint32_t)lround(fCurrentDimLevel * 100.0f));
                #endif
            } else {
                // Calculate elapsed time since transition started
                uint32_t elapsed_time = current_time - dim_start_time;

                // Calculate progress (0.0 to 1.0)
                float progress = (float)elapsed_time / (float)uiDimTimeMs;

                // Clamp progress to 1.0
                progress = (progress > 1.0f) ? 1.0f : progress;
                
                // Calculate new dimming level using linear interpolation
                fCurrentDimLevel = dim_start_level + (fDimTarget - dim_start_level) * progress;
                
                // Check if transition is complete
                if (progress >= 1.0f) {
                    fCurrentDimLevel = fDimTarget;  // Ensure exact target value
                    dim_in_progress = false;
                    #ifdef DEBUG_PWM_DIMMING
                    printf("[PWM] Dim transition complete: %d%%\r\n", 
                           (uint32_t)lround(fCurrentDimLevel * 100.0f));
                    #endif
                }
            }
        }
        // If no dimming time specified, use immediate transition
        else if (dim_in_progress && uiDimTimeMs == 0) {
            fCurrentDimLevel = fDimTarget;
            dim_in_progress = false;

            #ifdef DEBUG_PWM_DIMMING
            printf("[PWM] Immediate dim transition: %d%%\r\n", 
                   (uint32_t)lround(fCurrentDimLevel * 100.0f));
            #endif
        }

        // Clamp dimming level to valid range
        fCurrentDimLevel = (fCurrentDimLevel < 0.0f) ? 0.0f : fCurrentDimLevel;
        fCurrentDimLevel = (fCurrentDimLevel > 1.0f) ? 1.0f : fCurrentDimLevel;
        
        // Apply min/max brightness constraints: map 0-1 input to min_brightness-max_brightness output
        float constrained_dim_level = min_brightness_01 + (max_brightness_01 - min_brightness_01) * fCurrentDimLevel;
        
        // Control relay based on current dim level (only when relay is enabled)
        if (relay_enabled && relay_initialized && relay_connector_id > 0 && relay_connector_id <= NUM_OUTPUT_2_PIN) {
            bool new_relay_state = (constrained_dim_level > min_brightness_01);

            // Only update relay if state has changed
            if (new_relay_state != last_relay_state) {
                twoOut[relay_connector_id - 1].gpio_state = new_relay_state;
                gpio_put(twoOut[relay_connector_id - 1].gpio_pin, new_relay_state);
                last_relay_state = new_relay_state;

                #ifdef DEBUG_PWM_RELAY
                printf("[PWM] Relay %d %s (dim level: %d%%)\r\n",
                       relay_connector_id,
                       new_relay_state ? "ON" : "OFF",
                       (uint32_t)lround(constrained_dim_level * 100.0f));
                #endif
            }
        }
        // Update PWM duty cycle with constrained brightness level and proper rounding
        pwm_param.duty_cycle = (uint16_t)lround((float)pwm_param.config.top * constrained_dim_level);
        pwm_set_chan_level( pwm_param.slice, pwm_param.channel, pwm_param.duty_cycle );

        watchdog_pet_self();
        vTaskDelay( pdMS_TO_TICKS( 17 ) ); // 60 Hz ≈ 16.67ms period, rounded to 17ms
    }
}

static void vInitIO(uint32_t gpio, bool input) {
    gpio_init( gpio );
    gpio_set_dir( gpio, (input) ? GPIO_IN : GPIO_OUT );

    // Commented out due to v4 having the pull up resistors on the board
    if( input == true ){
        gpio_pull_up( gpio );
    }
    #ifdef DEBUG_IO
    printf("[IO] Pin Number: %d initialized as GPIO\r\n", gpio);
    #endif
}

static void vInitADC(uint32_t gpio, uint32_t channel) {
    adc_init();
    adc_gpio_init( gpio );
    adc_select_input( channel );
    #ifdef DEBUG_IO
    printf("[IO] Pin Number: %d initialized as ADC\r\n", gpio);
    #endif
}

static void vDeInitIO(uint32_t gpio) {
    gpio_deinit( gpio );
    gpio_disable_pulls( gpio );
    #ifdef DEBUG_IO
    printf("[IO] Pin Number: %d de-initialized as GPIO\r\n", gpio);
    #endif
}

void vPWMSetup(){
    gpio_set_function( pwm_param.pin, GPIO_FUNC_PWM );
    pwm_param.slice = pwm_gpio_to_slice_num( pwm_param.pin );
    pwm_param.channel = pwm_gpio_to_channel( pwm_param.pin );

    #ifdef DEBUG_PWM_DIMMING_SETUP
    printf( "[PWM] Slice: %d\r\n", pwm_param.slice );
    printf( "[PWM] Channel: %d\r\n", pwm_param.channel );
    #endif

    pwm_param.config = pwm_get_default_config(); // has clkdiv value of 1 for max resolution

    uint16_t wrap_value = (SYS_CLOCK_150MHZ / pwm_param.frequency) - 1;
    pwm_config_set_wrap( &pwm_param.config, wrap_value ); // wrap = 149999 for 1kHz

    pwm_init( pwm_param.slice, &pwm_param.config, false );

    // Continuous mode - reloads the counter automatically
    pwm_set_enabled( pwm_param.slice, true );
}

uint16_t usADCtoPWM( float fValue ){
    return (uint16_t)lround(fValue * ((1 << 12) - 1) / 3.3f);
}

static bool same_two_pin_input_configs( CanboProvisioningCommand_TwoPinInput *active, CanboProvisioningCommand_TwoPinInput *last, size_t len ) {
    return memcmp(active, last, len) == 0;
}

static bool same_three_pin_input_configs( CanboProvisioningCommand_ThreePinInput *active, CanboProvisioningCommand_ThreePinInput *last, size_t len ) {
    return memcmp(active, last, len) == 0;
}

static bool same_adc_input_config( CanboProvisioningCommand_ADCInput *active, CanboProvisioningCommand_ADCInput *last ) {
    if (active->connector_type != last->connector_type) {
        return false;
    }

    // First ensure the same range tag is used
    if (active->which_range != last->which_range) {
        return false;
    }
    
    // Compare range values based on the active tag
    if (active->which_range == CanboProvisioningCommand_ADCInput_min_max_tag) {
        return (active->range.min_max.min == last->range.min_max.min) &&
               (active->range.min_max.max == last->range.min_max.max);
    } else if (active->which_range == CanboProvisioningCommand_ADCInput_offset_scaling_tag) {
        return (active->range.offset_scaling.offset == last->range.offset_scaling.offset) &&
               (active->range.offset_scaling.scaling_factor == last->range.offset_scaling.scaling_factor);
    }
    
    // Unknown tag — force re-init
    return false;
}

// ============================================================================
// PWM DIMMING CONTROL FUNCTIONS
// ============================================================================

void vSetPWMDimVariables(uint32_t target, uint32_t time_ms) {
    vSetPWMDimTime( time_ms );
    vSetPWMDimTarget( target );
}

void vSetPWMDimTarget(uint32_t target) {
    // Clamp target to valid range (0-100)
    if (target > 100) {
        target = 100;
    }

    // Convert 0-100 integer to 0.0-1.0 float for internal precision
    fDimTarget = (float)target / 100.0f;

    #ifdef DEBUG_PWM_DIMMING
    printf("[PWM] Dim target set to: %d (%.4f internal)\r\n", target, fDimTarget);
    #endif
}

void vSetPWMDimTime(uint32_t time_ms) {
    uiDimTimeMs = time_ms;

    #ifdef DEBUG_PWM_DIMMING
    printf("[PWM] Dim time set to: %d ms\r\n", uiDimTimeMs);
    #endif
}

uint32_t uiGetPWMDimTarget(void) {
    // Convert internal 0.0-1.0 float to 0-100 integer
    return (uint32_t)lround(fDimTarget * 100.0f);
}

uint32_t uiGetPWMDimTime(void) {
    return uiDimTimeMs;
}

void vStopPWMDimming(void) {
    // Set the target to the current level, effectively stopping the dimming
    fDimTarget = fCurrentDimLevel;

    #ifdef DEBUG_PWM_DIMMING
    printf("[PWM] Dimming stopped at current level: %d (%.4f internal)\r\n", 
           (uint32_t)lround(fCurrentDimLevel * 100.0f), fCurrentDimLevel);
    #endif
}

uint32_t uiGetCurrentPWMDimLevel(void) {
    // Convert internal 0.0-1.0 float to 0-100 integer
    return (uint32_t)lround(fCurrentDimLevel * 100.0f);
}
