#ifndef __CAN_TASK_H__
#define __CAN_TASK_H__

// C standard libraries
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

// pico sdk libraries
#include "pico/stdlib.h"

#ifdef __cplusplus
extern "C" {
#endif

// pico sdk libraries
#include "FreeRTOS.h"
#include "queue.h"
#include "task.h"

// user libraries
#include "can2040.h"
#include "can_varlen.h"
#include "defs.h"
#include "pinout.h"
#include "types.h"

#include "commands.pb.h"
#include "events.h"
#include <pb_decode.h>
#include <pb_encode.h>

#include "uart_task.h"

// global definitions
extern TaskHandle_t xCAN0RxTaskHandle;
extern TaskHandle_t xCAN0TxTaskHandle;
extern QueueHandle_t xCANRxQueue;
extern QueueHandle_t xCANTxQueue;
extern CanboProvisioningCommand active_configuration;
extern SemaphoreHandle_t active_config_mutex;
extern char global_qr_code[13];
extern SemaphoreHandle_t global_qr_code_mutex;

// local definitions/enums
// >> CAN behaviour definitions
// >> END OF DEFINITIONS

typedef struct {
    uint8_t event_id;
    uint32_t channel;
    struct can2040_msg message;
    uint32_t rx_timestamp; // Timestamp in microseconds
} CANMessage_t;

#define CAN_BAUDRATE_125KBPS 125000UL
// #define CAN_BAUDRATE_500KBPS 500000UL
#define SYS_CLOCK_150MHZ 150000000UL

#define CANRX_QUEUE_LENGTH 30                    // Max number of items in each individual queue
#define CANRX_MSG_ITEM_SIZE sizeof(CANMessage_t) // Size of each item

#define CANTX_QUEUE_LENGTH 30                    // Max number of items in each individual queue
#define CANTX_MSG_ITEM_SIZE sizeof(CANMessage_t) // Size of each item

// function prototyping
static void can_provisioning_rx_cb(struct can2040 *cd, uint32_t node_id, const uint8_t *data, uint32_t len, uint32_t timestamp);
static void can_start_zero_to_ten_dimming_rx_cb(struct can2040 *cd, uint32_t node_id, const uint8_t *data, uint32_t len, uint32_t timestamp);
static void can_stop_zero_to_ten_dimming_rx_cb(struct can2040 *cd, uint32_t node_id, const uint8_t *data, uint32_t len, uint32_t timestamp);
static void can_toggle_relay_rx_cb(struct can2040 *cd, uint32_t node_id, const uint8_t *data, uint32_t len, uint32_t timestamp);

static void can_varlen_rx_cb(struct can2040 *cd, uint32_t node_id, const uint8_t *data, uint32_t len, uint32_t timestamp);
static void can_varlen_tx_cb(struct can2040 *cd, struct can2040_msg *msg);

static void vCANCallback(struct can2040 *cd, uint32_t notify, struct can2040_msg *msg);

static void vCAN_CH0_IRQHandler(void);

void vPingTask(void *pvParameters);
void vCANRxTask(void *pvParameters);
void vCANTxTask(void *pvParameters);

void vCANBusSetup(CANParams_t *pxParams, uint8_t ucChannel);
void vCANBusTx(CANMessage_t *pxOutMessage);

// Safe access functions for active_configuration
bool get_active_configuration(CanboProvisioningCommand *config);
bool set_active_configuration(const CanboProvisioningCommand *config);
bool get_active_config_qr_code(char *qr_code, size_t max_len);
bool set_active_config_qr_code(const char *qr_code);

// Safe access functions for global_qr_code
bool get_global_qr_code(char *qr_code, size_t max_len);
bool set_global_qr_code(const char *qr_code);

// Command sending functions
void send_momentary_button_state(uint32_t connector_id, MomentaryButtonCommand_State state);
void send_toggle_button_state(uint32_t connector_id, ToggleButtonCommand_State state);
void send_motion_detected_state(uint32_t connector_id, MotionDetectedCommand_State state);
void send_door_sensor_state(uint32_t connector_id, DoorSensorCommand_State state);
void send_knob_value(float percent_turn);
void send_thermostat_value(float degree_celcius);
void send_pir_value(bool is_motion_detected);
void send_ping();

static void can_announce_command(uint8_t *data, size_t len, uint8_t node_id);

bool canbo_get_current_unix_time(uint32_t *unix_time);

/**
 * @brief Safely copies a CanboProvisioningCommand structure.
 * This function performs a deep copy and validates the data integrity.
 * @param dest Pointer to destination structure
 * @param src Pointer to source structure
 * @return true on successful copy, false on validation failure
 */
bool copy_provisioning_command(CanboProvisioningCommand *dest, const CanboProvisioningCommand *src);

#ifndef __cplusplus
#include <stdbool.h>
#endif

#ifdef __cplusplus
}
#endif

#endif // __CAN_TASK_H__
