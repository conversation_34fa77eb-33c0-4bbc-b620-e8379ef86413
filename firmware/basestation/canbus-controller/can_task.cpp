#include "can_task.h"
#include "watchdog_manager.h"

#include "flash_storage.h"

TaskHandle_t xCAN0RxTaskHandle;
TaskHandle_t xCAN0TxTaskHandle;
TaskHandle_t xPingTaskHandle;

CANParams_t can_params[NUM_CAN_CHANNELS] = CAN_PARAMS_DEFAULT();
static struct can2040 cbus[NUM_CAN_CHANNELS];

static struct can2040_stats stats;
QueueHandle_t xCANRxQueue;
QueueHandle_t xCANTxQueue;

CanboProvisioningCommand active_configuration = CanboProvisioningCommand_init_zero;
SemaphoreHandle_t active_config_mutex = NULL;

// Time synchronization variables
static uint32_t unix_base_time = 0;   // Unix timestamp from last sync
static uint32_t millis_base = 0;      // millis() value when unix_base_time was set
static uint32_t last_sync_millis = 0; // millis() of last sync for tracking sync age
static bool time_synced = false;      // Whether we have valid time sync
char global_qr_code[13] = "";
SemaphoreHandle_t global_qr_code_mutex = NULL;

static void can_provisioning_rx_cb(struct can2040 *cd, uint32_t node_id, const uint8_t *data, uint32_t len, uint32_t timestamp) {
    CanboProvisioningCommand incoming_configuration = CanboProvisioningCommand_init_zero;

    pb_istream_t stream = pb_istream_from_buffer(data + 1, len - 1);
    bool status = pb_decode(&stream, CanboProvisioningCommand_fields, &incoming_configuration);

    if (!status) {
        printf("[ERROR] pb_decode failed: %s\r\n", PB_GET_ERROR(&stream));
        return;
    }

    // Validate QR code against current global QR snapshot
    char current_qr[sizeof(global_qr_code)] = {0};
    if (!get_global_qr_code(current_qr, sizeof(current_qr))) {
#ifdef DEBUG_PROVISIONING
        printf("[CANRx] Failed to read global QR code for validation\r\n");
#endif
        return;
    }
    if (strcmp(incoming_configuration.qr_code, current_qr) != 0) {
#ifdef DEBUG_PROVISIONING
        printf("[CANRx] QR code doesn't match! Received: %s Global: %s\r\n", incoming_configuration.qr_code, current_qr);
#endif
        return;
    }

    bool flash_success = flash_storage_set_provisioning_command_sync(&incoming_configuration);
    if (!flash_success) {
        printf("> Error: Failed to write debug configuration to flash\r\n");
        return;
    }

    bool provision_success = flash_storage_set_provisioned_sync(true);
    if (!provision_success) {
        printf("> Error: Failed to set provisioned status\r\n");
        return;
    }

    // Handle time sync from provisioning command
    if (incoming_configuration.unix_timestamp > 0) {
        unix_base_time = incoming_configuration.unix_timestamp;
        millis_base = xTaskGetTickCount() * portTICK_PERIOD_MS;
        last_sync_millis = millis_base;
        time_synced = true;
        printf("[TIME_SYNC] Initial sync from provisioning: unix=%u\r\n", unix_base_time);
    }

    // Set the new configuration safely
    if (!set_active_configuration(&incoming_configuration)) {
        printf("[ERROR] Failed to set active configuration\r\n");
        return;
    }

#ifdef DEBUG_PROVISIONING
    printf(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>\r\n");
    printf("[CANRx] New Configuration:\r\n");
    printf("[CANRx] ID: %d\r\n", incoming_configuration.node_id);
    printf("[CANRx] Version: %s\r\n", incoming_configuration.version);
    printf("[CANRx] QR Code: %s\r\n", incoming_configuration.qr_code);

    printf("[CANRx] 3 Pin In Count: %d\r\n", incoming_configuration.three_pin_inputs_count);
    printf("[CANRx] 2 Pin In Count: %d\r\n", incoming_configuration.two_pin_inputs_count);
    printf("[CANRx] ADC Present: %s\r\n", incoming_configuration.has_adc_inputs ? "Yes" : "No");
    if (incoming_configuration.has_adc_inputs) {
        const char *adc_type = "UNKNOWN";
        switch (incoming_configuration.adc_inputs.connector_type) {
        case CanboProvisioningCommand_ADCInput_ConnectorType_KNOB:
            adc_type = "KNOB";
            break;
        case CanboProvisioningCommand_ADCInput_ConnectorType_THERMOSTAT:
            adc_type = "THERMOSTAT";
            break;
        case CanboProvisioningCommand_ADCInput_ConnectorType_PIR:
            adc_type = "PIR";
            break;
        default:
            break;
        }
        printf("[CANRx] ADC Type: %s\r\n", adc_type);
        if (incoming_configuration.adc_inputs.which_range == CanboProvisioningCommand_ADCInput_OffsetScalingFactorRange_offset_tag) {
            printf("[CANRx] ADC Offset=%.2f, Scaling=%.2f\r\n",
                   incoming_configuration.adc_inputs.range.offset_scaling.offset,
                   incoming_configuration.adc_inputs.range.offset_scaling.scaling_factor);
        } else if (incoming_configuration.adc_inputs.which_range == CanboProvisioningCommand_ADCInput_MinMaxRange_min_tag) {
            printf("[CANRx] ADC Min=%.2f, Max=%.2f\r\n",
                   incoming_configuration.adc_inputs.range.min_max.min,
                   incoming_configuration.adc_inputs.range.min_max.max);
        }
    }

    printf("[CANRx] Out Count: %d\r\n", incoming_configuration.outputs_count);
    for (size_t i = 0; i < incoming_configuration.outputs_count; ++i) {
        printf("[CANRx] Out #%u: Conn #%u,%u\r\n", i, incoming_configuration.outputs[i].connector_id, incoming_configuration.outputs[i].connector_type);
    }

    if (incoming_configuration.has_zero_to_ten_volt_config) {
        printf("[CANRx] Type: %s\r\n", incoming_configuration.zero_to_ten_volt_config.type == CanboProvisioningCommand_ZeroToTenVoltConfig_Type_SOURCING ? "Sourcing" : "Sinking");

        printf("[CANRx] Min Brightness: %.2f\r\n", incoming_configuration.zero_to_ten_volt_config.min_brightness);
        printf("[CANRx] Max Brightness: %.2f\r\n", incoming_configuration.zero_to_ten_volt_config.max_brightness);

        printf("[CANRx] Use Relay: %s\r\n", incoming_configuration.zero_to_ten_volt_config.use_relay ? "Yes" : "No");
        printf("[CANRx] Relay Connector ID: %d\r\n", incoming_configuration.zero_to_ten_volt_config.relay_connector_id);
    }
    printf("[CANRx] Kleverness Connector?: %d\r\n", incoming_configuration.has_kleverness_connector);
    printf("<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<\r\n\r\n");
#endif
}

static void can_start_zero_to_ten_dimming_rx_cb(struct can2040 *cd, uint32_t node_id, const uint8_t *data, uint32_t len, uint32_t timestamp) {
    StartDimmingCommand incoming_command = StartDimmingCommand_init_zero;

    pb_istream_t stream = pb_istream_from_buffer(data + 1, len - 1);
    bool status = pb_decode(&stream, StartDimmingCommand_fields, &incoming_command);

    if (!status) {
        printf("[ERROR] pb_decode failed: %s\r\n", PB_GET_ERROR(&stream));
        return;
    }

    if (incoming_command.node_id != active_configuration.node_id) {
        printf("[ERROR] Node ID mismatch: %d != %d\r\n", incoming_command.node_id, active_configuration.node_id);
        return;
    }
    vSetPWMDimVariables(incoming_command.brightness, incoming_command.dim_speed_msec);
}

static void can_stop_zero_to_ten_dimming_rx_cb(struct can2040 *cd, uint32_t node_id, const uint8_t *data, uint32_t len, uint32_t timestamp) {
    StopDimmingCommand incoming_command = StopDimmingCommand_init_zero;

    pb_istream_t stream = pb_istream_from_buffer(data + 1, len - 1);
    bool status = pb_decode(&stream, StopDimmingCommand_fields, &incoming_command);

    if (!status) {
        printf("[ERROR] pb_decode failed: %s\r\n", PB_GET_ERROR(&stream));
        return;
    }

    if (incoming_command.node_id != active_configuration.node_id) {
        printf("[ERROR] Node ID mismatch: %d != %d\r\n", incoming_command.node_id, active_configuration.node_id);
        return;
    }

    vStopPWMDimming();
}

static void can_toggle_relay_rx_cb(struct can2040 *cd, uint32_t node_id, const uint8_t *data, uint32_t len, uint32_t timestamp) {
    ToggleRelayCommand incoming_command = ToggleRelayCommand_init_zero;

    pb_istream_t stream = pb_istream_from_buffer(data + 1, len - 1);
    bool status = pb_decode(&stream, ToggleRelayCommand_fields, &incoming_command);

    if (!status) {
        printf("[ERROR] pb_decode failed for ToggleRelayCommand: %s\r\n", PB_GET_ERROR(&stream));
        return;
    }

    // Validate connector_id (1-4 for the 4 relays)
    if (incoming_command.connector_id < 1 || incoming_command.connector_id > NUM_OUTPUT_2_PIN) {
        printf("[ERROR] Invalid relay connector ID: %d (must be 1-%d)\r\n", 
               incoming_command.connector_id, NUM_OUTPUT_2_PIN);
        return;
    }

    // Get the current configuration to check if this relay is configured
    CanboProvisioningCommand current_config = CanboProvisioningCommand_init_zero;
    if (!get_active_configuration(&current_config)) {
        printf("[ERROR] Failed to get active configuration for relay control\r\n");
        return;
    }

    // Check if this connector is configured as a relay output
    bool relay_configured = false;
    for (size_t i = 0; i < current_config.outputs_count; i++) {
        if (current_config.outputs[i].connector_id == incoming_command.connector_id &&
            current_config.outputs[i].connector_type == CanboProvisioningCommand_Output_ConnectorType_Relay) {
            relay_configured = true;
            break;
        }
    }

    if (!relay_configured) {
        printf("[ERROR] Relay connector %d is not configured as an output\r\n", 
               incoming_command.connector_id);
        return;
    }

    // Control the relay based on the command state
    extern IO2PinParams_t twoOut[];
    uint32_t relay_index = incoming_command.connector_id - 1;
    bool new_state = (incoming_command.state == ToggleRelayCommand_State_On);
    
    // Update the relay state
    twoOut[relay_index].gpio_state = new_state;
    gpio_put(twoOut[relay_index].gpio_pin, new_state);

#ifdef DEBUG_RELAY_CONTROL
    printf("[RELAY] Relay %d set to %s\r\n", 
           incoming_command.connector_id, 
           new_state ? "ON" : "OFF");
#endif
}

static void can_time_sync_response_rx_cb(struct can2040 *cd, uint32_t node_id, const uint8_t *data, uint32_t len, uint32_t timestamp) {
    if (len <= 1) {
        printf("[ERROR] TimeSyncResponse frame too short for payload\r\n");
        return;
    }

    TimeSyncResponse response = TimeSyncResponse_init_zero;

    pb_istream_t stream = pb_istream_from_buffer(data + 1, len - 1);
    bool status = pb_decode(&stream, TimeSyncResponse_fields, &response);

    if (!status) {
        printf("[ERROR] Failed to decode TimeSyncResponse: %s\r\n", PB_GET_ERROR(&stream));
        return;
    }

    // Validate QR code against global QR code (thread-safe read)
    char current_qr[sizeof(global_qr_code)] = {0};
    (void)get_global_qr_code(current_qr, sizeof(current_qr));
    if (strcmp(response.qr_code, current_qr) != 0) {
#ifdef DEBUG_TIME_SYNC
        printf("[TIME_SYNC] QR code doesn't match! Received: %s Global: %s\r\n",
               response.qr_code, current_qr);
#endif
        return;
    }

    // Ignore invalid/unspecified unix times
    if (response.unix_timestamp == 0) {
#ifdef DEBUG_TIME_SYNC
        printf("[TIME_SYNC] Skipping time sync: unix_timestamp == 0\r\n");
#endif
        return;
    }

    // Calculate round-trip time and network latency
    uint32_t current_millis = xTaskGetTickCount() * portTICK_PERIOD_MS;
    uint32_t round_trip_time = current_millis - response.echo_millis;

    // Calculate actual network latency by subtracting processing delay
    uint32_t processing_delay_ms = response.processing_delay_us / 1000;
    uint32_t network_latency_ms = (round_trip_time > processing_delay_ms) ? (round_trip_time - processing_delay_ms) / 2 : round_trip_time / 2; // Fallback if delay weird

#ifdef DEBUG_TIME_SYNC
    printf("[TIME_SYNC] Round-trip time: %ums, processing delay: %ums, network latency: %ums\r\n",
           round_trip_time, processing_delay_ms, network_latency_ms);
#endif

    uint32_t latency_ms = network_latency_ms;

    // Update our time base with latency compensation
    unix_base_time = response.unix_timestamp + (latency_ms / 1000); // Add seconds part
    millis_base = current_millis;
    last_sync_millis = current_millis;
    time_synced = true;

#ifdef DEBUG_TIME_SYNC
    printf("[TIME_SYNC] Synced: unix=%u, RTT=%ums, processing=%ums, network_latency=%ums\r\n",
           unix_base_time, round_trip_time, processing_delay_ms, latency_ms);
#endif
}

static void can_varlen_rx_cb(struct can2040 *cd, uint32_t node_id, const uint8_t *data, uint32_t len, uint32_t timestamp) {
#ifdef DEBUG_CAN_PACKET_RX
    printf("[ CAN ] Rx - ch: %02d | id: %02d | size: %02d | pkt: ", cd->pio_num, node_id, len);
    for (uint32_t i = 0; i < len; ++i) {
        printf("%02X ", data[i]);
    }
    printf("\r\n");
#endif
    if (len < 1) {
        printf("[ERROR] Invalid message length: %d\r\n", len);
        return;
    }

    switch (data[0]) {
    case MessageType::MESSAGE_CANBO_PROVISIONING:
        can_provisioning_rx_cb(cd, node_id, data, len, timestamp);
        break;
    case MessageType::MESSAGE_START_ZERO_TO_TEN_DIMMING:
        can_start_zero_to_ten_dimming_rx_cb(cd, node_id, data, len, timestamp);
        break;
    case MessageType::MESSAGE_STOP_ZERO_TO_TEN_DIMMING:
        can_stop_zero_to_ten_dimming_rx_cb(cd, node_id, data, len, timestamp);
        break;
    case MessageType::MESSAGE_TIME_SYNC_RESPONSE:
        can_time_sync_response_rx_cb(cd, node_id, data, len, timestamp);
        break;
    case MessageType::MESSAGE_TOGGLE_RELAY:
        can_toggle_relay_rx_cb(cd, node_id, data, len, timestamp);
        break;
    }
}

static void can_varlen_tx_cb(struct can2040 *cd, struct can2040_msg *msg) {
    CANMessage_t cb_msg;

    // Clear the buffer to prevent possible malformations
    memset(&cb_msg, 0, sizeof(CANMessage_t));
    // Get the message for future circutit handling
    cb_msg.channel = cd->pio_num;
    // copy to temporal structure
    memcpy(&cb_msg.message, msg, sizeof(struct can2040_msg));

    // Motify via queue
    xQueueSend(xCANTxQueue, &cb_msg, portMAX_DELAY);
}

// ISRs & IRQs
static void vCANCallback(struct can2040 *cd, uint32_t notify, struct can2040_msg *msg) {
    BaseType_t xHigherPriorityTaskWoken = pdFALSE;
    CANMessage_t cb_msg;

    switch (notify) {
    case CAN2040_NOTIFY_RX: {
        UBaseType_t uxSavedInterruptStatus = taskENTER_CRITICAL_FROM_ISR();
        // Clear the buffer to prevent possible malformations
        memset(&cb_msg, 0, sizeof(CANMessage_t));
        cb_msg.channel = cd->pio_num;
        cb_msg.rx_timestamp = time_us_32();
        memcpy(&cb_msg.message, msg, sizeof(struct can2040_msg));

        if (xCANRxQueue != NULL) {
            if (xQueueSendFromISR(xCANRxQueue, &cb_msg, &xHigherPriorityTaskWoken) != pdPASS) {
                // Queue full; drop oldest, then enqueue latest
                CANMessage_t dropped;
                xQueueReceiveFromISR(xCANRxQueue, &dropped, &xHigherPriorityTaskWoken);
                xQueueSendFromISR(xCANRxQueue, &cb_msg, &xHigherPriorityTaskWoken);
            }
        }
        taskEXIT_CRITICAL_FROM_ISR(uxSavedInterruptStatus);
        portYIELD_FROM_ISR(xHigherPriorityTaskWoken);
        break;
    }
    case CAN2040_NOTIFY_TX:
        break;
    case CAN2040_NOTIFY_ERROR:
        break;
    default:
        break;
    }
}

static void vCAN_CH0_IRQHandler(void) {
    can2040_pio_irq_handler(&cbus[CHANNEL_0]);
}

// tasks
void vCANRxTask(void *pvParameters) {
    CANMessage_t xRxMessage;

    printf("[START] CAN Rx Task started\r\n");

    // Register with watchdog (12s timeout to allow idle periods)
    watchdog_register_task("CAN0RxTask", xTaskGetCurrentTaskHandle(), 12000);

    for (;;) {
        // Wake periodically to pet the watchdog even if no traffic
        if (xQueueReceive(xCANRxQueue, &xRxMessage, pdMS_TO_TICKS(250)) == pdPASS) {
            // pass it on to varlen
            can_var_on_can2040_callback(&cbus[xRxMessage.channel], CAN2040_NOTIFY_RX, &xRxMessage.message, xRxMessage.rx_timestamp);
            // // TODO: Insert event queueing function
        }
        watchdog_pet_self();
    }
}

void vCANTxTask(void *pvParameters) {
    CANMessage_t xTxMessage;
    // TODO: review if it's possible to handle with just one task/queue messages
    xCANTxQueue = xQueueCreate(CANTX_QUEUE_LENGTH, CANTX_MSG_ITEM_SIZE);
    xCANRxQueue = xQueueCreate(CANRX_QUEUE_LENGTH, CANRX_MSG_ITEM_SIZE);

    // Declare and set addresses for callbacks
    // Channel 0
    can_params[CHANNEL_0].en = true;
    can_params[CHANNEL_0].sys_clock = SYS_CLOCK_150MHZ;
    can_params[CHANNEL_0].baudrate = CAN_BAUDRATE_125KBPS;
    can_params[CHANNEL_0].irq_cb_func = (void *)&vCAN_CH0_IRQHandler;
    can_params[CHANNEL_0].rx_cb_func = vCANCallback;
    // Start the can bus depending on the number of CAN channels available in device

    xTaskCreate(vCANRxTask, "CAN0RxTask", 4096, NULL, 1, &xCAN0RxTaskHandle);
    xTaskCreate(vPingTask, "PingTask", 1024, NULL, 1, &xPingTaskHandle);
    vTaskCoreAffinitySet(xCAN0RxTaskHandle, 2);
    vTaskCoreAffinitySet(xPingTaskHandle, 1);

    // Register tasks with watchdog - longer timeouts for network tasks that can idle
    watchdog_register_task("CAN0TxTask", xTaskGetCurrentTaskHandle(), 5000);  // 5s for potential queue waits
    watchdog_register_task("CAN0RxTask", xCAN0RxTaskHandle, 12000);          // 12s already appropriate
    watchdog_register_task("PingTask", xPingTaskHandle, 15000);              // 15s already appropriate

    can_var_set_rx_callback(&can_varlen_rx_cb);
    can_var_set_tx_callback(&can_varlen_tx_cb);

    vCANBusSetup(&can_params[CHANNEL_0], CHANNEL_0);

    printf("[START] CAN Tx Task started\r\n");

    for (;;) {
        if (xQueueReceive(xCANTxQueue, &xTxMessage, pdMS_TO_TICKS(250)) == pdPASS) {
            if (can2040_check_transmit(&cbus[xTxMessage.channel])) {
                (void)can2040_transmit(&cbus[xTxMessage.channel], &(xTxMessage.message));
            }
        }
        watchdog_pet_self();
    }
}

// functions
void vCANBusSetup(CANParams_t *pxParams, uint8_t ucChannel) {
    // Setup canbus
    can2040_setup(&cbus[ucChannel], pxParams->pio_num);
    can2040_callback_config(&cbus[ucChannel], pxParams->rx_cb_func);
    // Enable irqs for canbus
    irq_set_exclusive_handler(pxParams->irq_num, (irq_handler_t)pxParams->irq_cb_func);
    // TODO: Check if it's even neccesary to set this one
    // irq_set_priority(PIO0_IRQ_0, 20);
    irq_set_enabled(pxParams->irq_num, true);

    // TODO: Maybe set as a on-demand task so if any data is needed to know it may get logged
    // can2040_get_statistics(&cbus, &stats);

    // Start canbus
    can2040_start(&cbus[ucChannel], pxParams->sys_clock, pxParams->baudrate, pxParams->rx_pin, pxParams->tx_pin);
}

void vPingTask(void *pvParameters) {
    printf("[START] Ping task created\r\n");

    for (;;) {
        printf("[PING] Pinging...\r\n");
        send_ping();
        watchdog_pet_self();
        vTaskDelay(pdMS_TO_TICKS(1 * 10000)); // 10s per ping
        watchdog_pet_self();
    }
}

// General command sending function
static void send_command(MessageType message_type, const void *command, const pb_msgdesc_t *fields) {
    uint8_t data[64] = {0}; // Increased buffer size for larger commands
    data[0] = static_cast<uint8_t>(message_type);

    pb_ostream_t stream = pb_ostream_from_buffer(data + 1, sizeof(data) - 1);
    bool status = pb_encode(&stream, fields, command);

    if (status) {
        // Direct read - no semaphore needed
        can_announce_command(data, stream.bytes_written + 1, active_configuration.node_id);
    } else {
        printf("[ERROR] Failed to encode command type %d\n", static_cast<int>(message_type));
    }
}

// Specific command functions
void send_momentary_button_state(uint32_t connector_id, MomentaryButtonCommand_State state) {
    MomentaryButtonCommand command = MomentaryButtonCommand_init_zero;
    command.connector_id = connector_id;
    command.state = state;
    uint32_t timestamp = 0;
    canbo_get_current_unix_time(&timestamp);
    command.timestamp = timestamp;

    send_command(MessageType::MESSAGE_CANBO_MOMENTARY_BUTTON, &command, MomentaryButtonCommand_fields);
}

void send_toggle_button_state(uint32_t connector_id, ToggleButtonCommand_State state) {
    ToggleButtonCommand command = ToggleButtonCommand_init_zero;
    command.connector_id = connector_id;
    command.state = state;
    uint32_t timestamp = 0;
    canbo_get_current_unix_time(&timestamp);
    command.timestamp = timestamp;

    send_command(MessageType::MESSAGE_CANBO_TOGGLE_BUTTON, &command, ToggleButtonCommand_fields);
}

void send_motion_detected_state(uint32_t connector_id, MotionDetectedCommand_State state) {
    MotionDetectedCommand command = MotionDetectedCommand_init_zero;
    command.connector_id = connector_id;
    command.state = state;
    uint32_t timestamp = 0;
    canbo_get_current_unix_time(&timestamp);
    command.timestamp = timestamp;

    send_command(MessageType::MESSAGE_MOTION_DETECTED, &command, MotionDetectedCommand_fields);
}

void send_door_sensor_state(uint32_t connector_id, DoorSensorCommand_State state) {
    DoorSensorCommand command = DoorSensorCommand_init_zero;
    command.connector_id = connector_id;
    command.state = state;
    uint32_t timestamp = 0;
    canbo_get_current_unix_time(&timestamp);
    command.timestamp = timestamp;

    send_command(MessageType::MESSAGE_DOOR_SENSOR, &command, DoorSensorCommand_fields);
}

void send_knob_value(float percent_turn) {
    KnobCommand command = KnobCommand_init_zero;
    command.percent_turn = percent_turn;
    uint32_t timestamp = 0;
    canbo_get_current_unix_time(&timestamp);
    command.timestamp = timestamp;

    send_command(MessageType::MESSAGE_KNOB_VALUE, &command, KnobCommand_fields);
}

void send_thermostat_value(float degree_celcius) {
    ThermostatCommand command = ThermostatCommand_init_zero;
    command.degree_celcius = degree_celcius;
    uint32_t timestamp = 0;
    canbo_get_current_unix_time(&timestamp);
    command.timestamp = timestamp;

    send_command(MessageType::MESSAGE_THERMOSTAT_VALUE, &command, ThermostatCommand_fields);
}

void send_pir_value(bool is_motion_detected) {
    PIRCommand command = PIRCommand_init_zero;
    command.is_motion_detected = is_motion_detected;
    uint32_t timestamp = 0;
    canbo_get_current_unix_time(&timestamp);
    command.timestamp = timestamp;

#ifdef DEBUG_PIR_SEND
    printf("[CAN] Sending PIR motion: %s\r\n", is_motion_detected ? "DETECTED" : "NONE");
#endif

    send_command(MessageType::MESSAGE_PIR_VALUE, &command, PIRCommand_fields);
}

void send_ping() {
    PingCommand command = PingCommand_init_zero;

    // Direct reads - no semaphore needed
    strncpy(command.qr_code, global_qr_code, sizeof(command.qr_code) - 1);
    command.qr_code[sizeof(command.qr_code) - 1] = '\0';
    command.is_provisioned = flash_storage_is_provisioned();
    strncpy(command.version, active_configuration.version, sizeof(command.version) - 1);
    command.version[sizeof(command.version) - 1] = '\0';

    // Add milliseconds since boot for time sync
    command.millis_since_boot = xTaskGetTickCount() * portTICK_PERIOD_MS;
    uint32_t timestamp = 0;
    canbo_get_current_unix_time(&timestamp);
    command.timestamp = timestamp;

    send_command(MessageType::MESSAGE_CANBO_PING, &command, PingCommand_fields);
}

static void can_announce_command(uint8_t *data, size_t len, uint8_t node_id) {
#if defined(DEBUG_CAN_PACKET_TX)
    printf("[ CAN ] Tx - size: %02d | pkt: ", len);
    for (uint32_t i = 0; i < len; ++i) {
        printf("%02X ", data[i]);
    }
    printf("\r\n");
#endif
    can_var_send(&cbus[0], node_id, data, len);
}

bool copy_provisioning_command(CanboProvisioningCommand *dest, const CanboProvisioningCommand *src) {
    if (!dest || !src) {
        return false;
    }

    // Validate count fields to prevent buffer overflows
    if (src->three_pin_inputs_count > 4) {
        printf("[ERROR] Invalid three_pin_inputs_count: %d (max 4)\n", src->three_pin_inputs_count);
        return false;
    }
    if (src->two_pin_inputs_count > 2) {
        printf("[ERROR] Invalid two_pin_inputs_count: %d (max 2)\n", src->two_pin_inputs_count);
        return false;
    }
    if (src->outputs_count > 4) {
        printf("[ERROR] Invalid outputs_count: %d (max 4)\n", src->outputs_count);
        return false;
    }

    // Validate string lengths
    if (strlen(src->version) >= sizeof(dest->version)) {
        printf("[ERROR] Version string too long: %s\n", src->version);
        return false;
    }
    if (strlen(src->qr_code) >= sizeof(dest->qr_code)) {
        printf("[ERROR] QR code string too long: %s\n", src->qr_code);
        return false;
    }

    // Clear destination first to ensure clean state
    memset(dest, 0, sizeof(CanboProvisioningCommand));

    // Copy basic fields
    dest->node_id = src->node_id;
    strncpy(dest->version, src->version, sizeof(dest->version) - 1);
    dest->version[sizeof(dest->version) - 1] = '\0';
    strncpy(dest->qr_code, src->qr_code, sizeof(dest->qr_code) - 1);
    dest->qr_code[sizeof(dest->qr_code) - 1] = '\0';

    // Copy count fields
    dest->three_pin_inputs_count = src->three_pin_inputs_count;
    dest->two_pin_inputs_count = src->two_pin_inputs_count;
    dest->outputs_count = src->outputs_count;

    // Copy arrays - only copy the actual count, not the full array
    for (pb_size_t i = 0; i < src->three_pin_inputs_count; i++) {
        dest->three_pin_inputs[i] = src->three_pin_inputs[i];
    }

    for (pb_size_t i = 0; i < src->two_pin_inputs_count; i++) {
        dest->two_pin_inputs[i] = src->two_pin_inputs[i];
    }

    for (pb_size_t i = 0; i < src->outputs_count; i++) {
        dest->outputs[i] = src->outputs[i];
    }

    // Copy optional fields
    dest->zero_to_ten_volt_config.type = src->zero_to_ten_volt_config.type;
    dest->has_zero_to_ten_volt_config = src->has_zero_to_ten_volt_config;
    if (src->has_zero_to_ten_volt_config) {
        dest->zero_to_ten_volt_config = src->zero_to_ten_volt_config;
    }
    dest->has_adc_inputs = src->has_adc_inputs;
    if (src->has_adc_inputs) {
        dest->adc_inputs = src->adc_inputs;
    }
    dest->has_kleverness_connector = src->has_kleverness_connector;
    if (src->has_kleverness_connector) {
        dest->kleverness_connector = src->kleverness_connector;
    }

    return true;
}

// Helper functions to safely access active_configuration
static bool acquire_active_config(void) {
    if (active_config_mutex == NULL) {
        return false;
    }
    return (xSemaphoreTake(active_config_mutex, pdMS_TO_TICKS(1000)) == pdTRUE);
}

static void release_active_config(void) {
    if (active_config_mutex != NULL) {
        xSemaphoreGive(active_config_mutex);
    }
}

// Safe getter for active_configuration - no semaphore needed for reads
bool get_active_configuration(CanboProvisioningCommand *config) {
    if (config == NULL) {
        return false;
    }

    // Direct read - no semaphore needed
    memcpy(config, &active_configuration, sizeof(CanboProvisioningCommand));
    return true;
}

// Safe setter for active_configuration - semaphore protection for writes
bool set_active_configuration(const CanboProvisioningCommand *config) {
    if (config == NULL) {
        return false;
    }

    if (!acquire_active_config()) {
        return false;
    }

    copy_provisioning_command(&active_configuration, config);
    release_active_config();
    return true;
}

// Safe access to specific fields - no semaphore needed for reads
bool get_active_config_qr_code(char *qr_code, size_t max_len) {
    if (qr_code == NULL || max_len == 0) {
        return false;
    }

    // Direct read - no semaphore needed
    strncpy(qr_code, active_configuration.qr_code, max_len - 1);
    qr_code[max_len - 1] = '\0';
    return true;
}

bool set_active_config_qr_code(const char *qr_code) {
    if (qr_code == NULL) {
        return false;
    }

    if (!acquire_active_config()) {
        return false;
    }

    strncpy(active_configuration.qr_code, qr_code, sizeof(active_configuration.qr_code) - 1);
    active_configuration.qr_code[sizeof(active_configuration.qr_code) - 1] = '\0';
    release_active_config();
    return true;
}

// Time synchronization functions
bool canbo_get_current_unix_time(uint32_t *unix_time) {
    if (!unix_time || !time_synced) {
        return false;
    }

    uint32_t current_millis = xTaskGetTickCount() * portTICK_PERIOD_MS;
    uint32_t elapsed_seconds = (current_millis - millis_base) / 1000;
    *unix_time = unix_base_time + elapsed_seconds;

    return true;
}

bool canbo_is_time_synced(void) {
    return time_synced;
}

uint32_t canbo_get_millis_since_sync(void) {
    if (!time_synced) {
        return UINT32_MAX; // Return max value to indicate no sync
    }

    uint32_t current_millis = xTaskGetTickCount() * portTICK_PERIOD_MS;
    return current_millis - last_sync_millis;
}

// Helper functions to safely access global_qr_code
static bool acquire_global_qr_code(void) {
    if (global_qr_code_mutex == NULL) {
        return false;
    }
    return (xSemaphoreTake(global_qr_code_mutex, pdMS_TO_TICKS(1000)) == pdTRUE);
}

static void release_global_qr_code(void) {
    if (global_qr_code_mutex != NULL) {
        xSemaphoreGive(global_qr_code_mutex);
    }
}

// Safe getter for global_qr_code - requires synchronization for consistent reads
bool get_global_qr_code(char *qr_code, size_t max_len) {
    if (qr_code == NULL || max_len == 0) {
        return false;
    }

    // Acquire mutex to ensure consistent read
    if (!acquire_global_qr_code()) {
        // Failed to acquire mutex, return empty string as safe fallback
        qr_code[0] = '\0';
        return false;
    }

    // Perform the copy while holding the mutex
    strncpy(qr_code, global_qr_code, max_len - 1);
    qr_code[max_len - 1] = '\0';

    // Release mutex immediately after copy
    release_global_qr_code();

    return true;
}

// Safe setter for global_qr_code - semaphore protection for writes
bool set_global_qr_code(const char *qr_code) {
    if (qr_code == NULL) {
        return false;
    }

    if (!acquire_global_qr_code()) {
        return false;
    }

    strncpy(global_qr_code, qr_code, sizeof(global_qr_code) - 1);
    global_qr_code[sizeof(global_qr_code) - 1] = '\0';
    release_global_qr_code();
    return true;
}
