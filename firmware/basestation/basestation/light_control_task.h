#ifndef LIGHT_CONTROL_TASK_H
#define LIGHT_CONTROL_TASK_H

#include "FreeRTOS.h"
extern "C"
{
// C standard libraries
#include <stdio.h>

// pico sdk libraries
#include "FreeRTOS.h"
#include "task.h"
#include "queue.h"

// protobuf libraries
#include "active_configuration.h"
#include "message_handler.h"

extern TaskHandle_t xLightControlTaskHandle;

void vLightControlTask(void *pvParameters);
void send_rf_light_command(LightState *pLightState, uint16_t filter_node_id);
void send_can_light_command(LightState *pLightState, uint16_t filter_node_id);
void send_rf_dimmer_stop_command(LightState *pLightState);
void send_can_stop_command(LightState *pLightState);
void sync_dimmer_state_for_node_id(uint8_t node_id);
void sync_rf_lights_with_state(LightState *pLightState, bool previous_transitioning_states[10], LightConfig *pLightConfig);
}
#endif // LIGHT_CONTROL_TASK_H