#include "light_control_task.h"
#include "active_configuration.h"
#include "can_task.h"
#include "commands.pb.h"
#include "defs.h"
#include "events.h"
#include "rf_task.h"
#include "message_handler.h"

TaskHandle_t xLightControlTaskHandle;

/**********************************************************************************************
 * DMX Simulation Test
 **********************************************************************************************/
void simulate_dmx_test() {
    static bool dimming_up = true;
    static uint32_t last_dim_time = 0;
    uint32_t current_time = xTaskGetTickCount();

    // Check if 5 seconds have passed
    if (current_time - last_dim_time >= pdMS_TO_TICKS(5000)) {
        if (g_active_configuration.state.lights_count > 0) {
            LightState *light = &g_active_configuration.state.lights[0];

            // Toggle between dimming up and down
            if (dimming_up) {
                light->target_value = 100.0f;
            } else {
                light->target_value = 0.0f;
            }
            light->dim_speed_msec = 1000; // 1 second dim duration
            light->last_modified_time = get_time_in_ms();

            dimming_up = !dimming_up;

#ifdef DEBUG_LIGHT_CONTROL
            printf("Dimming light %d to %f\r\n", light->id, light->target_value);
#endif
        }
        last_dim_time = current_time;
    }
}

void handle_light_activation(LightState *pLightState, uint32_t now) {
    if (pLightState == nullptr) {
#ifdef DEBUG_LIGHT_CONTROL_DETAILED
        printf("ERROR: NULL light state pointer\r\n");
#endif
        return;
    }
    static bool previous_transitioning_states[10] = {false}; // Track previous state for each light

    if (pLightState->active_after_time > now) {
        // not active yet
        return;
    }

    // Check if transition is starting (is_transitioning just became true)
    if (pLightState->is_transitioning && !previous_transitioning_states[pLightState->id]) {
#ifdef DEBUG_LIGHT_CONTROL_DETAILED
        printf("Light %d starting dimming transition: %.2f -> %.2f over %d ms\n",
               pLightState->id, pLightState->brightness, pLightState->target_value, pLightState->dim_speed_msec);
#endif
        flash_save_active_config(); // Save active config before starting transition

        LightConfig *pLightConfig = get_light_config_from_id(pLightState->id);
        if (pLightConfig == nullptr) {
// not found
#ifdef DEBUG_LIGHT_CONTROL_DETAILED
            printf("ERROR: Light config not found for light ID %d\r\n", pLightState->id);
#endif
            previous_transitioning_states[pLightState->id] = pLightState->is_transitioning;
            return;
        }

        // Validate fixtures count
        if (pLightConfig->fixtures_count == 0) {
#ifdef DEBUG_LIGHT_CONTROL_DETAILED
            printf("WARNING: Light %d has no fixtures configured\r\n", pLightState->id);
#endif
            previous_transitioning_states[pLightState->id] = pLightState->is_transitioning;
            return;
        }

        send_rf_light_command(pLightState, 0);              // 0 means no filter, send to all RF fixtures
        send_can_light_command(pLightState, 0); // 0 means no filter, send to all CAN fixtures
    }

    // Check if transition is completing (is_transitioning just became false)
    if (!pLightState->is_transitioning && previous_transitioning_states[pLightState->id]) {
#ifdef DEBUG_LIGHT_CONTROL_DETAILED
        printf("Light %d transition completed - brightness: %.2f\n", pLightState->id, pLightState->brightness);
#endif
        flash_save_active_config(); // Save active config before starting transition
        // TODO: we probably just want to clean up the light state and send the desired light state again to all lights

        // if (pLightState->last_transition_stop_reason == LightState_TransitionStopReason_TRANSITION_STOP_HOLD_RELEASE) {
        // send_rf_dimmer_stop_command(pLightState);
        // send_can_stop_command(pLightState);
        //     pLightState->last_transition_stop_reason = LightState_TransitionStopReason_TRANSITION_STOP_UNKNOWN;
        // } else {
        send_rf_light_command(pLightState, 0);              // 0 means no filter, send to all RF fixtures
        send_can_light_command(pLightState, 0); // 0 means no filter, send to all CAN fixtures
        // }
    }

    // Update previous state
    previous_transitioning_states[pLightState->id] = pLightState->is_transitioning;
}

void send_can_stop_command(LightState *pLightState) {
    LightConfig *pLightConfig = get_light_config_from_id(pLightState->id);
    if (pLightConfig == nullptr) {
#ifdef DEBUG_LIGHT_CONTROL_DETAILED
        printf("ERROR: Light config not found for light ID %d\r\n", pLightState->id);
#endif
        return;
    }
    for (uint8_t i = 0; i < pLightConfig->fixtures_count; i++) {
        LightConfig_FixtureConfig *pFixture = &pLightConfig->fixtures[i];
        if (pFixture->which_config == LightConfig_FixtureConfig_zero_to_ten_volt_tag) {
#ifdef DEBUG_LIGHT_CONTROL_DETAILED
            printf("Can Fixture %d: Node ID: %d\n", i, pFixture->config.zero_to_ten_volt.node_id);
#endif

            // check if the Canbo device is provisioned
            ProvisioningState *pProvisioningState = get_provisioned_device_state_from_node_id(pFixture->config.zero_to_ten_volt.node_id);
            if (!pProvisioningState || pProvisioningState->is_provisioned == false) {
#ifdef DEBUG_LIGHT_CONTROL_DETAILED
                printf("ERROR: Canbo device with Node ID %d is not provisioned\r\n", pFixture->config.zero_to_ten_volt.node_id);
#endif
                continue;
            }

            // get canbo config
            CanboConfig *pCanboConfig = get_canbo_config_from_node_id(pFixture->config.zero_to_ten_volt.node_id);
            if (pCanboConfig == nullptr) {
#ifdef DEBUG_LIGHT_CONTROL_DETAILED
                printf("ERROR: Canbo config not found for Node ID %d\r\n", pFixture->config.zero_to_ten_volt.node_id);
#endif
                continue;
            }

            StopDimmingCommand incoming_command = StopDimmingCommand_init_zero;
            incoming_command.node_id = pFixture->config.zero_to_ten_volt.node_id;

            uint8_t pcOutMessage[StopDimmingCommand_size + 1];
            pcOutMessage[0] = MessageType::MESSAGE_STOP_ZERO_TO_TEN_DIMMING;
            pb_ostream_t stream = pb_ostream_from_buffer(pcOutMessage + 1, sizeof(pcOutMessage) - 1);
            bool status = pb_encode(&stream, StartDimmingCommand_fields, &incoming_command);
            if (!status) {
#ifdef DEBUG_LIGHT_CONTROL_DETAILED
                printf("ERROR: Failed to encode StopDimmingCommand for Canbo Node ID %d\r\n", pFixture->config.zero_to_ten_volt.node_id);
#endif
                continue;
            }
            send_can_message(pcOutMessage, stream.bytes_written + 1);
        }
    }
}

void send_rf_dimmer_stop_command(LightState *pLightState) {
    LightConfig *pLightConfig = get_light_config_from_id(pLightState->id);
    if (pLightConfig == nullptr) {
#ifdef DEBUG_LIGHT_CONTROL_DETAILED
        printf("ERROR: Light config not found for light ID %d\r\n", pLightState->id);
#endif
        return;
    }
    for (uint8_t i = 0; i < pLightConfig->fixtures_count; i++) {
        LightConfig_FixtureConfig *pFixture = &pLightConfig->fixtures[i];
        if (pFixture->which_config == LightConfig_FixtureConfig_rf_tag && pFixture->config.rf.type == LightConfig_FixtureConfig_RFConfig_Type_DIMMER) {
#ifdef DEBUG_LIGHT_CONTROL_DETAILED
            printf("RF Fixture %d: Node ID: %d\n", i, pFixture->config.rf.node_id);
#endif

            // check if the RF device is provisioned
            ProvisioningState *pProvisioningState = get_provisioned_device_state_from_node_id(pFixture->config.rf.node_id);
            if (!pProvisioningState || pProvisioningState->is_provisioned == false) {
#ifdef DEBUG_LIGHT_CONTROL_DETAILED
                printf("ERROR: RF device with Node ID %d is not provisioned\r\n", pFixture->config.rf.node_id);
#endif
                continue;
            }

            // send the RF command to stop dimming
            uint8_t pcRF4463TxBuffer[RF4463_MAX_BUFFER_LENGTH];
            uint8_t ucMsgLength = vRFPrepareKlevernessMessage(nullptr, 0, TABLE_RF_DIMMER, RF_DIMMER_STOP_DIMMING, pFixture->config.rf.node_id, pcRF4463TxBuffer);
            vRfTxMessage(pcRF4463TxBuffer, RF_MESSAGE_HEADER_SIZE + ucMsgLength);
        }
    }
}

void send_can_light_command(LightState *pLightState, uint16_t filter_node_id) {
    LightConfig *pLightConfig = get_light_config_from_id(pLightState->id);
    if (pLightConfig == nullptr) {
#ifdef DEBUG_LIGHT_CONTROL_DETAILED
        printf("ERROR: Light config not found for light ID %d\r\n", pLightState->id);
#endif
        return;
    }
    for (uint8_t i = 0; i < pLightConfig->fixtures_count; i++) {
        LightConfig_FixtureConfig *pFixture = &pLightConfig->fixtures[i];
        if (pFixture->which_config == LightConfig_FixtureConfig_zero_to_ten_volt_tag) {
            // check if the Canbo device is provisioned
            ProvisioningState *pProvisioningState = get_provisioned_device_state_from_node_id(pFixture->config.zero_to_ten_volt.node_id);
            if (!pProvisioningState || pProvisioningState->is_provisioned == false) {
#ifdef DEBUG_LIGHT_CONTROL_DETAILED
                printf("ERROR: Canbo device with Node ID %d is not provisioned\r\n", pFixture->config.zero_to_ten_volt.node_id);
#endif
                continue;
            }

            // get canbo config
            CanboConfig *pCanboConfig = get_canbo_config_from_node_id(pFixture->config.zero_to_ten_volt.node_id);
            if (pCanboConfig == nullptr) {
#ifdef DEBUG_LIGHT_CONTROL_DETAILED
                printf("ERROR: Canbo config not found for Node ID %d\r\n", pFixture->config.zero_to_ten_volt.node_id);
#endif
                continue;
            }

            StartDimmingCommand incoming_command = StartDimmingCommand_init_zero;
            incoming_command.node_id = pFixture->config.zero_to_ten_volt.node_id;
            incoming_command.dim_speed_msec = pLightState->dim_speed_msec;
            incoming_command.brightness = (uint8_t)pLightState->target_value;

            uint8_t pcOutMessage[StartDimmingCommand_size + 1];
            pcOutMessage[0] = MessageType::MESSAGE_START_ZERO_TO_TEN_DIMMING;
            pb_ostream_t stream = pb_ostream_from_buffer(pcOutMessage + 1, sizeof(pcOutMessage) - 1);
            bool status = pb_encode(&stream, StartDimmingCommand_fields, &incoming_command);
            if (!status) {
#ifdef DEBUG_LIGHT_CONTROL_DETAILED
                printf("ERROR: Failed to encode StartDimmingCommand for Canbo Node ID %d\r\n", pFixture->config.zero_to_ten_volt.node_id);
#endif
                continue;
            }
            send_can_message(pcOutMessage, stream.bytes_written + 1);
        } else if (pFixture->which_config == LightConfig_FixtureConfig_relay_tag) {
            // Handle RELAY fixtures with relay control
            if (filter_node_id != 0 && pFixture->config.relay.node_id != filter_node_id) {
                continue; // Skip if node ID does not match the filter
            }
            
            // Check if the Canbo device is provisioned
            ProvisioningState *pProvisioningState = get_provisioned_device_state_from_node_id(pFixture->config.relay.node_id);
            if (!pProvisioningState || pProvisioningState->is_provisioned == false) {
#ifdef DEBUG_LIGHT_CONTROL_DETAILED
                printf("ERROR: Canbo device with Node ID %d is not provisioned\r\n", pFixture->config.relay.node_id);
#endif
                continue;
            }

            // Get canbo config to verify it exists
            CanboConfig *pCanboConfig = get_canbo_config_from_node_id(pFixture->config.relay.node_id);
            if (pCanboConfig == nullptr) {
#ifdef DEBUG_LIGHT_CONTROL_DETAILED
                printf("ERROR: Canbo config not found for Node ID %d\r\n", pFixture->config.relay.node_id);
#endif
                continue;
            }

            // Create ToggleRelayCommand
            ToggleRelayCommand relay_command = ToggleRelayCommand_init_zero;
            relay_command.connector_id = pFixture->config.relay.out_connector_id;
            // Turn relay on if brightness > 0, off otherwise
            relay_command.state = (pLightState->target_value > 0) ? ToggleRelayCommand_State_On : ToggleRelayCommand_State_Off;

#ifdef DEBUG_LIGHT_CONTROL_DETAILED
            printf("Sending relay command to Node ID %d, Connector %d, State: %s\r\n", 
                   pFixture->config.relay.node_id, 
                   relay_command.connector_id,
                   relay_command.state == ToggleRelayCommand_State_On ? "ON" : "OFF");
#endif

            // Encode and send the relay command
            uint8_t pcOutMessage[ToggleRelayCommand_size + 1];
            pcOutMessage[0] = MessageType::MESSAGE_TOGGLE_RELAY;
            pb_ostream_t stream = pb_ostream_from_buffer(pcOutMessage + 1, sizeof(pcOutMessage) - 1);
            bool status = pb_encode(&stream, ToggleRelayCommand_fields, &relay_command);
            if (!status) {
#ifdef DEBUG_LIGHT_CONTROL_DETAILED
                printf("ERROR: Failed to encode ToggleRelayCommand for Canbo Node ID %d\r\n", pFixture->config.relay.node_id);
#endif
                continue;
            }
            send_can_message(pcOutMessage, stream.bytes_written + 1);
        }
    }
}

void send_rf_light_command(LightState *pLightState, uint16_t filter_node_id) {
    LightConfig *pLightConfig = get_light_config_from_id(pLightState->id);
    if (pLightConfig == nullptr) {
#ifdef DEBUG_LIGHT_CONTROL_DETAILED
        printf("ERROR: Light config not found for light ID %d\r\n", pLightState->id);
#endif
        return;
    }
    bool filtered_node_processed = false;
    for (uint8_t i = 0; i < pLightConfig->fixtures_count; i++) {
        LightConfig_FixtureConfig *pFixture = &pLightConfig->fixtures[i];
        if (pFixture->which_config == LightConfig_FixtureConfig_rf_tag) {
            if (filter_node_id != 0 && pFixture->config.rf.node_id != filter_node_id) {
                continue; // Skip if node ID does not match the filter
            }
#ifdef DEBUG_LIGHT_CONTROL_DETAILED
            printf("RF Fixture %d: Node ID: %d\n", i, pFixture->config.rf.node_id);
#endif
            // check if the RF device is provisioned
            ProvisioningState *pProvisioningState = get_provisioned_device_state_from_node_id(pFixture->config.rf.node_id);
            if (!pProvisioningState || pProvisioningState->is_provisioned == false) {
#ifdef DEBUG_LIGHT_CONTROL_DETAILED
                printf("ERROR: RF device with Node ID %d is not provisioned\r\n", pFixture->config.rf.node_id);
#endif
                continue;
            }

            // send the RF command to set brightness
            uint8_t pcRF4463TxBuffer[RF4463_MAX_BUFFER_LENGTH];
            uint8_t ucMsgLength = 0;
            if (pFixture->config.rf.type == LightConfig_FixtureConfig_RFConfig_Type_DIMMER) {
                uint8_t pcBuffer[5] = {(uint8_t)(pLightState->target_value), 0x00, 0x00, 0x00, 0x00};
                // Set last 4 bytes of dimming speed DimmingSpeed (LSB - MSB i.e. 2000ms should be D0 07 00 00)
                pcBuffer[1] = (pLightState->dim_speed_msec & 0xFF);
                pcBuffer[2] = (pLightState->dim_speed_msec >> 8) & 0xFF;
                pcBuffer[3] = (pLightState->dim_speed_msec >> 16) & 0xFF;
                pcBuffer[4] = (pLightState->dim_speed_msec >> 24) & 0xFF;
                uint8_t ucLength = sizeof(pcBuffer);
                ucMsgLength = vRFPrepareKlevernessMessage(pcBuffer, ucLength, TABLE_RF_DIMMER, RF_DIMMER_SET_LEVEL, pFixture->config.rf.node_id, pcRF4463TxBuffer);
            } else if (pFixture->config.rf.type == LightConfig_FixtureConfig_RFConfig_Type_SWITCH) {
                uint8_t pcBuffer[1];
                uint8_t ucLength = 1;
                uint8_t ucCommand = pLightState->target_value > 0 ? RF_SWITCH_ON : RF_SWITCH_OFF;
                ucMsgLength = vRFPrepareKlevernessMessage(pcBuffer, ucLength, TABLE_RF_SWITCH, ucCommand, pFixture->config.rf.node_id, pcRF4463TxBuffer);
            } else {
#ifdef DEBUG_LIGHT_CONTROL_DETAILED
                printf("ERROR: Unsupported RF fixture type for light %d\r\n", pLightState->id);
#endif
                continue;
            }
            vRfTxMessage(pcRF4463TxBuffer, RF_MESSAGE_HEADER_SIZE + ucMsgLength);
            filtered_node_processed;
        }
    }
    if (filtered_node_processed) {
#ifdef DEBUG_LIGHT_CONTROL_DETAILED
        printf("Processed node %d\r\n", filter_node_id);
#endif
    }
}

void sync_dimmer_state_for_node_id(uint8_t node_id) {
    for (uint8_t i = 0; i < g_active_configuration.state.lights_count; i++) {
        LightState *pLightState = &g_active_configuration.state.lights[i];
        send_rf_light_command(pLightState, node_id);
    }
}

void process_all_lights(uint32_t now) {
    for (uint8_t i = 0; i < g_active_configuration.state.lights_count; i++) {
        LightState *pLightState = &g_active_configuration.state.lights[i];
        handle_light_activation(pLightState, now);
    }
}

void vLightControlTask(void *pvParameters) {
#ifdef DEBUG_LIGHT_CONTROL_DETAILED
    printf("Light Control Task\r\n");
#endif

    // wait until rf is initialzied
    if (g_active_configuration.config.has_rf_config) {
        while (!xRfTaskInitialized) {
            vTaskDelay(pdMS_TO_TICKS(100));
        }
#ifdef DEBUG_LIGHT_CONTROL_DETAILED
        printf("RF task initialized, proceeding with light control task.\r\n");
#endif
    }

    // initialize rf lights
    for (uint8_t i = 0; i < g_active_configuration.config.lights_count; i++) {
        LightState *pLightState = &g_active_configuration.state.lights[i];
        if (g_active_configuration.config.has_rf_config) {
            send_rf_light_command(pLightState, 0);              // 0 means no filter, send to all RF fixtures
        }

        send_can_light_command(pLightState, 0); // 0 means no filter, send to all CAN fixtures
    }

    for (;;) {
        uint32_t start_time = time_us_32();
        uint32_t now = get_time_in_ms();

        // Update light states first
        update_light_states(now);

        process_all_lights(now);
        
        // Process PIR state timeouts
        process_pir_states();

        // Calculate precise delay to maintain 40 Hz frequency
        uint32_t delta = time_us_32();
        delta = (delta < start_time) ? (delta + (0xFFFFFFFF - start_time)) : (delta - start_time);
        int32_t ms = pdMS_TO_TICKS(1000 / 40 - (delta / 1000)); // 40 Hz = 25ms period
        ms = (ms < 0) ? 0 : ms;

        vTaskDelay(ms);
    }
}