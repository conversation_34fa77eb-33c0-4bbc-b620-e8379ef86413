#include "tls_hooks.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/semphr.h"

static const char *TAG = "TLS_HOOKS";

// Current registered callback
static tls_hook_callback_t tls_callback = NULL;
static SemaphoreHandle_t callback_mutex = NULL;

// Initialize mutex on first use
static void ensure_mutex_init(void) {
    if (callback_mutex == NULL) {
        callback_mutex = xSemaphoreCreateMutex();
    }
}

esp_err_t tls_hooks_register_callback(tls_hook_callback_t callback) {
    ensure_mutex_init();
    
    if (xSemaphoreTake(callback_mutex, portMAX_DELAY) != pdTRUE) {
        return ESP_FAIL;
    }
    
    tls_callback = callback;
    ESP_LOGI(TAG, "TLS callback registered");
    
    xSemaphoreGive(callback_mutex);
    return ESP_OK;
}

esp_err_t tls_hooks_unregister_callback(void) {
    ensure_mutex_init();
    
    if (xSemaphoreTake(callback_mutex, portMAX_DELAY) != pdTRUE) {
        return ESP_FAIL;
    }
    
    tls_callback = NULL;
    ESP_LOGI(TAG, "TLS callback unregistered");
    
    xSemaphoreGive(callback_mutex);
    return ESP_OK;
}

esp_err_t tls_hooks_trigger(tls_hook_event_t event, void *data) {
    ensure_mutex_init();
    
    esp_err_t ret = ESP_OK;
    
    if (xSemaphoreTake(callback_mutex, pdMS_TO_TICKS(100)) != pdTRUE) {
        // Don't block indefinitely on trigger
        return ESP_OK; // Continue anyway if we can't get mutex
    }
    
    if (tls_callback != NULL) {
        const char *event_name = "UNKNOWN";
        switch (event) {
            case TLS_HOOK_BEFORE_HANDSHAKE:
                event_name = "BEFORE_HANDSHAKE";
                break;
            case TLS_HOOK_AFTER_HANDSHAKE:
                event_name = "AFTER_HANDSHAKE";
                break;
            case TLS_HOOK_HANDSHAKE_FAILED:
                event_name = "HANDSHAKE_FAILED";
                break;
            case TLS_HOOK_CONNECTION_CLOSED:
                event_name = "CONNECTION_CLOSED";
                break;
        }
        
        ESP_LOGD(TAG, "Triggering TLS hook: %s", event_name);
        ret = tls_callback(event, data);
    }
    
    xSemaphoreGive(callback_mutex);
    return ret;
}