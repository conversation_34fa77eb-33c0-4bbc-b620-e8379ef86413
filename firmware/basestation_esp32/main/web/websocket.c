#include "websocket.h"
#include "esp_status_sender.h"
#include "network_manager.h"
#include "protobuf_extractors.h"
#include "global_config.h"
#include "esp_netif.h"
#include "esp_log.h"
#include "bluetooth/ble_coexist.h"
#include "bluetooth/ble_manager.h"
#include "esp_heap_caps.h"
#include "esp_psram.h"
#include "esp_tls.h"

#define ENABLE_DEBUG_WEBSOCKET_RX

static const char *FILE_TAG = FILE_TAG_WEBSOCKET;

TaskHandle_t xWebsocketTaskHandle;

static TimerHandle_t shutdown_signal_timer;
static SemaphoreHandle_t shutdown_sema;

static uint8_t flag_connected = 0;
static uint8_t flag_disconnect = 0;
static char advertise_message_buffer[256] = {0};

WebsocketPacket_t xPacket;
esp_websocket_client_handle_t xWebsocketClient;

#if WS_USE_STREAMING
static WebSocketStreamContext_t stream_context = {0};
#endif

static void build_advertise_message(char* buffer, size_t buffer_size)
{
    // Get QR code from global config
    const char* qr_code = g_esp_config.qr_code[0] != '\0' ? g_esp_config.qr_code : "UNKNOWN";
    
    // Get config version from global config
    const char* version = g_esp_config.config_version[0] != '\0' ? g_esp_config.config_version : "1";
    // TODO: optionally escape qr_code/version or filter to a safe subset before embedding in JSON

    // Get current IP address
    char ip_str[16] = "0.0.0.0";
    esp_netif_t* netif = NULL;
    
    // Check active network interface
    network_interface_t active_interface = network_manager_get_active_interface();
    
    if (active_interface == NETWORK_INTERFACE_ETHERNET) {
        netif = esp_netif_get_handle_from_ifkey("ETH_DEF");
    } else if (active_interface == NETWORK_INTERFACE_WIFI) {
        netif = esp_netif_get_handle_from_ifkey("WIFI_STA_DEF");
    }
    
    if (netif) {
        esp_netif_ip_info_t ip_info;
        if (esp_netif_get_ip_info(netif, &ip_info) == ESP_OK) {
            esp_ip4addr_ntoa(&ip_info.ip, ip_str, sizeof(ip_str));
        }
    }
    
    // Build the JSON message
    snprintf(buffer, buffer_size,
             "{\"action\":\"advertise\",\"qrCode\":\"%s\",\"version\":\"%s\",\"ip\":\"%s\"}",
             qr_code, version, ip_str);
    
    ESP_LOGI(FILE_TAG, "Built advertise message: %s", buffer);
}

static void shutdown_signaler(TimerHandle_t xTimer)
{
    ESP_LOGI(FILE_TAG, "No data received for %d seconds, signaling shutdown", NO_DATA_TIMEOUT_SEC);
    xSemaphoreGive(shutdown_sema);
}

// Helper callback to skip protobuf fields without decoding
__attribute__((unused)) static bool skip_field_callback(pb_istream_t *stream, const pb_field_t *field, void **arg)
{
    return pb_skip_field(stream, field->type);
}

#if WS_USE_STREAMING
// Clean up streaming context
static void websocket_stream_cleanup(void)
{
    if (stream_context.accumulation_buffer) {
        free(stream_context.accumulation_buffer);
        stream_context.accumulation_buffer = NULL;
    }
    stream_context.total_expected_length = 0;
    stream_context.current_length = 0;
    stream_context.allocation_size = 0;
    stream_context.message_in_progress = false;
    stream_context.message_type = 0;
}

// Process complete accumulated message
static void websocket_stream_process_complete_message(void)
{
    ESP_LOGI(FILE_TAG, "Processing complete streamed message: type=0x%02X, size=%lu bytes", 
             stream_context.message_type, (unsigned long)stream_context.current_length);
    
    // Check if message fits in the packet buffer
    if (stream_context.current_length <= WS_BUFFER_SIZE) {
        // Copy to packet buffer and notify task
        xPacket.len = stream_context.current_length;
        memcpy(xPacket.payload, stream_context.accumulation_buffer, stream_context.current_length);
        xTaskNotifyGive(xWebsocketTaskHandle);
    } else {
        // For very large messages that don't fit even after streaming, send directly
        ESP_LOGW(FILE_TAG, "Streamed message too large for packet buffer (%lu bytes), sending directly to UART", 
                 (unsigned long)stream_context.current_length);
        vUARTSendPacket(UART_CH_PICO, stream_context.accumulation_buffer, stream_context.current_length);
    }
    
    // Clean up
    websocket_stream_cleanup();
}

// Handle incoming WebSocket fragment
static bool websocket_stream_handle_fragment(esp_websocket_event_data_t *data)
{
    // First fragment of a new message
    if (data->payload_offset == 0) {
        // Clean up any previous incomplete message
        if (stream_context.message_in_progress) {
            ESP_LOGW(FILE_TAG, "Previous message incomplete, starting new message");
            websocket_stream_cleanup();
        }
        
        // Check message size
        if (data->payload_len > WS_MAX_MESSAGE_SIZE) {
            ESP_LOGE(FILE_TAG, "Message too large: %d bytes (max: %d)", data->payload_len, WS_MAX_MESSAGE_SIZE);
            return false;
        }
        
        // Allocate buffer for complete message
        stream_context.accumulation_buffer = (uint8_t*)malloc(data->payload_len);
        if (!stream_context.accumulation_buffer) {
            ESP_LOGE(FILE_TAG, "Failed to allocate %d bytes for message", data->payload_len);
            return false;
        }
        
        stream_context.total_expected_length = data->payload_len;
        stream_context.allocation_size = data->payload_len;
        stream_context.current_length = 0;
        stream_context.message_in_progress = true;
        stream_context.first_fragment_time = xTaskGetTickCount();
        
        ESP_LOGI(FILE_TAG, "Starting new fragmented message: %d bytes", data->payload_len);
    }
    
    // Check if we're in the middle of receiving a message
    if (!stream_context.message_in_progress) {
        ESP_LOGE(FILE_TAG, "Received fragment without message in progress");
        return false;
    }
    
    // Check timeout
    if ((xTaskGetTickCount() - stream_context.first_fragment_time) > pdMS_TO_TICKS(WS_FRAGMENT_TIMEOUT_MS)) {
        ESP_LOGE(FILE_TAG, "Fragment timeout exceeded");
        websocket_stream_cleanup();
        return false;
    }
    
    // Verify offset matches our current position
    if (data->payload_offset != stream_context.current_length) {
        ESP_LOGE(FILE_TAG, "Fragment offset mismatch: expected %lu, got %d", 
                 (unsigned long)stream_context.current_length, data->payload_offset);
        websocket_stream_cleanup();
        return false;
    }
    
    // Copy fragment data
    if (stream_context.current_length + data->data_len > stream_context.allocation_size) {
        ESP_LOGE(FILE_TAG, "Fragment would exceed buffer size");
        websocket_stream_cleanup();
        return false;
    }
    
    memcpy(stream_context.accumulation_buffer + stream_context.current_length, 
           data->data_ptr, data->data_len);
    stream_context.current_length += data->data_len;
    
    // Store message type from first byte if this is the first fragment
    if (data->payload_offset == 0 && data->data_len > 0) {
        stream_context.message_type = data->data_ptr[0];
    }
    
    ESP_LOGI(FILE_TAG, "Received fragment: %lu/%lu bytes (offset=%d, fin=%d)", 
             (unsigned long)stream_context.current_length, (unsigned long)stream_context.total_expected_length, 
             data->payload_offset, data->fin);
    
    // Check if message is complete
    if (data->fin && stream_context.current_length == stream_context.total_expected_length) {
        websocket_stream_process_complete_message();
        return true;
    }
    
    return true;
}
#endif

static void websocket_event_handler(void *handler_args, esp_event_base_t base, int32_t event_id, void *event_data)
{
    esp_websocket_event_data_t *data = (esp_websocket_event_data_t *)event_data;

    switch (event_id)
    {
    case WEBSOCKET_EVENT_BEGIN:
        ESP_LOGI(FILE_TAG, "WEBSOCKET_EVENT_BEGIN");
        ESP_LOGI(FILE_TAG, "TAG: %d", BasestationConfig_lights_tag);
        
        // Shutdown BLE to free memory for TLS handshake
        if (ble_manager_is_active()) {
            ESP_LOGI(FILE_TAG, "Shutting down BLE to free memory for WebSocket TLS handshake");
            ble_manager_full_shutdown();
        }
        break;
    //
    case WEBSOCKET_EVENT_CONNECTED:
        ESP_LOGI(FILE_TAG, "WEBSOCKET_EVENT_CONNECTED");
        // Log memory status after successful connection
        ESP_LOGI(FILE_TAG, "Memory after TLS handshake:");
        ESP_LOGI(FILE_TAG, "  Internal free: %d KB", heap_caps_get_free_size(MALLOC_CAP_INTERNAL)/1024);
        #ifdef CONFIG_SPIRAM
        if (esp_psram_get_size() > 0) {
            ESP_LOGI(FILE_TAG, "  PSRAM free: %d KB", heap_caps_get_free_size(MALLOC_CAP_SPIRAM)/1024);
        }
        #endif
        // Resume normal BT/WiFi operation after successful connection
        ble_coexist_resume_after_critical_operation();
        flag_connected = 1;
        if (flag_disconnect)
        {
            flag_disconnect = 0;
            ESP_LOGI(FILE_TAG, "Send reconnection message");
            char local_advertise[256];
            build_advertise_message(local_advertise, sizeof(local_advertise));
            // Use a bounded timeout; consider queuing to the websocket task instead of sending in the callback
            esp_websocket_client_send_text(xWebsocketClient, local_advertise, strlen(local_advertise), pdMS_TO_TICKS(5000));
        }

        network_manager_set_status(ESPStatus_Status_WEBSOCKET_CONNECTED);
        esp_status_send(ESPStatus_Status_WEBSOCKET_CONNECTED);
        break;
    //
    case WEBSOCKET_EVENT_DISCONNECTED:
        ESP_LOGW(FILE_TAG, "WEBSOCKET_EVENT_DISCONNECTED");

        // Log disconnection details
        if (data->error_handle.esp_ws_handshake_status_code != 0)
        {
            ESP_LOGW(FILE_TAG, "HTTP status code: %d", data->error_handle.esp_ws_handshake_status_code);
        }
        
        // Restart BLE when WebSocket disconnects
        if (!ble_manager_is_active()) {
            ESP_LOGI(FILE_TAG, "Restarting BLE after WebSocket disconnection");
            // Need to reinitialize after full shutdown
            esp_err_t ble_err = ble_manager_init();
            if (ble_err == ESP_OK) {
                ble_err = ble_manager_start_provisioning();
                if (ble_err != ESP_OK) {
                    ESP_LOGE(FILE_TAG, "Failed to start BLE provisioning: %s", esp_err_to_name(ble_err));
                }
            } else {
                ESP_LOGE(FILE_TAG, "Failed to reinitialize BLE: %s", esp_err_to_name(ble_err));
            }
        }
        
        if (data->error_handle.error_type == WEBSOCKET_ERROR_TYPE_TCP_TRANSPORT)
        {
            if (data->error_handle.esp_tls_last_esp_err != 0)
            {
                ESP_LOGW(FILE_TAG, "TLS error: 0x%04X", -data->error_handle.esp_tls_last_esp_err);
            }
            if (data->error_handle.esp_transport_sock_errno != 0)
            {
                ESP_LOGW(FILE_TAG, "Socket errno: %d", data->error_handle.esp_transport_sock_errno);
            }
        }

#if WS_USE_STREAMING
        // Clean up any in-progress streaming message
        if (stream_context.message_in_progress) {
            ESP_LOGW(FILE_TAG, "Cleaning up incomplete streaming message due to disconnect");
            websocket_stream_cleanup();
        }
#endif

        if (flag_connected)
        {
            flag_connected = 0;
            flag_disconnect = 1;
            ESP_LOGI(FILE_TAG, "Will attempt auto-reconnection...");
        }

        network_manager_set_status(ESPStatus_Status_WEBSOCKET_DISCONNECTED);
        esp_status_send(ESPStatus_Status_WEBSOCKET_DISCONNECTED);
        break;
    //
    case WEBSOCKET_EVENT_DATA:
        ESP_LOGI(FILE_TAG, "WEBSOCKET_EVENT_DATA");
        ESP_LOGI(FILE_TAG, "Received opcode=%d", data->op_code);
        // ESP_LOGW(FILE_TAG, "Total payload length=%d, data_len=%d, current payload offset=%d\r\n", data->payload_len, data->data_len, data->payload_offset);

        if (data->op_code == WS_OPCODE_BINARY)
        { // Opcode 0x2 indicates binary data
            ESP_LOGI(FILE_TAG, "Received binary data");
            ESP_LOGW(FILE_TAG, "Total payload length=%d, data_len=%d, current payload offset=%d, fin=%d\r\n", 
                     data->payload_len, data->data_len, data->payload_offset, data->fin);

#if WS_USE_STREAMING
            // Check if this is a fragmented message (multiple fragments or single large fragment)
            if (data->payload_len > WS_BUFFER_SIZE || data->payload_offset > 0 || !data->fin) {
                // Handle as fragmented/streaming message
                if (!websocket_stream_handle_fragment(data)) {
                    ESP_LOGE(FILE_TAG, "Failed to handle WebSocket fragment");
                }
                // Don't notify task yet - wait for complete message
            } else {
                // Small message that fits in buffer - handle immediately
                if (data->data_len <= WS_BUFFER_SIZE) {
                    xPacket.len = data->data_len;
                    memcpy(xPacket.payload, data->data_ptr, data->data_len);
                    xTaskNotifyGive(xWebsocketTaskHandle);
                } else {
                    ESP_LOGE(FILE_TAG, "Single fragment too large: %d bytes (max: %d)", data->data_len, WS_BUFFER_SIZE);
                }
            }
#else
            // Original fixed buffer implementation
            if (data->data_len <= WS_BUFFER_SIZE) {
                xPacket.len = data->data_len;
                memcpy(xPacket.payload, data->data_ptr, data->data_len);
                xTaskNotifyGive(xWebsocketTaskHandle);
            } else {
                ESP_LOGE(FILE_TAG, "Received fragment too large: %d bytes (max: %d)", data->data_len, WS_BUFFER_SIZE);
            }
#endif
        }
        else if (data->op_code == WS_OPCODE_CLOSE && data->data_len == 2)
        {
            ESP_LOGW(FILE_TAG, "Received closed message with code=%d", 256 * data->data_ptr[0] + data->data_ptr[1]);
        }
        else if (data->op_code == WS_OPCODE_PONG)
        {
            ESP_LOGV(FILE_TAG, "PONG");
        }
        else
        {
            ESP_LOGW(FILE_TAG, "Received=%.*s\n\n", data->data_len, (char *)data->data_ptr);
        }

        xTimerReset(shutdown_signal_timer, portMAX_DELAY);
        break;
    //
    case WEBSOCKET_EVENT_ERROR:
        ESP_LOGE(FILE_TAG, "WEBSOCKET_EVENT_ERROR");

        // Always log error details for debugging
        ESP_LOGE(FILE_TAG, "HTTP status code: %d", data->error_handle.esp_ws_handshake_status_code);
        if (data->error_handle.error_type == WEBSOCKET_ERROR_TYPE_TCP_TRANSPORT)
        {
            ESP_LOGE(FILE_TAG, "Error reported from esp-tls: 0x%04X", -data->error_handle.esp_tls_last_esp_err);
            ESP_LOGE(FILE_TAG, "Error reported from tls stack: 0x%04X", data->error_handle.esp_tls_stack_err);
            ESP_LOGE(FILE_TAG, "Error captured as transport's socket errno: %d", data->error_handle.esp_transport_sock_errno);

            // Log specific error messages
            if (data->error_handle.esp_tls_last_esp_err == -0x004C)
            {
                ESP_LOGE(FILE_TAG, "Connection reset by peer (MBEDTLS_ERR_NET_CONN_RESET)");
            }
            else if (data->error_handle.esp_tls_last_esp_err == -0x004E)
            {
                ESP_LOGE(FILE_TAG, "Connection timeout (MBEDTLS_ERR_NET_RECV_FAILED)");
            }
        }
        
        // Send connection failed status if we haven't connected yet
        if (!flag_connected)
        {
            network_manager_set_status(ESPStatus_Status_WEBSOCKET_CONNECTION_FAILED);
            esp_status_send(ESPStatus_Status_WEBSOCKET_CONNECTION_FAILED);
        }
        break;
    //
    case WEBSOCKET_EVENT_FINISH:
        ESP_LOGI(FILE_TAG, "WEBSOCKET_EVENT_FINISH");
        break;
        //
    }
}

void vWebsocketTask(void *pvParameters)
{
    uint32_t ulWebsocketEvent = 0;
    ESP_LOGI(FILE_TAG, "Create task");
    // Create timer and semaphore to handle disconnection at certain time
    shutdown_signal_timer = xTimerCreate("Websocket shutdown timer", NO_DATA_TIMEOUT_SEC * 1000 / portTICK_PERIOD_MS, pdFALSE, NULL, shutdown_signaler);
    shutdown_sema = xSemaphoreCreateBinary();
    ESP_LOGV(FILE_TAG, "[ %u ] free heap: %ld\n", __LINE__, esp_get_free_heap_size());

    // Setup websocket client configuration
    // esp_websocket_client_config_t websocket_cfg = {
    //     .uri = "wss://somo-server.fly.dev/ws/service", // Prod environment
    //     .task_stack = INTERNAL_BUFFER_MAX_LENGTH + 2048,
    //     .buffer_size = INTERNAL_BUFFER_MAX_LENGTH,
    //     .network_timeout_ms = 20000,
    //     .headers = "User-Agent: basestation\r\n",
    //     // TCP keep-alive settings (different from WebSocket ping/pong)
    //     .keep_alive_enable = true,
    //     .keep_alive_interval = 60, // Send keepalive every 60 seconds
    //     .keep_alive_idle = 120,    // Start keepalive after 120 seconds idle
    //     .keep_alive_count = 3,     // Send 3 keepalive probes before timing out
    //     // WebSocket ping/pong settings
    //     .ping_interval_sec = 30,    // Send WebSocket ping every 30 seconds
    //     .pingpong_timeout_sec = 10, // Timeout for pong response
    //     // Reconnection settings
    //     .reconnect_timeout_ms = 10000,   // Wait 10 seconds before reconnecting
    //     .disable_auto_reconnect = false, // Enable auto-reconnect
    //     // TLS settings
    //     .crt_bundle_attach = esp_crt_bundle_attach, // Use system certificates
    //     .use_global_ca_store = true,
    //     .if_name = NULL,
    // };

    // Get server address from global config, fallback to default if not set
    const char* server_uri = "wss://somo-server.fly.dev/ws/service";
    if (g_esp_config.server_address[0] != '\0') {
        server_uri = g_esp_config.server_address;
        ESP_LOGI(FILE_TAG, "Using configured server address: %s", server_uri);
    } else {
        ESP_LOGI(FILE_TAG, "No server address configured, using default: %s", server_uri);
    }

    // Configure with memory-optimized settings for BT/WiFi coexistence
    // INSECURE MODE: Accept any certificate without validation
    // This is necessary to avoid memory-intensive certificate parsing with BLE active
    
    esp_websocket_client_config_t websocket_cfg = {
        .uri = server_uri,
        .task_stack = 8192,  // 8KB stack should be sufficient
        .buffer_size = 2048, // 2KB buffer for WebSocket client (slightly larger than our 1KB processing buffer)
        .network_timeout_ms = 20000,
        .headers = "User-Agent: somo-basestation\r\n",
        // TCP keep-alive settings (different from WebSocket ping/pong)
        .keep_alive_enable = true,
        .keep_alive_interval = 60, // Send keepalive every 60 seconds
        .keep_alive_idle = 120,    // Start keepalive after 120 seconds idle
        .keep_alive_count = 3,     // Send 3 keepalive probes before timing out
        // WebSocket ping/pong settings
        .ping_interval_sec = 30,    // Send WebSocket ping every 30 seconds
        .pingpong_timeout_sec = 10, // Timeout for pong response
        // Reconnection settings
        .reconnect_timeout_ms = 10000,   // Wait 10 seconds before reconnecting
        .disable_auto_reconnect = false, // Enable auto-reconnect
        // TLS settings - Skip certificate verification
        .crt_bundle_attach = NULL,  // Disable certificate bundle
        .use_global_ca_store = false,  // Disable global CA store
        .skip_cert_common_name_check = true,  // Skip common name verification
        .if_name = NULL,
    };

    ESP_LOGV(FILE_TAG, "[ %u ] free heap: %ld\n", __LINE__, esp_get_free_heap_size());
    // Initialize websocket client
    ESP_LOGI(FILE_TAG, "Connecting to %s...", websocket_cfg.uri);
    
    // Shutdown BLE before starting WebSocket connection to free memory for TLS
    if (ble_manager_is_active()) {
        ESP_LOGI(FILE_TAG, "Shutting down BLE before WebSocket connection attempt");
        ble_manager_full_shutdown();
    }
    
    xWebsocketClient = esp_websocket_client_init(&websocket_cfg);
    if (!xWebsocketClient)
    {
        ESP_LOGE(FILE_TAG, "Failed to initialize websocket client");
        vTaskDelete(NULL);
    }
    esp_websocket_register_events(xWebsocketClient, WEBSOCKET_EVENT_ANY, websocket_event_handler, (void *)xWebsocketClient);
    // Start connection and wait for connections
    // if not connected then exit
    // if connected then send connection message
    ESP_LOGI(FILE_TAG, "Start websocket");
    esp_websocket_client_start(xWebsocketClient);
    xTimerStart(shutdown_signal_timer, portMAX_DELAY);

    // Wait for connection with timeout
    TickType_t start_time = xTaskGetTickCount();
    const TickType_t connection_timeout = pdMS_TO_TICKS(30000); // 30 second timeout
    
    while (!flag_connected)
    {
        if ((xTaskGetTickCount() - start_time) > connection_timeout)
        {
            ESP_LOGE(FILE_TAG, "WebSocket connection timeout after 30 seconds");
            network_manager_set_status(ESPStatus_Status_WEBSOCKET_CONNECTION_FAILED);
            esp_status_send(ESPStatus_Status_WEBSOCKET_CONNECTION_FAILED);
            esp_websocket_client_stop(xWebsocketClient);
            vTaskDelete(NULL);
        }
        vTaskDelay(pdMS_TO_TICKS(100));
    }

    ESP_LOGI(FILE_TAG, "Check if connected");
    ESP_LOGI(FILE_TAG, "WebSocket buffer size: %d bytes (fragmentation will occur for messages > %d bytes)", 
             WS_BUFFER_SIZE, WS_BUFFER_SIZE);
    vTaskDelay(pdMS_TO_TICKS(100));

    memset(xPacket.payload, 0, WS_BUFFER_SIZE);
    xPacket.len = 0;

    ESP_LOGV(FILE_TAG, "[ %u ] free heap: %ld\n", __LINE__, esp_get_free_heap_size());

    if (esp_websocket_client_is_connected(xWebsocketClient))
    {
        ESP_LOGI(FILE_TAG, "Send advertisment message");
        build_advertise_message(advertise_message_buffer, sizeof(advertise_message_buffer));
        esp_websocket_client_send_text(xWebsocketClient, advertise_message_buffer, strlen(advertise_message_buffer), pdMS_TO_TICKS(5000));
    }
    else
    {
        ESP_LOGE(FILE_TAG, "Failed to connect");
        network_manager_set_status(ESPStatus_Status_WEBSOCKET_CONNECTION_FAILED);
        esp_status_send(ESPStatus_Status_WEBSOCKET_CONNECTION_FAILED);
        esp_websocket_client_stop(xWebsocketClient);
        vTaskDelete(NULL);
    }
    // Once connected, start task superloop
    ESP_LOGI(FILE_TAG, "Connected... start packet handler");
    ESP_LOGV(FILE_TAG, "[ %u ] free heap: %ld\n", __LINE__, esp_get_free_heap_size());

    for (;;)
    {
        ulWebsocketEvent = ulTaskNotifyTake(pdTRUE, pdMS_TO_TICKS(5000));

        // Check if we're still connected
        if (!esp_websocket_client_is_connected(xWebsocketClient))
        {
            if (flag_connected)
            {
                ESP_LOGW(FILE_TAG, "Connection lost, waiting for auto-reconnect...");
                flag_connected = 0;
                flag_disconnect = 1;
            }
            continue; // Skip processing and wait for reconnection
        }
        else if (ulWebsocketEvent == 0)
        {
            ESP_LOGI(FILE_TAG, "Send advertisment message");
            build_advertise_message(advertise_message_buffer, sizeof(advertise_message_buffer));
            esp_websocket_client_send_text(xWebsocketClient, advertise_message_buffer, strlen(advertise_message_buffer), pdMS_TO_TICKS(5000));
        }

        if (ulWebsocketEvent > 0)
        {
            uint8_t msg_type = xPacket.payload[0];
            // uint8_t *data = &xPacket.payload[1]; // Unused - commented out
            uint8_t size = xPacket.len - 1;

            // // Extract only WifiConfig from BasestationUpdateMessage
            // if (msg_type == 0x01)
            // { // BasestationUpdateMessage
            //     BasestationConfig_WifiConfig wifi_config = BasestationConfig_WifiConfig_init_zero;

            //     // Use the efficient extraction function
            //     if (extract_wifi_config_from_update_message(data, size, &wifi_config))
            //     {
            //         ESP_LOGI(FILE_TAG, "Successfully extracted WiFi config!");
            //         ESP_LOGI(FILE_TAG, "  SSID: %s", wifi_config.ssid);
            //         ESP_LOGI(FILE_TAG, "  Password: [%zu chars]", strlen(wifi_config.password));

            //         // TODO: Apply the WiFi configuration
            //         // Example: update ESP32 WiFi settings
            //         // wifi_config_t esp_wifi_cfg = {0};
            //         // strncpy((char*)esp_wifi_cfg.sta.ssid, wifi_config.ssid, 32);
            //         // strncpy((char*)esp_wifi_cfg.sta.password, wifi_config.password, 64);
            //         // esp_wifi_set_config(WIFI_IF_STA, &esp_wifi_cfg);
            //         // esp_wifi_disconnect();
            //         // esp_wifi_connect();
            //     }
            //     else
            //     {
            //         ESP_LOGW(FILE_TAG, "No WiFi config found in BasestationUpdateMessage");
            //     }
            // }

            printf("[ UART ] Len: %d, Type: %02X\r\n", size, msg_type);

#ifdef ENABLE_DEBUG_WEBSOCKET_RX
            printf("[ Websocket ] >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>\r\n");
            for (uint32_t ulLengthDebug = 0; ulLengthDebug < xPacket.len; ulLengthDebug++)
            {
                if (ulLengthDebug > 0 && (ulLengthDebug % 32 == 0))
                {
                    printf("\r\n");
                }
                printf("%02X ", xPacket.payload[ulLengthDebug]);
            }
            printf("\r\n");
            printf("<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< Tx pkt end\r\n");
#endif

            vUARTSendPacket(UART_CH_PICO, xPacket.payload, xPacket.len);

            memset(xPacket.payload, 0, WS_BUFFER_SIZE);
            xPacket.len = 0;
        }
    }
}



void vWebsocketSetAdvertiseFields(void) {}

void vWebsocketReconnectWithNewServer(const char* new_server_address)
{
    if (!xWebsocketClient) {
        ESP_LOGE(FILE_TAG, "WebSocket client not initialized");
        return;
    }

    const char* uri = (new_server_address && new_server_address[0])
                      ? new_server_address
                      : DEFAULT_WS_URI;
    ESP_LOGI(FILE_TAG, "Reconnecting WebSocket to new server: %s", uri);

    // Pause the WebSocket task to avoid races while swapping the client
    if (xWebsocketTaskHandle) {
        vTaskSuspend(xWebsocketTaskHandle);
    }

    // Stop current WebSocket connection
    esp_websocket_client_stop(xWebsocketClient);
    vTaskDelay(pdMS_TO_TICKS(500));  // Brief delay to ensure clean disconnect

    // Destroy old client
    esp_websocket_client_destroy(xWebsocketClient);
    xWebsocketClient = NULL;

    // Create new client with new server address - memory-optimized config
    // INSECURE MODE: Accept any certificate without validation
    
    esp_websocket_client_config_t websocket_cfg = {
        .uri = uri,
        .task_stack = 8192,  // 8KB stack should be sufficient
        .buffer_size = 2048, // 2KB buffer for WebSocket client (slightly larger than our 1KB processing buffer)
        .network_timeout_ms = 20000,
        .headers = "User-Agent: somo-basestation\r\n",
        // TCP keep-alive settings (different from WebSocket ping/pong)
        .keep_alive_enable = true,
        .keep_alive_interval = 60, // Send keepalive every 60 seconds
        .keep_alive_idle = 120,    // Start keepalive after 120 seconds idle
        .keep_alive_count = 3,     // Send 3 keepalive probes before timing out
        // WebSocket ping/pong settings
        .ping_interval_sec = 30,    // Send WebSocket ping every 30 seconds
        .pingpong_timeout_sec = 10, // Timeout for pong response
        // Reconnection settings
        .reconnect_timeout_ms = 10000,   // Wait 10 seconds before reconnecting
        .disable_auto_reconnect = false, // Enable auto-reconnect
        // TLS settings - Skip certificate verification
        .crt_bundle_attach = NULL,  // Disable certificate bundle
        .use_global_ca_store = false,  // Disable global CA store
        .skip_cert_common_name_check = true,  // Skip common name verification
        .if_name = NULL,
    };

    xWebsocketClient = esp_websocket_client_init(&websocket_cfg);
    if (!xWebsocketClient) {
        ESP_LOGE(FILE_TAG, "Failed to reinitialize websocket client");
        if (xWebsocketTaskHandle) {
            vTaskResume(xWebsocketTaskHandle);
        }
        return;
    }

    esp_websocket_register_events(
        xWebsocketClient,
        WEBSOCKET_EVENT_ANY,
        websocket_event_handler,
        (void*) xWebsocketClient
    );
    esp_websocket_client_start(xWebsocketClient);

    ESP_LOGI(FILE_TAG, "WebSocket reconnection initiated");

    if (xWebsocketTaskHandle) {
        vTaskResume(xWebsocketTaskHandle);
    }
}
#if WS_USE_STREAMING
// Send large binary data using WebSocket fragmentation
int websocket_send_large_binary(const uint8_t* data, size_t total_length, size_t fragment_size)
{
    if (!xWebsocketClient || !esp_websocket_client_is_connected(xWebsocketClient)) {
        ESP_LOGE(FILE_TAG, "WebSocket not connected");
        return -1;
    }
    
    if (!data || total_length == 0) {
        ESP_LOGE(FILE_TAG, "Invalid data or length");
        return -1;
    }
    
    // Use default fragment size if not specified
    if (fragment_size == 0 || fragment_size > WS_BUFFER_SIZE) {
        fragment_size = WS_BUFFER_SIZE;
    }
    
    ESP_LOGI(FILE_TAG, "Sending large binary message: %zu bytes in %zu byte fragments", 
             total_length, fragment_size);
    
    size_t sent = 0;
    int result;
    
    while (sent < total_length) {
        size_t chunk_size = (total_length - sent > fragment_size) ? fragment_size : (total_length - sent);
        bool is_last = (sent + chunk_size >= total_length);
        
        if (sent == 0) {
            // First fragment - send without FIN flag if not the only fragment
            if (is_last) {
                // Single fragment - send normally with FIN
                result = esp_websocket_client_send_bin(xWebsocketClient, 
                                                      (const char*)(data + sent), 
                                                      chunk_size, 
                                                      portMAX_DELAY);
            } else {
                // First of multiple fragments - send without FIN
                result = esp_websocket_client_send_bin_partial(xWebsocketClient, 
                                                              (const char*)(data + sent), 
                                                              chunk_size, 
                                                              portMAX_DELAY);
            }
        } else if (is_last) {
            // Last fragment - send as continuation with data, then send FIN
            result = esp_websocket_client_send_cont_msg(xWebsocketClient, 
                                                        (const char*)(data + sent), 
                                                        chunk_size, 
                                                        portMAX_DELAY);
            if (result >= 0) {
                // Send the FIN frame to mark end of message
                result = esp_websocket_client_send_fin(xWebsocketClient, portMAX_DELAY);
            }
        } else {
            // Middle fragment - send as continuation
            result = esp_websocket_client_send_cont_msg(xWebsocketClient, 
                                                        (const char*)(data + sent), 
                                                        chunk_size, 
                                                        portMAX_DELAY);
        }
        
        if (result < 0) {
            ESP_LOGE(FILE_TAG, "Failed to send fragment at offset %zu", sent);
            return -1;
        }
        
        sent += chunk_size;
        ESP_LOGD(FILE_TAG, "Sent fragment: %zu/%zu bytes", sent, total_length);
        
        // Small delay between fragments to avoid overwhelming the connection
        if (!is_last) {
            vTaskDelay(pdMS_TO_TICKS(10));
        }
    }
    
    ESP_LOGI(FILE_TAG, "Successfully sent %zu bytes in fragments", total_length);
    return 0;
}
#endif