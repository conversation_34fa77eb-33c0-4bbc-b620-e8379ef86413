#ifndef TLS_HOOKS_H
#define TLS_HOOKS_H

#include "esp_err.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief TLS operation types for callbacks
 */
typedef enum {
    TLS_HOOK_BEFORE_HANDSHAKE,  // About to start TLS handshake
    TLS_HOOK_AFTER_HANDSHAKE,   // TLS handshake completed (success or failure)
    TLS_HOOK_HANDSHAKE_FAILED,  // TLS handshake failed
    TLS_HOOK_CONNECTION_CLOSED  // TLS connection closed
} tls_hook_event_t;

/**
 * @brief TLS hook callback function type
 * 
 * @param event The TLS event that occurred
 * @param data Optional event data (may be NULL)
 * @return ESP_OK to continue, ESP_FAIL to abort operation
 */
typedef esp_err_t (*tls_hook_callback_t)(tls_hook_event_t event, void *data);

/**
 * @brief Register a callback for TLS operations
 * 
 * @param callback The callback function to register
 * @return ESP_OK on success
 */
esp_err_t tls_hooks_register_callback(tls_hook_callback_t callback);

/**
 * @brief Unregister the TLS callback
 * 
 * @return ESP_OK on success
 */
esp_err_t tls_hooks_unregister_callback(void);

/**
 * @brief Trigger a TLS hook event (internal use)
 * 
 * @param event The event to trigger
 * @param data Optional event data
 * @return ESP_OK if callback allows continuation, ESP_FAIL to abort
 */
esp_err_t tls_hooks_trigger(tls_hook_event_t event, void *data);

#ifdef __cplusplus
}
#endif

#endif // TLS_HOOKS_H