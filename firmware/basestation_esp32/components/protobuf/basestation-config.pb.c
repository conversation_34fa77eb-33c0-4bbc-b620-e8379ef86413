/* Automatically generated nanopb constant definitions */
/* Generated by nanopb-0.4.9.1 */

#include "basestation-config.pb.h"
#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

PB_BIND(ActiveConfiguration, ActiveConfiguration, 4)


PB_BIND(BasestationUpdateMessage, BasestationUpdateMessage, 4)


PB_BIND(BasestationConfig, BasestationConfig, 4)


PB_BIND(BasestationConfig_RFConfig, BasestationConfig_RFConfig, AUTO)


PB_BIND(BasestationConfig_WifiConfig, BasestationConfig_WifiConfig, AUTO)


PB_BIND(BasestationConfig_MACConfig, BasestationConfig_MACConfig, AUTO)


PB_BIND(BasestationConfig_DHCPConfig, BasestationConfig_DHCPConfig, AUTO)


PB_BIND(BasestationConfig_NodeQRMapping, BasestationConfig_NodeQRMapping, AUTO)


PB_BIND(Action, Action, AUTO)


PB_BIND(RFDimmerConfig, RFDimmerConfig, AUTO)


PB_BIND(RFReedSensorConfig, RFReedSensorConfig, AUTO)


PB_BIND(RFPresenceSensorConfig, RFPresenceSensorConfig, AUTO)


PB_BIND(CanboConfig, CanboConfig, 2)


PB_BIND(CanboConfig_ThreePinInput, CanboConfig_ThreePinInput, AUTO)


PB_BIND(CanboConfig_ThreePinInput_ToggleConfig, CanboConfig_ThreePinInput_ToggleConfig, AUTO)


PB_BIND(CanboConfig_ThreePinInput_MomentaryConfig, CanboConfig_ThreePinInput_MomentaryConfig, AUTO)


PB_BIND(CanboConfig_TwoPinInput, CanboConfig_TwoPinInput, AUTO)


PB_BIND(CanboConfig_TwoPinInput_MomentaryConfig, CanboConfig_TwoPinInput_MomentaryConfig, AUTO)


PB_BIND(CanboConfig_TwoPinInput_DoorSensorConfig, CanboConfig_TwoPinInput_DoorSensorConfig, AUTO)


PB_BIND(CanboConfig_ADCInput, CanboConfig_ADCInput, AUTO)


PB_BIND(CanboConfig_ADCInput_KnobConfig, CanboConfig_ADCInput_KnobConfig, AUTO)


PB_BIND(CanboConfig_ADCInput_ThermostatConfig, CanboConfig_ADCInput_ThermostatConfig, AUTO)


PB_BIND(CanboConfig_ADCInput_PIRConfig, CanboConfig_ADCInput_PIRConfig, AUTO)


PB_BIND(CanboConfig_Output, CanboConfig_Output, AUTO)


PB_BIND(LightConfig, LightConfig, 2)


PB_BIND(LightConfig_FixtureConfig, LightConfig_FixtureConfig, AUTO)


PB_BIND(LightConfig_FixtureConfig_DMXConfig, LightConfig_FixtureConfig_DMXConfig, AUTO)


PB_BIND(LightConfig_FixtureConfig_DMXConfig_LightParams, LightConfig_FixtureConfig_DMXConfig_LightParams, AUTO)


PB_BIND(LightConfig_FixtureConfig_DMXConfig_RGBConfig, LightConfig_FixtureConfig_DMXConfig_RGBConfig, AUTO)


PB_BIND(LightConfig_FixtureConfig_RFConfig, LightConfig_FixtureConfig_RFConfig, AUTO)


PB_BIND(LightConfig_FixtureConfig_ZeroToTenVoltConfig, LightConfig_FixtureConfig_ZeroToTenVoltConfig, AUTO)


PB_BIND(LightConfig_FixtureConfig_RelayConfig, LightConfig_FixtureConfig_RelayConfig, AUTO)


PB_BIND(SomfyShadesConfig, SomfyShadesConfig, AUTO)


PB_BIND(BasestationState, BasestationState, 2)


PB_BIND(RFReedState, RFReedState, AUTO)


PB_BIND(RFPresenceState, RFPresenceState, AUTO)


PB_BIND(ProvisioningState, ProvisioningState, AUTO)


PB_BIND(LightState, LightState, AUTO)


PB_BIND(ButtonState, ButtonState, AUTO)


PB_BIND(PIRState, PIRState, AUTO)


PB_BIND(ESPStatus, ESPStatus, AUTO)


PB_BIND(ESPUpdateConfig, ESPUpdateConfig, 2)


PB_BIND(ESPUpdateConfig_MACConfig, ESPUpdateConfig_MACConfig, AUTO)


PB_BIND(ESPUpdateConfig_DHCPConfig, ESPUpdateConfig_DHCPConfig, AUTO)


PB_BIND(ESPUpdateConfig_WifiConfig, ESPUpdateConfig_WifiConfig, AUTO)

































