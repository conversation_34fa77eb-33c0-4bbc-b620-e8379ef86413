/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.9.1 */

#ifndef PB_BASESTATION_CONFIG_PB_H_INCLUDED
#define PB_BASESTATION_CONFIG_PB_H_INCLUDED
#include <pb.h>

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Enum definitions */
typedef enum _BasestationConfig_NodeQRMapping_DeviceType {
    BasestationConfig_NodeQRMapping_DeviceType_CAN = 0,
    BasestationConfig_NodeQRMapping_DeviceType_RF = 1
} BasestationConfig_NodeQRMapping_DeviceType;

typedef enum _CanboConfig_ThreePinInput_ConnectorType {
    CanboConfig_ThreePinInput_ConnectorType_TOGGLE = 0,
    CanboConfig_ThreePinInput_ConnectorType_MOMENTARY = 1
} CanboConfig_ThreePinInput_ConnectorType;

typedef enum _CanboConfig_TwoPinInput_ConnectorType {
    CanboConfig_TwoPinInput_ConnectorType_MOMENTARY = 0,
    CanboConfig_TwoPinInput_ConnectorType_DOOR_SENSOR = 1
} CanboConfig_TwoPinInput_ConnectorType;

typedef enum _CanboConfig_ADCInput_ConnectorType {
    CanboConfig_ADCInput_ConnectorType_KNOB = 0,
    CanboConfig_ADCInput_ConnectorType_THERMOSTAT = 1,
    CanboConfig_ADCInput_ConnectorType_PIR = 2
} CanboConfig_ADCInput_ConnectorType;

typedef enum _CanboConfig_Output_ConnectorType {
    CanboConfig_Output_ConnectorType_RELAY = 0
} CanboConfig_Output_ConnectorType;

typedef enum _LightConfig_FixtureConfig_FixtureType {
    LightConfig_FixtureConfig_FixtureType_DMX = 0,
    LightConfig_FixtureConfig_FixtureType_RF = 1,
    LightConfig_FixtureConfig_FixtureType_ZERO_TO_TEN_VOLT = 2,
    LightConfig_FixtureConfig_FixtureType_RELAY = 3
} LightConfig_FixtureConfig_FixtureType;

typedef enum _LightConfig_FixtureConfig_DMXConfig_FixtureTypeConfig {
    LightConfig_FixtureConfig_DMXConfig_FixtureTypeConfig_D4 = 0,
    LightConfig_FixtureConfig_DMXConfig_FixtureTypeConfig_TUNABLE_WHITE = 1,
    LightConfig_FixtureConfig_DMXConfig_FixtureTypeConfig_ELV = 2,
    LightConfig_FixtureConfig_DMXConfig_FixtureTypeConfig_DF_12 = 3,
    LightConfig_FixtureConfig_DMXConfig_FixtureTypeConfig_EST = 4,
    LightConfig_FixtureConfig_DMXConfig_FixtureTypeConfig_RGB_STRIP = 5
} LightConfig_FixtureConfig_DMXConfig_FixtureTypeConfig;

typedef enum _LightConfig_FixtureConfig_RFConfig_Type {
    LightConfig_FixtureConfig_RFConfig_Type_DIMMER = 0,
    LightConfig_FixtureConfig_RFConfig_Type_SWITCH = 1
} LightConfig_FixtureConfig_RFConfig_Type;

typedef enum _LightConfig_FixtureConfig_ZeroToTenVoltConfig_Type {
    LightConfig_FixtureConfig_ZeroToTenVoltConfig_Type_SOURCING = 0,
    LightConfig_FixtureConfig_ZeroToTenVoltConfig_Type_SINKING = 1
} LightConfig_FixtureConfig_ZeroToTenVoltConfig_Type;

typedef enum _RFReedState_Status {
    RFReedState_Status_UNKNOWN = 0,
    RFReedState_Status_OPEN = 1,
    RFReedState_Status_CLOSED = 2
} RFReedState_Status;

typedef enum _RFPresenceState_Status {
    RFPresenceState_Status_UNKNOWN = 0,
    RFPresenceState_Status_ACTIVATED = 1,
    RFPresenceState_Status_DEACTIVATED = 2
} RFPresenceState_Status;

typedef enum _ProvisioningState_ProvisioningErrorCode {
    ProvisioningState_ProvisioningErrorCode_NONE = 0,
    ProvisioningState_ProvisioningErrorCode_NOT_FOUND = 1,
    ProvisioningState_ProvisioningErrorCode_NO_CANBO_CONFIG = 2,
    ProvisioningState_ProvisioningErrorCode_COULD_NOT_SEND_PROVISIONING_COMMAND = 3,
    ProvisioningState_ProvisioningErrorCode_NO_REED_CONFIG = 4,
    ProvisioningState_ProvisioningErrorCode_NO_PRESENCE_CONFIG = 5
} ProvisioningState_ProvisioningErrorCode;

typedef enum _LightState_TransitionStopReason {
    LightState_TransitionStopReason_TRANSITION_STOP_UNKNOWN = 0,
    LightState_TransitionStopReason_TRANSITION_STOP_HOLD_RELEASE = 1,
    LightState_TransitionStopReason_TRANSITION_STOP_CLICK_COMMAND = 2,
    LightState_TransitionStopReason_TRANSITION_STOP_TARGET_REACHED = 3
} LightState_TransitionStopReason;

typedef enum _ButtonState_State {
    ButtonState_State_BUTTON_STATE_RELEASED = 0,
    ButtonState_State_BUTTON_STATE_UP_PRESSED = 1,
    ButtonState_State_BUTTON_STATE_DOWN_PRESSED = 2
} ButtonState_State;

typedef enum _ESPStatus_Status {
    ESPStatus_Status_UNKNOWN = 0,
    ESPStatus_Status_CONNECTING = 1,
    ESPStatus_Status_CONNECTED = 2,
    ESPStatus_Status_WEBSOCKET_CONNECTED = 3,
    ESPStatus_Status_WEBSOCKET_DISCONNECTED = 4,
    ESPStatus_Status_WEBSOCKET_CONNECTION_FAILED = 5,
    ESPStatus_Status_DISCONNECTED = 6,
    ESPStatus_Status_WIFI_ERROR = 7,
    ESPStatus_Status_DHCP_ERROR = 8
} ESPStatus_Status;

/* Struct definitions */
typedef struct _BasestationConfig_RFConfig {
    uint8_t channel;
    uint16_t network;
} BasestationConfig_RFConfig;

typedef struct _BasestationConfig_WifiConfig {
    char ssid[33];
    char password[33];
} BasestationConfig_WifiConfig;

typedef struct _BasestationConfig_MACConfig {
    bool use_mac_address;
    char mac_address[18];
} BasestationConfig_MACConfig;

typedef struct _BasestationConfig_DHCPConfig {
    bool static_ip;
    char ip_address[16];
    char subnet_mask[16];
    char gateway[16];
    char dns_server[16];
} BasestationConfig_DHCPConfig;

typedef struct _BasestationConfig_NodeQRMapping {
    /* The QR code string value for this device */
    char qr_code[13];
    /* Either the CAN or the RF node ID */
    uint8_t node_id;
    BasestationConfig_NodeQRMapping_DeviceType type;
} BasestationConfig_NodeQRMapping;

/* *
 Action that can be triggered by nodes to control lights. */
typedef struct _Action {
    uint16_t id;
    pb_size_t which_target;
    union {
        uint8_t light_id;
        uint8_t somfy_shade_id;
    } target;
    uint32_t dim_speed_msec;
    float target_brightness;
    /* Optional fields for different action types
 Note: we may use a oneof here for even smaller messages */
    float on_brightness;
    float off_brightness;
    uint32_t delay_in_msec;
    uint32_t activate_delay_msec;
} Action;

typedef struct _RFDimmerConfig {
    uint8_t node_id;
    /* Refer to relevant Action IDs */
    pb_size_t middle_button_click_count;
    uint16_t middle_button_click[5];
    pb_size_t up_button_click_count;
    uint16_t up_button_click[5];
    pb_size_t down_button_click_count;
    uint16_t down_button_click[5];
    pb_size_t middle_button_hold_count;
    uint16_t middle_button_hold[5];
    pb_size_t up_button_hold_count;
    uint16_t up_button_hold[5];
    pb_size_t down_button_hold_count;
    uint16_t down_button_hold[5];
} RFDimmerConfig;

typedef struct _RFReedSensorConfig {
    uint8_t node_id;
    /* Refer to relevant Action IDs */
    pb_size_t door_close_count;
    uint16_t door_close[5];
    pb_size_t door_open_count;
    uint16_t door_open[5];
} RFReedSensorConfig;

typedef struct _RFPresenceSensorConfig {
    uint8_t node_id;
    pb_size_t on_activate_count;
    uint16_t on_activate[5];
    pb_size_t on_deactivate_count;
    uint16_t on_deactivate[5];
} RFPresenceSensorConfig;

typedef struct _CanboConfig_ThreePinInput_ToggleConfig {
    /* Refer to relevant Action IDs */
    pb_size_t up_click_count;
    uint16_t up_click[5];
    pb_size_t up_hold_count;
    uint16_t up_hold[5];
    pb_size_t down_click_count;
    uint16_t down_click[5];
    pb_size_t down_hold_count;
    uint16_t down_hold[5];
    pb_size_t up_press_count;
    uint16_t up_press[5];
    pb_size_t up_release_count;
    uint16_t up_release[5];
    pb_size_t down_press_count;
    uint16_t down_press[5];
    pb_size_t down_release_count;
    uint16_t down_release[5];
    pb_size_t up_hold_release_count;
    uint16_t up_hold_release[5];
    pb_size_t down_hold_release_count;
    uint16_t down_hold_release[5];
} CanboConfig_ThreePinInput_ToggleConfig;

typedef struct _CanboConfig_ThreePinInput_MomentaryConfig {
    /* Refer to relevant Action IDs */
    pb_size_t up_click_count;
    uint16_t up_click[5];
    pb_size_t up_hold_count;
    uint16_t up_hold[5];
    pb_size_t up_press_count;
    uint16_t up_press[5];
    pb_size_t up_release_count;
    uint16_t up_release[5];
    pb_size_t up_hold_release_count;
    uint16_t up_hold_release[5];
} CanboConfig_ThreePinInput_MomentaryConfig;

typedef struct _CanboConfig_ThreePinInput {
    uint8_t connector_id;
    CanboConfig_ThreePinInput_ConnectorType type;
    pb_size_t which_config;
    union {
        CanboConfig_ThreePinInput_ToggleConfig toggle;
        CanboConfig_ThreePinInput_MomentaryConfig momentary;
    } config;
} CanboConfig_ThreePinInput;

typedef struct _CanboConfig_TwoPinInput_MomentaryConfig {
    /* Refer to relevant Action IDs */
    pb_size_t up_click_count;
    uint16_t up_click[5];
    pb_size_t up_hold_count;
    uint16_t up_hold[5];
    pb_size_t up_press_count;
    uint16_t up_press[5];
    pb_size_t up_release_count;
    uint16_t up_release[5];
    pb_size_t up_hold_release_count;
    uint16_t up_hold_release[5];
} CanboConfig_TwoPinInput_MomentaryConfig;

typedef struct _CanboConfig_TwoPinInput_DoorSensorConfig {
    /* Refer to relevant Action IDs */
    pb_size_t on_open_count;
    uint16_t on_open[5];
    pb_size_t on_close_count;
    uint16_t on_close[5];
} CanboConfig_TwoPinInput_DoorSensorConfig;

typedef struct _CanboConfig_TwoPinInput {
    uint8_t connector_id;
    CanboConfig_TwoPinInput_ConnectorType type;
    pb_size_t which_config;
    union {
        CanboConfig_TwoPinInput_MomentaryConfig momentary;
        CanboConfig_TwoPinInput_DoorSensorConfig door_sensor;
    } config;
} CanboConfig_TwoPinInput;

typedef struct _CanboConfig_ADCInput_KnobConfig {
    /* Refer to relevant Action IDs */
    pb_size_t on_turn_count;
    uint16_t on_turn[5];
} CanboConfig_ADCInput_KnobConfig;

typedef struct _CanboConfig_ADCInput_ThermostatConfig {
    /* Refer to relevant Action IDs */
    pb_size_t thermostat_action_count;
    uint16_t thermostat_action[5];
} CanboConfig_ADCInput_ThermostatConfig;

typedef struct _CanboConfig_ADCInput_PIRConfig {
    /* Refer to relevant Action IDs */
    pb_size_t on_activate_count;
    uint16_t on_activate[5];
    pb_size_t on_deactivate_count;
    uint16_t on_deactivate[5];
} CanboConfig_ADCInput_PIRConfig;

typedef struct _CanboConfig_ADCInput {
    uint8_t connector_id;
    CanboConfig_ADCInput_ConnectorType type;
    pb_size_t which_config;
    union {
        CanboConfig_ADCInput_KnobConfig knob;
        CanboConfig_ADCInput_ThermostatConfig thermostat;
        CanboConfig_ADCInput_PIRConfig pir;
    } config;
} CanboConfig_ADCInput;

typedef struct _CanboConfig_Output {
    uint8_t connector_id;
    CanboConfig_Output_ConnectorType connector_type;
} CanboConfig_Output;

typedef struct _CanboConfig {
    uint8_t node_id;
    pb_size_t three_pin_inputs_count;
    CanboConfig_ThreePinInput three_pin_inputs[4];
    pb_size_t two_pin_inputs_count;
    CanboConfig_TwoPinInput two_pin_inputs[2];
    bool has_adc_inputs;
    CanboConfig_ADCInput adc_inputs;
    pb_size_t outputs_count;
    CanboConfig_Output outputs[4];
} CanboConfig;

typedef struct _LightConfig_FixtureConfig_DMXConfig_LightParams {
    float min1;
    float max1;
    float gamma1;
    float min2;
    float max2;
    float gamma2;
} LightConfig_FixtureConfig_DMXConfig_LightParams;

typedef struct _LightConfig_FixtureConfig_DMXConfig_RGBConfig {
    uint8_t red;
    uint8_t green;
    uint8_t blue;
} LightConfig_FixtureConfig_DMXConfig_RGBConfig;

typedef struct _LightConfig_FixtureConfig_DMXConfig {
    bool has_params;
    LightConfig_FixtureConfig_DMXConfig_LightParams params;
    bool has_rgb;
    LightConfig_FixtureConfig_DMXConfig_RGBConfig rgb;
    pb_size_t channels_count;
    uint8_t channels[10];
    LightConfig_FixtureConfig_DMXConfig_FixtureTypeConfig type;
} LightConfig_FixtureConfig_DMXConfig;

typedef struct _LightConfig_FixtureConfig_RFConfig {
    LightConfig_FixtureConfig_RFConfig_Type type;
    uint8_t node_id;
} LightConfig_FixtureConfig_RFConfig;

typedef struct _LightConfig_FixtureConfig_ZeroToTenVoltConfig {
    LightConfig_FixtureConfig_ZeroToTenVoltConfig_Type type;
    uint8_t node_id;
    bool use_relay;
    uint8_t out_connector_id;
} LightConfig_FixtureConfig_ZeroToTenVoltConfig;

typedef struct _LightConfig_FixtureConfig_RelayConfig {
    uint8_t node_id;
    uint8_t out_connector_id;
} LightConfig_FixtureConfig_RelayConfig;

typedef struct _LightConfig_FixtureConfig {
    /* Min and max brightness of the fixture */
    float min_brightness;
    float max_brightness;
    /* Configuration for the fixture */
    LightConfig_FixtureConfig_FixtureType type;
    pb_size_t which_config;
    union {
        LightConfig_FixtureConfig_DMXConfig dmx;
        LightConfig_FixtureConfig_RFConfig rf;
        LightConfig_FixtureConfig_ZeroToTenVoltConfig zero_to_ten_volt;
        LightConfig_FixtureConfig_RelayConfig relay; /* Analog0To10VConfig analog_0_10 = 7; */
    } config;
} LightConfig_FixtureConfig;

typedef struct _LightConfig {
    uint8_t id;
    /* How fast the light dims in milliseconds */
    uint32_t dim_speed_msec;
    /* Configurations for the fixtures */
    pb_size_t fixtures_count;
    LightConfig_FixtureConfig fixtures[5];
} LightConfig;

/* Need to map the id used in the configurator to the id assigned by the basestation during pairing */
typedef struct _SomfyShadesConfig {
    uint8_t internal_id;
    uint8_t device_id;
} SomfyShadesConfig;

typedef struct _BasestationConfig {
    /* Unique identifier for this basestation on the network */
    uint8_t id;
    /* Version number incremented on each config change */
    char version[9];
    /* Configuration for radio-frequency devices */
    bool has_rf_config;
    BasestationConfig_RFConfig rf_config;
    bool has_wifi_config;
    BasestationConfig_WifiConfig wifi_config;
    bool has_mac_config;
    BasestationConfig_MACConfig mac_config;
    bool has_dhcp_config;
    BasestationConfig_DHCPConfig dhcp_config;
    /* WebSocket server address for cloud connectivity */
    char server_address[129];
    /* Configurations for the inputs */
    pb_size_t canbo_configs_count;
    CanboConfig canbo_configs[20];
    pb_size_t rf_reed_configs_count;
    RFReedSensorConfig rf_reed_configs[10];
    pb_size_t rf_presence_configs_count;
    RFPresenceSensorConfig rf_presence_configs[10];
    pb_size_t rf_dimmer_configs_count;
    RFDimmerConfig rf_dimmer_configs[10];
    /* Configurations for the lights */
    pb_size_t lights_count;
    LightConfig lights[20];
    /* Configurations for the somfy shades */
    pb_size_t somfy_shades_count;
    SomfyShadesConfig somfy_shades[16];
    /* Maps CAN and RF devices to their QR code */
    pb_size_t node_qr_mappings_count;
    BasestationConfig_NodeQRMapping node_qr_mappings[20];
    /* Actions referenced by configs (shared here to reduce config size) */
    pb_size_t actions_count;
    Action actions[500];
} BasestationConfig;

typedef struct _BasestationUpdateMessage {
    char qr_code[13];
    bool has_config;
    BasestationConfig config;
} BasestationUpdateMessage;

typedef struct _RFReedState {
    uint8_t node_id;
    RFReedState_Status sensor_status;
    uint64_t last_modified_time;
    float battery_voltage;
} RFReedState;

typedef struct _RFPresenceState {
    uint8_t node_id;
    RFPresenceState_Status sensor_status;
    uint64_t last_modified_time;
    float battery_voltage;
} RFPresenceState;

typedef struct _ProvisioningState {
    uint8_t node_id;
    bool is_provisioned;
    ProvisioningState_ProvisioningErrorCode error_code;
    uint64_t last_seen_time;
    int8_t rssi;
} ProvisioningState;

typedef struct _LightState {
    uint8_t id;
    float brightness;
    float target_value;
    uint32_t dim_speed_msec;
    uint64_t last_modified_time;
    uint64_t active_after_time;
    bool is_transitioning;
    float last_brightness_before_action;
    LightState_TransitionStopReason last_transition_stop_reason;
} LightState;

typedef struct _ButtonState {
    uint8_t node_id;
    uint8_t connector_id;
    ButtonState_State current_state;
    uint64_t last_modified_time;
} ButtonState;

typedef struct _PIRState {
    uint8_t node_id;
    bool is_activated;
    uint64_t last_activated_time;
    uint64_t deactivate_after_time;
} PIRState;

typedef struct _BasestationState {
    pb_size_t lights_count;
    LightState lights[10];
    pb_size_t buttons_count;
    ButtonState buttons[30];
    pb_size_t provisioned_devices_count;
    ProvisioningState provisioned_devices[10];
    pb_size_t reeds_count;
    RFReedState reeds[10];
    pb_size_t presences_count;
    RFPresenceState presences[10];
    pb_size_t pirs_count;
    PIRState pirs[10];
} BasestationState;

typedef struct _ActiveConfiguration {
    bool has_config;
    BasestationConfig config;
    bool has_state;
    BasestationState state;
} ActiveConfiguration;

typedef struct _ESPStatus {
    ESPStatus_Status status;
} ESPStatus;

typedef struct _ESPUpdateConfig_MACConfig {
    bool use_mac_address;
    char mac_address[18];
} ESPUpdateConfig_MACConfig;

typedef struct _ESPUpdateConfig_DHCPConfig {
    bool static_ip;
    char ip_address[16];
    char subnet_mask[16];
    char gateway[16];
    char dns_server[16];
} ESPUpdateConfig_DHCPConfig;

typedef struct _ESPUpdateConfig_WifiConfig {
    char ssid[33];
    char password[33];
} ESPUpdateConfig_WifiConfig;

typedef struct _ESPUpdateConfig {
    bool has_mac_config;
    ESPUpdateConfig_MACConfig mac_config;
    bool has_dhcp_config;
    ESPUpdateConfig_DHCPConfig dhcp_config;
    bool has_wifi_config;
    ESPUpdateConfig_WifiConfig wifi_config;
    char qr_code[13];
    char server_address[129];
    char config_version[9];
} ESPUpdateConfig;


#ifdef __cplusplus
extern "C" {
#endif

/* Helper constants for enums */
#define _BasestationConfig_NodeQRMapping_DeviceType_MIN BasestationConfig_NodeQRMapping_DeviceType_CAN
#define _BasestationConfig_NodeQRMapping_DeviceType_MAX BasestationConfig_NodeQRMapping_DeviceType_RF
#define _BasestationConfig_NodeQRMapping_DeviceType_ARRAYSIZE ((BasestationConfig_NodeQRMapping_DeviceType)(BasestationConfig_NodeQRMapping_DeviceType_RF+1))

#define _CanboConfig_ThreePinInput_ConnectorType_MIN CanboConfig_ThreePinInput_ConnectorType_TOGGLE
#define _CanboConfig_ThreePinInput_ConnectorType_MAX CanboConfig_ThreePinInput_ConnectorType_MOMENTARY
#define _CanboConfig_ThreePinInput_ConnectorType_ARRAYSIZE ((CanboConfig_ThreePinInput_ConnectorType)(CanboConfig_ThreePinInput_ConnectorType_MOMENTARY+1))

#define _CanboConfig_TwoPinInput_ConnectorType_MIN CanboConfig_TwoPinInput_ConnectorType_MOMENTARY
#define _CanboConfig_TwoPinInput_ConnectorType_MAX CanboConfig_TwoPinInput_ConnectorType_DOOR_SENSOR
#define _CanboConfig_TwoPinInput_ConnectorType_ARRAYSIZE ((CanboConfig_TwoPinInput_ConnectorType)(CanboConfig_TwoPinInput_ConnectorType_DOOR_SENSOR+1))

#define _CanboConfig_ADCInput_ConnectorType_MIN CanboConfig_ADCInput_ConnectorType_KNOB
#define _CanboConfig_ADCInput_ConnectorType_MAX CanboConfig_ADCInput_ConnectorType_PIR
#define _CanboConfig_ADCInput_ConnectorType_ARRAYSIZE ((CanboConfig_ADCInput_ConnectorType)(CanboConfig_ADCInput_ConnectorType_PIR+1))

#define _CanboConfig_Output_ConnectorType_MIN CanboConfig_Output_ConnectorType_RELAY
#define _CanboConfig_Output_ConnectorType_MAX CanboConfig_Output_ConnectorType_RELAY
#define _CanboConfig_Output_ConnectorType_ARRAYSIZE ((CanboConfig_Output_ConnectorType)(CanboConfig_Output_ConnectorType_RELAY+1))

#define _LightConfig_FixtureConfig_FixtureType_MIN LightConfig_FixtureConfig_FixtureType_DMX
#define _LightConfig_FixtureConfig_FixtureType_MAX LightConfig_FixtureConfig_FixtureType_RELAY
#define _LightConfig_FixtureConfig_FixtureType_ARRAYSIZE ((LightConfig_FixtureConfig_FixtureType)(LightConfig_FixtureConfig_FixtureType_RELAY+1))

#define _LightConfig_FixtureConfig_DMXConfig_FixtureTypeConfig_MIN LightConfig_FixtureConfig_DMXConfig_FixtureTypeConfig_D4
#define _LightConfig_FixtureConfig_DMXConfig_FixtureTypeConfig_MAX LightConfig_FixtureConfig_DMXConfig_FixtureTypeConfig_RGB_STRIP
#define _LightConfig_FixtureConfig_DMXConfig_FixtureTypeConfig_ARRAYSIZE ((LightConfig_FixtureConfig_DMXConfig_FixtureTypeConfig)(LightConfig_FixtureConfig_DMXConfig_FixtureTypeConfig_RGB_STRIP+1))

#define _LightConfig_FixtureConfig_RFConfig_Type_MIN LightConfig_FixtureConfig_RFConfig_Type_DIMMER
#define _LightConfig_FixtureConfig_RFConfig_Type_MAX LightConfig_FixtureConfig_RFConfig_Type_SWITCH
#define _LightConfig_FixtureConfig_RFConfig_Type_ARRAYSIZE ((LightConfig_FixtureConfig_RFConfig_Type)(LightConfig_FixtureConfig_RFConfig_Type_SWITCH+1))

#define _LightConfig_FixtureConfig_ZeroToTenVoltConfig_Type_MIN LightConfig_FixtureConfig_ZeroToTenVoltConfig_Type_SOURCING
#define _LightConfig_FixtureConfig_ZeroToTenVoltConfig_Type_MAX LightConfig_FixtureConfig_ZeroToTenVoltConfig_Type_SINKING
#define _LightConfig_FixtureConfig_ZeroToTenVoltConfig_Type_ARRAYSIZE ((LightConfig_FixtureConfig_ZeroToTenVoltConfig_Type)(LightConfig_FixtureConfig_ZeroToTenVoltConfig_Type_SINKING+1))

#define _RFReedState_Status_MIN RFReedState_Status_UNKNOWN
#define _RFReedState_Status_MAX RFReedState_Status_CLOSED
#define _RFReedState_Status_ARRAYSIZE ((RFReedState_Status)(RFReedState_Status_CLOSED+1))

#define _RFPresenceState_Status_MIN RFPresenceState_Status_UNKNOWN
#define _RFPresenceState_Status_MAX RFPresenceState_Status_DEACTIVATED
#define _RFPresenceState_Status_ARRAYSIZE ((RFPresenceState_Status)(RFPresenceState_Status_DEACTIVATED+1))

#define _ProvisioningState_ProvisioningErrorCode_MIN ProvisioningState_ProvisioningErrorCode_NONE
#define _ProvisioningState_ProvisioningErrorCode_MAX ProvisioningState_ProvisioningErrorCode_NO_PRESENCE_CONFIG
#define _ProvisioningState_ProvisioningErrorCode_ARRAYSIZE ((ProvisioningState_ProvisioningErrorCode)(ProvisioningState_ProvisioningErrorCode_NO_PRESENCE_CONFIG+1))

#define _LightState_TransitionStopReason_MIN LightState_TransitionStopReason_TRANSITION_STOP_UNKNOWN
#define _LightState_TransitionStopReason_MAX LightState_TransitionStopReason_TRANSITION_STOP_TARGET_REACHED
#define _LightState_TransitionStopReason_ARRAYSIZE ((LightState_TransitionStopReason)(LightState_TransitionStopReason_TRANSITION_STOP_TARGET_REACHED+1))

#define _ButtonState_State_MIN ButtonState_State_BUTTON_STATE_RELEASED
#define _ButtonState_State_MAX ButtonState_State_BUTTON_STATE_DOWN_PRESSED
#define _ButtonState_State_ARRAYSIZE ((ButtonState_State)(ButtonState_State_BUTTON_STATE_DOWN_PRESSED+1))

#define _ESPStatus_Status_MIN ESPStatus_Status_UNKNOWN
#define _ESPStatus_Status_MAX ESPStatus_Status_DHCP_ERROR
#define _ESPStatus_Status_ARRAYSIZE ((ESPStatus_Status)(ESPStatus_Status_DHCP_ERROR+1))








#define BasestationConfig_NodeQRMapping_type_ENUMTYPE BasestationConfig_NodeQRMapping_DeviceType






#define CanboConfig_ThreePinInput_type_ENUMTYPE CanboConfig_ThreePinInput_ConnectorType



#define CanboConfig_TwoPinInput_type_ENUMTYPE CanboConfig_TwoPinInput_ConnectorType



#define CanboConfig_ADCInput_type_ENUMTYPE CanboConfig_ADCInput_ConnectorType




#define CanboConfig_Output_connector_type_ENUMTYPE CanboConfig_Output_ConnectorType


#define LightConfig_FixtureConfig_type_ENUMTYPE LightConfig_FixtureConfig_FixtureType

#define LightConfig_FixtureConfig_DMXConfig_type_ENUMTYPE LightConfig_FixtureConfig_DMXConfig_FixtureTypeConfig



#define LightConfig_FixtureConfig_RFConfig_type_ENUMTYPE LightConfig_FixtureConfig_RFConfig_Type

#define LightConfig_FixtureConfig_ZeroToTenVoltConfig_type_ENUMTYPE LightConfig_FixtureConfig_ZeroToTenVoltConfig_Type




#define RFReedState_sensor_status_ENUMTYPE RFReedState_Status

#define RFPresenceState_sensor_status_ENUMTYPE RFPresenceState_Status

#define ProvisioningState_error_code_ENUMTYPE ProvisioningState_ProvisioningErrorCode

#define LightState_last_transition_stop_reason_ENUMTYPE LightState_TransitionStopReason

#define ButtonState_current_state_ENUMTYPE ButtonState_State


#define ESPStatus_status_ENUMTYPE ESPStatus_Status






/* Initializer values for message structs */
#define ActiveConfiguration_init_default         {false, BasestationConfig_init_default, false, BasestationState_init_default}
#define BasestationUpdateMessage_init_default    {"", false, BasestationConfig_init_default}
#define BasestationConfig_init_default           {0, "", false, BasestationConfig_RFConfig_init_default, false, BasestationConfig_WifiConfig_init_default, false, BasestationConfig_MACConfig_init_default, false, BasestationConfig_DHCPConfig_init_default, "", 0, {CanboConfig_init_default, CanboConfig_init_default, CanboConfig_init_default, CanboConfig_init_default, CanboConfig_init_default, CanboConfig_init_default, CanboConfig_init_default, CanboConfig_init_default, CanboConfig_init_default, CanboConfig_init_default, CanboConfig_init_default, CanboConfig_init_default, CanboConfig_init_default, CanboConfig_init_default, CanboConfig_init_default, CanboConfig_init_default, CanboConfig_init_default, CanboConfig_init_default, CanboConfig_init_default, CanboConfig_init_default}, 0, {RFReedSensorConfig_init_default, RFReedSensorConfig_init_default, RFReedSensorConfig_init_default, RFReedSensorConfig_init_default, RFReedSensorConfig_init_default, RFReedSensorConfig_init_default, RFReedSensorConfig_init_default, RFReedSensorConfig_init_default, RFReedSensorConfig_init_default, RFReedSensorConfig_init_default}, 0, {RFPresenceSensorConfig_init_default, RFPresenceSensorConfig_init_default, RFPresenceSensorConfig_init_default, RFPresenceSensorConfig_init_default, RFPresenceSensorConfig_init_default, RFPresenceSensorConfig_init_default, RFPresenceSensorConfig_init_default, RFPresenceSensorConfig_init_default, RFPresenceSensorConfig_init_default, RFPresenceSensorConfig_init_default}, 0, {RFDimmerConfig_init_default, RFDimmerConfig_init_default, RFDimmerConfig_init_default, RFDimmerConfig_init_default, RFDimmerConfig_init_default, RFDimmerConfig_init_default, RFDimmerConfig_init_default, RFDimmerConfig_init_default, RFDimmerConfig_init_default, RFDimmerConfig_init_default}, 0, {LightConfig_init_default, LightConfig_init_default, LightConfig_init_default, LightConfig_init_default, LightConfig_init_default, LightConfig_init_default, LightConfig_init_default, LightConfig_init_default, LightConfig_init_default, LightConfig_init_default, LightConfig_init_default, LightConfig_init_default, LightConfig_init_default, LightConfig_init_default, LightConfig_init_default, LightConfig_init_default, LightConfig_init_default, LightConfig_init_default, LightConfig_init_default, LightConfig_init_default}, 0, {SomfyShadesConfig_init_default, SomfyShadesConfig_init_default, SomfyShadesConfig_init_default, SomfyShadesConfig_init_default, SomfyShadesConfig_init_default, SomfyShadesConfig_init_default, SomfyShadesConfig_init_default, SomfyShadesConfig_init_default, SomfyShadesConfig_init_default, SomfyShadesConfig_init_default, SomfyShadesConfig_init_default, SomfyShadesConfig_init_default, SomfyShadesConfig_init_default, SomfyShadesConfig_init_default, SomfyShadesConfig_init_default, SomfyShadesConfig_init_default}, 0, {BasestationConfig_NodeQRMapping_init_default, BasestationConfig_NodeQRMapping_init_default, BasestationConfig_NodeQRMapping_init_default, BasestationConfig_NodeQRMapping_init_default, BasestationConfig_NodeQRMapping_init_default, BasestationConfig_NodeQRMapping_init_default, BasestationConfig_NodeQRMapping_init_default, BasestationConfig_NodeQRMapping_init_default, BasestationConfig_NodeQRMapping_init_default, BasestationConfig_NodeQRMapping_init_default, BasestationConfig_NodeQRMapping_init_default, BasestationConfig_NodeQRMapping_init_default, BasestationConfig_NodeQRMapping_init_default, BasestationConfig_NodeQRMapping_init_default, BasestationConfig_NodeQRMapping_init_default, BasestationConfig_NodeQRMapping_init_default, BasestationConfig_NodeQRMapping_init_default, BasestationConfig_NodeQRMapping_init_default, BasestationConfig_NodeQRMapping_init_default, BasestationConfig_NodeQRMapping_init_default}, 0, {Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default, Action_init_default}}
#define BasestationConfig_RFConfig_init_default  {0, 0}
#define BasestationConfig_WifiConfig_init_default {"", ""}
#define BasestationConfig_MACConfig_init_default {0, ""}
#define BasestationConfig_DHCPConfig_init_default {0, "", "", "", ""}
#define BasestationConfig_NodeQRMapping_init_default {"", 0, _BasestationConfig_NodeQRMapping_DeviceType_MIN}
#define Action_init_default                      {0, 0, {0}, 0, 0, 0, 0, 0, 0}
#define RFDimmerConfig_init_default              {0, 0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}}
#define RFReedSensorConfig_init_default          {0, 0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}}
#define RFPresenceSensorConfig_init_default      {0, 0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}}
#define CanboConfig_init_default                 {0, 0, {CanboConfig_ThreePinInput_init_default, CanboConfig_ThreePinInput_init_default, CanboConfig_ThreePinInput_init_default, CanboConfig_ThreePinInput_init_default}, 0, {CanboConfig_TwoPinInput_init_default, CanboConfig_TwoPinInput_init_default}, false, CanboConfig_ADCInput_init_default, 0, {CanboConfig_Output_init_default, CanboConfig_Output_init_default, CanboConfig_Output_init_default, CanboConfig_Output_init_default}}
#define CanboConfig_ThreePinInput_init_default   {0, _CanboConfig_ThreePinInput_ConnectorType_MIN, 0, {CanboConfig_ThreePinInput_ToggleConfig_init_default}}
#define CanboConfig_ThreePinInput_ToggleConfig_init_default {0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}}
#define CanboConfig_ThreePinInput_MomentaryConfig_init_default {0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}}
#define CanboConfig_TwoPinInput_init_default     {0, _CanboConfig_TwoPinInput_ConnectorType_MIN, 0, {CanboConfig_TwoPinInput_MomentaryConfig_init_default}}
#define CanboConfig_TwoPinInput_MomentaryConfig_init_default {0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}}
#define CanboConfig_TwoPinInput_DoorSensorConfig_init_default {0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}}
#define CanboConfig_ADCInput_init_default        {0, _CanboConfig_ADCInput_ConnectorType_MIN, 0, {CanboConfig_ADCInput_KnobConfig_init_default}}
#define CanboConfig_ADCInput_KnobConfig_init_default {0, {0, 0, 0, 0, 0}}
#define CanboConfig_ADCInput_ThermostatConfig_init_default {0, {0, 0, 0, 0, 0}}
#define CanboConfig_ADCInput_PIRConfig_init_default {0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}}
#define CanboConfig_Output_init_default          {0, _CanboConfig_Output_ConnectorType_MIN}
#define LightConfig_init_default                 {0, 0, 0, {LightConfig_FixtureConfig_init_default, LightConfig_FixtureConfig_init_default, LightConfig_FixtureConfig_init_default, LightConfig_FixtureConfig_init_default, LightConfig_FixtureConfig_init_default}}
#define LightConfig_FixtureConfig_init_default   {0, 0, _LightConfig_FixtureConfig_FixtureType_MIN, 0, {LightConfig_FixtureConfig_DMXConfig_init_default}}
#define LightConfig_FixtureConfig_DMXConfig_init_default {false, LightConfig_FixtureConfig_DMXConfig_LightParams_init_default, false, LightConfig_FixtureConfig_DMXConfig_RGBConfig_init_default, 0, {0, 0, 0, 0, 0, 0, 0, 0, 0, 0}, _LightConfig_FixtureConfig_DMXConfig_FixtureTypeConfig_MIN}
#define LightConfig_FixtureConfig_DMXConfig_LightParams_init_default {0, 0, 0, 0, 0, 0}
#define LightConfig_FixtureConfig_DMXConfig_RGBConfig_init_default {0, 0, 0}
#define LightConfig_FixtureConfig_RFConfig_init_default {_LightConfig_FixtureConfig_RFConfig_Type_MIN, 0}
#define LightConfig_FixtureConfig_ZeroToTenVoltConfig_init_default {_LightConfig_FixtureConfig_ZeroToTenVoltConfig_Type_MIN, 0, 0, 0}
#define LightConfig_FixtureConfig_RelayConfig_init_default {0, 0}
#define SomfyShadesConfig_init_default           {0, 0}
#define BasestationState_init_default            {0, {LightState_init_default, LightState_init_default, LightState_init_default, LightState_init_default, LightState_init_default, LightState_init_default, LightState_init_default, LightState_init_default, LightState_init_default, LightState_init_default}, 0, {ButtonState_init_default, ButtonState_init_default, ButtonState_init_default, ButtonState_init_default, ButtonState_init_default, ButtonState_init_default, ButtonState_init_default, ButtonState_init_default, ButtonState_init_default, ButtonState_init_default, ButtonState_init_default, ButtonState_init_default, ButtonState_init_default, ButtonState_init_default, ButtonState_init_default, ButtonState_init_default, ButtonState_init_default, ButtonState_init_default, ButtonState_init_default, ButtonState_init_default, ButtonState_init_default, ButtonState_init_default, ButtonState_init_default, ButtonState_init_default, ButtonState_init_default, ButtonState_init_default, ButtonState_init_default, ButtonState_init_default, ButtonState_init_default, ButtonState_init_default}, 0, {ProvisioningState_init_default, ProvisioningState_init_default, ProvisioningState_init_default, ProvisioningState_init_default, ProvisioningState_init_default, ProvisioningState_init_default, ProvisioningState_init_default, ProvisioningState_init_default, ProvisioningState_init_default, ProvisioningState_init_default}, 0, {RFReedState_init_default, RFReedState_init_default, RFReedState_init_default, RFReedState_init_default, RFReedState_init_default, RFReedState_init_default, RFReedState_init_default, RFReedState_init_default, RFReedState_init_default, RFReedState_init_default}, 0, {RFPresenceState_init_default, RFPresenceState_init_default, RFPresenceState_init_default, RFPresenceState_init_default, RFPresenceState_init_default, RFPresenceState_init_default, RFPresenceState_init_default, RFPresenceState_init_default, RFPresenceState_init_default, RFPresenceState_init_default}, 0, {PIRState_init_default, PIRState_init_default, PIRState_init_default, PIRState_init_default, PIRState_init_default, PIRState_init_default, PIRState_init_default, PIRState_init_default, PIRState_init_default, PIRState_init_default}}
#define RFReedState_init_default                 {0, _RFReedState_Status_MIN, 0, 0}
#define RFPresenceState_init_default             {0, _RFPresenceState_Status_MIN, 0, 0}
#define ProvisioningState_init_default           {0, 0, _ProvisioningState_ProvisioningErrorCode_MIN, 0, 0}
#define LightState_init_default                  {0, 0, 0, 0, 0, 0, 0, 0, _LightState_TransitionStopReason_MIN}
#define ButtonState_init_default                 {0, 0, _ButtonState_State_MIN, 0}
#define PIRState_init_default                    {0, 0, 0, 0}
#define ESPStatus_init_default                   {_ESPStatus_Status_MIN}
#define ESPUpdateConfig_init_default             {false, ESPUpdateConfig_MACConfig_init_default, false, ESPUpdateConfig_DHCPConfig_init_default, false, ESPUpdateConfig_WifiConfig_init_default, "", "", ""}
#define ESPUpdateConfig_MACConfig_init_default   {0, ""}
#define ESPUpdateConfig_DHCPConfig_init_default  {0, "", "", "", ""}
#define ESPUpdateConfig_WifiConfig_init_default  {"", ""}
#define ActiveConfiguration_init_zero            {false, BasestationConfig_init_zero, false, BasestationState_init_zero}
#define BasestationUpdateMessage_init_zero       {"", false, BasestationConfig_init_zero}
#define BasestationConfig_init_zero              {0, "", false, BasestationConfig_RFConfig_init_zero, false, BasestationConfig_WifiConfig_init_zero, false, BasestationConfig_MACConfig_init_zero, false, BasestationConfig_DHCPConfig_init_zero, "", 0, {CanboConfig_init_zero, CanboConfig_init_zero, CanboConfig_init_zero, CanboConfig_init_zero, CanboConfig_init_zero, CanboConfig_init_zero, CanboConfig_init_zero, CanboConfig_init_zero, CanboConfig_init_zero, CanboConfig_init_zero, CanboConfig_init_zero, CanboConfig_init_zero, CanboConfig_init_zero, CanboConfig_init_zero, CanboConfig_init_zero, CanboConfig_init_zero, CanboConfig_init_zero, CanboConfig_init_zero, CanboConfig_init_zero, CanboConfig_init_zero}, 0, {RFReedSensorConfig_init_zero, RFReedSensorConfig_init_zero, RFReedSensorConfig_init_zero, RFReedSensorConfig_init_zero, RFReedSensorConfig_init_zero, RFReedSensorConfig_init_zero, RFReedSensorConfig_init_zero, RFReedSensorConfig_init_zero, RFReedSensorConfig_init_zero, RFReedSensorConfig_init_zero}, 0, {RFPresenceSensorConfig_init_zero, RFPresenceSensorConfig_init_zero, RFPresenceSensorConfig_init_zero, RFPresenceSensorConfig_init_zero, RFPresenceSensorConfig_init_zero, RFPresenceSensorConfig_init_zero, RFPresenceSensorConfig_init_zero, RFPresenceSensorConfig_init_zero, RFPresenceSensorConfig_init_zero, RFPresenceSensorConfig_init_zero}, 0, {RFDimmerConfig_init_zero, RFDimmerConfig_init_zero, RFDimmerConfig_init_zero, RFDimmerConfig_init_zero, RFDimmerConfig_init_zero, RFDimmerConfig_init_zero, RFDimmerConfig_init_zero, RFDimmerConfig_init_zero, RFDimmerConfig_init_zero, RFDimmerConfig_init_zero}, 0, {LightConfig_init_zero, LightConfig_init_zero, LightConfig_init_zero, LightConfig_init_zero, LightConfig_init_zero, LightConfig_init_zero, LightConfig_init_zero, LightConfig_init_zero, LightConfig_init_zero, LightConfig_init_zero, LightConfig_init_zero, LightConfig_init_zero, LightConfig_init_zero, LightConfig_init_zero, LightConfig_init_zero, LightConfig_init_zero, LightConfig_init_zero, LightConfig_init_zero, LightConfig_init_zero, LightConfig_init_zero}, 0, {SomfyShadesConfig_init_zero, SomfyShadesConfig_init_zero, SomfyShadesConfig_init_zero, SomfyShadesConfig_init_zero, SomfyShadesConfig_init_zero, SomfyShadesConfig_init_zero, SomfyShadesConfig_init_zero, SomfyShadesConfig_init_zero, SomfyShadesConfig_init_zero, SomfyShadesConfig_init_zero, SomfyShadesConfig_init_zero, SomfyShadesConfig_init_zero, SomfyShadesConfig_init_zero, SomfyShadesConfig_init_zero, SomfyShadesConfig_init_zero, SomfyShadesConfig_init_zero}, 0, {BasestationConfig_NodeQRMapping_init_zero, BasestationConfig_NodeQRMapping_init_zero, BasestationConfig_NodeQRMapping_init_zero, BasestationConfig_NodeQRMapping_init_zero, BasestationConfig_NodeQRMapping_init_zero, BasestationConfig_NodeQRMapping_init_zero, BasestationConfig_NodeQRMapping_init_zero, BasestationConfig_NodeQRMapping_init_zero, BasestationConfig_NodeQRMapping_init_zero, BasestationConfig_NodeQRMapping_init_zero, BasestationConfig_NodeQRMapping_init_zero, BasestationConfig_NodeQRMapping_init_zero, BasestationConfig_NodeQRMapping_init_zero, BasestationConfig_NodeQRMapping_init_zero, BasestationConfig_NodeQRMapping_init_zero, BasestationConfig_NodeQRMapping_init_zero, BasestationConfig_NodeQRMapping_init_zero, BasestationConfig_NodeQRMapping_init_zero, BasestationConfig_NodeQRMapping_init_zero, BasestationConfig_NodeQRMapping_init_zero}, 0, {Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero, Action_init_zero}}
#define BasestationConfig_RFConfig_init_zero     {0, 0}
#define BasestationConfig_WifiConfig_init_zero   {"", ""}
#define BasestationConfig_MACConfig_init_zero    {0, ""}
#define BasestationConfig_DHCPConfig_init_zero   {0, "", "", "", ""}
#define BasestationConfig_NodeQRMapping_init_zero {"", 0, _BasestationConfig_NodeQRMapping_DeviceType_MIN}
#define Action_init_zero                         {0, 0, {0}, 0, 0, 0, 0, 0, 0}
#define RFDimmerConfig_init_zero                 {0, 0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}}
#define RFReedSensorConfig_init_zero             {0, 0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}}
#define RFPresenceSensorConfig_init_zero         {0, 0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}}
#define CanboConfig_init_zero                    {0, 0, {CanboConfig_ThreePinInput_init_zero, CanboConfig_ThreePinInput_init_zero, CanboConfig_ThreePinInput_init_zero, CanboConfig_ThreePinInput_init_zero}, 0, {CanboConfig_TwoPinInput_init_zero, CanboConfig_TwoPinInput_init_zero}, false, CanboConfig_ADCInput_init_zero, 0, {CanboConfig_Output_init_zero, CanboConfig_Output_init_zero, CanboConfig_Output_init_zero, CanboConfig_Output_init_zero}}
#define CanboConfig_ThreePinInput_init_zero      {0, _CanboConfig_ThreePinInput_ConnectorType_MIN, 0, {CanboConfig_ThreePinInput_ToggleConfig_init_zero}}
#define CanboConfig_ThreePinInput_ToggleConfig_init_zero {0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}}
#define CanboConfig_ThreePinInput_MomentaryConfig_init_zero {0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}}
#define CanboConfig_TwoPinInput_init_zero        {0, _CanboConfig_TwoPinInput_ConnectorType_MIN, 0, {CanboConfig_TwoPinInput_MomentaryConfig_init_zero}}
#define CanboConfig_TwoPinInput_MomentaryConfig_init_zero {0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}}
#define CanboConfig_TwoPinInput_DoorSensorConfig_init_zero {0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}}
#define CanboConfig_ADCInput_init_zero           {0, _CanboConfig_ADCInput_ConnectorType_MIN, 0, {CanboConfig_ADCInput_KnobConfig_init_zero}}
#define CanboConfig_ADCInput_KnobConfig_init_zero {0, {0, 0, 0, 0, 0}}
#define CanboConfig_ADCInput_ThermostatConfig_init_zero {0, {0, 0, 0, 0, 0}}
#define CanboConfig_ADCInput_PIRConfig_init_zero {0, {0, 0, 0, 0, 0}, 0, {0, 0, 0, 0, 0}}
#define CanboConfig_Output_init_zero             {0, _CanboConfig_Output_ConnectorType_MIN}
#define LightConfig_init_zero                    {0, 0, 0, {LightConfig_FixtureConfig_init_zero, LightConfig_FixtureConfig_init_zero, LightConfig_FixtureConfig_init_zero, LightConfig_FixtureConfig_init_zero, LightConfig_FixtureConfig_init_zero}}
#define LightConfig_FixtureConfig_init_zero      {0, 0, _LightConfig_FixtureConfig_FixtureType_MIN, 0, {LightConfig_FixtureConfig_DMXConfig_init_zero}}
#define LightConfig_FixtureConfig_DMXConfig_init_zero {false, LightConfig_FixtureConfig_DMXConfig_LightParams_init_zero, false, LightConfig_FixtureConfig_DMXConfig_RGBConfig_init_zero, 0, {0, 0, 0, 0, 0, 0, 0, 0, 0, 0}, _LightConfig_FixtureConfig_DMXConfig_FixtureTypeConfig_MIN}
#define LightConfig_FixtureConfig_DMXConfig_LightParams_init_zero {0, 0, 0, 0, 0, 0}
#define LightConfig_FixtureConfig_DMXConfig_RGBConfig_init_zero {0, 0, 0}
#define LightConfig_FixtureConfig_RFConfig_init_zero {_LightConfig_FixtureConfig_RFConfig_Type_MIN, 0}
#define LightConfig_FixtureConfig_ZeroToTenVoltConfig_init_zero {_LightConfig_FixtureConfig_ZeroToTenVoltConfig_Type_MIN, 0, 0, 0}
#define LightConfig_FixtureConfig_RelayConfig_init_zero {0, 0}
#define SomfyShadesConfig_init_zero              {0, 0}
#define BasestationState_init_zero               {0, {LightState_init_zero, LightState_init_zero, LightState_init_zero, LightState_init_zero, LightState_init_zero, LightState_init_zero, LightState_init_zero, LightState_init_zero, LightState_init_zero, LightState_init_zero}, 0, {ButtonState_init_zero, ButtonState_init_zero, ButtonState_init_zero, ButtonState_init_zero, ButtonState_init_zero, ButtonState_init_zero, ButtonState_init_zero, ButtonState_init_zero, ButtonState_init_zero, ButtonState_init_zero, ButtonState_init_zero, ButtonState_init_zero, ButtonState_init_zero, ButtonState_init_zero, ButtonState_init_zero, ButtonState_init_zero, ButtonState_init_zero, ButtonState_init_zero, ButtonState_init_zero, ButtonState_init_zero, ButtonState_init_zero, ButtonState_init_zero, ButtonState_init_zero, ButtonState_init_zero, ButtonState_init_zero, ButtonState_init_zero, ButtonState_init_zero, ButtonState_init_zero, ButtonState_init_zero, ButtonState_init_zero}, 0, {ProvisioningState_init_zero, ProvisioningState_init_zero, ProvisioningState_init_zero, ProvisioningState_init_zero, ProvisioningState_init_zero, ProvisioningState_init_zero, ProvisioningState_init_zero, ProvisioningState_init_zero, ProvisioningState_init_zero, ProvisioningState_init_zero}, 0, {RFReedState_init_zero, RFReedState_init_zero, RFReedState_init_zero, RFReedState_init_zero, RFReedState_init_zero, RFReedState_init_zero, RFReedState_init_zero, RFReedState_init_zero, RFReedState_init_zero, RFReedState_init_zero}, 0, {RFPresenceState_init_zero, RFPresenceState_init_zero, RFPresenceState_init_zero, RFPresenceState_init_zero, RFPresenceState_init_zero, RFPresenceState_init_zero, RFPresenceState_init_zero, RFPresenceState_init_zero, RFPresenceState_init_zero, RFPresenceState_init_zero}, 0, {PIRState_init_zero, PIRState_init_zero, PIRState_init_zero, PIRState_init_zero, PIRState_init_zero, PIRState_init_zero, PIRState_init_zero, PIRState_init_zero, PIRState_init_zero, PIRState_init_zero}}
#define RFReedState_init_zero                    {0, _RFReedState_Status_MIN, 0, 0}
#define RFPresenceState_init_zero                {0, _RFPresenceState_Status_MIN, 0, 0}
#define ProvisioningState_init_zero              {0, 0, _ProvisioningState_ProvisioningErrorCode_MIN, 0, 0}
#define LightState_init_zero                     {0, 0, 0, 0, 0, 0, 0, 0, _LightState_TransitionStopReason_MIN}
#define ButtonState_init_zero                    {0, 0, _ButtonState_State_MIN, 0}
#define PIRState_init_zero                       {0, 0, 0, 0}
#define ESPStatus_init_zero                      {_ESPStatus_Status_MIN}
#define ESPUpdateConfig_init_zero                {false, ESPUpdateConfig_MACConfig_init_zero, false, ESPUpdateConfig_DHCPConfig_init_zero, false, ESPUpdateConfig_WifiConfig_init_zero, "", "", ""}
#define ESPUpdateConfig_MACConfig_init_zero      {0, ""}
#define ESPUpdateConfig_DHCPConfig_init_zero     {0, "", "", "", ""}
#define ESPUpdateConfig_WifiConfig_init_zero     {"", ""}

/* Field tags (for use in manual encoding/decoding) */
#define BasestationConfig_RFConfig_channel_tag   1
#define BasestationConfig_RFConfig_network_tag   2
#define BasestationConfig_WifiConfig_ssid_tag    1
#define BasestationConfig_WifiConfig_password_tag 2
#define BasestationConfig_MACConfig_use_mac_address_tag 1
#define BasestationConfig_MACConfig_mac_address_tag 2
#define BasestationConfig_DHCPConfig_static_ip_tag 1
#define BasestationConfig_DHCPConfig_ip_address_tag 2
#define BasestationConfig_DHCPConfig_subnet_mask_tag 3
#define BasestationConfig_DHCPConfig_gateway_tag 4
#define BasestationConfig_DHCPConfig_dns_server_tag 5
#define BasestationConfig_NodeQRMapping_qr_code_tag 1
#define BasestationConfig_NodeQRMapping_node_id_tag 2
#define BasestationConfig_NodeQRMapping_type_tag 3
#define Action_id_tag                            1
#define Action_light_id_tag                      2
#define Action_somfy_shade_id_tag                3
#define Action_dim_speed_msec_tag                4
#define Action_target_brightness_tag             5
#define Action_on_brightness_tag                 6
#define Action_off_brightness_tag                7
#define Action_delay_in_msec_tag                 8
#define Action_activate_delay_msec_tag           9
#define RFDimmerConfig_node_id_tag               1
#define RFDimmerConfig_middle_button_click_tag   2
#define RFDimmerConfig_up_button_click_tag       3
#define RFDimmerConfig_down_button_click_tag     4
#define RFDimmerConfig_middle_button_hold_tag    5
#define RFDimmerConfig_up_button_hold_tag        6
#define RFDimmerConfig_down_button_hold_tag      7
#define RFReedSensorConfig_node_id_tag           1
#define RFReedSensorConfig_door_close_tag        2
#define RFReedSensorConfig_door_open_tag         3
#define RFPresenceSensorConfig_node_id_tag       1
#define RFPresenceSensorConfig_on_activate_tag   2
#define RFPresenceSensorConfig_on_deactivate_tag 3
#define CanboConfig_ThreePinInput_ToggleConfig_up_click_tag 1
#define CanboConfig_ThreePinInput_ToggleConfig_up_hold_tag 2
#define CanboConfig_ThreePinInput_ToggleConfig_down_click_tag 3
#define CanboConfig_ThreePinInput_ToggleConfig_down_hold_tag 4
#define CanboConfig_ThreePinInput_ToggleConfig_up_press_tag 5
#define CanboConfig_ThreePinInput_ToggleConfig_up_release_tag 6
#define CanboConfig_ThreePinInput_ToggleConfig_down_press_tag 7
#define CanboConfig_ThreePinInput_ToggleConfig_down_release_tag 8
#define CanboConfig_ThreePinInput_ToggleConfig_up_hold_release_tag 9
#define CanboConfig_ThreePinInput_ToggleConfig_down_hold_release_tag 10
#define CanboConfig_ThreePinInput_MomentaryConfig_up_click_tag 1
#define CanboConfig_ThreePinInput_MomentaryConfig_up_hold_tag 2
#define CanboConfig_ThreePinInput_MomentaryConfig_up_press_tag 3
#define CanboConfig_ThreePinInput_MomentaryConfig_up_release_tag 4
#define CanboConfig_ThreePinInput_MomentaryConfig_up_hold_release_tag 5
#define CanboConfig_ThreePinInput_connector_id_tag 1
#define CanboConfig_ThreePinInput_type_tag       2
#define CanboConfig_ThreePinInput_toggle_tag     3
#define CanboConfig_ThreePinInput_momentary_tag  4
#define CanboConfig_TwoPinInput_MomentaryConfig_up_click_tag 1
#define CanboConfig_TwoPinInput_MomentaryConfig_up_hold_tag 2
#define CanboConfig_TwoPinInput_MomentaryConfig_up_press_tag 3
#define CanboConfig_TwoPinInput_MomentaryConfig_up_release_tag 4
#define CanboConfig_TwoPinInput_MomentaryConfig_up_hold_release_tag 5
#define CanboConfig_TwoPinInput_DoorSensorConfig_on_open_tag 1
#define CanboConfig_TwoPinInput_DoorSensorConfig_on_close_tag 2
#define CanboConfig_TwoPinInput_connector_id_tag 1
#define CanboConfig_TwoPinInput_type_tag         2
#define CanboConfig_TwoPinInput_momentary_tag    3
#define CanboConfig_TwoPinInput_door_sensor_tag  4
#define CanboConfig_ADCInput_KnobConfig_on_turn_tag 1
#define CanboConfig_ADCInput_ThermostatConfig_thermostat_action_tag 1
#define CanboConfig_ADCInput_PIRConfig_on_activate_tag 1
#define CanboConfig_ADCInput_PIRConfig_on_deactivate_tag 2
#define CanboConfig_ADCInput_connector_id_tag    1
#define CanboConfig_ADCInput_type_tag            2
#define CanboConfig_ADCInput_knob_tag            3
#define CanboConfig_ADCInput_thermostat_tag      4
#define CanboConfig_ADCInput_pir_tag             5
#define CanboConfig_Output_connector_id_tag      1
#define CanboConfig_Output_connector_type_tag    2
#define CanboConfig_node_id_tag                  1
#define CanboConfig_three_pin_inputs_tag         2
#define CanboConfig_two_pin_inputs_tag           3
#define CanboConfig_adc_inputs_tag               4
#define CanboConfig_outputs_tag                  5
#define LightConfig_FixtureConfig_DMXConfig_LightParams_min1_tag 1
#define LightConfig_FixtureConfig_DMXConfig_LightParams_max1_tag 2
#define LightConfig_FixtureConfig_DMXConfig_LightParams_gamma1_tag 3
#define LightConfig_FixtureConfig_DMXConfig_LightParams_min2_tag 4
#define LightConfig_FixtureConfig_DMXConfig_LightParams_max2_tag 5
#define LightConfig_FixtureConfig_DMXConfig_LightParams_gamma2_tag 6
#define LightConfig_FixtureConfig_DMXConfig_RGBConfig_red_tag 1
#define LightConfig_FixtureConfig_DMXConfig_RGBConfig_green_tag 2
#define LightConfig_FixtureConfig_DMXConfig_RGBConfig_blue_tag 3
#define LightConfig_FixtureConfig_DMXConfig_params_tag 1
#define LightConfig_FixtureConfig_DMXConfig_rgb_tag 2
#define LightConfig_FixtureConfig_DMXConfig_channels_tag 3
#define LightConfig_FixtureConfig_DMXConfig_type_tag 4
#define LightConfig_FixtureConfig_RFConfig_type_tag 1
#define LightConfig_FixtureConfig_RFConfig_node_id_tag 2
#define LightConfig_FixtureConfig_ZeroToTenVoltConfig_type_tag 1
#define LightConfig_FixtureConfig_ZeroToTenVoltConfig_node_id_tag 2
#define LightConfig_FixtureConfig_ZeroToTenVoltConfig_use_relay_tag 3
#define LightConfig_FixtureConfig_ZeroToTenVoltConfig_out_connector_id_tag 4
#define LightConfig_FixtureConfig_RelayConfig_node_id_tag 1
#define LightConfig_FixtureConfig_RelayConfig_out_connector_id_tag 2
#define LightConfig_FixtureConfig_min_brightness_tag 1
#define LightConfig_FixtureConfig_max_brightness_tag 2
#define LightConfig_FixtureConfig_type_tag       3
#define LightConfig_FixtureConfig_dmx_tag        4
#define LightConfig_FixtureConfig_rf_tag         5
#define LightConfig_FixtureConfig_zero_to_ten_volt_tag 6
#define LightConfig_FixtureConfig_relay_tag      7
#define LightConfig_id_tag                       1
#define LightConfig_dim_speed_msec_tag           2
#define LightConfig_fixtures_tag                 3
#define SomfyShadesConfig_internal_id_tag        1
#define SomfyShadesConfig_device_id_tag          2
#define BasestationConfig_id_tag                 1
#define BasestationConfig_version_tag            2
#define BasestationConfig_rf_config_tag          3
#define BasestationConfig_wifi_config_tag        4
#define BasestationConfig_mac_config_tag         5
#define BasestationConfig_dhcp_config_tag        6
#define BasestationConfig_server_address_tag     7
#define BasestationConfig_canbo_configs_tag      8
#define BasestationConfig_rf_reed_configs_tag    9
#define BasestationConfig_rf_presence_configs_tag 10
#define BasestationConfig_rf_dimmer_configs_tag  11
#define BasestationConfig_lights_tag             12
#define BasestationConfig_somfy_shades_tag       13
#define BasestationConfig_node_qr_mappings_tag   14
#define BasestationConfig_actions_tag            15
#define BasestationUpdateMessage_qr_code_tag     1
#define BasestationUpdateMessage_config_tag      2
#define RFReedState_node_id_tag                  1
#define RFReedState_sensor_status_tag            2
#define RFReedState_last_modified_time_tag       3
#define RFReedState_battery_voltage_tag          4
#define RFPresenceState_node_id_tag              1
#define RFPresenceState_sensor_status_tag        2
#define RFPresenceState_last_modified_time_tag   3
#define RFPresenceState_battery_voltage_tag      4
#define ProvisioningState_node_id_tag            1
#define ProvisioningState_is_provisioned_tag     2
#define ProvisioningState_error_code_tag         3
#define ProvisioningState_last_seen_time_tag     4
#define ProvisioningState_rssi_tag               5
#define LightState_id_tag                        1
#define LightState_brightness_tag                2
#define LightState_target_value_tag              3
#define LightState_dim_speed_msec_tag            4
#define LightState_last_modified_time_tag        5
#define LightState_active_after_time_tag         6
#define LightState_is_transitioning_tag          7
#define LightState_last_brightness_before_action_tag 8
#define LightState_last_transition_stop_reason_tag 9
#define ButtonState_node_id_tag                  1
#define ButtonState_connector_id_tag             2
#define ButtonState_current_state_tag            3
#define ButtonState_last_modified_time_tag       4
#define PIRState_node_id_tag                     1
#define PIRState_is_activated_tag                2
#define PIRState_last_activated_time_tag         3
#define PIRState_deactivate_after_time_tag       4
#define BasestationState_lights_tag              1
#define BasestationState_buttons_tag             2
#define BasestationState_provisioned_devices_tag 3
#define BasestationState_reeds_tag               4
#define BasestationState_presences_tag           5
#define BasestationState_pirs_tag                6
#define ActiveConfiguration_config_tag           1
#define ActiveConfiguration_state_tag            2
#define ESPStatus_status_tag                     1
#define ESPUpdateConfig_MACConfig_use_mac_address_tag 1
#define ESPUpdateConfig_MACConfig_mac_address_tag 2
#define ESPUpdateConfig_DHCPConfig_static_ip_tag 1
#define ESPUpdateConfig_DHCPConfig_ip_address_tag 2
#define ESPUpdateConfig_DHCPConfig_subnet_mask_tag 3
#define ESPUpdateConfig_DHCPConfig_gateway_tag   4
#define ESPUpdateConfig_DHCPConfig_dns_server_tag 5
#define ESPUpdateConfig_WifiConfig_ssid_tag      1
#define ESPUpdateConfig_WifiConfig_password_tag  2
#define ESPUpdateConfig_mac_config_tag           1
#define ESPUpdateConfig_dhcp_config_tag          2
#define ESPUpdateConfig_wifi_config_tag          3
#define ESPUpdateConfig_qr_code_tag              4
#define ESPUpdateConfig_server_address_tag       5
#define ESPUpdateConfig_config_version_tag       6

/* Struct field encoding specification for nanopb */
#define ActiveConfiguration_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  config,            1) \
X(a, STATIC,   OPTIONAL, MESSAGE,  state,             2)
#define ActiveConfiguration_CALLBACK NULL
#define ActiveConfiguration_DEFAULT NULL
#define ActiveConfiguration_config_MSGTYPE BasestationConfig
#define ActiveConfiguration_state_MSGTYPE BasestationState

#define BasestationUpdateMessage_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, STRING,   qr_code,           1) \
X(a, STATIC,   OPTIONAL, MESSAGE,  config,            2)
#define BasestationUpdateMessage_CALLBACK NULL
#define BasestationUpdateMessage_DEFAULT NULL
#define BasestationUpdateMessage_config_MSGTYPE BasestationConfig

#define BasestationConfig_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   id,                1) \
X(a, STATIC,   SINGULAR, STRING,   version,           2) \
X(a, STATIC,   OPTIONAL, MESSAGE,  rf_config,         3) \
X(a, STATIC,   OPTIONAL, MESSAGE,  wifi_config,       4) \
X(a, STATIC,   OPTIONAL, MESSAGE,  mac_config,        5) \
X(a, STATIC,   OPTIONAL, MESSAGE,  dhcp_config,       6) \
X(a, STATIC,   SINGULAR, STRING,   server_address,    7) \
X(a, STATIC,   REPEATED, MESSAGE,  canbo_configs,     8) \
X(a, STATIC,   REPEATED, MESSAGE,  rf_reed_configs,   9) \
X(a, STATIC,   REPEATED, MESSAGE,  rf_presence_configs,  10) \
X(a, STATIC,   REPEATED, MESSAGE,  rf_dimmer_configs,  11) \
X(a, STATIC,   REPEATED, MESSAGE,  lights,           12) \
X(a, STATIC,   REPEATED, MESSAGE,  somfy_shades,     13) \
X(a, STATIC,   REPEATED, MESSAGE,  node_qr_mappings,  14) \
X(a, STATIC,   REPEATED, MESSAGE,  actions,          15)
#define BasestationConfig_CALLBACK NULL
#define BasestationConfig_DEFAULT NULL
#define BasestationConfig_rf_config_MSGTYPE BasestationConfig_RFConfig
#define BasestationConfig_wifi_config_MSGTYPE BasestationConfig_WifiConfig
#define BasestationConfig_mac_config_MSGTYPE BasestationConfig_MACConfig
#define BasestationConfig_dhcp_config_MSGTYPE BasestationConfig_DHCPConfig
#define BasestationConfig_canbo_configs_MSGTYPE CanboConfig
#define BasestationConfig_rf_reed_configs_MSGTYPE RFReedSensorConfig
#define BasestationConfig_rf_presence_configs_MSGTYPE RFPresenceSensorConfig
#define BasestationConfig_rf_dimmer_configs_MSGTYPE RFDimmerConfig
#define BasestationConfig_lights_MSGTYPE LightConfig
#define BasestationConfig_somfy_shades_MSGTYPE SomfyShadesConfig
#define BasestationConfig_node_qr_mappings_MSGTYPE BasestationConfig_NodeQRMapping
#define BasestationConfig_actions_MSGTYPE Action

#define BasestationConfig_RFConfig_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   channel,           1) \
X(a, STATIC,   SINGULAR, UINT32,   network,           2)
#define BasestationConfig_RFConfig_CALLBACK NULL
#define BasestationConfig_RFConfig_DEFAULT NULL

#define BasestationConfig_WifiConfig_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, STRING,   ssid,              1) \
X(a, STATIC,   SINGULAR, STRING,   password,          2)
#define BasestationConfig_WifiConfig_CALLBACK NULL
#define BasestationConfig_WifiConfig_DEFAULT NULL

#define BasestationConfig_MACConfig_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BOOL,     use_mac_address,   1) \
X(a, STATIC,   SINGULAR, STRING,   mac_address,       2)
#define BasestationConfig_MACConfig_CALLBACK NULL
#define BasestationConfig_MACConfig_DEFAULT NULL

#define BasestationConfig_DHCPConfig_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BOOL,     static_ip,         1) \
X(a, STATIC,   SINGULAR, STRING,   ip_address,        2) \
X(a, STATIC,   SINGULAR, STRING,   subnet_mask,       3) \
X(a, STATIC,   SINGULAR, STRING,   gateway,           4) \
X(a, STATIC,   SINGULAR, STRING,   dns_server,        5)
#define BasestationConfig_DHCPConfig_CALLBACK NULL
#define BasestationConfig_DHCPConfig_DEFAULT NULL

#define BasestationConfig_NodeQRMapping_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, STRING,   qr_code,           1) \
X(a, STATIC,   SINGULAR, UINT32,   node_id,           2) \
X(a, STATIC,   SINGULAR, UENUM,    type,              3)
#define BasestationConfig_NodeQRMapping_CALLBACK NULL
#define BasestationConfig_NodeQRMapping_DEFAULT NULL

#define Action_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   id,                1) \
X(a, STATIC,   ONEOF,    UINT32,   (target,light_id,target.light_id),   2) \
X(a, STATIC,   ONEOF,    UINT32,   (target,somfy_shade_id,target.somfy_shade_id),   3) \
X(a, STATIC,   SINGULAR, UINT32,   dim_speed_msec,    4) \
X(a, STATIC,   SINGULAR, FLOAT,    target_brightness,   5) \
X(a, STATIC,   SINGULAR, FLOAT,    on_brightness,     6) \
X(a, STATIC,   SINGULAR, FLOAT,    off_brightness,    7) \
X(a, STATIC,   SINGULAR, UINT32,   delay_in_msec,     8) \
X(a, STATIC,   SINGULAR, UINT32,   activate_delay_msec,   9)
#define Action_CALLBACK NULL
#define Action_DEFAULT NULL

#define RFDimmerConfig_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   node_id,           1) \
X(a, STATIC,   REPEATED, UINT32,   middle_button_click,   2) \
X(a, STATIC,   REPEATED, UINT32,   up_button_click,   3) \
X(a, STATIC,   REPEATED, UINT32,   down_button_click,   4) \
X(a, STATIC,   REPEATED, UINT32,   middle_button_hold,   5) \
X(a, STATIC,   REPEATED, UINT32,   up_button_hold,    6) \
X(a, STATIC,   REPEATED, UINT32,   down_button_hold,   7)
#define RFDimmerConfig_CALLBACK NULL
#define RFDimmerConfig_DEFAULT NULL

#define RFReedSensorConfig_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   node_id,           1) \
X(a, STATIC,   REPEATED, UINT32,   door_close,        2) \
X(a, STATIC,   REPEATED, UINT32,   door_open,         3)
#define RFReedSensorConfig_CALLBACK NULL
#define RFReedSensorConfig_DEFAULT NULL

#define RFPresenceSensorConfig_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   node_id,           1) \
X(a, STATIC,   REPEATED, UINT32,   on_activate,       2) \
X(a, STATIC,   REPEATED, UINT32,   on_deactivate,     3)
#define RFPresenceSensorConfig_CALLBACK NULL
#define RFPresenceSensorConfig_DEFAULT NULL

#define CanboConfig_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   node_id,           1) \
X(a, STATIC,   REPEATED, MESSAGE,  three_pin_inputs,   2) \
X(a, STATIC,   REPEATED, MESSAGE,  two_pin_inputs,    3) \
X(a, STATIC,   OPTIONAL, MESSAGE,  adc_inputs,        4) \
X(a, STATIC,   REPEATED, MESSAGE,  outputs,           5)
#define CanboConfig_CALLBACK NULL
#define CanboConfig_DEFAULT NULL
#define CanboConfig_three_pin_inputs_MSGTYPE CanboConfig_ThreePinInput
#define CanboConfig_two_pin_inputs_MSGTYPE CanboConfig_TwoPinInput
#define CanboConfig_adc_inputs_MSGTYPE CanboConfig_ADCInput
#define CanboConfig_outputs_MSGTYPE CanboConfig_Output

#define CanboConfig_ThreePinInput_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   connector_id,      1) \
X(a, STATIC,   SINGULAR, UENUM,    type,              2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (config,toggle,config.toggle),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (config,momentary,config.momentary),   4)
#define CanboConfig_ThreePinInput_CALLBACK NULL
#define CanboConfig_ThreePinInput_DEFAULT NULL
#define CanboConfig_ThreePinInput_config_toggle_MSGTYPE CanboConfig_ThreePinInput_ToggleConfig
#define CanboConfig_ThreePinInput_config_momentary_MSGTYPE CanboConfig_ThreePinInput_MomentaryConfig

#define CanboConfig_ThreePinInput_ToggleConfig_FIELDLIST(X, a) \
X(a, STATIC,   REPEATED, UINT32,   up_click,          1) \
X(a, STATIC,   REPEATED, UINT32,   up_hold,           2) \
X(a, STATIC,   REPEATED, UINT32,   down_click,        3) \
X(a, STATIC,   REPEATED, UINT32,   down_hold,         4) \
X(a, STATIC,   REPEATED, UINT32,   up_press,          5) \
X(a, STATIC,   REPEATED, UINT32,   up_release,        6) \
X(a, STATIC,   REPEATED, UINT32,   down_press,        7) \
X(a, STATIC,   REPEATED, UINT32,   down_release,      8) \
X(a, STATIC,   REPEATED, UINT32,   up_hold_release,   9) \
X(a, STATIC,   REPEATED, UINT32,   down_hold_release,  10)
#define CanboConfig_ThreePinInput_ToggleConfig_CALLBACK NULL
#define CanboConfig_ThreePinInput_ToggleConfig_DEFAULT NULL

#define CanboConfig_ThreePinInput_MomentaryConfig_FIELDLIST(X, a) \
X(a, STATIC,   REPEATED, UINT32,   up_click,          1) \
X(a, STATIC,   REPEATED, UINT32,   up_hold,           2) \
X(a, STATIC,   REPEATED, UINT32,   up_press,          3) \
X(a, STATIC,   REPEATED, UINT32,   up_release,        4) \
X(a, STATIC,   REPEATED, UINT32,   up_hold_release,   5)
#define CanboConfig_ThreePinInput_MomentaryConfig_CALLBACK NULL
#define CanboConfig_ThreePinInput_MomentaryConfig_DEFAULT NULL

#define CanboConfig_TwoPinInput_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   connector_id,      1) \
X(a, STATIC,   SINGULAR, UENUM,    type,              2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (config,momentary,config.momentary),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (config,door_sensor,config.door_sensor),   4)
#define CanboConfig_TwoPinInput_CALLBACK NULL
#define CanboConfig_TwoPinInput_DEFAULT NULL
#define CanboConfig_TwoPinInput_config_momentary_MSGTYPE CanboConfig_TwoPinInput_MomentaryConfig
#define CanboConfig_TwoPinInput_config_door_sensor_MSGTYPE CanboConfig_TwoPinInput_DoorSensorConfig

#define CanboConfig_TwoPinInput_MomentaryConfig_FIELDLIST(X, a) \
X(a, STATIC,   REPEATED, UINT32,   up_click,          1) \
X(a, STATIC,   REPEATED, UINT32,   up_hold,           2) \
X(a, STATIC,   REPEATED, UINT32,   up_press,          3) \
X(a, STATIC,   REPEATED, UINT32,   up_release,        4) \
X(a, STATIC,   REPEATED, UINT32,   up_hold_release,   5)
#define CanboConfig_TwoPinInput_MomentaryConfig_CALLBACK NULL
#define CanboConfig_TwoPinInput_MomentaryConfig_DEFAULT NULL

#define CanboConfig_TwoPinInput_DoorSensorConfig_FIELDLIST(X, a) \
X(a, STATIC,   REPEATED, UINT32,   on_open,           1) \
X(a, STATIC,   REPEATED, UINT32,   on_close,          2)
#define CanboConfig_TwoPinInput_DoorSensorConfig_CALLBACK NULL
#define CanboConfig_TwoPinInput_DoorSensorConfig_DEFAULT NULL

#define CanboConfig_ADCInput_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   connector_id,      1) \
X(a, STATIC,   SINGULAR, UENUM,    type,              2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (config,knob,config.knob),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (config,thermostat,config.thermostat),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (config,pir,config.pir),   5)
#define CanboConfig_ADCInput_CALLBACK NULL
#define CanboConfig_ADCInput_DEFAULT NULL
#define CanboConfig_ADCInput_config_knob_MSGTYPE CanboConfig_ADCInput_KnobConfig
#define CanboConfig_ADCInput_config_thermostat_MSGTYPE CanboConfig_ADCInput_ThermostatConfig
#define CanboConfig_ADCInput_config_pir_MSGTYPE CanboConfig_ADCInput_PIRConfig

#define CanboConfig_ADCInput_KnobConfig_FIELDLIST(X, a) \
X(a, STATIC,   REPEATED, UINT32,   on_turn,           1)
#define CanboConfig_ADCInput_KnobConfig_CALLBACK NULL
#define CanboConfig_ADCInput_KnobConfig_DEFAULT NULL

#define CanboConfig_ADCInput_ThermostatConfig_FIELDLIST(X, a) \
X(a, STATIC,   REPEATED, UINT32,   thermostat_action,   1)
#define CanboConfig_ADCInput_ThermostatConfig_CALLBACK NULL
#define CanboConfig_ADCInput_ThermostatConfig_DEFAULT NULL

#define CanboConfig_ADCInput_PIRConfig_FIELDLIST(X, a) \
X(a, STATIC,   REPEATED, UINT32,   on_activate,       1) \
X(a, STATIC,   REPEATED, UINT32,   on_deactivate,     2)
#define CanboConfig_ADCInput_PIRConfig_CALLBACK NULL
#define CanboConfig_ADCInput_PIRConfig_DEFAULT NULL

#define CanboConfig_Output_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   connector_id,      1) \
X(a, STATIC,   SINGULAR, UENUM,    connector_type,    2)
#define CanboConfig_Output_CALLBACK NULL
#define CanboConfig_Output_DEFAULT NULL

#define LightConfig_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   id,                1) \
X(a, STATIC,   SINGULAR, UINT32,   dim_speed_msec,    2) \
X(a, STATIC,   REPEATED, MESSAGE,  fixtures,          3)
#define LightConfig_CALLBACK NULL
#define LightConfig_DEFAULT NULL
#define LightConfig_fixtures_MSGTYPE LightConfig_FixtureConfig

#define LightConfig_FixtureConfig_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, FLOAT,    min_brightness,    1) \
X(a, STATIC,   SINGULAR, FLOAT,    max_brightness,    2) \
X(a, STATIC,   SINGULAR, UENUM,    type,              3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (config,dmx,config.dmx),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (config,rf,config.rf),   5) \
X(a, STATIC,   ONEOF,    MESSAGE,  (config,zero_to_ten_volt,config.zero_to_ten_volt),   6) \
X(a, STATIC,   ONEOF,    MESSAGE,  (config,relay,config.relay),   7)
#define LightConfig_FixtureConfig_CALLBACK NULL
#define LightConfig_FixtureConfig_DEFAULT NULL
#define LightConfig_FixtureConfig_config_dmx_MSGTYPE LightConfig_FixtureConfig_DMXConfig
#define LightConfig_FixtureConfig_config_rf_MSGTYPE LightConfig_FixtureConfig_RFConfig
#define LightConfig_FixtureConfig_config_zero_to_ten_volt_MSGTYPE LightConfig_FixtureConfig_ZeroToTenVoltConfig
#define LightConfig_FixtureConfig_config_relay_MSGTYPE LightConfig_FixtureConfig_RelayConfig

#define LightConfig_FixtureConfig_DMXConfig_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  params,            1) \
X(a, STATIC,   OPTIONAL, MESSAGE,  rgb,               2) \
X(a, STATIC,   REPEATED, UINT32,   channels,          3) \
X(a, STATIC,   SINGULAR, UENUM,    type,              4)
#define LightConfig_FixtureConfig_DMXConfig_CALLBACK NULL
#define LightConfig_FixtureConfig_DMXConfig_DEFAULT NULL
#define LightConfig_FixtureConfig_DMXConfig_params_MSGTYPE LightConfig_FixtureConfig_DMXConfig_LightParams
#define LightConfig_FixtureConfig_DMXConfig_rgb_MSGTYPE LightConfig_FixtureConfig_DMXConfig_RGBConfig

#define LightConfig_FixtureConfig_DMXConfig_LightParams_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, FLOAT,    min1,              1) \
X(a, STATIC,   SINGULAR, FLOAT,    max1,              2) \
X(a, STATIC,   SINGULAR, FLOAT,    gamma1,            3) \
X(a, STATIC,   SINGULAR, FLOAT,    min2,              4) \
X(a, STATIC,   SINGULAR, FLOAT,    max2,              5) \
X(a, STATIC,   SINGULAR, FLOAT,    gamma2,            6)
#define LightConfig_FixtureConfig_DMXConfig_LightParams_CALLBACK NULL
#define LightConfig_FixtureConfig_DMXConfig_LightParams_DEFAULT NULL

#define LightConfig_FixtureConfig_DMXConfig_RGBConfig_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   red,               1) \
X(a, STATIC,   SINGULAR, UINT32,   green,             2) \
X(a, STATIC,   SINGULAR, UINT32,   blue,              3)
#define LightConfig_FixtureConfig_DMXConfig_RGBConfig_CALLBACK NULL
#define LightConfig_FixtureConfig_DMXConfig_RGBConfig_DEFAULT NULL

#define LightConfig_FixtureConfig_RFConfig_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UENUM,    type,              1) \
X(a, STATIC,   SINGULAR, UINT32,   node_id,           2)
#define LightConfig_FixtureConfig_RFConfig_CALLBACK NULL
#define LightConfig_FixtureConfig_RFConfig_DEFAULT NULL

#define LightConfig_FixtureConfig_ZeroToTenVoltConfig_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UENUM,    type,              1) \
X(a, STATIC,   SINGULAR, UINT32,   node_id,           2) \
X(a, STATIC,   SINGULAR, BOOL,     use_relay,         3) \
X(a, STATIC,   SINGULAR, UINT32,   out_connector_id,   4)
#define LightConfig_FixtureConfig_ZeroToTenVoltConfig_CALLBACK NULL
#define LightConfig_FixtureConfig_ZeroToTenVoltConfig_DEFAULT NULL

#define LightConfig_FixtureConfig_RelayConfig_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   node_id,           1) \
X(a, STATIC,   SINGULAR, UINT32,   out_connector_id,   2)
#define LightConfig_FixtureConfig_RelayConfig_CALLBACK NULL
#define LightConfig_FixtureConfig_RelayConfig_DEFAULT NULL

#define SomfyShadesConfig_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   internal_id,       1) \
X(a, STATIC,   SINGULAR, UINT32,   device_id,         2)
#define SomfyShadesConfig_CALLBACK NULL
#define SomfyShadesConfig_DEFAULT NULL

#define BasestationState_FIELDLIST(X, a) \
X(a, STATIC,   REPEATED, MESSAGE,  lights,            1) \
X(a, STATIC,   REPEATED, MESSAGE,  buttons,           2) \
X(a, STATIC,   REPEATED, MESSAGE,  provisioned_devices,   3) \
X(a, STATIC,   REPEATED, MESSAGE,  reeds,             4) \
X(a, STATIC,   REPEATED, MESSAGE,  presences,         5) \
X(a, STATIC,   REPEATED, MESSAGE,  pirs,              6)
#define BasestationState_CALLBACK NULL
#define BasestationState_DEFAULT NULL
#define BasestationState_lights_MSGTYPE LightState
#define BasestationState_buttons_MSGTYPE ButtonState
#define BasestationState_provisioned_devices_MSGTYPE ProvisioningState
#define BasestationState_reeds_MSGTYPE RFReedState
#define BasestationState_presences_MSGTYPE RFPresenceState
#define BasestationState_pirs_MSGTYPE PIRState

#define RFReedState_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   node_id,           1) \
X(a, STATIC,   SINGULAR, UENUM,    sensor_status,     2) \
X(a, STATIC,   SINGULAR, UINT64,   last_modified_time,   3) \
X(a, STATIC,   SINGULAR, FLOAT,    battery_voltage,   4)
#define RFReedState_CALLBACK NULL
#define RFReedState_DEFAULT NULL

#define RFPresenceState_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   node_id,           1) \
X(a, STATIC,   SINGULAR, UENUM,    sensor_status,     2) \
X(a, STATIC,   SINGULAR, UINT64,   last_modified_time,   3) \
X(a, STATIC,   SINGULAR, FLOAT,    battery_voltage,   4)
#define RFPresenceState_CALLBACK NULL
#define RFPresenceState_DEFAULT NULL

#define ProvisioningState_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   node_id,           1) \
X(a, STATIC,   SINGULAR, BOOL,     is_provisioned,    2) \
X(a, STATIC,   SINGULAR, UENUM,    error_code,        3) \
X(a, STATIC,   SINGULAR, UINT64,   last_seen_time,    4) \
X(a, STATIC,   SINGULAR, INT32,    rssi,              5)
#define ProvisioningState_CALLBACK NULL
#define ProvisioningState_DEFAULT NULL

#define LightState_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   id,                1) \
X(a, STATIC,   SINGULAR, FLOAT,    brightness,        2) \
X(a, STATIC,   SINGULAR, FLOAT,    target_value,      3) \
X(a, STATIC,   SINGULAR, UINT32,   dim_speed_msec,    4) \
X(a, STATIC,   SINGULAR, UINT64,   last_modified_time,   5) \
X(a, STATIC,   SINGULAR, UINT64,   active_after_time,   6) \
X(a, STATIC,   SINGULAR, BOOL,     is_transitioning,   7) \
X(a, STATIC,   SINGULAR, FLOAT,    last_brightness_before_action,   8) \
X(a, STATIC,   SINGULAR, UENUM,    last_transition_stop_reason,   9)
#define LightState_CALLBACK NULL
#define LightState_DEFAULT NULL

#define ButtonState_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   node_id,           1) \
X(a, STATIC,   SINGULAR, UINT32,   connector_id,      2) \
X(a, STATIC,   SINGULAR, UENUM,    current_state,     3) \
X(a, STATIC,   SINGULAR, UINT64,   last_modified_time,   4)
#define ButtonState_CALLBACK NULL
#define ButtonState_DEFAULT NULL

#define PIRState_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   node_id,           1) \
X(a, STATIC,   SINGULAR, BOOL,     is_activated,      2) \
X(a, STATIC,   SINGULAR, UINT64,   last_activated_time,   3) \
X(a, STATIC,   SINGULAR, UINT64,   deactivate_after_time,   4)
#define PIRState_CALLBACK NULL
#define PIRState_DEFAULT NULL

#define ESPStatus_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UENUM,    status,            1)
#define ESPStatus_CALLBACK NULL
#define ESPStatus_DEFAULT NULL

#define ESPUpdateConfig_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  mac_config,        1) \
X(a, STATIC,   OPTIONAL, MESSAGE,  dhcp_config,       2) \
X(a, STATIC,   OPTIONAL, MESSAGE,  wifi_config,       3) \
X(a, STATIC,   SINGULAR, STRING,   qr_code,           4) \
X(a, STATIC,   SINGULAR, STRING,   server_address,    5) \
X(a, STATIC,   SINGULAR, STRING,   config_version,    6)
#define ESPUpdateConfig_CALLBACK NULL
#define ESPUpdateConfig_DEFAULT NULL
#define ESPUpdateConfig_mac_config_MSGTYPE ESPUpdateConfig_MACConfig
#define ESPUpdateConfig_dhcp_config_MSGTYPE ESPUpdateConfig_DHCPConfig
#define ESPUpdateConfig_wifi_config_MSGTYPE ESPUpdateConfig_WifiConfig

#define ESPUpdateConfig_MACConfig_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BOOL,     use_mac_address,   1) \
X(a, STATIC,   SINGULAR, STRING,   mac_address,       2)
#define ESPUpdateConfig_MACConfig_CALLBACK NULL
#define ESPUpdateConfig_MACConfig_DEFAULT NULL

#define ESPUpdateConfig_DHCPConfig_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BOOL,     static_ip,         1) \
X(a, STATIC,   SINGULAR, STRING,   ip_address,        2) \
X(a, STATIC,   SINGULAR, STRING,   subnet_mask,       3) \
X(a, STATIC,   SINGULAR, STRING,   gateway,           4) \
X(a, STATIC,   SINGULAR, STRING,   dns_server,        5)
#define ESPUpdateConfig_DHCPConfig_CALLBACK NULL
#define ESPUpdateConfig_DHCPConfig_DEFAULT NULL

#define ESPUpdateConfig_WifiConfig_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, STRING,   ssid,              1) \
X(a, STATIC,   SINGULAR, STRING,   password,          2)
#define ESPUpdateConfig_WifiConfig_CALLBACK NULL
#define ESPUpdateConfig_WifiConfig_DEFAULT NULL

extern const pb_msgdesc_t ActiveConfiguration_msg;
extern const pb_msgdesc_t BasestationUpdateMessage_msg;
extern const pb_msgdesc_t BasestationConfig_msg;
extern const pb_msgdesc_t BasestationConfig_RFConfig_msg;
extern const pb_msgdesc_t BasestationConfig_WifiConfig_msg;
extern const pb_msgdesc_t BasestationConfig_MACConfig_msg;
extern const pb_msgdesc_t BasestationConfig_DHCPConfig_msg;
extern const pb_msgdesc_t BasestationConfig_NodeQRMapping_msg;
extern const pb_msgdesc_t Action_msg;
extern const pb_msgdesc_t RFDimmerConfig_msg;
extern const pb_msgdesc_t RFReedSensorConfig_msg;
extern const pb_msgdesc_t RFPresenceSensorConfig_msg;
extern const pb_msgdesc_t CanboConfig_msg;
extern const pb_msgdesc_t CanboConfig_ThreePinInput_msg;
extern const pb_msgdesc_t CanboConfig_ThreePinInput_ToggleConfig_msg;
extern const pb_msgdesc_t CanboConfig_ThreePinInput_MomentaryConfig_msg;
extern const pb_msgdesc_t CanboConfig_TwoPinInput_msg;
extern const pb_msgdesc_t CanboConfig_TwoPinInput_MomentaryConfig_msg;
extern const pb_msgdesc_t CanboConfig_TwoPinInput_DoorSensorConfig_msg;
extern const pb_msgdesc_t CanboConfig_ADCInput_msg;
extern const pb_msgdesc_t CanboConfig_ADCInput_KnobConfig_msg;
extern const pb_msgdesc_t CanboConfig_ADCInput_ThermostatConfig_msg;
extern const pb_msgdesc_t CanboConfig_ADCInput_PIRConfig_msg;
extern const pb_msgdesc_t CanboConfig_Output_msg;
extern const pb_msgdesc_t LightConfig_msg;
extern const pb_msgdesc_t LightConfig_FixtureConfig_msg;
extern const pb_msgdesc_t LightConfig_FixtureConfig_DMXConfig_msg;
extern const pb_msgdesc_t LightConfig_FixtureConfig_DMXConfig_LightParams_msg;
extern const pb_msgdesc_t LightConfig_FixtureConfig_DMXConfig_RGBConfig_msg;
extern const pb_msgdesc_t LightConfig_FixtureConfig_RFConfig_msg;
extern const pb_msgdesc_t LightConfig_FixtureConfig_ZeroToTenVoltConfig_msg;
extern const pb_msgdesc_t LightConfig_FixtureConfig_RelayConfig_msg;
extern const pb_msgdesc_t SomfyShadesConfig_msg;
extern const pb_msgdesc_t BasestationState_msg;
extern const pb_msgdesc_t RFReedState_msg;
extern const pb_msgdesc_t RFPresenceState_msg;
extern const pb_msgdesc_t ProvisioningState_msg;
extern const pb_msgdesc_t LightState_msg;
extern const pb_msgdesc_t ButtonState_msg;
extern const pb_msgdesc_t PIRState_msg;
extern const pb_msgdesc_t ESPStatus_msg;
extern const pb_msgdesc_t ESPUpdateConfig_msg;
extern const pb_msgdesc_t ESPUpdateConfig_MACConfig_msg;
extern const pb_msgdesc_t ESPUpdateConfig_DHCPConfig_msg;
extern const pb_msgdesc_t ESPUpdateConfig_WifiConfig_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define ActiveConfiguration_fields &ActiveConfiguration_msg
#define BasestationUpdateMessage_fields &BasestationUpdateMessage_msg
#define BasestationConfig_fields &BasestationConfig_msg
#define BasestationConfig_RFConfig_fields &BasestationConfig_RFConfig_msg
#define BasestationConfig_WifiConfig_fields &BasestationConfig_WifiConfig_msg
#define BasestationConfig_MACConfig_fields &BasestationConfig_MACConfig_msg
#define BasestationConfig_DHCPConfig_fields &BasestationConfig_DHCPConfig_msg
#define BasestationConfig_NodeQRMapping_fields &BasestationConfig_NodeQRMapping_msg
#define Action_fields &Action_msg
#define RFDimmerConfig_fields &RFDimmerConfig_msg
#define RFReedSensorConfig_fields &RFReedSensorConfig_msg
#define RFPresenceSensorConfig_fields &RFPresenceSensorConfig_msg
#define CanboConfig_fields &CanboConfig_msg
#define CanboConfig_ThreePinInput_fields &CanboConfig_ThreePinInput_msg
#define CanboConfig_ThreePinInput_ToggleConfig_fields &CanboConfig_ThreePinInput_ToggleConfig_msg
#define CanboConfig_ThreePinInput_MomentaryConfig_fields &CanboConfig_ThreePinInput_MomentaryConfig_msg
#define CanboConfig_TwoPinInput_fields &CanboConfig_TwoPinInput_msg
#define CanboConfig_TwoPinInput_MomentaryConfig_fields &CanboConfig_TwoPinInput_MomentaryConfig_msg
#define CanboConfig_TwoPinInput_DoorSensorConfig_fields &CanboConfig_TwoPinInput_DoorSensorConfig_msg
#define CanboConfig_ADCInput_fields &CanboConfig_ADCInput_msg
#define CanboConfig_ADCInput_KnobConfig_fields &CanboConfig_ADCInput_KnobConfig_msg
#define CanboConfig_ADCInput_ThermostatConfig_fields &CanboConfig_ADCInput_ThermostatConfig_msg
#define CanboConfig_ADCInput_PIRConfig_fields &CanboConfig_ADCInput_PIRConfig_msg
#define CanboConfig_Output_fields &CanboConfig_Output_msg
#define LightConfig_fields &LightConfig_msg
#define LightConfig_FixtureConfig_fields &LightConfig_FixtureConfig_msg
#define LightConfig_FixtureConfig_DMXConfig_fields &LightConfig_FixtureConfig_DMXConfig_msg
#define LightConfig_FixtureConfig_DMXConfig_LightParams_fields &LightConfig_FixtureConfig_DMXConfig_LightParams_msg
#define LightConfig_FixtureConfig_DMXConfig_RGBConfig_fields &LightConfig_FixtureConfig_DMXConfig_RGBConfig_msg
#define LightConfig_FixtureConfig_RFConfig_fields &LightConfig_FixtureConfig_RFConfig_msg
#define LightConfig_FixtureConfig_ZeroToTenVoltConfig_fields &LightConfig_FixtureConfig_ZeroToTenVoltConfig_msg
#define LightConfig_FixtureConfig_RelayConfig_fields &LightConfig_FixtureConfig_RelayConfig_msg
#define SomfyShadesConfig_fields &SomfyShadesConfig_msg
#define BasestationState_fields &BasestationState_msg
#define RFReedState_fields &RFReedState_msg
#define RFPresenceState_fields &RFPresenceState_msg
#define ProvisioningState_fields &ProvisioningState_msg
#define LightState_fields &LightState_msg
#define ButtonState_fields &ButtonState_msg
#define PIRState_fields &PIRState_msg
#define ESPStatus_fields &ESPStatus_msg
#define ESPUpdateConfig_fields &ESPUpdateConfig_msg
#define ESPUpdateConfig_MACConfig_fields &ESPUpdateConfig_MACConfig_msg
#define ESPUpdateConfig_DHCPConfig_fields &ESPUpdateConfig_DHCPConfig_msg
#define ESPUpdateConfig_WifiConfig_fields &ESPUpdateConfig_WifiConfig_msg

/* Maximum encoded size of messages (where known) */
#define Action_size                              40
#define ActiveConfiguration_size                 58473
#define BASESTATION_CONFIG_PB_H_MAX_SIZE         ActiveConfiguration_size
#define BasestationConfig_DHCPConfig_size        70
#define BasestationConfig_MACConfig_size         21
#define BasestationConfig_NodeQRMapping_size     19
#define BasestationConfig_RFConfig_size          7
#define BasestationConfig_WifiConfig_size        68
#define BasestationConfig_size                   56256
#define BasestationState_size                    2210
#define BasestationUpdateMessage_size            56274
#define ButtonState_size                         19
#define CanboConfig_ADCInput_KnobConfig_size     20
#define CanboConfig_ADCInput_PIRConfig_size      40
#define CanboConfig_ADCInput_ThermostatConfig_size 20
#define CanboConfig_ADCInput_size                47
#define CanboConfig_Output_size                  5
#define CanboConfig_ThreePinInput_MomentaryConfig_size 100
#define CanboConfig_ThreePinInput_ToggleConfig_size 200
#define CanboConfig_ThreePinInput_size           208
#define CanboConfig_TwoPinInput_DoorSensorConfig_size 40
#define CanboConfig_TwoPinInput_MomentaryConfig_size 100
#define CanboConfig_TwoPinInput_size             107
#define CanboConfig_size                         1142
#define ESPStatus_size                           2
#define ESPUpdateConfig_DHCPConfig_size          70
#define ESPUpdateConfig_MACConfig_size           21
#define ESPUpdateConfig_WifiConfig_size          68
#define ESPUpdateConfig_size                     320
#define LightConfig_FixtureConfig_DMXConfig_LightParams_size 30
#define LightConfig_FixtureConfig_DMXConfig_RGBConfig_size 9
#define LightConfig_FixtureConfig_DMXConfig_size 75
#define LightConfig_FixtureConfig_RFConfig_size  5
#define LightConfig_FixtureConfig_RelayConfig_size 6
#define LightConfig_FixtureConfig_ZeroToTenVoltConfig_size 10
#define LightConfig_FixtureConfig_size           89
#define LightConfig_size                         464
#define LightState_size                          50
#define PIRState_size                            27
#define ProvisioningState_size                   29
#define RFDimmerConfig_size                      123
#define RFPresenceSensorConfig_size              43
#define RFPresenceState_size                     21
#define RFReedSensorConfig_size                  43
#define RFReedState_size                         21
#define SomfyShadesConfig_size                   6

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif
