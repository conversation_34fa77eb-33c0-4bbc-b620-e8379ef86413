import { match } from "ts-pattern";
import { SomfyShadesBaseDevice } from "../components/devices/BaseStation";
import {
  RelayOutputDevice,
  ZeroToTenVoltDimmerDevice,
} from "../components/devices/CanBusController";
import { DmxDevice } from "../components/devices/Dmx";
import { SomoShadesDevice } from "../components/devices/SomoShades";
import {
  filterNodes,
  GraphNode,
  GraphNodesByType,
  LayoutContainerNode,
} from "../components/GraphNode";
import { RoomDimmerDevice } from "../components/RoomDimmer";
import { RoomSwitchDevice } from "../components/RoomSwitch";
import {
  Action,
  ActiveConfiguration,
  BasestationConfig_NodeQRMapping,
  BasestationConfig_NodeQRMapping_DeviceType,
  CanboConfig,
  LightConfig,
  LightConfig_FixtureConfig,
  LightConfig_FixtureConfig_DMXConfig_FixtureTypeConfig,
  LightConfig_FixtureConfig_FixtureType,
  LightState,
  RFDimmerConfig,
  RFPresenceSensorConfig,
  RFPresenceState,
  RFPresenceState_Status,
  RFReedSensorConfig,
  RFReedState,
  RFReedState_Status,
  SomfyShadesConfig,
} from "../proto/basestation-config";

interface Params {
  name: string;
  roomId: string;
  version: string;
  nodes: GraphNode[];
  wifiConfig?: {
    ssid: string;
    password: string;
  };
  serverAddress?: string;
  rfConfig?: {
    channel: number;
    network: number;
  };
  macConfig?: {
    useMacAddress: boolean;
    macAddress: string;
  };
  dhcpConfig?: {
    staticIp: boolean;
    ipAddress: string;
    subnetMask: string;
    gateway: string;
    dnsServer: string;
  };
  nodeQrMappings?: {
    deviceId: string;
    qrCode: string;
  }[];
  somfyShadesDeviceIdMapping?: {
    baseStationId: string;
    shadeKey: string;
    deviceId: number;
  }[];
}

/**
 * Converts our graph into a data stream to update the basestation.
 *
 * It uses [ts-proto](https://github.com/stephenh/ts-proto) to generate the data
 * for the basestation, according to its protobuf definition.
 */
export function getActiveConfigurationForBasestation(params: Params) {
  const lightsIds = collectLightsIds(params.nodes);
  const shadesIds = collectShadesIds(params.nodes);
  const actionManager = createActionManager();

  const nodeQrMappings = createNodeQrMappings(
    params.nodes,
    params.nodeQrMappings,
  );

  const deviceIdToNodeId: {
    deviceId: string;
    nodeId: number;
  }[] = [];
  params.nodeQrMappings?.forEach((mapping) => {
    const nodeId = nodeQrMappings.find(
      (m) => m.qrCode === mapping.qrCode,
    )?.nodeId;
    deviceIdToNodeId.push({ deviceId: mapping.deviceId, nodeId: nodeId ?? -1 });
  });

  const canbusControllers = filterNodes(
    params.nodes,
    "canbusControllerContainer",
  );

  const canboConfigs = createCanboConfigs(
    canbusControllers,
    lightsIds,
    shadesIds,
    nodeQrMappings,
    params.nodeQrMappings,
    actionManager,
  );

  const doorSensors = filterNodes(params.nodes, "doorSensorContainer");

  const rfReedConfigs = createRFReedConfigs(
    doorSensors,
    lightsIds,
    shadesIds,
    nodeQrMappings,
    params.nodeQrMappings,
    actionManager,
  );

  const roomDimmers = filterNodes(params.nodes, "roomDimmerContainer");
  const outletDimmers = filterNodes(params.nodes, "outletDimmerContainer");
  const dimmers = [...roomDimmers, ...outletDimmers];

  const roomDimmerConfigs = createRFDimmerConfigs(
    dimmers,
    lightsIds,
    shadesIds,
    nodeQrMappings,
    params.nodeQrMappings,
    actionManager,
  );

  const presenceSensors = params.nodes.filter(
    (node) => node.type === "presenceSensorContainer",
  );

  const rfPresenceConfigs = createRFPresenceConfigs(
    presenceSensors,
    lightsIds,
    shadesIds,
    nodeQrMappings,
    params.nodeQrMappings,
    actionManager,
  );

  // Find all base station container nodes to get Somfy shades
  const baseStations = params.nodes.filter(
    (node) => node.type === "baseStationContainer",
  );

  const somfyShadesConfigs = createSomfyShadesConfigs(
    baseStations,
    params.somfyShadesDeviceIdMapping,
  );

  const activeConfiguration = {
    config: {
      id: 1,
      version: params.version,
      lights: toLights(params.nodes, lightsIds, deviceIdToNodeId),
      rfConfig: params.rfConfig,
      wifiConfig: params.wifiConfig,
      serverAddress: (params.serverAddress || "").slice(0, 128),
      macConfig: params.macConfig,
      dhcpConfig: params.dhcpConfig,
      nodeQrMappings,
      canboConfigs,
      rfReedConfigs,
      roomDimmerConfigs,
      rfPresenceConfigs,
      somfyShades: somfyShadesConfigs,
      actions: toActions(actionManager),
    },
    state: {
      lights: toLightStates(params.nodes, lightsIds),
      buttons: [],
      provisionedDevices: [],
      reeds: toRFReedStates(doorSensors, nodeQrMappings, params.nodeQrMappings),
      presences: toRFPresenceStates(
        presenceSensors,
        nodeQrMappings,
        params.nodeQrMappings,
      ),
      pirs: [], // PIR states are managed at runtime
    },
  } satisfies ActiveConfiguration;

  return {
    lightsIds,
    activeConfiguration,
  };
}

// Use an Action manager instead of passing around `actions` and `nextId`
interface ActionManager {
  actions: Map<string, { id: number; action: Action }>;
  nextId: number;
}

function createActionManager(): ActionManager {
  return {
    actions: new Map(),
    nextId: 1,
  };
}

function addAction(
  manager: ActionManager,
  actionData: Omit<Action, "id">,
): number {
  // Create unique key based on action properties
  const key = JSON.stringify(actionData);

  const existing = manager.actions.get(key);
  if (existing) {
    return existing.id;
  }

  const actionWithId = {
    id: manager.nextId,
    ...actionData,
  };

  manager.actions.set(key, { id: manager.nextId, action: actionWithId });
  return manager.nextId++;
}

function toActions(actionManager: ActionManager): Action[] {
  return Array.from(actionManager.actions.values()).map(({ action }) => action);
}

/**
 * Creates node QR mappings for the basestation configuration.
 * Maps physical device containers to their QR codes and assigns sequential node IDs.
 */
function createNodeQrMappings(
  nodes: GraphNode[],
  nodeQrMappings?: { deviceId: string; qrCode: string }[],
): BasestationConfig_NodeQRMapping[] {
  const mappings: BasestationConfig_NodeQRMapping[] = [];

  // Build the container to QR mapping
  const containerToQrMapping = new Map<string, string>();
  nodeQrMappings?.forEach((mapping) => {
    containerToQrMapping.set(mapping.deviceId, mapping.qrCode);
  });

  // Track which containers we've already added to avoid duplicates
  const processedContainers = new Set<string>();

  // Helper function to determine device type based on container type
  const getDeviceType = (containerType: string) => {
    return containerType === "controllerContainer" ||
      containerType === "canbusControllerContainer"
      ? BasestationConfig_NodeQRMapping_DeviceType.CAN
      : BasestationConfig_NodeQRMapping_DeviceType.RF;
  };

  let currentDeviceIndex = 40;

  for (const node of nodes) {
    // "container" nodes refers to the physical devices have QR codes
    if (
      node.type?.endsWith("Container") &&
      node.type !== "baseStationContainer"
    ) {
      const qrCode = containerToQrMapping.get(node.id);

      if (qrCode && !processedContainers.has(node.id)) {
        // Use the json represenation of the node, then compute a bounded CRC8 and use that
        // as the nodeId. Ensure that there's no collision with the existing nodeIds.
        // and make sure that its larger than 40.
        // const currentDeviceIndex =
        //   boundedCRC8(JSON.stringify(node.data), 215, 10) + 40;

        currentDeviceIndex++;

        mappings.push({
          qrCode,
          nodeId: currentDeviceIndex,
          type: getDeviceType(node.type),
        });

        processedContainers.add(node.id);
      }
    }
  }

  return mappings;
}

export function serializeNodesForBasestation(params: Params) {
  const { lightsIds, activeConfiguration } =
    getActiveConfigurationForBasestation(params);

  const data = ActiveConfiguration.encode(activeConfiguration).finish();

  return { lightsIds, data };
}

type LightsIds = { basestationId: number; deviceId: string }[];
type ShadesIds = { basestationId: number; deviceId: string }[];

/**
 * The basestation uses int32 IDs so it's smaller than UUIDs.
 * Generate a mapping from our UUIDs -> basestation IDs, so we can use them
 * in the protobuf messages.
 */
function collectLightsIds(nodes: GraphNode[]): LightsIds {
  return getLights(nodes).map((device, index) => ({
    deviceId: device.id,
    basestationId: index,
  }));
}

function collectShadesIds(nodes: GraphNode[]): ShadesIds {
  return getShades(nodes).map((device, index) => ({
    deviceId: device.id,
    basestationId: index,
  }));
}

function getLightIndex(lightsIds: LightsIds, deviceId: string) {
  const light = lightsIds.find((l) => l.deviceId === deviceId);
  if (!light) {
    console.error("Can't find light that should have been mapped", {
      deviceId,
      lightsIds,
    });
    return;
  }

  return light.basestationId;
}

/**
 * Creates an action for either a light or shade device
 */
function createDeviceAction(
  actionManager: ActionManager,
  lightsIds: LightsIds,
  shadesIds: ShadesIds,
  action: {
    deviceId: string;
    dimSpeed: number;
    targetValue: number;
    onValue: number;
    offValue: number;
  },
  additionalParams: {
    delayInMsec?: number;
    activateDelayMsec?: number;
  } = {},
): number {
  // Check if it's a light device
  const lightId = lightsIds.find(
    (l) => l.deviceId === action.deviceId,
  )?.basestationId;
  if (lightId !== undefined) {
    return addAction(actionManager, {
      dimSpeedMsec: action.dimSpeed * 1000,
      lightId,
      targetBrightness: action.targetValue,
      onBrightness: action.onValue,
      offBrightness: action.offValue,
      delayInMsec: additionalParams.delayInMsec ?? 0,
      activateDelayMsec: additionalParams.activateDelayMsec ?? 0,
    });
  }

  // Check if it's a shade device
  const shadeId = shadesIds.find(
    (s) => s.deviceId === action.deviceId,
  )?.basestationId;
  if (shadeId !== undefined) {
    return addAction(actionManager, {
      dimSpeedMsec: action.dimSpeed * 1000,
      somfyShadeId: shadeId,
      targetBrightness: action.targetValue,
      onBrightness: action.onValue,
      offBrightness: action.offValue,
      delayInMsec: additionalParams.delayInMsec ?? 0,
      activateDelayMsec: additionalParams.activateDelayMsec ?? 0,
    });
  }

  throw new Error(
    `Can't find device (light or shade) ID for action ${action.deviceId}`,
  );
}

/**
 * Creates an action for either a light or shade device (for sensor triggers)
 */

//TODO: implement by fetching data properly
// function createDeviceActionForSensor(
//   actionManager: ActionManager,
//   lightsIds: LightsIds,
//   shadesIds: ShadesIds,
//   action: {
//     deviceId: string;
//     dimSpeed: number;
//     targetValue: number;
//   },
//   activateDelayMsec: number,
// ): number {
//   // Check if it's a light device
//   const lightId = lightsIds.find(
//     (l) => l.deviceId === action.deviceId,
//   )?.basestationId;
//   if (lightId !== undefined) {
//     return addAction(actionManager, {
//       dimSpeedMsec: action.dimSpeed * 1000,
//       lightId,
//       targetBrightness: action.targetValue,
//       activateDelayMsec,
//       onBrightness: 100,
//       offBrightness: 0,
//       delayInMsec: 0,
//     });
//   }

//   // Check if it's a shade device
//   const shadeId = shadesIds.find(
//     (s) => s.deviceId === action.deviceId,
//   )?.basestationId;
//   if (shadeId !== undefined) {
//     return addAction(actionManager, {
//       dimSpeedMsec: action.dimSpeed * 1000,
//       somfyShadeId: shadeId,
//       targetBrightness: action.targetValue,
//       activateDelayMsec,
//       onBrightness: 100,
//       offBrightness: 0,
//       delayInMsec: 0,
//     });
//   }

//   throw new Error(
//     `Can't find device (light or shade) ID for action ${action.deviceId}`,
//   );
// }

function toLightStates(nodes: GraphNode[], lightsIds: LightsIds): LightState[] {
  return [];
  // return getLights(nodes)
  //   .map((device) => {
  //     const id = getLightIndex(lightsIds, device.id);
  //     if (id === undefined) {
  //       return;
  //     }

  //     return (
  //       match(device)
  //         // TODO: implement with type: "light"
  //         // .with({ type: "dmx" }, (device) => ({
  //         //   id,
  //         //   brightness: 0,
  //         //   targetValue: 0,
  //         //   dimSpeedMsec: device.defaultDimmingSpeed * 1000,
  //         //   lastModifiedTime: Date.now(),
  //         //   activeAfterTime: 0,
  //         //   isTransitioning: false,
  //         //   lastBrightnessBeforeAction: 0,
  //         //   lastTransitionStopReason:
  //         //     LightState_TransitionStopReason.TRANSITION_STOP_UNKNOWN,
  //         // }))
  //         .with({ type: "roomSwitch" }, (_device) => ({
  //           id,
  //           brightness: 0,
  //           targetValue: 0,
  //           dimSpeedMsec: 0,
  //           lastModifiedTime: Date.now(),
  //           activeAfterTime: 0,
  //           isTransitioning: false,
  //           lastBrightnessBeforeAction: 0,
  //           lastTransitionStopReason:
  //             LightState_TransitionStopReason.TRANSITION_STOP_UNKNOWN,
  //         }))
  //         .with({ type: "roomDimmer" }, { type: "outletDimmer" }, (device) => ({
  //           id,
  //           brightness: 0,
  //           targetValue: 0,
  //           dimSpeedMsec: (device.defaultDimmingSpeed ?? 0) * 1000,
  //           lastModifiedTime: Date.now(),
  //           activeAfterTime: 0,
  //           isTransitioning: false,
  //           lastBrightnessBeforeAction: 0,
  //           lastTransitionStopReason:
  //             LightState_TransitionStopReason.TRANSITION_STOP_UNKNOWN,
  //         }))
  //         .with({ type: "zeroToTenVoltDimmer" }, (_device) => ({
  //           id,
  //           brightness: 0,
  //           targetValue: 0,
  //           dimSpeedMsec: 0, // 0-10V dimmers don't have dimming speed in our current implementation
  //           lastModifiedTime: Date.now(),
  //           activeAfterTime: 0,
  //           isTransitioning: false,
  //           lastBrightnessBeforeAction: 0,
  //           lastTransitionStopReason:
  //             LightState_TransitionStopReason.TRANSITION_STOP_UNKNOWN,
  //         }))
  //         .with({ type: "relayOutput" }, (_device) => ({
  //           id,
  //           brightness: 0,
  //           targetValue: 0,
  //           dimSpeedMsec: 0, // Relay outputs don't dim, they're on/off
  //           lastModifiedTime: Date.now(),
  //           activeAfterTime: 0,
  //           isTransitioning: false,
  //           lastBrightnessBeforeAction: 0,
  //           lastTransitionStopReason:
  //             LightState_TransitionStopReason.TRANSITION_STOP_UNKNOWN,
  //         }))
  //         .exhaustive() satisfies LightState
  //     );
  //   })
  //   .filter((state) => state !== undefined);
}

function toRFReedStates(
  doorSensors: GraphNode[],
  nodeQrMappings: BasestationConfig_NodeQRMapping[],
  nodeQrMappingsParam?: { deviceId: string; qrCode: string }[],
): RFReedState[] {
  return doorSensors.map((doorSensor) => {
    const qrCode = nodeQrMappingsParam?.find(
      (mapping) => mapping.deviceId === doorSensor.id,
    )?.qrCode;
    if (!qrCode) {
      throw new Error(`Can't find QR code for door sensor ${doorSensor.id}`);
    }
    const nodeId = nodeQrMappings.find(
      (mapping) => mapping.qrCode === qrCode,
    )?.nodeId;
    if (!nodeId) {
      throw new Error(`Can't find node ID for door sensor ${doorSensor.id}`);
    }

    return {
      nodeId,
      sensorStatus: RFReedState_Status.UNKNOWN,
      lastModifiedTime: Date.now(),
      batteryVoltage: 0.0,
    } satisfies RFReedState;
  });
}

function toRFPresenceStates(
  presenceSensors: GraphNode[],
  nodeQrMappings: BasestationConfig_NodeQRMapping[],
  nodeQrMappingsParam?: { deviceId: string; qrCode: string }[],
): RFPresenceState[] {
  return presenceSensors.map((presenceSensor) => {
    const qrCode = nodeQrMappingsParam?.find(
      (mapping) => mapping.deviceId === presenceSensor.id,
    )?.qrCode;
    if (!qrCode) {
      throw new Error(
        `Can't find QR code for presence sensor ${presenceSensor.id}`,
      );
    }
    const nodeId = nodeQrMappings.find(
      (mapping) => mapping.qrCode === qrCode,
    )?.nodeId;
    if (!nodeId) {
      throw new Error(
        `Can't find node ID for presence sensor ${presenceSensor.id}`,
      );
    }

    return {
      nodeId,
      sensorStatus: RFPresenceState_Status.UNKNOWN,
      lastModifiedTime: Date.now(),
      batteryVoltage: 0.0,
    } satisfies RFPresenceState;
  });
}

function toLights(
  nodes: GraphNode[],
  lightsIds: LightsIds,
  deviceIdToNodeId: { deviceId: string; nodeId: number }[],
): LightConfig[] {
  return [];
  // return getLights(nodes)
  //   .map((device) => {
  //     const id = getLightIndex(lightsIds, device.id);
  //     if (id === undefined) {
  //       return;
  //     }

  //     return (
  //       match(device)
  //         // TODO: implement with type: "light"
  //         // .with(
  //         //   { type: "dmx" },
  //         //   (device) =>
  //         //     ({
  //         //       id,
  //         //       dimSpeedMsec: device.defaultDimmingSpeed * 1000,
  //         //       fixtures: toLightFixture(device),
  //         //     }) satisfies LightConfig,
  //         // )
  //         .with({ type: "roomSwitch" }, (device) => {
  //           return {
  //             id,
  //             dimSpeedMsec: 0,
  //             fixtures: [
  //               {
  //                 minBrightness: 0,
  //                 maxBrightness: 100,
  //                 type: LightConfig_FixtureConfig_FixtureType.RF,
  //                 rf: {
  //                   type: LightConfig_FixtureConfig_RFConfig_Type.SWITCH,
  //                   nodeId:
  //                     deviceIdToNodeId.find((d) => d.deviceId === device.nodeId)
  //                       ?.nodeId ?? -1,
  //                 },
  //               },
  //             ],
  //           } satisfies LightConfig;
  //         })
  //         .with(
  //           { type: "roomDimmer" },
  //           (device) =>
  //             ({
  //               id,
  //               dimSpeedMsec: (device.defaultDimmingSpeed ?? 0) * 1000,
  //               fixtures: [
  //                 {
  //                   minBrightness: 0,
  //                   maxBrightness: 100,
  //                   type: LightConfig_FixtureConfig_FixtureType.RF,
  //                   rf: {
  //                     type: LightConfig_FixtureConfig_RFConfig_Type.DIMMER,
  //                     nodeId:
  //                       deviceIdToNodeId.find(
  //                         (d) => d.deviceId === device.nodeId,
  //                       )?.nodeId ?? -1,
  //                   },
  //                 },
  //               ],
  //             }) satisfies LightConfig,
  //         )
  //         .with(
  //           { type: "outletDimmer" },
  //           (device) =>
  //             ({
  //               id,
  //               dimSpeedMsec: (device.defaultDimmingSpeed ?? 0) * 1000,
  //               fixtures: [
  //                 {
  //                   minBrightness: 0,
  //                   maxBrightness: 100,
  //                   type: LightConfig_FixtureConfig_FixtureType.RF,
  //                   rf: {
  //                     type: LightConfig_FixtureConfig_RFConfig_Type.DIMMER,
  //                     nodeId:
  //                       deviceIdToNodeId.find(
  //                         (d) => d.deviceId === device.nodeId,
  //                       )?.nodeId ?? -1,
  //                   },
  //                 },
  //               ],
  //             }) satisfies LightConfig,
  //         )
  //         .with({ type: "zeroToTenVoltDimmer" }, (device) => {
  //           // TODO FIXME: migrate to useData
  //           // // Find the actual dimmer from the CanBus controller to get current settings
  //           // const canBusNode = nodes.find(
  //           //   (n) =>
  //           //     n.type === "canbusControllerContainer" && n.id === device.nodeId,
  //           // );
  //           // Type guard to ensure we have the correct node type
  //           // const actualDimmer =
  //           //   canBusNode?.type === "canbusControllerContainer"
  //           //     ? canBusNode.data.zeroToTenVoltDimmers?.[device.viaId]
  //           //     : undefined;
  //           // const dimmingType = actualDimmer?.dimmingType || "sinking";
  //           // const useRelay = actualDimmer?.useRelay || false;
  //           // // Extract connector number from relay connector ID (e.g., "relay-connector-1" -> 1)
  //           // const connectorId = actualDimmer?.relayConnectorId
  //           //   ? parseInt(actualDimmer.relayConnectorId.split("-").pop() || "0")
  //           //   : 0;
  //           // return {
  //           //   id,
  //           //   dimSpeedMsec: (actualDimmer?.defaultDimmingSpeed || 0.2) * 1000,
  //           //   fixtures: [
  //           //     {
  //           //       minBrightness: actualDimmer?.minBrightness || 0,
  //           //       maxBrightness: actualDimmer?.maxBrightness || 100,
  //           //       type: LightConfig_FixtureConfig_FixtureType.ZERO_TO_TEN_VOLT,
  //           //       zeroToTenVolt: {
  //           //         type:
  //           //           dimmingType === "sourcing"
  //           //             ? LightConfig_FixtureConfig_ZeroToTenVoltConfig_Type.SOURCING
  //           //             : LightConfig_FixtureConfig_ZeroToTenVoltConfig_Type.SINKING,
  //           //         useRelay,
  //           //         outConnectorId: connectorId,
  //           //         nodeId:
  //           //           deviceIdToNodeId.find((d) => d.deviceId === canBusNode?.id)
  //           //             ?.nodeId ?? -1,
  //           //       },
  //           //     },
  //           //   ],
  //           // } satisfies LightConfig;
  //         })
  //         .with({ type: "relayOutput" }, (device) => {
  //           // Find the actual relay output from the CanBus controller to get current settings
  //           const canBusNode = nodes.find(
  //             (n) =>
  //               n.type === "canbusControllerContainer" &&
  //               n.id === device.nodeId,
  //           );

  //           // TODO FIXME: migrate to useData
  //           // Type guard to ensure we have the correct node type
  //           // const actualRelayOutput =
  //           //   canBusNode?.type === "canbusControllerContainer"
  //           //     ? canBusNode.data.relayOutputs?.[device.viaId]
  //           //     : undefined;

  //           // Extract connector number from relay connector ID (e.g., "relay-connector-1" -> 1)
  //           const connectorId = 0;
  //           // const connectorId = actualRelayOutput?.relayConnectorId
  //           //   ? parseInt(
  //           //       actualRelayOutput.relayConnectorId.split("-").pop() || "0",
  //           //     )
  //           //   : 0;

  //           return {
  //             id,
  //             dimSpeedMsec: 0, // Relay outputs don't dim, they're on/off
  //             fixtures: [
  //               {
  //                 minBrightness: 0,
  //                 maxBrightness: 100,
  //                 type: LightConfig_FixtureConfig_FixtureType.RELAY,
  //                 relay: {
  //                   nodeId:
  //                     deviceIdToNodeId.find(
  //                       (d) => d.deviceId === canBusNode?.id,
  //                     )?.nodeId ?? -1,
  //                   outConnectorId: connectorId,
  //                 },
  //               },
  //             ],
  //           } satisfies LightConfig;
  //         })
  //         .exhaustive()
  //     );
  //   })
  //   .filter((light) => light !== undefined);
}

type SectionLightDevice =
  | RoomSwitchDevice
  | RoomDimmerDevice
  | ZeroToTenVoltDimmerDevice
  | RelayOutputDevice;

type SectionShadeDevice = SomoShadesDevice | SomfyShadesBaseDevice;

function getLights(nodes: GraphNode[]): SectionLightDevice[] {
  const oldLightsToBeRemoved = filterNodes(
    nodes,
    "section",
  ).flatMap<SectionLightDevice>((node) => {
    const devices = Object.values(node.data.devices);
    return devices.filter(
      (device) =>
        device.type === "roomSwitch" ||
        device.type === "roomDimmer" ||
        device.type === "zeroToTenVoltDimmer" ||
        device.type === "relayOutput",
    );
  });
  // TODO: to fetch the data, we need to move that logic into the configurator (or pass the data array)
  // const lights = filterNodes(nodes, "light").map((node) =>
  //   getData(yDoc, node.data.dataId, "light"),
  // );
  // const outletDimmerLights = filterNodes(nodes, "outletDimmerLight").map((node) =>
  //   getData(yDoc, node.data.dataId, "outletDimmerLight"),
  // );

  return oldLightsToBeRemoved;
}

function getShades(nodes: GraphNode[]): SectionShadeDevice[] {
  return filterNodes(nodes, "section").flatMap<SectionShadeDevice>((node) => {
    const devices = Object.values(node.data.devices);
    return devices.filter(
      (device) => device.type === "somoShades" || device.type === "somfyShades",
    );
  });
}

function toLightFixture(device: DmxDevice): LightConfig_FixtureConfig[] {
  return Object.values(device.fixtures).map<LightConfig_FixtureConfig>(
    (fixture) =>
      match(fixture.type)
        // TODO FIXME: Add support for analog fixtures
        .with("Analog", () => ({
          minBrightness: fixture.minBrightness,
          maxBrightness: fixture.maxBrightness,
          type: LightConfig_FixtureConfig_FixtureType.DMX,
          dmx: {
            type: LightConfig_FixtureConfig_DMXConfig_FixtureTypeConfig.TUNABLE_WHITE,
            channels: [fixture.channel],
            params: undefined,
            rgb: undefined,
          },
        }))
        .with("D4", () => ({
          minBrightness: fixture.minBrightness,
          maxBrightness: fixture.maxBrightness,
          type: LightConfig_FixtureConfig_FixtureType.DMX,
          dmx: {
            type: LightConfig_FixtureConfig_DMXConfig_FixtureTypeConfig.D4,
            channels: [fixture.channel],
            params: undefined,
            rgb: undefined,
          },
        }))
        .with("DF_12", () => ({
          minBrightness: fixture.minBrightness,
          maxBrightness: fixture.maxBrightness,
          type: LightConfig_FixtureConfig_FixtureType.DMX,
          dmx: {
            type: LightConfig_FixtureConfig_DMXConfig_FixtureTypeConfig.DF_12,
            channels: [fixture.channel],
            params: undefined,
            rgb: undefined,
          },
        }))
        .with("ELV", () => ({
          minBrightness: fixture.minBrightness,
          maxBrightness: fixture.maxBrightness,
          type: LightConfig_FixtureConfig_FixtureType.DMX,
          dmx: {
            type: LightConfig_FixtureConfig_DMXConfig_FixtureTypeConfig.ELV,
            channels: [fixture.channel],
            params: undefined,
            rgb: undefined,
          },
        }))
        .with("EST", () => ({
          minBrightness: fixture.minBrightness,
          maxBrightness: fixture.maxBrightness,
          type: LightConfig_FixtureConfig_FixtureType.DMX,
          dmx: {
            type: LightConfig_FixtureConfig_DMXConfig_FixtureTypeConfig.EST,
            channels: [fixture.channel],
            params: undefined,
            rgb: undefined,
          },
        }))
        .with("Tunable White", () => ({
          minBrightness: fixture.minBrightness,
          maxBrightness: fixture.maxBrightness,
          type: LightConfig_FixtureConfig_FixtureType.DMX,
          dmx: {
            type: LightConfig_FixtureConfig_DMXConfig_FixtureTypeConfig.TUNABLE_WHITE,
            channels: [fixture.channel],
            params: {
              min1: 0.0,
              max1: 1.0,
              gamma1: 1.5,
              min2: 0.0,
              max2: 1.0,
              gamma2: 1.5,
            },
            rgb: undefined,
          },
        }))
        .exhaustive(),
  );
}

/**
 * Creates CAN bus controller configurations for the basestation.
 */
function createCanboConfigs(
  _canbusControllers: GraphNodesByType["canbusControllerContainer"][],
  _lightsIds: LightsIds,
  _shadesIds: ShadesIds,
  _nodeQrMappings: BasestationConfig_NodeQRMapping[],
  _nodeQrMappingsParam: { deviceId: string; qrCode: string }[] | undefined,
  _actionManager: ActionManager,
): CanboConfig[] {
  // TODO FIXME: migrate to useData
  return [];
  // return canbusControllers.map(({ data, id }) => {
  //   const qrCode = nodeQrMappingsParam?.find(
  //     (mapping) => mapping.deviceId === id,
  //   )?.qrCode;
  //   if (!qrCode) {
  //     throw new Error(`Can't find QR code for canbus controller ${id}`);
  //   }
  //   const nodeId = nodeQrMappings.find(
  //     (mapping) => mapping.qrCode === qrCode,
  //   )?.nodeId;
  //   if (!nodeId) {
  //     throw new Error(`Can't find node ID for canbus controller ${id}`);
  //   }

  //   // Process 3-pin inputs (toggle and momentary buttons)
  //   const threePinInputs: CanboConfig_ThreePinInput[] = Object.values(
  //     data.controllers,
  //   )
  //     .filter((controller) => controller.portId?.startsWith("3pin-"))
  //     .map((controller) => {
  //       const connectorId = getConnectorId(controller);

  //       if (controller.type === "toggle") {
  //         const upClickActions = Object.values(controller.onUpClick).map(
  //           (action) => {
  //             return createDeviceAction(
  //               actionManager,
  //               lightsIds,
  //               shadesIds,
  //               action,
  //               { delayInMsec: action.delay ?? 0, activateDelayMsec: 0 },
  //             );
  //           },
  //         );

  //         const upHoldActions = Object.values(controller.onUpHold).map(
  //           (action) => {
  //             return createDeviceAction(
  //               actionManager,
  //               lightsIds,
  //               shadesIds,
  //               action,
  //               { delayInMsec: action.delay ?? 0, activateDelayMsec: 0 },
  //             );
  //           },
  //         );

  //         const downClickActions = Object.values(controller.onDownClick).map(
  //           (action) => {
  //             return createDeviceAction(
  //               actionManager,
  //               lightsIds,
  //               shadesIds,
  //               action,
  //               { delayInMsec: action.delay ?? 0, activateDelayMsec: 0 },
  //             );
  //           },
  //         );

  //         const downHoldActions = Object.values(controller.onDownHold).map(
  //           (action) => {
  //             return createDeviceAction(
  //               actionManager,
  //               lightsIds,
  //               shadesIds,
  //               action,
  //               { delayInMsec: action.delay ?? 0, activateDelayMsec: 0 },
  //             );
  //           },
  //         );

  //         const upPressActions = Object.values(controller.onUpPress || {}).map(
  //           (action) => {
  //             return createDeviceAction(
  //               actionManager,
  //               lightsIds,
  //               shadesIds,
  //               action,
  //               { delayInMsec: action.delay ?? 0, activateDelayMsec: 0 },
  //             );
  //           },
  //         );

  //         const upReleaseActions = Object.values(
  //           controller.onUpRelease || {},
  //         ).map((action) => {
  //           return createDeviceAction(
  //             actionManager,
  //             lightsIds,
  //             shadesIds,
  //             action,
  //             { delayInMsec: action.delay ?? 0, activateDelayMsec: 0 },
  //           );
  //         });

  //         const downPressActions = Object.values(
  //           controller.onDownPress || {},
  //         ).map((action) => {
  //           return createDeviceAction(
  //             actionManager,
  //             lightsIds,
  //             shadesIds,
  //             action,
  //             { delayInMsec: action.delay ?? 0, activateDelayMsec: 0 },
  //           );
  //         });

  //         const downReleaseActions = Object.values(
  //           controller.onDownRelease || {},
  //         ).map((action) => {
  //           return createDeviceAction(
  //             actionManager,
  //             lightsIds,
  //             shadesIds,
  //             action,
  //             { delayInMsec: action.delay ?? 0, activateDelayMsec: 0 },
  //           );
  //         });

  //         const upHoldReleaseActions = Object.values(
  //           controller.onUpHoldRelease || {},
  //         ).map((action) => {
  //           return createDeviceAction(
  //             actionManager,
  //             lightsIds,
  //             shadesIds,
  //             action,
  //             { delayInMsec: action.delay ?? 0, activateDelayMsec: 0 },
  //           );
  //         });

  //         const downHoldReleaseActions = Object.values(
  //           controller.onDownHoldRelease || {},
  //         ).map((action) => {
  //           return createDeviceAction(
  //             actionManager,
  //             lightsIds,
  //             shadesIds,
  //             action,
  //             { delayInMsec: action.delay ?? 0, activateDelayMsec: 0 },
  //           );
  //         });

  //         return {
  //           connectorId,
  //           type: CanboConfig_ThreePinInput_ConnectorType.TOGGLE,
  //           toggle: {
  //             upClick: upClickActions,
  //             upHold: upHoldActions,
  //             downClick: downClickActions,
  //             downHold: downHoldActions,
  //             upPress: upPressActions,
  //             upRelease: upReleaseActions,
  //             downPress: downPressActions,
  //             downRelease: downReleaseActions,
  //             upHoldRelease: upHoldReleaseActions,
  //             downHoldRelease: downHoldReleaseActions,
  //           },
  //         } satisfies CanboConfig_ThreePinInput;
  //       }

  //       if (controller.type === "momentary") {
  //         const upClickActions = Object.values(controller.onUpClick).map(
  //           (action) => {
  //             return createDeviceAction(
  //               actionManager,
  //               lightsIds,
  //               shadesIds,
  //               action,
  //               { delayInMsec: action.delay ?? 0, activateDelayMsec: 0 },
  //             );
  //           },
  //         );

  //         const upHoldActions = Object.values(controller.onUpHold).map(
  //           (action) => {
  //             return createDeviceAction(
  //               actionManager,
  //               lightsIds,
  //               shadesIds,
  //               action,
  //               { delayInMsec: action.delay ?? 0, activateDelayMsec: 0 },
  //             );
  //           },
  //         );

  //         const upPressActions = Object.values(controller.onUpPress || {}).map(
  //           (action) => {
  //             return createDeviceAction(
  //               actionManager,
  //               lightsIds,
  //               shadesIds,
  //               action,
  //               { delayInMsec: action.delay ?? 0, activateDelayMsec: 0 },
  //             );
  //           },
  //         );

  //         const upReleaseActions = Object.values(
  //           controller.onUpRelease || {},
  //         ).map((action) => {
  //           return createDeviceAction(
  //             actionManager,
  //             lightsIds,
  //             shadesIds,
  //             action,
  //             { delayInMsec: action.delay ?? 0, activateDelayMsec: 0 },
  //           );
  //         });

  //         const upHoldReleaseActions = Object.values(
  //           controller.onUpHoldRelease || {},
  //         ).map((action) => {
  //           return createDeviceAction(
  //             actionManager,
  //             lightsIds,
  //             shadesIds,
  //             action,
  //             { delayInMsec: action.delay ?? 0, activateDelayMsec: 0 },
  //           );
  //         });

  //         return {
  //           connectorId,
  //           type: CanboConfig_ThreePinInput_ConnectorType.MOMENTARY,
  //           momentary: {
  //             upClick: upClickActions,
  //             upHold: upHoldActions,
  //             upPress: upPressActions,
  //             upRelease: upReleaseActions,
  //             upHoldRelease: upHoldReleaseActions,
  //           },
  //         } satisfies CanboConfig_ThreePinInput;
  //       }

  //       throw new Error(`Unknown controller type:`);
  //     });

  //   // Process 2-pin inputs (reed switch (door) sensors, momentary buttons)
  //   const twoPinInputs: CanboConfig_TwoPinInput[] = [];

  //   // Add reed switch sensors
  //   Object.values(data.reedSwitchSensors || {})
  //     .filter((sensor) => sensor.portId?.startsWith("2pin-"))
  //     .forEach((sensor) => {
  //       const connectorId = parseInt(sensor.portId?.split("-").pop() || "0");

  //       const onOpenActions = Object.values(sensor.onOpen).map((action) => {
  //         const lightId = lightsIds.find(
  //           (l) => l.deviceId === action.deviceId,
  //         )?.basestationId;
  //         if (lightId === undefined) {
  //           throw new Error(
  //             `Can't find light ID for action ${action.deviceId}`,
  //           );
  //         }
  //         return addAction(actionManager, {
  //           dimSpeedMsec: action.dimSpeed * 1000,
  //           lightId,
  //           delayInMsec: action.delay ?? 0,
  //           targetBrightness: action.targetValue,
  //           onBrightness: action.onValue,
  //           offBrightness: action.offValue,
  //           activateDelayMsec: 0,
  //         });
  //       });

  //       const onCloseActions = Object.values(sensor.onClose).map((action) => {
  //         const lightId = lightsIds.find(
  //           (l) => l.deviceId === action.deviceId,
  //         )?.basestationId;
  //         if (lightId === undefined) {
  //           throw new Error(
  //             `Can't find light ID for action ${action.deviceId}`,
  //           );
  //         }
  //         return addAction(actionManager, {
  //           dimSpeedMsec: action.dimSpeed * 1000,
  //           lightId,
  //           delayInMsec: action.delay ?? 0,
  //           targetBrightness: action.targetValue,
  //           onBrightness: action.onValue,
  //           offBrightness: action.offValue,
  //           activateDelayMsec: 0,
  //         });
  //       });

  //       twoPinInputs.push({
  //         connectorId,
  //         type: CanboConfig_TwoPinInput_ConnectorType.DOOR_SENSOR,
  //         doorSensor: {
  //           onOpen: onOpenActions,
  //           onClose: onCloseActions,
  //         },
  //       } satisfies CanboConfig_TwoPinInput);
  //     });

  //   // Add 2-pin momentary buttons (if any)
  //   Object.values(data.controllers)
  //     .filter((controller) => controller.portId?.startsWith("2pin-"))
  //     .forEach((controller) => {
  //       if (controller.type === "momentary") {
  //         const connectorId = getConnectorId(controller);

  //         const upClickActions = Object.values(controller.onUpClick).map(
  //           (action) => {
  //             const lightId = lightsIds.find(
  //               (l) => l.deviceId === action.deviceId,
  //             )?.basestationId;
  //             if (lightId === undefined) {
  //               throw new Error(
  //                 `Can't find light ID for action ${action.deviceId}`,
  //               );
  //             }
  //             return addAction(actionManager, {
  //               dimSpeedMsec: action.dimSpeed * 1000,
  //               lightId,
  //               delayInMsec: action.delay ?? 0,
  //               targetBrightness: action.targetValue,
  //               onBrightness: action.onValue,
  //               offBrightness: action.offValue,
  //               activateDelayMsec: 0,
  //             });
  //           },
  //         );

  //         const upHoldActions = Object.values(controller.onUpHold).map(
  //           (action) => {
  //             const lightId = lightsIds.find(
  //               (l) => l.deviceId === action.deviceId,
  //             )?.basestationId;
  //             if (lightId === undefined) {
  //               throw new Error(
  //                 `Can't find light ID for action ${action.deviceId}`,
  //               );
  //             }
  //             return addAction(actionManager, {
  //               dimSpeedMsec: action.dimSpeed * 1000,
  //               lightId,
  //               delayInMsec: action.delay ?? 0,
  //               targetBrightness: action.targetValue,
  //               onBrightness: action.onValue,
  //               offBrightness: action.offValue,
  //               activateDelayMsec: 0,
  //             });
  //           },
  //         );

  //         const upPressActions = Object.values(controller.onUpPress || {}).map(
  //           (action) => {
  //             const lightId = lightsIds.find(
  //               (l) => l.deviceId === action.deviceId,
  //             )?.basestationId;
  //             if (lightId === undefined) {
  //               throw new Error(
  //                 `Can't find light ID for action ${action.deviceId}`,
  //               );
  //             }
  //             return addAction(actionManager, {
  //               dimSpeedMsec: action.dimSpeed * 1000,
  //               lightId,
  //               delayInMsec: action.delay ?? 0,
  //               targetBrightness: action.targetValue,
  //               onBrightness: action.onValue,
  //               offBrightness: action.offValue,
  //               activateDelayMsec: 0,
  //             });
  //           },
  //         );

  //         const upReleaseActions = Object.values(
  //           controller.onUpRelease || {},
  //         ).map((action) => {
  //           const lightId = lightsIds.find(
  //             (l) => l.deviceId === action.deviceId,
  //           )?.basestationId;
  //           if (lightId === undefined) {
  //             throw new Error(
  //               `Can't find light ID for action ${action.deviceId}`,
  //             );
  //           }
  //           return addAction(actionManager, {
  //             dimSpeedMsec: action.dimSpeed * 1000,
  //             lightId,
  //             delayInMsec: action.delay ?? 0,
  //             targetBrightness: action.targetValue,
  //             onBrightness: action.onValue,
  //             offBrightness: action.offValue,
  //             activateDelayMsec: 0,
  //           });
  //         });

  //         const upHoldReleaseActions = Object.values(
  //           controller.onUpHoldRelease || {},
  //         ).map((action) => {
  //           const lightId = lightsIds.find(
  //             (l) => l.deviceId === action.deviceId,
  //           )?.basestationId;
  //           if (lightId === undefined) {
  //             throw new Error(
  //               `Can't find light ID for action ${action.deviceId}`,
  //             );
  //           }
  //           return addAction(actionManager, {
  //             dimSpeedMsec: action.dimSpeed * 1000,
  //             lightId,
  //             delayInMsec: action.delay ?? 0,
  //             targetBrightness: action.targetValue,
  //             onBrightness: action.onValue,
  //             offBrightness: action.offValue,
  //             activateDelayMsec: 0,
  //           });
  //         });

  //         twoPinInputs.push({
  //           connectorId,
  //           type: CanboConfig_TwoPinInput_ConnectorType.MOMENTARY,
  //           momentary: {
  //             upClick: upClickActions,
  //             upHold: upHoldActions,
  //             upPress: upPressActions,
  //             upRelease: upReleaseActions,
  //             upHoldRelease: upHoldReleaseActions,
  //           },
  //         } satisfies CanboConfig_TwoPinInput);
  //       }
  //     });

  //   // Process ADC inputs (PIR sensors)
  //   let adcInputs: CanboConfig_ADCInput | undefined = undefined;

  //   // Add PIR sensors to ADC inputs
  //   const pirSensors = Object.values(data.pirSensors || {}).filter((sensor) =>
  //     sensor.portId?.startsWith("adc-"),
  //   );

  //   if (pirSensors.length > 0) {
  //     const sensor = pirSensors[0]; // Take the first PIR sensor (single ADC input)
  //     const connectorId = parseInt(sensor.portId?.split("-").pop() || "1");

  //     const onActivateActions = Object.values(sensor.onActivate).map(
  //       (action) => {
  //         const lightId = lightsIds.find(
  //           (l) => l.deviceId === action.deviceId,
  //         )?.basestationId;
  //         if (lightId === undefined) {
  //           throw new Error(
  //             `Can't find light ID for action ${action.deviceId}`,
  //           );
  //         }
  //         return addAction(actionManager, {
  //           dimSpeedMsec: action.dimSpeed * 1000,
  //           lightId,
  //           targetBrightness: action.targetValue,
  //           onBrightness: action.onValue,
  //           offBrightness: action.offValue,
  //           delayInMsec: action.delay ?? 0,
  //           activateDelayMsec: sensor.onActivateDelay || 0, // Already in milliseconds
  //         });
  //       },
  //     );

  //     const onDeactivateActions = Object.values(sensor.onDeactivate).map(
  //       (action) => {
  //         const lightId = lightsIds.find(
  //           (l) => l.deviceId === action.deviceId,
  //         )?.basestationId;
  //         if (lightId === undefined) {
  //           throw new Error(
  //             `Can't find light ID for action ${action.deviceId}`,
  //           );
  //         }
  //         return addAction(actionManager, {
  //           dimSpeedMsec: action.dimSpeed * 1000,
  //           lightId,
  //           targetBrightness: action.targetValue,
  //           onBrightness: action.onValue,
  //           offBrightness: action.offValue,
  //           delayInMsec: action.delay ?? 0,
  //           activateDelayMsec: sensor.onDeactivateDelay || 0, // Already in milliseconds
  //         });
  //       },
  //     );

  //     adcInputs = {
  //       connectorId,
  //       type: CanboConfig_ADCInput_ConnectorType.PIR,
  //       pir: {
  //         onActivate: onActivateActions,
  //         onDeactivate: onDeactivateActions,
  //       },
  //     } satisfies CanboConfig_ADCInput;
  //   }

  //   // Process relay outputs
  //   const outputs: CanboConfig_Output[] = [];
  //   Object.values(data.relayOutputs || {}).forEach((relayOutput) => {
  //     // Extract connector ID from relay connector ID (e.g., "relay-connector-1" -> 1)
  //     const connectorId = relayOutput.relayConnectorId
  //       ? parseInt(relayOutput.relayConnectorId.split("-").pop() || "0")
  //       : 0;

  //     if (connectorId > 0) {
  //       outputs.push({
  //         connectorId,
  //         connectorType: CanboConfig_Output_ConnectorType.RELAY,
  //       });
  //     }
  //   });

  //   return {
  //     nodeId,
  //     threePinInputs,
  //     twoPinInputs,
  //     adcInputs,
  //     outputs,
  //   } satisfies CanboConfig;
  // });
}

function createRFDimmerConfigs(
  _dimmers: LayoutContainerNode[],
  _lightsIds: LightsIds,
  _shadesIds: ShadesIds,
  _nodeQrMappings: BasestationConfig_NodeQRMapping[],
  _nodeQrMappingsParam: { deviceId: string; qrCode: string }[] | undefined,
  _actionManager: ActionManager,
): RFDimmerConfig[] {
  return [];

  //TODO: implement by fetching data properly
  // return dimmers.map((dimmer) => {
  //   const data = dimmer.data;
  //   const qrCode = nodeQrMappingsParam?.find(
  //     (mapping) => mapping.deviceId === dimmer.id,
  //   )?.qrCode;
  //   if (!qrCode) {
  //     throw new Error(`Can't find QR code for dimmer ${dimmer.id}`);
  //   }
  //   const nodeId = nodeQrMappings.find(
  //     (mapping) => mapping.qrCode === qrCode,
  //   )?.nodeId;
  //   if (!nodeId) {
  //     throw new Error(`Can't find node ID for dimmer ${dimmer.id}`);
  //   }

  //   const upButtonClick = Object.values(data.viaUp.onUpClick).map((action) => {
  //     return createDeviceAction(actionManager, lightsIds, shadesIds, action, {
  //       delayInMsec: action.delay ?? 0,
  //       activateDelayMsec: 0,
  //     });
  //   });

  //   // TODO: re-enable Hold actions once we support Room Dimmers again!
  //   // const upButtonHold = Object.values(data.viaUp.onUpHold ?? {}).map(
  //   //   (action) => {
  //   //     return createDeviceAction(
  //   //       actionManager,
  //   //       lightsIds,
  //   //       shadesIds,
  //   //       action,
  //   //       {
  //   //         delayInMsec: action.delay ?? 0,
  //   //         activateDelayMsec: 0,
  //   //       },
  //   //     );
  //   //   },
  //   // );

  //   const middleButtonClick = Object.values(data.via.onUpClick).map(
  //     (action) => {
  //       return createDeviceAction(actionManager, lightsIds, shadesIds, action, {
  //         delayInMsec: action.delay ?? 0,
  //         activateDelayMsec: 0,
  //       });
  //     },
  //   );

  //   // const middleButtonHold = Object.values(data.via.onUpHold ?? {}).map(
  //   //   (action) => {
  //   //     return createDeviceAction(
  //   //       actionManager,
  //   //       lightsIds,
  //   //       shadesIds,
  //   //       action,
  //   //       {
  //   //         delayInMsec: action.delay ?? 0,
  //   //         activateDelayMsec: 0,
  //   //       },
  //   //     );
  //   //   },
  //   // );

  //   const downButtonClick = Object.values(data.viaDown.onUpClick ?? {}).map(
  //     (action) => {
  //       return createDeviceAction(actionManager, lightsIds, shadesIds, action, {
  //         delayInMsec: action.delay ?? 0,
  //         activateDelayMsec: 0,
  //       });
  //     },
  //   );

  //   // const downButtonHold = Object.values(data.viaDown.onUpClick).map(
  //   //   (action) => {
  //   //     return createDeviceAction(
  //   //       actionManager,
  //   //       lightsIds,
  //   //       shadesIds,
  //   //       { ...action, targetValue: 0 }, // Default target brightness for RF dimmers
  //   //       { delayInMsec: action.delay ?? 0, activateDelayMsec: 0 },
  //   //     );
  //   //   },
  //   // );

  //   return {
  //     nodeId,
  //     upButtonClick,
  //     upButtonHold: [],
  //     middleButtonClick,
  //     middleButtonHold: [],
  //     downButtonClick,
  //     downButtonHold: [],
  //   } satisfies RFDimmerConfig;
  // });
}

/**
 * Creates RF reed sensor configurations for the basestation.
 */
function createRFReedConfigs(
  _doorSensors: LayoutContainerNode[],
  _lightsIds: LightsIds,
  _shadesIds: ShadesIds,
  _nodeQrMappings: BasestationConfig_NodeQRMapping[],
  _nodeQrMappingsParam: { deviceId: string; qrCode: string }[] | undefined,
  _actionManager: ActionManager,
): RFReedSensorConfig[] {
  return [];
  // TODO: implement by fetching data properly
  // return doorSensors.map((doorSensor) => {
  //   const data = doorSensor.data;
  //   const qrCode = nodeQrMappingsParam?.find(
  //     (mapping) => mapping.deviceId === doorSensor.id,
  //   )?.qrCode;
  //   if (!qrCode) {
  //     throw new Error(`Can't find QR code for door sensor ${doorSensor.id}`);
  //   }
  //   const nodeId = nodeQrMappings.find(
  //     (mapping) => mapping.qrCode === qrCode,
  //   )?.nodeId;
  //   if (!nodeId) {
  //     throw new Error(`Can't find node ID for door sensor ${doorSensor.id}`);
  //   }

  //   const doorOpenDelayMsec = (data.onOpen?.offDelay ?? 0) * 1000;
  //   const doorCloseDelayMsec = (data.onClose?.offDelay ?? 0) * 1000;
  //   // Process door open actions
  //   const doorOpenActions = Object.values(data.onOpen?.onUpClick ?? {})
  //     .map((action) => {
  //       if (action.type === "thermostat") {
  //         //TODO: Add support for thermostat actions when we add hvac to the basestation
  //         return null;
  //       }
  //       return createDeviceActionForSensor(
  //         actionManager,
  //         lightsIds,
  //         shadesIds,
  //         action,
  //         doorOpenDelayMsec,
  //       );
  //     })
  //     .filter((actionId): actionId is number => actionId !== null);

  //   // Process door close actions
  //   const doorCloseActions = Object.values(data.onClose?.onUpClick ?? {})
  //     .map((action) => {
  //       if (action.type === "thermostat") {
  //         //TODO: Add support for thermostat actions when we add hvac to the basestation
  //         return null;
  //       }
  //       return createDeviceActionForSensor(
  //         actionManager,
  //         lightsIds,
  //         shadesIds,
  //         action,
  //         doorCloseDelayMsec,
  //       );
  //     })
  //     .filter((actionId): actionId is number => actionId !== null);

  //   return {
  //     nodeId,
  //     doorOpen: doorOpenActions,
  //     doorClose: doorCloseActions,
  //   } satisfies RFReedSensorConfig;
  // });
}

/**
 * Creates RF presence sensor configurations for the basestation.
 */
function createRFPresenceConfigs(
  _presenceSensors: GraphNode[],
  _lightsIds: LightsIds,
  _shadesIds: ShadesIds,
  _nodeQrMappings: BasestationConfig_NodeQRMapping[],
  _nodeQrMappingsParam: { deviceId: string; qrCode: string }[] | undefined,
  _actionManager: ActionManager,
): RFPresenceSensorConfig[] {
  return [];

  //TODO: implement by fetching data properly

  // return presenceSensors.map((presenceSensor) => {
  //   const data = presenceSensor.data as PresenceSensorContainerNodeData;
  //   const qrCode = nodeQrMappingsParam?.find(
  //     (mapping) => mapping.deviceId === presenceSensor.id,
  //   )?.qrCode;
  //   if (!qrCode) {
  //     throw new Error(
  //       `Can't find QR code for presence sensor ${presenceSensor.id}`,
  //     );
  //   }
  //   const nodeId = nodeQrMappings.find(
  //     (mapping) => mapping.qrCode === qrCode,
  //   )?.nodeId;
  //   if (!nodeId) {
  //     throw new Error(
  //       `Can't find node ID for presence sensor ${presenceSensor.id}`,
  //     );
  //   }

  //   const activateDelayMsec = (data.onActivate?.offDelay ?? 0) * 1000;
  //   const deactivateDelayMsec = (data.onDeactivate?.offDelay ?? 0) * 1000;
  //   // Process presence open actions
  //   const onActivateActions = Object.values(data.onActivate?.onUpClick ?? {})
  //     .map((action) => {
  //       return createDeviceActionForSensor(
  //         actionManager,
  //         lightsIds,
  //         shadesIds,
  //         action,
  //         activateDelayMsec,
  //       );
  //     })
  //     .filter((actionId): actionId is number => actionId !== null);

  //   // Process presence close actions
  //   const onDeactivateActions = Object.values(
  //     data.onDeactivate?.onUpClick ?? {},
  //   )
  //     .map((action) => {
  //       return createDeviceActionForSensor(
  //         actionManager,
  //         lightsIds,
  //         shadesIds,
  //         action,
  //         deactivateDelayMsec,
  //       );
  //     })
  //     .filter((actionId): actionId is number => actionId !== null);

  //   return {
  //     nodeId,
  //     onActivate: onActivateActions,
  //     onDeactivate: onDeactivateActions,
  //   } satisfies RFPresenceSensorConfig;
  // });
}

/**
 * Creates Somfy shades configurations for the basestation.
 */
function createSomfyShadesConfigs(
  _baseStations: GraphNode[],
  _somfyShadesDeviceIdMapping?: {
    baseStationId: string;
    shadeKey: string;
    deviceId: number;
  }[],
): SomfyShadesConfig[] {
  return [];
  // const configs: SomfyShadesConfig[] = [];

  // for (const baseStation of baseStations) {
  //   const data = baseStation.data as BaseStationContainerNodeData;
  //   const somfyShades = data.somfyShades || {};

  //   Object.entries(somfyShades).forEach(([shadeKey], index) => {
  //     const mapping = somfyShadesDeviceIdMapping?.find(
  //       (m) => m.baseStationId === baseStation.id && m.shadeKey === shadeKey,
  //     );

  //     if (!mapping?.deviceId) {
  //       throw new Error(
  //         `Invalid deviceId "${mapping?.deviceId}" for baseStation ${baseStation.id}, shade ${shadeKey}`,
  //       );
  //     }
  //     const deviceId = mapping.deviceId;

  //     const config = {
  //       internalId: index,
  //       deviceId: deviceId,
  //     } satisfies SomfyShadesConfig;

  //     // Validate the config before adding
  //     if (typeof config.internalId !== "number" || isNaN(config.internalId)) {
  //       console.error("Invalid internalId:", config.internalId);
  //       return;
  //     }
  //     if (typeof config.deviceId !== "number" || isNaN(config.deviceId)) {
  //       console.error("Invalid deviceId:", config.deviceId);
  //       return;
  //     }

  //     configs.push(config);
  //   });
  // }
  // return configs;
}
