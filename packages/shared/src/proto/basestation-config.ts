// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v6.31.1
// source: basestation-config.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "";

export interface ActiveConfiguration {
  config: BasestationConfig | undefined;
  state: BasestationState | undefined;
}

export interface BasestationUpdateMessage {
  qrCode: string;
  config: BasestationConfig | undefined;
}

export interface BasestationConfig {
  /** Unique identifier for this basestation on the network */
  id: number;
  /** Version number incremented on each config change */
  version: string;
  /** Configuration for radio-frequency devices */
  rfConfig: BasestationConfig_RFConfig | undefined;
  wifiConfig: BasestationConfig_WifiConfig | undefined;
  macConfig: BasestationConfig_MACConfig | undefined;
  dhcpConfig:
    | BasestationConfig_DHCPConfig
    | undefined;
  /** WebSocket server address for cloud connectivity */
  serverAddress: string;
  /** Configurations for the inputs */
  canboConfigs: CanboConfig[];
  rfReedConfigs: RFReedSensorConfig[];
  rfPresenceConfigs: RFPresenceSensorConfig[];
  roomDimmerConfigs: RFDimmerConfig[];
  /** Configurations for the lights */
  lights: LightConfig[];
  /** Configurations for the somfy shades */
  somfyShades: SomfyShadesConfig[];
  /** Maps CAN and RF devices to their QR code */
  nodeQrMappings: BasestationConfig_NodeQRMapping[];
  /** Actions referenced by configs (shared here to reduce config size) */
  actions: Action[];
}

export interface BasestationConfig_RFConfig {
  channel: number;
  network: number;
}

export interface BasestationConfig_WifiConfig {
  ssid: string;
  password: string;
}

export interface BasestationConfig_MACConfig {
  useMacAddress: boolean;
  macAddress: string;
}

export interface BasestationConfig_DHCPConfig {
  staticIp: boolean;
  ipAddress: string;
  subnetMask: string;
  gateway: string;
  dnsServer: string;
}

export interface BasestationConfig_NodeQRMapping {
  /** The QR code string value for this device */
  qrCode: string;
  /** Either the CAN or the RF node ID */
  nodeId: number;
  type: BasestationConfig_NodeQRMapping_DeviceType;
}

export enum BasestationConfig_NodeQRMapping_DeviceType {
  CAN = 0,
  RF = 1,
  UNRECOGNIZED = -1,
}

/** Action that can be triggered by nodes to control lights. */
export interface Action {
  id: number;
  lightId?: number | undefined;
  somfyShadeId?: number | undefined;
  dimSpeedMsec: number;
  targetBrightness: number;
  /**
   * Optional fields for different action types
   * Note: we may use a oneof here for even smaller messages
   */
  onBrightness: number;
  offBrightness: number;
  delayInMsec: number;
  activateDelayMsec: number;
}

export interface RFDimmerConfig {
  nodeId: number;
  /** Refer to relevant Action IDs */
  middleButtonClick: number[];
  upButtonClick: number[];
  downButtonClick: number[];
  middleButtonHold: number[];
  upButtonHold: number[];
  downButtonHold: number[];
}

export interface RFReedSensorConfig {
  nodeId: number;
  /** Refer to relevant Action IDs */
  doorClose: number[];
  doorOpen: number[];
}

export interface RFPresenceSensorConfig {
  nodeId: number;
  onActivate: number[];
  onDeactivate: number[];
}

export interface CanboConfig {
  nodeId: number;
  threePinInputs: CanboConfig_ThreePinInput[];
  twoPinInputs: CanboConfig_TwoPinInput[];
  adcInputs: CanboConfig_ADCInput | undefined;
  outputs: CanboConfig_Output[];
}

export interface CanboConfig_ThreePinInput {
  connectorId: number;
  type: CanboConfig_ThreePinInput_ConnectorType;
  toggle?: CanboConfig_ThreePinInput_ToggleConfig | undefined;
  momentary?: CanboConfig_ThreePinInput_MomentaryConfig | undefined;
}

export enum CanboConfig_ThreePinInput_ConnectorType {
  TOGGLE = 0,
  MOMENTARY = 1,
  UNRECOGNIZED = -1,
}

export interface CanboConfig_ThreePinInput_ToggleConfig {
  /** Refer to relevant Action IDs */
  upClick: number[];
  upHold: number[];
  downClick: number[];
  downHold: number[];
  upPress: number[];
  upRelease: number[];
  downPress: number[];
  downRelease: number[];
  upHoldRelease: number[];
  downHoldRelease: number[];
}

export interface CanboConfig_ThreePinInput_MomentaryConfig {
  /** Refer to relevant Action IDs */
  upClick: number[];
  upHold: number[];
  upPress: number[];
  upRelease: number[];
  upHoldRelease: number[];
}

export interface CanboConfig_TwoPinInput {
  connectorId: number;
  type: CanboConfig_TwoPinInput_ConnectorType;
  momentary?: CanboConfig_TwoPinInput_MomentaryConfig | undefined;
  doorSensor?: CanboConfig_TwoPinInput_DoorSensorConfig | undefined;
}

export enum CanboConfig_TwoPinInput_ConnectorType {
  MOMENTARY = 0,
  DOOR_SENSOR = 1,
  UNRECOGNIZED = -1,
}

export interface CanboConfig_TwoPinInput_MomentaryConfig {
  /** Refer to relevant Action IDs */
  upClick: number[];
  upHold: number[];
  upPress: number[];
  upRelease: number[];
  upHoldRelease: number[];
}

export interface CanboConfig_TwoPinInput_DoorSensorConfig {
  /** Refer to relevant Action IDs */
  onOpen: number[];
  onClose: number[];
}

export interface CanboConfig_ADCInput {
  connectorId: number;
  type: CanboConfig_ADCInput_ConnectorType;
  knob?: CanboConfig_ADCInput_KnobConfig | undefined;
  thermostat?: CanboConfig_ADCInput_ThermostatConfig | undefined;
  pir?: CanboConfig_ADCInput_PIRConfig | undefined;
}

export enum CanboConfig_ADCInput_ConnectorType {
  KNOB = 0,
  THERMOSTAT = 1,
  PIR = 2,
  UNRECOGNIZED = -1,
}

export interface CanboConfig_ADCInput_KnobConfig {
  /** Refer to relevant Action IDs */
  onTurn: number[];
}

export interface CanboConfig_ADCInput_ThermostatConfig {
  /** Refer to relevant Action IDs */
  thermostatAction: number[];
}

export interface CanboConfig_ADCInput_PIRConfig {
  /** Refer to relevant Action IDs */
  onActivate: number[];
  onDeactivate: number[];
}

export interface CanboConfig_Output {
  connectorId: number;
  connectorType: CanboConfig_Output_ConnectorType;
}

export enum CanboConfig_Output_ConnectorType {
  RELAY = 0,
  UNRECOGNIZED = -1,
}

export interface LightConfig {
  id: number;
  /** How fast the light dims in milliseconds */
  dimSpeedMsec: number;
  /** Configurations for the fixtures */
  fixtures: LightConfig_FixtureConfig[];
}

export interface LightConfig_FixtureConfig {
  /** Min and max brightness of the fixture */
  minBrightness: number;
  maxBrightness: number;
  /** Configuration for the fixture */
  type: LightConfig_FixtureConfig_FixtureType;
  dmx?: LightConfig_FixtureConfig_DMXConfig | undefined;
  rf?: LightConfig_FixtureConfig_RFConfig | undefined;
  zeroToTenVolt?:
    | LightConfig_FixtureConfig_ZeroToTenVoltConfig
    | undefined;
  /** Analog0To10VConfig analog_0_10 = 7; */
  relay?: LightConfig_FixtureConfig_RelayConfig | undefined;
}

export enum LightConfig_FixtureConfig_FixtureType {
  DMX = 0,
  RF = 1,
  ZERO_TO_TEN_VOLT = 2,
  RELAY = 3,
  UNRECOGNIZED = -1,
}

export interface LightConfig_FixtureConfig_DMXConfig {
  params: LightConfig_FixtureConfig_DMXConfig_LightParams | undefined;
  rgb: LightConfig_FixtureConfig_DMXConfig_RGBConfig | undefined;
  channels: number[];
  type: LightConfig_FixtureConfig_DMXConfig_FixtureTypeConfig;
}

export enum LightConfig_FixtureConfig_DMXConfig_FixtureTypeConfig {
  D4 = 0,
  TUNABLE_WHITE = 1,
  ELV = 2,
  DF_12 = 3,
  EST = 4,
  RGB_STRIP = 5,
  UNRECOGNIZED = -1,
}

export interface LightConfig_FixtureConfig_DMXConfig_LightParams {
  min1: number;
  max1: number;
  gamma1: number;
  min2: number;
  max2: number;
  gamma2: number;
}

export interface LightConfig_FixtureConfig_DMXConfig_RGBConfig {
  red: number;
  green: number;
  blue: number;
}

export interface LightConfig_FixtureConfig_RFConfig {
  type: LightConfig_FixtureConfig_RFConfig_Type;
  nodeId: number;
}

export enum LightConfig_FixtureConfig_RFConfig_Type {
  DIMMER = 0,
  SWITCH = 1,
  UNRECOGNIZED = -1,
}

export interface LightConfig_FixtureConfig_ZeroToTenVoltConfig {
  type: LightConfig_FixtureConfig_ZeroToTenVoltConfig_Type;
  nodeId: number;
  useRelay: boolean;
  outConnectorId: number;
}

export enum LightConfig_FixtureConfig_ZeroToTenVoltConfig_Type {
  SOURCING = 0,
  SINKING = 1,
  UNRECOGNIZED = -1,
}

export interface LightConfig_FixtureConfig_RelayConfig {
  nodeId: number;
  outConnectorId: number;
}

/** Need to map the id used in the configurator to the id assigned by the basestation during pairing */
export interface SomfyShadesConfig {
  internalId: number;
  deviceId: number;
}

export interface BasestationState {
  lights: LightState[];
  buttons: ButtonState[];
  provisionedDevices: ProvisioningState[];
  reeds: RFReedState[];
  presences: RFPresenceState[];
  pirs: PIRState[];
}

export interface RFReedState {
  nodeId: number;
  sensorStatus: RFReedState_Status;
  lastModifiedTime: number;
  batteryVoltage: number;
}

export enum RFReedState_Status {
  UNKNOWN = 0,
  OPEN = 1,
  CLOSED = 2,
  UNRECOGNIZED = -1,
}

export interface RFPresenceState {
  nodeId: number;
  sensorStatus: RFPresenceState_Status;
  lastModifiedTime: number;
  batteryVoltage: number;
}

export enum RFPresenceState_Status {
  UNKNOWN = 0,
  ACTIVATED = 1,
  DEACTIVATED = 2,
  UNRECOGNIZED = -1,
}

export interface ProvisioningState {
  nodeId: number;
  isProvisioned: boolean;
  errorCode: ProvisioningState_ProvisioningErrorCode;
  lastSeenTime: number;
  rssi: number;
}

export enum ProvisioningState_ProvisioningErrorCode {
  NONE = 0,
  NOT_FOUND = 1,
  NO_CANBO_CONFIG = 2,
  COULD_NOT_SEND_PROVISIONING_COMMAND = 3,
  NO_REED_CONFIG = 4,
  NO_PRESENCE_CONFIG = 5,
  UNRECOGNIZED = -1,
}

export interface LightState {
  id: number;
  brightness: number;
  targetValue: number;
  dimSpeedMsec: number;
  lastModifiedTime: number;
  activeAfterTime: number;
  isTransitioning: boolean;
  lastBrightnessBeforeAction: number;
  lastTransitionStopReason: LightState_TransitionStopReason;
}

export enum LightState_TransitionStopReason {
  TRANSITION_STOP_UNKNOWN = 0,
  TRANSITION_STOP_HOLD_RELEASE = 1,
  TRANSITION_STOP_CLICK_COMMAND = 2,
  TRANSITION_STOP_TARGET_REACHED = 3,
  UNRECOGNIZED = -1,
}

export interface ButtonState {
  nodeId: number;
  connectorId: number;
  currentState: ButtonState_State;
  lastModifiedTime: number;
}

export enum ButtonState_State {
  BUTTON_STATE_RELEASED = 0,
  BUTTON_STATE_UP_PRESSED = 1,
  BUTTON_STATE_DOWN_PRESSED = 2,
  UNRECOGNIZED = -1,
}

export interface PIRState {
  nodeId: number;
  isActivated: boolean;
  lastActivatedTime: number;
  deactivateAfterTime: number;
}

export interface ESPStatus {
  status: ESPStatus_Status;
}

export enum ESPStatus_Status {
  UNKNOWN = 0,
  CONNECTING = 1,
  CONNECTED = 2,
  WEBSOCKET_CONNECTED = 3,
  WEBSOCKET_DISCONNECTED = 4,
  WEBSOCKET_CONNECTION_FAILED = 5,
  DISCONNECTED = 6,
  WIFI_ERROR = 7,
  DHCP_ERROR = 8,
  UNRECOGNIZED = -1,
}

export interface ESPUpdateConfig {
  macConfig: ESPUpdateConfig_MACConfig | undefined;
  dhcpConfig: ESPUpdateConfig_DHCPConfig | undefined;
  wifiConfig: ESPUpdateConfig_WifiConfig | undefined;
  qrCode: string;
  serverAddress: string;
  configVersion: string;
}

export interface ESPUpdateConfig_MACConfig {
  useMacAddress: boolean;
  macAddress: string;
}

export interface ESPUpdateConfig_DHCPConfig {
  staticIp: boolean;
  ipAddress: string;
  subnetMask: string;
  gateway: string;
  dnsServer: string;
}

export interface ESPUpdateConfig_WifiConfig {
  ssid: string;
  password: string;
}

function createBaseActiveConfiguration(): ActiveConfiguration {
  return { config: undefined, state: undefined };
}

export const ActiveConfiguration: MessageFns<ActiveConfiguration> = {
  encode(message: ActiveConfiguration, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.config !== undefined) {
      BasestationConfig.encode(message.config, writer.uint32(10).fork()).join();
    }
    if (message.state !== undefined) {
      BasestationState.encode(message.state, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ActiveConfiguration {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseActiveConfiguration();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.config = BasestationConfig.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.state = BasestationState.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ActiveConfiguration>, I>>(base?: I): ActiveConfiguration {
    return ActiveConfiguration.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ActiveConfiguration>, I>>(object: I): ActiveConfiguration {
    const message = createBaseActiveConfiguration();
    message.config = (object.config !== undefined && object.config !== null)
      ? BasestationConfig.fromPartial(object.config)
      : undefined;
    message.state = (object.state !== undefined && object.state !== null)
      ? BasestationState.fromPartial(object.state)
      : undefined;
    return message;
  },
};

function createBaseBasestationUpdateMessage(): BasestationUpdateMessage {
  return { qrCode: "", config: undefined };
}

export const BasestationUpdateMessage: MessageFns<BasestationUpdateMessage> = {
  encode(message: BasestationUpdateMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.qrCode !== "") {
      writer.uint32(10).string(message.qrCode);
    }
    if (message.config !== undefined) {
      BasestationConfig.encode(message.config, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BasestationUpdateMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBasestationUpdateMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.qrCode = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.config = BasestationConfig.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<BasestationUpdateMessage>, I>>(base?: I): BasestationUpdateMessage {
    return BasestationUpdateMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BasestationUpdateMessage>, I>>(object: I): BasestationUpdateMessage {
    const message = createBaseBasestationUpdateMessage();
    message.qrCode = object.qrCode ?? "";
    message.config = (object.config !== undefined && object.config !== null)
      ? BasestationConfig.fromPartial(object.config)
      : undefined;
    return message;
  },
};

function createBaseBasestationConfig(): BasestationConfig {
  return {
    id: 0,
    version: "",
    rfConfig: undefined,
    wifiConfig: undefined,
    macConfig: undefined,
    dhcpConfig: undefined,
    serverAddress: "",
    canboConfigs: [],
    rfReedConfigs: [],
    rfPresenceConfigs: [],
    roomDimmerConfigs: [],
    lights: [],
    somfyShades: [],
    nodeQrMappings: [],
    actions: [],
  };
}

export const BasestationConfig: MessageFns<BasestationConfig> = {
  encode(message: BasestationConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== 0) {
      writer.uint32(8).uint32(message.id);
    }
    if (message.version !== "") {
      writer.uint32(18).string(message.version);
    }
    if (message.rfConfig !== undefined) {
      BasestationConfig_RFConfig.encode(message.rfConfig, writer.uint32(26).fork()).join();
    }
    if (message.wifiConfig !== undefined) {
      BasestationConfig_WifiConfig.encode(message.wifiConfig, writer.uint32(34).fork()).join();
    }
    if (message.macConfig !== undefined) {
      BasestationConfig_MACConfig.encode(message.macConfig, writer.uint32(42).fork()).join();
    }
    if (message.dhcpConfig !== undefined) {
      BasestationConfig_DHCPConfig.encode(message.dhcpConfig, writer.uint32(50).fork()).join();
    }
    if (message.serverAddress !== "") {
      writer.uint32(58).string(message.serverAddress);
    }
    for (const v of message.canboConfigs) {
      CanboConfig.encode(v!, writer.uint32(66).fork()).join();
    }
    for (const v of message.rfReedConfigs) {
      RFReedSensorConfig.encode(v!, writer.uint32(74).fork()).join();
    }
    for (const v of message.rfPresenceConfigs) {
      RFPresenceSensorConfig.encode(v!, writer.uint32(82).fork()).join();
    }
    for (const v of message.roomDimmerConfigs) {
      RFDimmerConfig.encode(v!, writer.uint32(90).fork()).join();
    }
    for (const v of message.lights) {
      LightConfig.encode(v!, writer.uint32(98).fork()).join();
    }
    for (const v of message.somfyShades) {
      SomfyShadesConfig.encode(v!, writer.uint32(106).fork()).join();
    }
    for (const v of message.nodeQrMappings) {
      BasestationConfig_NodeQRMapping.encode(v!, writer.uint32(114).fork()).join();
    }
    for (const v of message.actions) {
      Action.encode(v!, writer.uint32(122).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BasestationConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBasestationConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.id = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.version = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.rfConfig = BasestationConfig_RFConfig.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.wifiConfig = BasestationConfig_WifiConfig.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.macConfig = BasestationConfig_MACConfig.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.dhcpConfig = BasestationConfig_DHCPConfig.decode(reader, reader.uint32());
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.serverAddress = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.canboConfigs.push(CanboConfig.decode(reader, reader.uint32()));
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.rfReedConfigs.push(RFReedSensorConfig.decode(reader, reader.uint32()));
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.rfPresenceConfigs.push(RFPresenceSensorConfig.decode(reader, reader.uint32()));
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.roomDimmerConfigs.push(RFDimmerConfig.decode(reader, reader.uint32()));
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.lights.push(LightConfig.decode(reader, reader.uint32()));
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.somfyShades.push(SomfyShadesConfig.decode(reader, reader.uint32()));
          continue;
        }
        case 14: {
          if (tag !== 114) {
            break;
          }

          message.nodeQrMappings.push(BasestationConfig_NodeQRMapping.decode(reader, reader.uint32()));
          continue;
        }
        case 15: {
          if (tag !== 122) {
            break;
          }

          message.actions.push(Action.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<BasestationConfig>, I>>(base?: I): BasestationConfig {
    return BasestationConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BasestationConfig>, I>>(object: I): BasestationConfig {
    const message = createBaseBasestationConfig();
    message.id = object.id ?? 0;
    message.version = object.version ?? "";
    message.rfConfig = (object.rfConfig !== undefined && object.rfConfig !== null)
      ? BasestationConfig_RFConfig.fromPartial(object.rfConfig)
      : undefined;
    message.wifiConfig = (object.wifiConfig !== undefined && object.wifiConfig !== null)
      ? BasestationConfig_WifiConfig.fromPartial(object.wifiConfig)
      : undefined;
    message.macConfig = (object.macConfig !== undefined && object.macConfig !== null)
      ? BasestationConfig_MACConfig.fromPartial(object.macConfig)
      : undefined;
    message.dhcpConfig = (object.dhcpConfig !== undefined && object.dhcpConfig !== null)
      ? BasestationConfig_DHCPConfig.fromPartial(object.dhcpConfig)
      : undefined;
    message.serverAddress = object.serverAddress ?? "";
    message.canboConfigs = object.canboConfigs?.map((e) => CanboConfig.fromPartial(e)) || [];
    message.rfReedConfigs = object.rfReedConfigs?.map((e) => RFReedSensorConfig.fromPartial(e)) || [];
    message.rfPresenceConfigs = object.rfPresenceConfigs?.map((e) => RFPresenceSensorConfig.fromPartial(e)) || [];
    message.roomDimmerConfigs = object.roomDimmerConfigs?.map((e) => RFDimmerConfig.fromPartial(e)) || [];
    message.lights = object.lights?.map((e) => LightConfig.fromPartial(e)) || [];
    message.somfyShades = object.somfyShades?.map((e) => SomfyShadesConfig.fromPartial(e)) || [];
    message.nodeQrMappings = object.nodeQrMappings?.map((e) => BasestationConfig_NodeQRMapping.fromPartial(e)) || [];
    message.actions = object.actions?.map((e) => Action.fromPartial(e)) || [];
    return message;
  },
};

function createBaseBasestationConfig_RFConfig(): BasestationConfig_RFConfig {
  return { channel: 0, network: 0 };
}

export const BasestationConfig_RFConfig: MessageFns<BasestationConfig_RFConfig> = {
  encode(message: BasestationConfig_RFConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.channel !== 0) {
      writer.uint32(8).uint32(message.channel);
    }
    if (message.network !== 0) {
      writer.uint32(16).uint32(message.network);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BasestationConfig_RFConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBasestationConfig_RFConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.channel = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.network = reader.uint32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<BasestationConfig_RFConfig>, I>>(base?: I): BasestationConfig_RFConfig {
    return BasestationConfig_RFConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BasestationConfig_RFConfig>, I>>(object: I): BasestationConfig_RFConfig {
    const message = createBaseBasestationConfig_RFConfig();
    message.channel = object.channel ?? 0;
    message.network = object.network ?? 0;
    return message;
  },
};

function createBaseBasestationConfig_WifiConfig(): BasestationConfig_WifiConfig {
  return { ssid: "", password: "" };
}

export const BasestationConfig_WifiConfig: MessageFns<BasestationConfig_WifiConfig> = {
  encode(message: BasestationConfig_WifiConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.ssid !== "") {
      writer.uint32(10).string(message.ssid);
    }
    if (message.password !== "") {
      writer.uint32(18).string(message.password);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BasestationConfig_WifiConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBasestationConfig_WifiConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.ssid = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.password = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<BasestationConfig_WifiConfig>, I>>(base?: I): BasestationConfig_WifiConfig {
    return BasestationConfig_WifiConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BasestationConfig_WifiConfig>, I>>(object: I): BasestationConfig_WifiConfig {
    const message = createBaseBasestationConfig_WifiConfig();
    message.ssid = object.ssid ?? "";
    message.password = object.password ?? "";
    return message;
  },
};

function createBaseBasestationConfig_MACConfig(): BasestationConfig_MACConfig {
  return { useMacAddress: false, macAddress: "" };
}

export const BasestationConfig_MACConfig: MessageFns<BasestationConfig_MACConfig> = {
  encode(message: BasestationConfig_MACConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.useMacAddress !== false) {
      writer.uint32(8).bool(message.useMacAddress);
    }
    if (message.macAddress !== "") {
      writer.uint32(18).string(message.macAddress);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BasestationConfig_MACConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBasestationConfig_MACConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.useMacAddress = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.macAddress = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<BasestationConfig_MACConfig>, I>>(base?: I): BasestationConfig_MACConfig {
    return BasestationConfig_MACConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BasestationConfig_MACConfig>, I>>(object: I): BasestationConfig_MACConfig {
    const message = createBaseBasestationConfig_MACConfig();
    message.useMacAddress = object.useMacAddress ?? false;
    message.macAddress = object.macAddress ?? "";
    return message;
  },
};

function createBaseBasestationConfig_DHCPConfig(): BasestationConfig_DHCPConfig {
  return { staticIp: false, ipAddress: "", subnetMask: "", gateway: "", dnsServer: "" };
}

export const BasestationConfig_DHCPConfig: MessageFns<BasestationConfig_DHCPConfig> = {
  encode(message: BasestationConfig_DHCPConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.staticIp !== false) {
      writer.uint32(8).bool(message.staticIp);
    }
    if (message.ipAddress !== "") {
      writer.uint32(18).string(message.ipAddress);
    }
    if (message.subnetMask !== "") {
      writer.uint32(26).string(message.subnetMask);
    }
    if (message.gateway !== "") {
      writer.uint32(34).string(message.gateway);
    }
    if (message.dnsServer !== "") {
      writer.uint32(42).string(message.dnsServer);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BasestationConfig_DHCPConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBasestationConfig_DHCPConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.staticIp = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.ipAddress = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.subnetMask = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.gateway = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.dnsServer = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<BasestationConfig_DHCPConfig>, I>>(base?: I): BasestationConfig_DHCPConfig {
    return BasestationConfig_DHCPConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BasestationConfig_DHCPConfig>, I>>(object: I): BasestationConfig_DHCPConfig {
    const message = createBaseBasestationConfig_DHCPConfig();
    message.staticIp = object.staticIp ?? false;
    message.ipAddress = object.ipAddress ?? "";
    message.subnetMask = object.subnetMask ?? "";
    message.gateway = object.gateway ?? "";
    message.dnsServer = object.dnsServer ?? "";
    return message;
  },
};

function createBaseBasestationConfig_NodeQRMapping(): BasestationConfig_NodeQRMapping {
  return { qrCode: "", nodeId: 0, type: 0 };
}

export const BasestationConfig_NodeQRMapping: MessageFns<BasestationConfig_NodeQRMapping> = {
  encode(message: BasestationConfig_NodeQRMapping, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.qrCode !== "") {
      writer.uint32(10).string(message.qrCode);
    }
    if (message.nodeId !== 0) {
      writer.uint32(16).uint32(message.nodeId);
    }
    if (message.type !== 0) {
      writer.uint32(24).int32(message.type);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BasestationConfig_NodeQRMapping {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBasestationConfig_NodeQRMapping();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.qrCode = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.nodeId = reader.uint32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.type = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<BasestationConfig_NodeQRMapping>, I>>(base?: I): BasestationConfig_NodeQRMapping {
    return BasestationConfig_NodeQRMapping.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BasestationConfig_NodeQRMapping>, I>>(
    object: I,
  ): BasestationConfig_NodeQRMapping {
    const message = createBaseBasestationConfig_NodeQRMapping();
    message.qrCode = object.qrCode ?? "";
    message.nodeId = object.nodeId ?? 0;
    message.type = object.type ?? 0;
    return message;
  },
};

function createBaseAction(): Action {
  return {
    id: 0,
    lightId: undefined,
    somfyShadeId: undefined,
    dimSpeedMsec: 0,
    targetBrightness: 0,
    onBrightness: 0,
    offBrightness: 0,
    delayInMsec: 0,
    activateDelayMsec: 0,
  };
}

export const Action: MessageFns<Action> = {
  encode(message: Action, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== 0) {
      writer.uint32(8).uint32(message.id);
    }
    if (message.lightId !== undefined) {
      writer.uint32(16).uint32(message.lightId);
    }
    if (message.somfyShadeId !== undefined) {
      writer.uint32(24).uint32(message.somfyShadeId);
    }
    if (message.dimSpeedMsec !== 0) {
      writer.uint32(32).uint32(message.dimSpeedMsec);
    }
    if (message.targetBrightness !== 0) {
      writer.uint32(45).float(message.targetBrightness);
    }
    if (message.onBrightness !== 0) {
      writer.uint32(53).float(message.onBrightness);
    }
    if (message.offBrightness !== 0) {
      writer.uint32(61).float(message.offBrightness);
    }
    if (message.delayInMsec !== 0) {
      writer.uint32(64).uint32(message.delayInMsec);
    }
    if (message.activateDelayMsec !== 0) {
      writer.uint32(72).uint32(message.activateDelayMsec);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Action {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAction();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.id = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.lightId = reader.uint32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.somfyShadeId = reader.uint32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.dimSpeedMsec = reader.uint32();
          continue;
        }
        case 5: {
          if (tag !== 45) {
            break;
          }

          message.targetBrightness = reader.float();
          continue;
        }
        case 6: {
          if (tag !== 53) {
            break;
          }

          message.onBrightness = reader.float();
          continue;
        }
        case 7: {
          if (tag !== 61) {
            break;
          }

          message.offBrightness = reader.float();
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.delayInMsec = reader.uint32();
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.activateDelayMsec = reader.uint32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<Action>, I>>(base?: I): Action {
    return Action.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Action>, I>>(object: I): Action {
    const message = createBaseAction();
    message.id = object.id ?? 0;
    message.lightId = object.lightId ?? undefined;
    message.somfyShadeId = object.somfyShadeId ?? undefined;
    message.dimSpeedMsec = object.dimSpeedMsec ?? 0;
    message.targetBrightness = object.targetBrightness ?? 0;
    message.onBrightness = object.onBrightness ?? 0;
    message.offBrightness = object.offBrightness ?? 0;
    message.delayInMsec = object.delayInMsec ?? 0;
    message.activateDelayMsec = object.activateDelayMsec ?? 0;
    return message;
  },
};

function createBaseRFDimmerConfig(): RFDimmerConfig {
  return {
    nodeId: 0,
    middleButtonClick: [],
    upButtonClick: [],
    downButtonClick: [],
    middleButtonHold: [],
    upButtonHold: [],
    downButtonHold: [],
  };
}

export const RFDimmerConfig: MessageFns<RFDimmerConfig> = {
  encode(message: RFDimmerConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.nodeId !== 0) {
      writer.uint32(8).uint32(message.nodeId);
    }
    writer.uint32(18).fork();
    for (const v of message.middleButtonClick) {
      writer.uint32(v);
    }
    writer.join();
    writer.uint32(26).fork();
    for (const v of message.upButtonClick) {
      writer.uint32(v);
    }
    writer.join();
    writer.uint32(34).fork();
    for (const v of message.downButtonClick) {
      writer.uint32(v);
    }
    writer.join();
    writer.uint32(42).fork();
    for (const v of message.middleButtonHold) {
      writer.uint32(v);
    }
    writer.join();
    writer.uint32(50).fork();
    for (const v of message.upButtonHold) {
      writer.uint32(v);
    }
    writer.join();
    writer.uint32(58).fork();
    for (const v of message.downButtonHold) {
      writer.uint32(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RFDimmerConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRFDimmerConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.nodeId = reader.uint32();
          continue;
        }
        case 2: {
          if (tag === 16) {
            message.middleButtonClick.push(reader.uint32());

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.middleButtonClick.push(reader.uint32());
            }

            continue;
          }

          break;
        }
        case 3: {
          if (tag === 24) {
            message.upButtonClick.push(reader.uint32());

            continue;
          }

          if (tag === 26) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.upButtonClick.push(reader.uint32());
            }

            continue;
          }

          break;
        }
        case 4: {
          if (tag === 32) {
            message.downButtonClick.push(reader.uint32());

            continue;
          }

          if (tag === 34) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.downButtonClick.push(reader.uint32());
            }

            continue;
          }

          break;
        }
        case 5: {
          if (tag === 40) {
            message.middleButtonHold.push(reader.uint32());

            continue;
          }

          if (tag === 42) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.middleButtonHold.push(reader.uint32());
            }

            continue;
          }

          break;
        }
        case 6: {
          if (tag === 48) {
            message.upButtonHold.push(reader.uint32());

            continue;
          }

          if (tag === 50) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.upButtonHold.push(reader.uint32());
            }

            continue;
          }

          break;
        }
        case 7: {
          if (tag === 56) {
            message.downButtonHold.push(reader.uint32());

            continue;
          }

          if (tag === 58) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.downButtonHold.push(reader.uint32());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<RFDimmerConfig>, I>>(base?: I): RFDimmerConfig {
    return RFDimmerConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RFDimmerConfig>, I>>(object: I): RFDimmerConfig {
    const message = createBaseRFDimmerConfig();
    message.nodeId = object.nodeId ?? 0;
    message.middleButtonClick = object.middleButtonClick?.map((e) => e) || [];
    message.upButtonClick = object.upButtonClick?.map((e) => e) || [];
    message.downButtonClick = object.downButtonClick?.map((e) => e) || [];
    message.middleButtonHold = object.middleButtonHold?.map((e) => e) || [];
    message.upButtonHold = object.upButtonHold?.map((e) => e) || [];
    message.downButtonHold = object.downButtonHold?.map((e) => e) || [];
    return message;
  },
};

function createBaseRFReedSensorConfig(): RFReedSensorConfig {
  return { nodeId: 0, doorClose: [], doorOpen: [] };
}

export const RFReedSensorConfig: MessageFns<RFReedSensorConfig> = {
  encode(message: RFReedSensorConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.nodeId !== 0) {
      writer.uint32(8).uint32(message.nodeId);
    }
    writer.uint32(18).fork();
    for (const v of message.doorClose) {
      writer.uint32(v);
    }
    writer.join();
    writer.uint32(26).fork();
    for (const v of message.doorOpen) {
      writer.uint32(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RFReedSensorConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRFReedSensorConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.nodeId = reader.uint32();
          continue;
        }
        case 2: {
          if (tag === 16) {
            message.doorClose.push(reader.uint32());

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.doorClose.push(reader.uint32());
            }

            continue;
          }

          break;
        }
        case 3: {
          if (tag === 24) {
            message.doorOpen.push(reader.uint32());

            continue;
          }

          if (tag === 26) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.doorOpen.push(reader.uint32());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<RFReedSensorConfig>, I>>(base?: I): RFReedSensorConfig {
    return RFReedSensorConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RFReedSensorConfig>, I>>(object: I): RFReedSensorConfig {
    const message = createBaseRFReedSensorConfig();
    message.nodeId = object.nodeId ?? 0;
    message.doorClose = object.doorClose?.map((e) => e) || [];
    message.doorOpen = object.doorOpen?.map((e) => e) || [];
    return message;
  },
};

function createBaseRFPresenceSensorConfig(): RFPresenceSensorConfig {
  return { nodeId: 0, onActivate: [], onDeactivate: [] };
}

export const RFPresenceSensorConfig: MessageFns<RFPresenceSensorConfig> = {
  encode(message: RFPresenceSensorConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.nodeId !== 0) {
      writer.uint32(8).uint32(message.nodeId);
    }
    writer.uint32(18).fork();
    for (const v of message.onActivate) {
      writer.uint32(v);
    }
    writer.join();
    writer.uint32(26).fork();
    for (const v of message.onDeactivate) {
      writer.uint32(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RFPresenceSensorConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRFPresenceSensorConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.nodeId = reader.uint32();
          continue;
        }
        case 2: {
          if (tag === 16) {
            message.onActivate.push(reader.uint32());

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.onActivate.push(reader.uint32());
            }

            continue;
          }

          break;
        }
        case 3: {
          if (tag === 24) {
            message.onDeactivate.push(reader.uint32());

            continue;
          }

          if (tag === 26) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.onDeactivate.push(reader.uint32());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<RFPresenceSensorConfig>, I>>(base?: I): RFPresenceSensorConfig {
    return RFPresenceSensorConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RFPresenceSensorConfig>, I>>(object: I): RFPresenceSensorConfig {
    const message = createBaseRFPresenceSensorConfig();
    message.nodeId = object.nodeId ?? 0;
    message.onActivate = object.onActivate?.map((e) => e) || [];
    message.onDeactivate = object.onDeactivate?.map((e) => e) || [];
    return message;
  },
};

function createBaseCanboConfig(): CanboConfig {
  return { nodeId: 0, threePinInputs: [], twoPinInputs: [], adcInputs: undefined, outputs: [] };
}

export const CanboConfig: MessageFns<CanboConfig> = {
  encode(message: CanboConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.nodeId !== 0) {
      writer.uint32(8).uint32(message.nodeId);
    }
    for (const v of message.threePinInputs) {
      CanboConfig_ThreePinInput.encode(v!, writer.uint32(18).fork()).join();
    }
    for (const v of message.twoPinInputs) {
      CanboConfig_TwoPinInput.encode(v!, writer.uint32(26).fork()).join();
    }
    if (message.adcInputs !== undefined) {
      CanboConfig_ADCInput.encode(message.adcInputs, writer.uint32(34).fork()).join();
    }
    for (const v of message.outputs) {
      CanboConfig_Output.encode(v!, writer.uint32(42).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CanboConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCanboConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.nodeId = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.threePinInputs.push(CanboConfig_ThreePinInput.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.twoPinInputs.push(CanboConfig_TwoPinInput.decode(reader, reader.uint32()));
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.adcInputs = CanboConfig_ADCInput.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.outputs.push(CanboConfig_Output.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CanboConfig>, I>>(base?: I): CanboConfig {
    return CanboConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CanboConfig>, I>>(object: I): CanboConfig {
    const message = createBaseCanboConfig();
    message.nodeId = object.nodeId ?? 0;
    message.threePinInputs = object.threePinInputs?.map((e) => CanboConfig_ThreePinInput.fromPartial(e)) || [];
    message.twoPinInputs = object.twoPinInputs?.map((e) => CanboConfig_TwoPinInput.fromPartial(e)) || [];
    message.adcInputs = (object.adcInputs !== undefined && object.adcInputs !== null)
      ? CanboConfig_ADCInput.fromPartial(object.adcInputs)
      : undefined;
    message.outputs = object.outputs?.map((e) => CanboConfig_Output.fromPartial(e)) || [];
    return message;
  },
};

function createBaseCanboConfig_ThreePinInput(): CanboConfig_ThreePinInput {
  return { connectorId: 0, type: 0, toggle: undefined, momentary: undefined };
}

export const CanboConfig_ThreePinInput: MessageFns<CanboConfig_ThreePinInput> = {
  encode(message: CanboConfig_ThreePinInput, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.connectorId !== 0) {
      writer.uint32(8).uint32(message.connectorId);
    }
    if (message.type !== 0) {
      writer.uint32(16).int32(message.type);
    }
    if (message.toggle !== undefined) {
      CanboConfig_ThreePinInput_ToggleConfig.encode(message.toggle, writer.uint32(26).fork()).join();
    }
    if (message.momentary !== undefined) {
      CanboConfig_ThreePinInput_MomentaryConfig.encode(message.momentary, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CanboConfig_ThreePinInput {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCanboConfig_ThreePinInput();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.connectorId = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.type = reader.int32() as any;
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.toggle = CanboConfig_ThreePinInput_ToggleConfig.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.momentary = CanboConfig_ThreePinInput_MomentaryConfig.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CanboConfig_ThreePinInput>, I>>(base?: I): CanboConfig_ThreePinInput {
    return CanboConfig_ThreePinInput.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CanboConfig_ThreePinInput>, I>>(object: I): CanboConfig_ThreePinInput {
    const message = createBaseCanboConfig_ThreePinInput();
    message.connectorId = object.connectorId ?? 0;
    message.type = object.type ?? 0;
    message.toggle = (object.toggle !== undefined && object.toggle !== null)
      ? CanboConfig_ThreePinInput_ToggleConfig.fromPartial(object.toggle)
      : undefined;
    message.momentary = (object.momentary !== undefined && object.momentary !== null)
      ? CanboConfig_ThreePinInput_MomentaryConfig.fromPartial(object.momentary)
      : undefined;
    return message;
  },
};

function createBaseCanboConfig_ThreePinInput_ToggleConfig(): CanboConfig_ThreePinInput_ToggleConfig {
  return {
    upClick: [],
    upHold: [],
    downClick: [],
    downHold: [],
    upPress: [],
    upRelease: [],
    downPress: [],
    downRelease: [],
    upHoldRelease: [],
    downHoldRelease: [],
  };
}

export const CanboConfig_ThreePinInput_ToggleConfig: MessageFns<CanboConfig_ThreePinInput_ToggleConfig> = {
  encode(message: CanboConfig_ThreePinInput_ToggleConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    writer.uint32(10).fork();
    for (const v of message.upClick) {
      writer.uint32(v);
    }
    writer.join();
    writer.uint32(18).fork();
    for (const v of message.upHold) {
      writer.uint32(v);
    }
    writer.join();
    writer.uint32(26).fork();
    for (const v of message.downClick) {
      writer.uint32(v);
    }
    writer.join();
    writer.uint32(34).fork();
    for (const v of message.downHold) {
      writer.uint32(v);
    }
    writer.join();
    writer.uint32(42).fork();
    for (const v of message.upPress) {
      writer.uint32(v);
    }
    writer.join();
    writer.uint32(50).fork();
    for (const v of message.upRelease) {
      writer.uint32(v);
    }
    writer.join();
    writer.uint32(58).fork();
    for (const v of message.downPress) {
      writer.uint32(v);
    }
    writer.join();
    writer.uint32(66).fork();
    for (const v of message.downRelease) {
      writer.uint32(v);
    }
    writer.join();
    writer.uint32(74).fork();
    for (const v of message.upHoldRelease) {
      writer.uint32(v);
    }
    writer.join();
    writer.uint32(82).fork();
    for (const v of message.downHoldRelease) {
      writer.uint32(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CanboConfig_ThreePinInput_ToggleConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCanboConfig_ThreePinInput_ToggleConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag === 8) {
            message.upClick.push(reader.uint32());

            continue;
          }

          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.upClick.push(reader.uint32());
            }

            continue;
          }

          break;
        }
        case 2: {
          if (tag === 16) {
            message.upHold.push(reader.uint32());

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.upHold.push(reader.uint32());
            }

            continue;
          }

          break;
        }
        case 3: {
          if (tag === 24) {
            message.downClick.push(reader.uint32());

            continue;
          }

          if (tag === 26) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.downClick.push(reader.uint32());
            }

            continue;
          }

          break;
        }
        case 4: {
          if (tag === 32) {
            message.downHold.push(reader.uint32());

            continue;
          }

          if (tag === 34) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.downHold.push(reader.uint32());
            }

            continue;
          }

          break;
        }
        case 5: {
          if (tag === 40) {
            message.upPress.push(reader.uint32());

            continue;
          }

          if (tag === 42) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.upPress.push(reader.uint32());
            }

            continue;
          }

          break;
        }
        case 6: {
          if (tag === 48) {
            message.upRelease.push(reader.uint32());

            continue;
          }

          if (tag === 50) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.upRelease.push(reader.uint32());
            }

            continue;
          }

          break;
        }
        case 7: {
          if (tag === 56) {
            message.downPress.push(reader.uint32());

            continue;
          }

          if (tag === 58) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.downPress.push(reader.uint32());
            }

            continue;
          }

          break;
        }
        case 8: {
          if (tag === 64) {
            message.downRelease.push(reader.uint32());

            continue;
          }

          if (tag === 66) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.downRelease.push(reader.uint32());
            }

            continue;
          }

          break;
        }
        case 9: {
          if (tag === 72) {
            message.upHoldRelease.push(reader.uint32());

            continue;
          }

          if (tag === 74) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.upHoldRelease.push(reader.uint32());
            }

            continue;
          }

          break;
        }
        case 10: {
          if (tag === 80) {
            message.downHoldRelease.push(reader.uint32());

            continue;
          }

          if (tag === 82) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.downHoldRelease.push(reader.uint32());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CanboConfig_ThreePinInput_ToggleConfig>, I>>(
    base?: I,
  ): CanboConfig_ThreePinInput_ToggleConfig {
    return CanboConfig_ThreePinInput_ToggleConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CanboConfig_ThreePinInput_ToggleConfig>, I>>(
    object: I,
  ): CanboConfig_ThreePinInput_ToggleConfig {
    const message = createBaseCanboConfig_ThreePinInput_ToggleConfig();
    message.upClick = object.upClick?.map((e) => e) || [];
    message.upHold = object.upHold?.map((e) => e) || [];
    message.downClick = object.downClick?.map((e) => e) || [];
    message.downHold = object.downHold?.map((e) => e) || [];
    message.upPress = object.upPress?.map((e) => e) || [];
    message.upRelease = object.upRelease?.map((e) => e) || [];
    message.downPress = object.downPress?.map((e) => e) || [];
    message.downRelease = object.downRelease?.map((e) => e) || [];
    message.upHoldRelease = object.upHoldRelease?.map((e) => e) || [];
    message.downHoldRelease = object.downHoldRelease?.map((e) => e) || [];
    return message;
  },
};

function createBaseCanboConfig_ThreePinInput_MomentaryConfig(): CanboConfig_ThreePinInput_MomentaryConfig {
  return { upClick: [], upHold: [], upPress: [], upRelease: [], upHoldRelease: [] };
}

export const CanboConfig_ThreePinInput_MomentaryConfig: MessageFns<CanboConfig_ThreePinInput_MomentaryConfig> = {
  encode(message: CanboConfig_ThreePinInput_MomentaryConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    writer.uint32(10).fork();
    for (const v of message.upClick) {
      writer.uint32(v);
    }
    writer.join();
    writer.uint32(18).fork();
    for (const v of message.upHold) {
      writer.uint32(v);
    }
    writer.join();
    writer.uint32(26).fork();
    for (const v of message.upPress) {
      writer.uint32(v);
    }
    writer.join();
    writer.uint32(34).fork();
    for (const v of message.upRelease) {
      writer.uint32(v);
    }
    writer.join();
    writer.uint32(42).fork();
    for (const v of message.upHoldRelease) {
      writer.uint32(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CanboConfig_ThreePinInput_MomentaryConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCanboConfig_ThreePinInput_MomentaryConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag === 8) {
            message.upClick.push(reader.uint32());

            continue;
          }

          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.upClick.push(reader.uint32());
            }

            continue;
          }

          break;
        }
        case 2: {
          if (tag === 16) {
            message.upHold.push(reader.uint32());

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.upHold.push(reader.uint32());
            }

            continue;
          }

          break;
        }
        case 3: {
          if (tag === 24) {
            message.upPress.push(reader.uint32());

            continue;
          }

          if (tag === 26) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.upPress.push(reader.uint32());
            }

            continue;
          }

          break;
        }
        case 4: {
          if (tag === 32) {
            message.upRelease.push(reader.uint32());

            continue;
          }

          if (tag === 34) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.upRelease.push(reader.uint32());
            }

            continue;
          }

          break;
        }
        case 5: {
          if (tag === 40) {
            message.upHoldRelease.push(reader.uint32());

            continue;
          }

          if (tag === 42) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.upHoldRelease.push(reader.uint32());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CanboConfig_ThreePinInput_MomentaryConfig>, I>>(
    base?: I,
  ): CanboConfig_ThreePinInput_MomentaryConfig {
    return CanboConfig_ThreePinInput_MomentaryConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CanboConfig_ThreePinInput_MomentaryConfig>, I>>(
    object: I,
  ): CanboConfig_ThreePinInput_MomentaryConfig {
    const message = createBaseCanboConfig_ThreePinInput_MomentaryConfig();
    message.upClick = object.upClick?.map((e) => e) || [];
    message.upHold = object.upHold?.map((e) => e) || [];
    message.upPress = object.upPress?.map((e) => e) || [];
    message.upRelease = object.upRelease?.map((e) => e) || [];
    message.upHoldRelease = object.upHoldRelease?.map((e) => e) || [];
    return message;
  },
};

function createBaseCanboConfig_TwoPinInput(): CanboConfig_TwoPinInput {
  return { connectorId: 0, type: 0, momentary: undefined, doorSensor: undefined };
}

export const CanboConfig_TwoPinInput: MessageFns<CanboConfig_TwoPinInput> = {
  encode(message: CanboConfig_TwoPinInput, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.connectorId !== 0) {
      writer.uint32(8).uint32(message.connectorId);
    }
    if (message.type !== 0) {
      writer.uint32(16).int32(message.type);
    }
    if (message.momentary !== undefined) {
      CanboConfig_TwoPinInput_MomentaryConfig.encode(message.momentary, writer.uint32(26).fork()).join();
    }
    if (message.doorSensor !== undefined) {
      CanboConfig_TwoPinInput_DoorSensorConfig.encode(message.doorSensor, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CanboConfig_TwoPinInput {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCanboConfig_TwoPinInput();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.connectorId = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.type = reader.int32() as any;
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.momentary = CanboConfig_TwoPinInput_MomentaryConfig.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.doorSensor = CanboConfig_TwoPinInput_DoorSensorConfig.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CanboConfig_TwoPinInput>, I>>(base?: I): CanboConfig_TwoPinInput {
    return CanboConfig_TwoPinInput.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CanboConfig_TwoPinInput>, I>>(object: I): CanboConfig_TwoPinInput {
    const message = createBaseCanboConfig_TwoPinInput();
    message.connectorId = object.connectorId ?? 0;
    message.type = object.type ?? 0;
    message.momentary = (object.momentary !== undefined && object.momentary !== null)
      ? CanboConfig_TwoPinInput_MomentaryConfig.fromPartial(object.momentary)
      : undefined;
    message.doorSensor = (object.doorSensor !== undefined && object.doorSensor !== null)
      ? CanboConfig_TwoPinInput_DoorSensorConfig.fromPartial(object.doorSensor)
      : undefined;
    return message;
  },
};

function createBaseCanboConfig_TwoPinInput_MomentaryConfig(): CanboConfig_TwoPinInput_MomentaryConfig {
  return { upClick: [], upHold: [], upPress: [], upRelease: [], upHoldRelease: [] };
}

export const CanboConfig_TwoPinInput_MomentaryConfig: MessageFns<CanboConfig_TwoPinInput_MomentaryConfig> = {
  encode(message: CanboConfig_TwoPinInput_MomentaryConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    writer.uint32(10).fork();
    for (const v of message.upClick) {
      writer.uint32(v);
    }
    writer.join();
    writer.uint32(18).fork();
    for (const v of message.upHold) {
      writer.uint32(v);
    }
    writer.join();
    writer.uint32(26).fork();
    for (const v of message.upPress) {
      writer.uint32(v);
    }
    writer.join();
    writer.uint32(34).fork();
    for (const v of message.upRelease) {
      writer.uint32(v);
    }
    writer.join();
    writer.uint32(42).fork();
    for (const v of message.upHoldRelease) {
      writer.uint32(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CanboConfig_TwoPinInput_MomentaryConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCanboConfig_TwoPinInput_MomentaryConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag === 8) {
            message.upClick.push(reader.uint32());

            continue;
          }

          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.upClick.push(reader.uint32());
            }

            continue;
          }

          break;
        }
        case 2: {
          if (tag === 16) {
            message.upHold.push(reader.uint32());

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.upHold.push(reader.uint32());
            }

            continue;
          }

          break;
        }
        case 3: {
          if (tag === 24) {
            message.upPress.push(reader.uint32());

            continue;
          }

          if (tag === 26) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.upPress.push(reader.uint32());
            }

            continue;
          }

          break;
        }
        case 4: {
          if (tag === 32) {
            message.upRelease.push(reader.uint32());

            continue;
          }

          if (tag === 34) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.upRelease.push(reader.uint32());
            }

            continue;
          }

          break;
        }
        case 5: {
          if (tag === 40) {
            message.upHoldRelease.push(reader.uint32());

            continue;
          }

          if (tag === 42) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.upHoldRelease.push(reader.uint32());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CanboConfig_TwoPinInput_MomentaryConfig>, I>>(
    base?: I,
  ): CanboConfig_TwoPinInput_MomentaryConfig {
    return CanboConfig_TwoPinInput_MomentaryConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CanboConfig_TwoPinInput_MomentaryConfig>, I>>(
    object: I,
  ): CanboConfig_TwoPinInput_MomentaryConfig {
    const message = createBaseCanboConfig_TwoPinInput_MomentaryConfig();
    message.upClick = object.upClick?.map((e) => e) || [];
    message.upHold = object.upHold?.map((e) => e) || [];
    message.upPress = object.upPress?.map((e) => e) || [];
    message.upRelease = object.upRelease?.map((e) => e) || [];
    message.upHoldRelease = object.upHoldRelease?.map((e) => e) || [];
    return message;
  },
};

function createBaseCanboConfig_TwoPinInput_DoorSensorConfig(): CanboConfig_TwoPinInput_DoorSensorConfig {
  return { onOpen: [], onClose: [] };
}

export const CanboConfig_TwoPinInput_DoorSensorConfig: MessageFns<CanboConfig_TwoPinInput_DoorSensorConfig> = {
  encode(message: CanboConfig_TwoPinInput_DoorSensorConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    writer.uint32(10).fork();
    for (const v of message.onOpen) {
      writer.uint32(v);
    }
    writer.join();
    writer.uint32(18).fork();
    for (const v of message.onClose) {
      writer.uint32(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CanboConfig_TwoPinInput_DoorSensorConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCanboConfig_TwoPinInput_DoorSensorConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag === 8) {
            message.onOpen.push(reader.uint32());

            continue;
          }

          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.onOpen.push(reader.uint32());
            }

            continue;
          }

          break;
        }
        case 2: {
          if (tag === 16) {
            message.onClose.push(reader.uint32());

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.onClose.push(reader.uint32());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CanboConfig_TwoPinInput_DoorSensorConfig>, I>>(
    base?: I,
  ): CanboConfig_TwoPinInput_DoorSensorConfig {
    return CanboConfig_TwoPinInput_DoorSensorConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CanboConfig_TwoPinInput_DoorSensorConfig>, I>>(
    object: I,
  ): CanboConfig_TwoPinInput_DoorSensorConfig {
    const message = createBaseCanboConfig_TwoPinInput_DoorSensorConfig();
    message.onOpen = object.onOpen?.map((e) => e) || [];
    message.onClose = object.onClose?.map((e) => e) || [];
    return message;
  },
};

function createBaseCanboConfig_ADCInput(): CanboConfig_ADCInput {
  return { connectorId: 0, type: 0, knob: undefined, thermostat: undefined, pir: undefined };
}

export const CanboConfig_ADCInput: MessageFns<CanboConfig_ADCInput> = {
  encode(message: CanboConfig_ADCInput, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.connectorId !== 0) {
      writer.uint32(8).uint32(message.connectorId);
    }
    if (message.type !== 0) {
      writer.uint32(16).int32(message.type);
    }
    if (message.knob !== undefined) {
      CanboConfig_ADCInput_KnobConfig.encode(message.knob, writer.uint32(26).fork()).join();
    }
    if (message.thermostat !== undefined) {
      CanboConfig_ADCInput_ThermostatConfig.encode(message.thermostat, writer.uint32(34).fork()).join();
    }
    if (message.pir !== undefined) {
      CanboConfig_ADCInput_PIRConfig.encode(message.pir, writer.uint32(42).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CanboConfig_ADCInput {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCanboConfig_ADCInput();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.connectorId = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.type = reader.int32() as any;
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.knob = CanboConfig_ADCInput_KnobConfig.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.thermostat = CanboConfig_ADCInput_ThermostatConfig.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.pir = CanboConfig_ADCInput_PIRConfig.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CanboConfig_ADCInput>, I>>(base?: I): CanboConfig_ADCInput {
    return CanboConfig_ADCInput.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CanboConfig_ADCInput>, I>>(object: I): CanboConfig_ADCInput {
    const message = createBaseCanboConfig_ADCInput();
    message.connectorId = object.connectorId ?? 0;
    message.type = object.type ?? 0;
    message.knob = (object.knob !== undefined && object.knob !== null)
      ? CanboConfig_ADCInput_KnobConfig.fromPartial(object.knob)
      : undefined;
    message.thermostat = (object.thermostat !== undefined && object.thermostat !== null)
      ? CanboConfig_ADCInput_ThermostatConfig.fromPartial(object.thermostat)
      : undefined;
    message.pir = (object.pir !== undefined && object.pir !== null)
      ? CanboConfig_ADCInput_PIRConfig.fromPartial(object.pir)
      : undefined;
    return message;
  },
};

function createBaseCanboConfig_ADCInput_KnobConfig(): CanboConfig_ADCInput_KnobConfig {
  return { onTurn: [] };
}

export const CanboConfig_ADCInput_KnobConfig: MessageFns<CanboConfig_ADCInput_KnobConfig> = {
  encode(message: CanboConfig_ADCInput_KnobConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    writer.uint32(10).fork();
    for (const v of message.onTurn) {
      writer.uint32(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CanboConfig_ADCInput_KnobConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCanboConfig_ADCInput_KnobConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag === 8) {
            message.onTurn.push(reader.uint32());

            continue;
          }

          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.onTurn.push(reader.uint32());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CanboConfig_ADCInput_KnobConfig>, I>>(base?: I): CanboConfig_ADCInput_KnobConfig {
    return CanboConfig_ADCInput_KnobConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CanboConfig_ADCInput_KnobConfig>, I>>(
    object: I,
  ): CanboConfig_ADCInput_KnobConfig {
    const message = createBaseCanboConfig_ADCInput_KnobConfig();
    message.onTurn = object.onTurn?.map((e) => e) || [];
    return message;
  },
};

function createBaseCanboConfig_ADCInput_ThermostatConfig(): CanboConfig_ADCInput_ThermostatConfig {
  return { thermostatAction: [] };
}

export const CanboConfig_ADCInput_ThermostatConfig: MessageFns<CanboConfig_ADCInput_ThermostatConfig> = {
  encode(message: CanboConfig_ADCInput_ThermostatConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    writer.uint32(10).fork();
    for (const v of message.thermostatAction) {
      writer.uint32(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CanboConfig_ADCInput_ThermostatConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCanboConfig_ADCInput_ThermostatConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag === 8) {
            message.thermostatAction.push(reader.uint32());

            continue;
          }

          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.thermostatAction.push(reader.uint32());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CanboConfig_ADCInput_ThermostatConfig>, I>>(
    base?: I,
  ): CanboConfig_ADCInput_ThermostatConfig {
    return CanboConfig_ADCInput_ThermostatConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CanboConfig_ADCInput_ThermostatConfig>, I>>(
    object: I,
  ): CanboConfig_ADCInput_ThermostatConfig {
    const message = createBaseCanboConfig_ADCInput_ThermostatConfig();
    message.thermostatAction = object.thermostatAction?.map((e) => e) || [];
    return message;
  },
};

function createBaseCanboConfig_ADCInput_PIRConfig(): CanboConfig_ADCInput_PIRConfig {
  return { onActivate: [], onDeactivate: [] };
}

export const CanboConfig_ADCInput_PIRConfig: MessageFns<CanboConfig_ADCInput_PIRConfig> = {
  encode(message: CanboConfig_ADCInput_PIRConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    writer.uint32(10).fork();
    for (const v of message.onActivate) {
      writer.uint32(v);
    }
    writer.join();
    writer.uint32(18).fork();
    for (const v of message.onDeactivate) {
      writer.uint32(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CanboConfig_ADCInput_PIRConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCanboConfig_ADCInput_PIRConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag === 8) {
            message.onActivate.push(reader.uint32());

            continue;
          }

          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.onActivate.push(reader.uint32());
            }

            continue;
          }

          break;
        }
        case 2: {
          if (tag === 16) {
            message.onDeactivate.push(reader.uint32());

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.onDeactivate.push(reader.uint32());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CanboConfig_ADCInput_PIRConfig>, I>>(base?: I): CanboConfig_ADCInput_PIRConfig {
    return CanboConfig_ADCInput_PIRConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CanboConfig_ADCInput_PIRConfig>, I>>(
    object: I,
  ): CanboConfig_ADCInput_PIRConfig {
    const message = createBaseCanboConfig_ADCInput_PIRConfig();
    message.onActivate = object.onActivate?.map((e) => e) || [];
    message.onDeactivate = object.onDeactivate?.map((e) => e) || [];
    return message;
  },
};

function createBaseCanboConfig_Output(): CanboConfig_Output {
  return { connectorId: 0, connectorType: 0 };
}

export const CanboConfig_Output: MessageFns<CanboConfig_Output> = {
  encode(message: CanboConfig_Output, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.connectorId !== 0) {
      writer.uint32(8).uint32(message.connectorId);
    }
    if (message.connectorType !== 0) {
      writer.uint32(16).int32(message.connectorType);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CanboConfig_Output {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCanboConfig_Output();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.connectorId = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.connectorType = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CanboConfig_Output>, I>>(base?: I): CanboConfig_Output {
    return CanboConfig_Output.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CanboConfig_Output>, I>>(object: I): CanboConfig_Output {
    const message = createBaseCanboConfig_Output();
    message.connectorId = object.connectorId ?? 0;
    message.connectorType = object.connectorType ?? 0;
    return message;
  },
};

function createBaseLightConfig(): LightConfig {
  return { id: 0, dimSpeedMsec: 0, fixtures: [] };
}

export const LightConfig: MessageFns<LightConfig> = {
  encode(message: LightConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== 0) {
      writer.uint32(8).uint32(message.id);
    }
    if (message.dimSpeedMsec !== 0) {
      writer.uint32(16).uint32(message.dimSpeedMsec);
    }
    for (const v of message.fixtures) {
      LightConfig_FixtureConfig.encode(v!, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LightConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLightConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.id = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.dimSpeedMsec = reader.uint32();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.fixtures.push(LightConfig_FixtureConfig.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<LightConfig>, I>>(base?: I): LightConfig {
    return LightConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LightConfig>, I>>(object: I): LightConfig {
    const message = createBaseLightConfig();
    message.id = object.id ?? 0;
    message.dimSpeedMsec = object.dimSpeedMsec ?? 0;
    message.fixtures = object.fixtures?.map((e) => LightConfig_FixtureConfig.fromPartial(e)) || [];
    return message;
  },
};

function createBaseLightConfig_FixtureConfig(): LightConfig_FixtureConfig {
  return {
    minBrightness: 0,
    maxBrightness: 0,
    type: 0,
    dmx: undefined,
    rf: undefined,
    zeroToTenVolt: undefined,
    relay: undefined,
  };
}

export const LightConfig_FixtureConfig: MessageFns<LightConfig_FixtureConfig> = {
  encode(message: LightConfig_FixtureConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.minBrightness !== 0) {
      writer.uint32(13).float(message.minBrightness);
    }
    if (message.maxBrightness !== 0) {
      writer.uint32(21).float(message.maxBrightness);
    }
    if (message.type !== 0) {
      writer.uint32(24).int32(message.type);
    }
    if (message.dmx !== undefined) {
      LightConfig_FixtureConfig_DMXConfig.encode(message.dmx, writer.uint32(34).fork()).join();
    }
    if (message.rf !== undefined) {
      LightConfig_FixtureConfig_RFConfig.encode(message.rf, writer.uint32(42).fork()).join();
    }
    if (message.zeroToTenVolt !== undefined) {
      LightConfig_FixtureConfig_ZeroToTenVoltConfig.encode(message.zeroToTenVolt, writer.uint32(50).fork()).join();
    }
    if (message.relay !== undefined) {
      LightConfig_FixtureConfig_RelayConfig.encode(message.relay, writer.uint32(58).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LightConfig_FixtureConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLightConfig_FixtureConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 13) {
            break;
          }

          message.minBrightness = reader.float();
          continue;
        }
        case 2: {
          if (tag !== 21) {
            break;
          }

          message.maxBrightness = reader.float();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.type = reader.int32() as any;
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.dmx = LightConfig_FixtureConfig_DMXConfig.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.rf = LightConfig_FixtureConfig_RFConfig.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.zeroToTenVolt = LightConfig_FixtureConfig_ZeroToTenVoltConfig.decode(reader, reader.uint32());
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.relay = LightConfig_FixtureConfig_RelayConfig.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<LightConfig_FixtureConfig>, I>>(base?: I): LightConfig_FixtureConfig {
    return LightConfig_FixtureConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LightConfig_FixtureConfig>, I>>(object: I): LightConfig_FixtureConfig {
    const message = createBaseLightConfig_FixtureConfig();
    message.minBrightness = object.minBrightness ?? 0;
    message.maxBrightness = object.maxBrightness ?? 0;
    message.type = object.type ?? 0;
    message.dmx = (object.dmx !== undefined && object.dmx !== null)
      ? LightConfig_FixtureConfig_DMXConfig.fromPartial(object.dmx)
      : undefined;
    message.rf = (object.rf !== undefined && object.rf !== null)
      ? LightConfig_FixtureConfig_RFConfig.fromPartial(object.rf)
      : undefined;
    message.zeroToTenVolt = (object.zeroToTenVolt !== undefined && object.zeroToTenVolt !== null)
      ? LightConfig_FixtureConfig_ZeroToTenVoltConfig.fromPartial(object.zeroToTenVolt)
      : undefined;
    message.relay = (object.relay !== undefined && object.relay !== null)
      ? LightConfig_FixtureConfig_RelayConfig.fromPartial(object.relay)
      : undefined;
    return message;
  },
};

function createBaseLightConfig_FixtureConfig_DMXConfig(): LightConfig_FixtureConfig_DMXConfig {
  return { params: undefined, rgb: undefined, channels: [], type: 0 };
}

export const LightConfig_FixtureConfig_DMXConfig: MessageFns<LightConfig_FixtureConfig_DMXConfig> = {
  encode(message: LightConfig_FixtureConfig_DMXConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.params !== undefined) {
      LightConfig_FixtureConfig_DMXConfig_LightParams.encode(message.params, writer.uint32(10).fork()).join();
    }
    if (message.rgb !== undefined) {
      LightConfig_FixtureConfig_DMXConfig_RGBConfig.encode(message.rgb, writer.uint32(18).fork()).join();
    }
    writer.uint32(26).fork();
    for (const v of message.channels) {
      writer.uint32(v);
    }
    writer.join();
    if (message.type !== 0) {
      writer.uint32(32).int32(message.type);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LightConfig_FixtureConfig_DMXConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLightConfig_FixtureConfig_DMXConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.params = LightConfig_FixtureConfig_DMXConfig_LightParams.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.rgb = LightConfig_FixtureConfig_DMXConfig_RGBConfig.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag === 24) {
            message.channels.push(reader.uint32());

            continue;
          }

          if (tag === 26) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.channels.push(reader.uint32());
            }

            continue;
          }

          break;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.type = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<LightConfig_FixtureConfig_DMXConfig>, I>>(
    base?: I,
  ): LightConfig_FixtureConfig_DMXConfig {
    return LightConfig_FixtureConfig_DMXConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LightConfig_FixtureConfig_DMXConfig>, I>>(
    object: I,
  ): LightConfig_FixtureConfig_DMXConfig {
    const message = createBaseLightConfig_FixtureConfig_DMXConfig();
    message.params = (object.params !== undefined && object.params !== null)
      ? LightConfig_FixtureConfig_DMXConfig_LightParams.fromPartial(object.params)
      : undefined;
    message.rgb = (object.rgb !== undefined && object.rgb !== null)
      ? LightConfig_FixtureConfig_DMXConfig_RGBConfig.fromPartial(object.rgb)
      : undefined;
    message.channels = object.channels?.map((e) => e) || [];
    message.type = object.type ?? 0;
    return message;
  },
};

function createBaseLightConfig_FixtureConfig_DMXConfig_LightParams(): LightConfig_FixtureConfig_DMXConfig_LightParams {
  return { min1: 0, max1: 0, gamma1: 0, min2: 0, max2: 0, gamma2: 0 };
}

export const LightConfig_FixtureConfig_DMXConfig_LightParams: MessageFns<
  LightConfig_FixtureConfig_DMXConfig_LightParams
> = {
  encode(
    message: LightConfig_FixtureConfig_DMXConfig_LightParams,
    writer: BinaryWriter = new BinaryWriter(),
  ): BinaryWriter {
    if (message.min1 !== 0) {
      writer.uint32(13).float(message.min1);
    }
    if (message.max1 !== 0) {
      writer.uint32(21).float(message.max1);
    }
    if (message.gamma1 !== 0) {
      writer.uint32(29).float(message.gamma1);
    }
    if (message.min2 !== 0) {
      writer.uint32(37).float(message.min2);
    }
    if (message.max2 !== 0) {
      writer.uint32(45).float(message.max2);
    }
    if (message.gamma2 !== 0) {
      writer.uint32(53).float(message.gamma2);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LightConfig_FixtureConfig_DMXConfig_LightParams {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLightConfig_FixtureConfig_DMXConfig_LightParams();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 13) {
            break;
          }

          message.min1 = reader.float();
          continue;
        }
        case 2: {
          if (tag !== 21) {
            break;
          }

          message.max1 = reader.float();
          continue;
        }
        case 3: {
          if (tag !== 29) {
            break;
          }

          message.gamma1 = reader.float();
          continue;
        }
        case 4: {
          if (tag !== 37) {
            break;
          }

          message.min2 = reader.float();
          continue;
        }
        case 5: {
          if (tag !== 45) {
            break;
          }

          message.max2 = reader.float();
          continue;
        }
        case 6: {
          if (tag !== 53) {
            break;
          }

          message.gamma2 = reader.float();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<LightConfig_FixtureConfig_DMXConfig_LightParams>, I>>(
    base?: I,
  ): LightConfig_FixtureConfig_DMXConfig_LightParams {
    return LightConfig_FixtureConfig_DMXConfig_LightParams.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LightConfig_FixtureConfig_DMXConfig_LightParams>, I>>(
    object: I,
  ): LightConfig_FixtureConfig_DMXConfig_LightParams {
    const message = createBaseLightConfig_FixtureConfig_DMXConfig_LightParams();
    message.min1 = object.min1 ?? 0;
    message.max1 = object.max1 ?? 0;
    message.gamma1 = object.gamma1 ?? 0;
    message.min2 = object.min2 ?? 0;
    message.max2 = object.max2 ?? 0;
    message.gamma2 = object.gamma2 ?? 0;
    return message;
  },
};

function createBaseLightConfig_FixtureConfig_DMXConfig_RGBConfig(): LightConfig_FixtureConfig_DMXConfig_RGBConfig {
  return { red: 0, green: 0, blue: 0 };
}

export const LightConfig_FixtureConfig_DMXConfig_RGBConfig: MessageFns<LightConfig_FixtureConfig_DMXConfig_RGBConfig> =
  {
    encode(
      message: LightConfig_FixtureConfig_DMXConfig_RGBConfig,
      writer: BinaryWriter = new BinaryWriter(),
    ): BinaryWriter {
      if (message.red !== 0) {
        writer.uint32(8).uint32(message.red);
      }
      if (message.green !== 0) {
        writer.uint32(16).uint32(message.green);
      }
      if (message.blue !== 0) {
        writer.uint32(24).uint32(message.blue);
      }
      return writer;
    },

    decode(input: BinaryReader | Uint8Array, length?: number): LightConfig_FixtureConfig_DMXConfig_RGBConfig {
      const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
      let end = length === undefined ? reader.len : reader.pos + length;
      const message = createBaseLightConfig_FixtureConfig_DMXConfig_RGBConfig();
      while (reader.pos < end) {
        const tag = reader.uint32();
        switch (tag >>> 3) {
          case 1: {
            if (tag !== 8) {
              break;
            }

            message.red = reader.uint32();
            continue;
          }
          case 2: {
            if (tag !== 16) {
              break;
            }

            message.green = reader.uint32();
            continue;
          }
          case 3: {
            if (tag !== 24) {
              break;
            }

            message.blue = reader.uint32();
            continue;
          }
        }
        if ((tag & 7) === 4 || tag === 0) {
          break;
        }
        reader.skip(tag & 7);
      }
      return message;
    },

    create<I extends Exact<DeepPartial<LightConfig_FixtureConfig_DMXConfig_RGBConfig>, I>>(
      base?: I,
    ): LightConfig_FixtureConfig_DMXConfig_RGBConfig {
      return LightConfig_FixtureConfig_DMXConfig_RGBConfig.fromPartial(base ?? ({} as any));
    },
    fromPartial<I extends Exact<DeepPartial<LightConfig_FixtureConfig_DMXConfig_RGBConfig>, I>>(
      object: I,
    ): LightConfig_FixtureConfig_DMXConfig_RGBConfig {
      const message = createBaseLightConfig_FixtureConfig_DMXConfig_RGBConfig();
      message.red = object.red ?? 0;
      message.green = object.green ?? 0;
      message.blue = object.blue ?? 0;
      return message;
    },
  };

function createBaseLightConfig_FixtureConfig_RFConfig(): LightConfig_FixtureConfig_RFConfig {
  return { type: 0, nodeId: 0 };
}

export const LightConfig_FixtureConfig_RFConfig: MessageFns<LightConfig_FixtureConfig_RFConfig> = {
  encode(message: LightConfig_FixtureConfig_RFConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.type !== 0) {
      writer.uint32(8).int32(message.type);
    }
    if (message.nodeId !== 0) {
      writer.uint32(16).uint32(message.nodeId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LightConfig_FixtureConfig_RFConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLightConfig_FixtureConfig_RFConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.type = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.nodeId = reader.uint32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<LightConfig_FixtureConfig_RFConfig>, I>>(
    base?: I,
  ): LightConfig_FixtureConfig_RFConfig {
    return LightConfig_FixtureConfig_RFConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LightConfig_FixtureConfig_RFConfig>, I>>(
    object: I,
  ): LightConfig_FixtureConfig_RFConfig {
    const message = createBaseLightConfig_FixtureConfig_RFConfig();
    message.type = object.type ?? 0;
    message.nodeId = object.nodeId ?? 0;
    return message;
  },
};

function createBaseLightConfig_FixtureConfig_ZeroToTenVoltConfig(): LightConfig_FixtureConfig_ZeroToTenVoltConfig {
  return { type: 0, nodeId: 0, useRelay: false, outConnectorId: 0 };
}

export const LightConfig_FixtureConfig_ZeroToTenVoltConfig: MessageFns<LightConfig_FixtureConfig_ZeroToTenVoltConfig> =
  {
    encode(
      message: LightConfig_FixtureConfig_ZeroToTenVoltConfig,
      writer: BinaryWriter = new BinaryWriter(),
    ): BinaryWriter {
      if (message.type !== 0) {
        writer.uint32(8).int32(message.type);
      }
      if (message.nodeId !== 0) {
        writer.uint32(16).uint32(message.nodeId);
      }
      if (message.useRelay !== false) {
        writer.uint32(24).bool(message.useRelay);
      }
      if (message.outConnectorId !== 0) {
        writer.uint32(32).uint32(message.outConnectorId);
      }
      return writer;
    },

    decode(input: BinaryReader | Uint8Array, length?: number): LightConfig_FixtureConfig_ZeroToTenVoltConfig {
      const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
      let end = length === undefined ? reader.len : reader.pos + length;
      const message = createBaseLightConfig_FixtureConfig_ZeroToTenVoltConfig();
      while (reader.pos < end) {
        const tag = reader.uint32();
        switch (tag >>> 3) {
          case 1: {
            if (tag !== 8) {
              break;
            }

            message.type = reader.int32() as any;
            continue;
          }
          case 2: {
            if (tag !== 16) {
              break;
            }

            message.nodeId = reader.uint32();
            continue;
          }
          case 3: {
            if (tag !== 24) {
              break;
            }

            message.useRelay = reader.bool();
            continue;
          }
          case 4: {
            if (tag !== 32) {
              break;
            }

            message.outConnectorId = reader.uint32();
            continue;
          }
        }
        if ((tag & 7) === 4 || tag === 0) {
          break;
        }
        reader.skip(tag & 7);
      }
      return message;
    },

    create<I extends Exact<DeepPartial<LightConfig_FixtureConfig_ZeroToTenVoltConfig>, I>>(
      base?: I,
    ): LightConfig_FixtureConfig_ZeroToTenVoltConfig {
      return LightConfig_FixtureConfig_ZeroToTenVoltConfig.fromPartial(base ?? ({} as any));
    },
    fromPartial<I extends Exact<DeepPartial<LightConfig_FixtureConfig_ZeroToTenVoltConfig>, I>>(
      object: I,
    ): LightConfig_FixtureConfig_ZeroToTenVoltConfig {
      const message = createBaseLightConfig_FixtureConfig_ZeroToTenVoltConfig();
      message.type = object.type ?? 0;
      message.nodeId = object.nodeId ?? 0;
      message.useRelay = object.useRelay ?? false;
      message.outConnectorId = object.outConnectorId ?? 0;
      return message;
    },
  };

function createBaseLightConfig_FixtureConfig_RelayConfig(): LightConfig_FixtureConfig_RelayConfig {
  return { nodeId: 0, outConnectorId: 0 };
}

export const LightConfig_FixtureConfig_RelayConfig: MessageFns<LightConfig_FixtureConfig_RelayConfig> = {
  encode(message: LightConfig_FixtureConfig_RelayConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.nodeId !== 0) {
      writer.uint32(8).uint32(message.nodeId);
    }
    if (message.outConnectorId !== 0) {
      writer.uint32(16).uint32(message.outConnectorId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LightConfig_FixtureConfig_RelayConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLightConfig_FixtureConfig_RelayConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.nodeId = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.outConnectorId = reader.uint32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<LightConfig_FixtureConfig_RelayConfig>, I>>(
    base?: I,
  ): LightConfig_FixtureConfig_RelayConfig {
    return LightConfig_FixtureConfig_RelayConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LightConfig_FixtureConfig_RelayConfig>, I>>(
    object: I,
  ): LightConfig_FixtureConfig_RelayConfig {
    const message = createBaseLightConfig_FixtureConfig_RelayConfig();
    message.nodeId = object.nodeId ?? 0;
    message.outConnectorId = object.outConnectorId ?? 0;
    return message;
  },
};

function createBaseSomfyShadesConfig(): SomfyShadesConfig {
  return { internalId: 0, deviceId: 0 };
}

export const SomfyShadesConfig: MessageFns<SomfyShadesConfig> = {
  encode(message: SomfyShadesConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.internalId !== 0) {
      writer.uint32(8).uint32(message.internalId);
    }
    if (message.deviceId !== 0) {
      writer.uint32(16).uint32(message.deviceId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SomfyShadesConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSomfyShadesConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.internalId = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.deviceId = reader.uint32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<SomfyShadesConfig>, I>>(base?: I): SomfyShadesConfig {
    return SomfyShadesConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SomfyShadesConfig>, I>>(object: I): SomfyShadesConfig {
    const message = createBaseSomfyShadesConfig();
    message.internalId = object.internalId ?? 0;
    message.deviceId = object.deviceId ?? 0;
    return message;
  },
};

function createBaseBasestationState(): BasestationState {
  return { lights: [], buttons: [], provisionedDevices: [], reeds: [], presences: [], pirs: [] };
}

export const BasestationState: MessageFns<BasestationState> = {
  encode(message: BasestationState, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.lights) {
      LightState.encode(v!, writer.uint32(10).fork()).join();
    }
    for (const v of message.buttons) {
      ButtonState.encode(v!, writer.uint32(18).fork()).join();
    }
    for (const v of message.provisionedDevices) {
      ProvisioningState.encode(v!, writer.uint32(26).fork()).join();
    }
    for (const v of message.reeds) {
      RFReedState.encode(v!, writer.uint32(34).fork()).join();
    }
    for (const v of message.presences) {
      RFPresenceState.encode(v!, writer.uint32(42).fork()).join();
    }
    for (const v of message.pirs) {
      PIRState.encode(v!, writer.uint32(50).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BasestationState {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBasestationState();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.lights.push(LightState.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.buttons.push(ButtonState.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.provisionedDevices.push(ProvisioningState.decode(reader, reader.uint32()));
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.reeds.push(RFReedState.decode(reader, reader.uint32()));
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.presences.push(RFPresenceState.decode(reader, reader.uint32()));
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.pirs.push(PIRState.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<BasestationState>, I>>(base?: I): BasestationState {
    return BasestationState.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BasestationState>, I>>(object: I): BasestationState {
    const message = createBaseBasestationState();
    message.lights = object.lights?.map((e) => LightState.fromPartial(e)) || [];
    message.buttons = object.buttons?.map((e) => ButtonState.fromPartial(e)) || [];
    message.provisionedDevices = object.provisionedDevices?.map((e) => ProvisioningState.fromPartial(e)) || [];
    message.reeds = object.reeds?.map((e) => RFReedState.fromPartial(e)) || [];
    message.presences = object.presences?.map((e) => RFPresenceState.fromPartial(e)) || [];
    message.pirs = object.pirs?.map((e) => PIRState.fromPartial(e)) || [];
    return message;
  },
};

function createBaseRFReedState(): RFReedState {
  return { nodeId: 0, sensorStatus: 0, lastModifiedTime: 0, batteryVoltage: 0 };
}

export const RFReedState: MessageFns<RFReedState> = {
  encode(message: RFReedState, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.nodeId !== 0) {
      writer.uint32(8).uint32(message.nodeId);
    }
    if (message.sensorStatus !== 0) {
      writer.uint32(16).int32(message.sensorStatus);
    }
    if (message.lastModifiedTime !== 0) {
      writer.uint32(24).uint64(message.lastModifiedTime);
    }
    if (message.batteryVoltage !== 0) {
      writer.uint32(37).float(message.batteryVoltage);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RFReedState {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRFReedState();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.nodeId = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.sensorStatus = reader.int32() as any;
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.lastModifiedTime = longToNumber(reader.uint64());
          continue;
        }
        case 4: {
          if (tag !== 37) {
            break;
          }

          message.batteryVoltage = reader.float();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<RFReedState>, I>>(base?: I): RFReedState {
    return RFReedState.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RFReedState>, I>>(object: I): RFReedState {
    const message = createBaseRFReedState();
    message.nodeId = object.nodeId ?? 0;
    message.sensorStatus = object.sensorStatus ?? 0;
    message.lastModifiedTime = object.lastModifiedTime ?? 0;
    message.batteryVoltage = object.batteryVoltage ?? 0;
    return message;
  },
};

function createBaseRFPresenceState(): RFPresenceState {
  return { nodeId: 0, sensorStatus: 0, lastModifiedTime: 0, batteryVoltage: 0 };
}

export const RFPresenceState: MessageFns<RFPresenceState> = {
  encode(message: RFPresenceState, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.nodeId !== 0) {
      writer.uint32(8).uint32(message.nodeId);
    }
    if (message.sensorStatus !== 0) {
      writer.uint32(16).int32(message.sensorStatus);
    }
    if (message.lastModifiedTime !== 0) {
      writer.uint32(24).uint64(message.lastModifiedTime);
    }
    if (message.batteryVoltage !== 0) {
      writer.uint32(37).float(message.batteryVoltage);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RFPresenceState {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRFPresenceState();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.nodeId = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.sensorStatus = reader.int32() as any;
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.lastModifiedTime = longToNumber(reader.uint64());
          continue;
        }
        case 4: {
          if (tag !== 37) {
            break;
          }

          message.batteryVoltage = reader.float();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<RFPresenceState>, I>>(base?: I): RFPresenceState {
    return RFPresenceState.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RFPresenceState>, I>>(object: I): RFPresenceState {
    const message = createBaseRFPresenceState();
    message.nodeId = object.nodeId ?? 0;
    message.sensorStatus = object.sensorStatus ?? 0;
    message.lastModifiedTime = object.lastModifiedTime ?? 0;
    message.batteryVoltage = object.batteryVoltage ?? 0;
    return message;
  },
};

function createBaseProvisioningState(): ProvisioningState {
  return { nodeId: 0, isProvisioned: false, errorCode: 0, lastSeenTime: 0, rssi: 0 };
}

export const ProvisioningState: MessageFns<ProvisioningState> = {
  encode(message: ProvisioningState, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.nodeId !== 0) {
      writer.uint32(8).uint32(message.nodeId);
    }
    if (message.isProvisioned !== false) {
      writer.uint32(16).bool(message.isProvisioned);
    }
    if (message.errorCode !== 0) {
      writer.uint32(24).int32(message.errorCode);
    }
    if (message.lastSeenTime !== 0) {
      writer.uint32(32).uint64(message.lastSeenTime);
    }
    if (message.rssi !== 0) {
      writer.uint32(40).int32(message.rssi);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ProvisioningState {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseProvisioningState();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.nodeId = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.isProvisioned = reader.bool();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.errorCode = reader.int32() as any;
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.lastSeenTime = longToNumber(reader.uint64());
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.rssi = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ProvisioningState>, I>>(base?: I): ProvisioningState {
    return ProvisioningState.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ProvisioningState>, I>>(object: I): ProvisioningState {
    const message = createBaseProvisioningState();
    message.nodeId = object.nodeId ?? 0;
    message.isProvisioned = object.isProvisioned ?? false;
    message.errorCode = object.errorCode ?? 0;
    message.lastSeenTime = object.lastSeenTime ?? 0;
    message.rssi = object.rssi ?? 0;
    return message;
  },
};

function createBaseLightState(): LightState {
  return {
    id: 0,
    brightness: 0,
    targetValue: 0,
    dimSpeedMsec: 0,
    lastModifiedTime: 0,
    activeAfterTime: 0,
    isTransitioning: false,
    lastBrightnessBeforeAction: 0,
    lastTransitionStopReason: 0,
  };
}

export const LightState: MessageFns<LightState> = {
  encode(message: LightState, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== 0) {
      writer.uint32(8).uint32(message.id);
    }
    if (message.brightness !== 0) {
      writer.uint32(21).float(message.brightness);
    }
    if (message.targetValue !== 0) {
      writer.uint32(29).float(message.targetValue);
    }
    if (message.dimSpeedMsec !== 0) {
      writer.uint32(32).uint32(message.dimSpeedMsec);
    }
    if (message.lastModifiedTime !== 0) {
      writer.uint32(40).uint64(message.lastModifiedTime);
    }
    if (message.activeAfterTime !== 0) {
      writer.uint32(48).uint64(message.activeAfterTime);
    }
    if (message.isTransitioning !== false) {
      writer.uint32(56).bool(message.isTransitioning);
    }
    if (message.lastBrightnessBeforeAction !== 0) {
      writer.uint32(69).float(message.lastBrightnessBeforeAction);
    }
    if (message.lastTransitionStopReason !== 0) {
      writer.uint32(72).int32(message.lastTransitionStopReason);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LightState {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLightState();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.id = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 21) {
            break;
          }

          message.brightness = reader.float();
          continue;
        }
        case 3: {
          if (tag !== 29) {
            break;
          }

          message.targetValue = reader.float();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.dimSpeedMsec = reader.uint32();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.lastModifiedTime = longToNumber(reader.uint64());
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.activeAfterTime = longToNumber(reader.uint64());
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.isTransitioning = reader.bool();
          continue;
        }
        case 8: {
          if (tag !== 69) {
            break;
          }

          message.lastBrightnessBeforeAction = reader.float();
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.lastTransitionStopReason = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<LightState>, I>>(base?: I): LightState {
    return LightState.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LightState>, I>>(object: I): LightState {
    const message = createBaseLightState();
    message.id = object.id ?? 0;
    message.brightness = object.brightness ?? 0;
    message.targetValue = object.targetValue ?? 0;
    message.dimSpeedMsec = object.dimSpeedMsec ?? 0;
    message.lastModifiedTime = object.lastModifiedTime ?? 0;
    message.activeAfterTime = object.activeAfterTime ?? 0;
    message.isTransitioning = object.isTransitioning ?? false;
    message.lastBrightnessBeforeAction = object.lastBrightnessBeforeAction ?? 0;
    message.lastTransitionStopReason = object.lastTransitionStopReason ?? 0;
    return message;
  },
};

function createBaseButtonState(): ButtonState {
  return { nodeId: 0, connectorId: 0, currentState: 0, lastModifiedTime: 0 };
}

export const ButtonState: MessageFns<ButtonState> = {
  encode(message: ButtonState, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.nodeId !== 0) {
      writer.uint32(8).uint32(message.nodeId);
    }
    if (message.connectorId !== 0) {
      writer.uint32(16).uint32(message.connectorId);
    }
    if (message.currentState !== 0) {
      writer.uint32(24).int32(message.currentState);
    }
    if (message.lastModifiedTime !== 0) {
      writer.uint32(32).uint64(message.lastModifiedTime);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ButtonState {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseButtonState();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.nodeId = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.connectorId = reader.uint32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.currentState = reader.int32() as any;
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.lastModifiedTime = longToNumber(reader.uint64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ButtonState>, I>>(base?: I): ButtonState {
    return ButtonState.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ButtonState>, I>>(object: I): ButtonState {
    const message = createBaseButtonState();
    message.nodeId = object.nodeId ?? 0;
    message.connectorId = object.connectorId ?? 0;
    message.currentState = object.currentState ?? 0;
    message.lastModifiedTime = object.lastModifiedTime ?? 0;
    return message;
  },
};

function createBasePIRState(): PIRState {
  return { nodeId: 0, isActivated: false, lastActivatedTime: 0, deactivateAfterTime: 0 };
}

export const PIRState: MessageFns<PIRState> = {
  encode(message: PIRState, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.nodeId !== 0) {
      writer.uint32(8).uint32(message.nodeId);
    }
    if (message.isActivated !== false) {
      writer.uint32(16).bool(message.isActivated);
    }
    if (message.lastActivatedTime !== 0) {
      writer.uint32(24).uint64(message.lastActivatedTime);
    }
    if (message.deactivateAfterTime !== 0) {
      writer.uint32(32).uint64(message.deactivateAfterTime);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PIRState {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePIRState();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.nodeId = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.isActivated = reader.bool();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.lastActivatedTime = longToNumber(reader.uint64());
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.deactivateAfterTime = longToNumber(reader.uint64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PIRState>, I>>(base?: I): PIRState {
    return PIRState.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PIRState>, I>>(object: I): PIRState {
    const message = createBasePIRState();
    message.nodeId = object.nodeId ?? 0;
    message.isActivated = object.isActivated ?? false;
    message.lastActivatedTime = object.lastActivatedTime ?? 0;
    message.deactivateAfterTime = object.deactivateAfterTime ?? 0;
    return message;
  },
};

function createBaseESPStatus(): ESPStatus {
  return { status: 0 };
}

export const ESPStatus: MessageFns<ESPStatus> = {
  encode(message: ESPStatus, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.status !== 0) {
      writer.uint32(8).int32(message.status);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ESPStatus {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseESPStatus();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.status = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ESPStatus>, I>>(base?: I): ESPStatus {
    return ESPStatus.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ESPStatus>, I>>(object: I): ESPStatus {
    const message = createBaseESPStatus();
    message.status = object.status ?? 0;
    return message;
  },
};

function createBaseESPUpdateConfig(): ESPUpdateConfig {
  return {
    macConfig: undefined,
    dhcpConfig: undefined,
    wifiConfig: undefined,
    qrCode: "",
    serverAddress: "",
    configVersion: "",
  };
}

export const ESPUpdateConfig: MessageFns<ESPUpdateConfig> = {
  encode(message: ESPUpdateConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.macConfig !== undefined) {
      ESPUpdateConfig_MACConfig.encode(message.macConfig, writer.uint32(10).fork()).join();
    }
    if (message.dhcpConfig !== undefined) {
      ESPUpdateConfig_DHCPConfig.encode(message.dhcpConfig, writer.uint32(18).fork()).join();
    }
    if (message.wifiConfig !== undefined) {
      ESPUpdateConfig_WifiConfig.encode(message.wifiConfig, writer.uint32(26).fork()).join();
    }
    if (message.qrCode !== "") {
      writer.uint32(34).string(message.qrCode);
    }
    if (message.serverAddress !== "") {
      writer.uint32(42).string(message.serverAddress);
    }
    if (message.configVersion !== "") {
      writer.uint32(50).string(message.configVersion);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ESPUpdateConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseESPUpdateConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.macConfig = ESPUpdateConfig_MACConfig.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.dhcpConfig = ESPUpdateConfig_DHCPConfig.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.wifiConfig = ESPUpdateConfig_WifiConfig.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.qrCode = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.serverAddress = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.configVersion = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ESPUpdateConfig>, I>>(base?: I): ESPUpdateConfig {
    return ESPUpdateConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ESPUpdateConfig>, I>>(object: I): ESPUpdateConfig {
    const message = createBaseESPUpdateConfig();
    message.macConfig = (object.macConfig !== undefined && object.macConfig !== null)
      ? ESPUpdateConfig_MACConfig.fromPartial(object.macConfig)
      : undefined;
    message.dhcpConfig = (object.dhcpConfig !== undefined && object.dhcpConfig !== null)
      ? ESPUpdateConfig_DHCPConfig.fromPartial(object.dhcpConfig)
      : undefined;
    message.wifiConfig = (object.wifiConfig !== undefined && object.wifiConfig !== null)
      ? ESPUpdateConfig_WifiConfig.fromPartial(object.wifiConfig)
      : undefined;
    message.qrCode = object.qrCode ?? "";
    message.serverAddress = object.serverAddress ?? "";
    message.configVersion = object.configVersion ?? "";
    return message;
  },
};

function createBaseESPUpdateConfig_MACConfig(): ESPUpdateConfig_MACConfig {
  return { useMacAddress: false, macAddress: "" };
}

export const ESPUpdateConfig_MACConfig: MessageFns<ESPUpdateConfig_MACConfig> = {
  encode(message: ESPUpdateConfig_MACConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.useMacAddress !== false) {
      writer.uint32(8).bool(message.useMacAddress);
    }
    if (message.macAddress !== "") {
      writer.uint32(18).string(message.macAddress);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ESPUpdateConfig_MACConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseESPUpdateConfig_MACConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.useMacAddress = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.macAddress = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ESPUpdateConfig_MACConfig>, I>>(base?: I): ESPUpdateConfig_MACConfig {
    return ESPUpdateConfig_MACConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ESPUpdateConfig_MACConfig>, I>>(object: I): ESPUpdateConfig_MACConfig {
    const message = createBaseESPUpdateConfig_MACConfig();
    message.useMacAddress = object.useMacAddress ?? false;
    message.macAddress = object.macAddress ?? "";
    return message;
  },
};

function createBaseESPUpdateConfig_DHCPConfig(): ESPUpdateConfig_DHCPConfig {
  return { staticIp: false, ipAddress: "", subnetMask: "", gateway: "", dnsServer: "" };
}

export const ESPUpdateConfig_DHCPConfig: MessageFns<ESPUpdateConfig_DHCPConfig> = {
  encode(message: ESPUpdateConfig_DHCPConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.staticIp !== false) {
      writer.uint32(8).bool(message.staticIp);
    }
    if (message.ipAddress !== "") {
      writer.uint32(18).string(message.ipAddress);
    }
    if (message.subnetMask !== "") {
      writer.uint32(26).string(message.subnetMask);
    }
    if (message.gateway !== "") {
      writer.uint32(34).string(message.gateway);
    }
    if (message.dnsServer !== "") {
      writer.uint32(42).string(message.dnsServer);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ESPUpdateConfig_DHCPConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseESPUpdateConfig_DHCPConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.staticIp = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.ipAddress = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.subnetMask = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.gateway = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.dnsServer = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ESPUpdateConfig_DHCPConfig>, I>>(base?: I): ESPUpdateConfig_DHCPConfig {
    return ESPUpdateConfig_DHCPConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ESPUpdateConfig_DHCPConfig>, I>>(object: I): ESPUpdateConfig_DHCPConfig {
    const message = createBaseESPUpdateConfig_DHCPConfig();
    message.staticIp = object.staticIp ?? false;
    message.ipAddress = object.ipAddress ?? "";
    message.subnetMask = object.subnetMask ?? "";
    message.gateway = object.gateway ?? "";
    message.dnsServer = object.dnsServer ?? "";
    return message;
  },
};

function createBaseESPUpdateConfig_WifiConfig(): ESPUpdateConfig_WifiConfig {
  return { ssid: "", password: "" };
}

export const ESPUpdateConfig_WifiConfig: MessageFns<ESPUpdateConfig_WifiConfig> = {
  encode(message: ESPUpdateConfig_WifiConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.ssid !== "") {
      writer.uint32(10).string(message.ssid);
    }
    if (message.password !== "") {
      writer.uint32(18).string(message.password);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ESPUpdateConfig_WifiConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseESPUpdateConfig_WifiConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.ssid = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.password = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ESPUpdateConfig_WifiConfig>, I>>(base?: I): ESPUpdateConfig_WifiConfig {
    return ESPUpdateConfig_WifiConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ESPUpdateConfig_WifiConfig>, I>>(object: I): ESPUpdateConfig_WifiConfig {
    const message = createBaseESPUpdateConfig_WifiConfig();
    message.ssid = object.ssid ?? "";
    message.password = object.password ?? "";
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
