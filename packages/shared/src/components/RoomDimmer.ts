import { z } from "zod";
import { DeviceIconKey } from "./DeviceIcons";
import { CurvePoint, CurveType } from "./DimmingCurve";
import { BaseDevice } from "./devices/BaseDevice";
import { DeviceControlSettings } from "./devices/DeviceControlSettings";

export const roomDimmerViaIds = ["via", "viaUp", "viaDown"] as const;
export const RoomDimmerViaId = z.enum(roomDimmerViaIds);
export type RoomDimmerViaId = z.infer<typeof RoomDimmerViaId>;

export const roomDimmerActions = ["onUpClick", "onUpHold"] as const;
export const RoomDimmerAction = z.enum(roomDimmerActions);
export type RoomDimmerAction = z.infer<typeof RoomDimmerAction>;

export const RoomDimmerVia = z.object({
  name: z.string(),
  icon: DeviceIconKey.optional(),
  lightName: z.string(),
  lightIcon: DeviceIconKey.optional(),
  showLabel: z.boolean(),
  onUpClick: z.record(z.string(), DeviceControlSettings),
  onUpHold: z.record(z.string(), DeviceControlSettings).default({}),
});
export type RoomDimmerVia = z.infer<typeof RoomDimmerVia>;

export function aRoomDimmerVia(params: Partial<RoomDimmerVia>): RoomDimmerVia {
  return {
    name: "Room Dimmer Via",
    showLabel: false,
    lightName: "Light",
    icon: undefined,
    lightIcon: undefined,
    onUpClick: {},
    onUpHold: {},
    ...params,
  };
}

export const RoomDimmerButton = z.object({
  enabled: z.boolean(),
  name: z.string(),
  icon: DeviceIconKey.optional(),
  showLabel: z.boolean(),
  onUpClick: z.record(z.string(), DeviceControlSettings),
  onUpHold: z.record(z.string(), DeviceControlSettings).default({}),
});
export type RoomDimmerButton = z.infer<typeof RoomDimmerButton>;

export function aRoomDimmerButton(
  params: Partial<RoomDimmerButton>,
): RoomDimmerButton {
  return {
    name: "Room Dimmer Button",
    enabled: false,
    showLabel: false,
    icon: undefined,
    onUpClick: {},
    onUpHold: {},
    ...params,
  };
}

export const RoomDimmerDevice = BaseDevice.extend({
  type: z.literal("roomDimmer"),
  nodeId: z.string(),
  viaId: RoomDimmerViaId,
  dimmingCurve: z.array(CurvePoint).optional(),
  dimmingCurveType: CurveType.optional(),
  defaultDimmingSpeed: z.number().optional(),
});
export type RoomDimmerDevice = z.infer<typeof RoomDimmerDevice>;

export const RoomDimmer = z.object({
  id: z.string(),
  type: z.literal("roomDimmer"),
  title: z.string(),
  via: RoomDimmerVia,
  viaUp: RoomDimmerButton,
  viaDown: RoomDimmerButton,
  dimSpeed: z.number(),
  dimmingCurve: z.object({
    points: z.array(CurvePoint),
    type: CurveType,
  }),
});
export type RoomDimmer = z.infer<typeof RoomDimmer>;
