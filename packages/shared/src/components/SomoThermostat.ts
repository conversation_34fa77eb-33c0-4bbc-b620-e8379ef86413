import { z } from "zod";
import { DeviceIconKey } from "./DeviceIcons";
import { CurvePoint, CurveType } from "./DimmingCurve";
import { BaseDevice } from "./devices/BaseDevice";
import { ThermostatControlSettings } from "./devices/ThermostatControlSettings";

export const somoThermostatViaIds = [
  "via",
  "viaUp",
  "viaDown",
  "modeCool",
  "modeHeat",
  "modeAuto",
  "modeFan",
  "fanAuto",
  "fanLow",
  "fanMedium",
  "fanHigh",
] as const;
export const SomoThermostatViaId = z.enum(somoThermostatViaIds);
export type SomoThermostatViaId = z.infer<typeof SomoThermostatViaId>;

export const SomoThermostatVia = z.object({
  name: z.string(),
  icon: DeviceIconKey.optional(),
  hvacName: z.string(),
  hvacIcon: DeviceIconKey.optional(),
  showLabel: z.boolean(),
  onUpClick: z
    .record(z.string(), ThermostatControlSettings)
    .refine((record) => Object.keys(record).length <= 1, {
      error: "Only one action is allowed.",
    }),
});
export type SomoThermostatVia = z.infer<typeof SomoThermostatVia>;

export function aSomoThermostatVia(
  params: Partial<SomoThermostatVia>,
): SomoThermostatVia {
  return {
    name: "Somo Thermostat Via",
    showLabel: false,
    hvacName: "Thermostat",
    icon: undefined,
    hvacIcon: undefined,
    onUpClick: {},
    ...params,
  };
}

export const SomoThermostatButton = z.object({
  enabled: z.boolean(),
  name: z.string(),
  icon: DeviceIconKey.optional(),
  showLabel: z.boolean(),
  onUpClick: z
    .record(z.string(), ThermostatControlSettings)
    .refine((record) => Object.keys(record).length <= 1, {
      error: "Only one action is allowed.",
    }),
});
export type SomoThermostatButton = z.infer<typeof SomoThermostatButton>;

export function aSomoThermostatButton(
  params: Partial<SomoThermostatButton>,
): SomoThermostatButton {
  return {
    name: "Somo Thermostat Button",
    enabled: false,
    showLabel: false,
    icon: undefined,
    onUpClick: {},
    ...params,
  };
}

export const ThermostatMode = z.enum(["heat", "cool", "fan", "auto"]);
export type ThermostatMode = z.infer<typeof ThermostatMode>;

export const ThermostatFanSpeed = z.enum(["low", "medium", "high", "auto"]);
export type ThermostatFanSpeed = z.infer<typeof ThermostatFanSpeed>;

export const SomoThermostat = z.object({
  id: z.string(),
  type: z.literal("somoThermostat"),
  title: z.string(),
  minTemp: z.number().describe("Default minimum temperature in degrees"),
  maxTemp: z.number().describe("Default maximum temperature in degrees"),
  temperatureUnit: z.enum(["C", "F"]),
  allowedModes: z.array(ThermostatMode),
  allowedFanSpeeds: z.array(ThermostatFanSpeed),
  via: SomoThermostatVia,
  viaUp: SomoThermostatButton,
  viaDown: SomoThermostatButton,
  modeCool: SomoThermostatButton,
  modeHeat: SomoThermostatButton,
  modeAuto: SomoThermostatButton,
  modeFan: SomoThermostatButton,
  fanAuto: SomoThermostatButton,
  fanLow: SomoThermostatButton,
  fanMedium: SomoThermostatButton,
  fanHigh: SomoThermostatButton,
  stepSize: z
    .number()
    .prefault(1)
    .describe("Step size for setpoint adjustment"),
});
export type SomoThermostat = z.infer<typeof SomoThermostat>;

export const SomoThermostatDevice = BaseDevice.extend({
  type: z.literal("somoThermostat"),
  nodeId: z.string(),
  viaId: SomoThermostatViaId,
  dimmingCurve: z.array(CurvePoint).optional(),
  dimmingCurveType: CurveType.optional(),
  defaultDimmingSpeed: z.number().optional(),
});
export type SomoThermostatDevice = z.infer<typeof SomoThermostatDevice>;
