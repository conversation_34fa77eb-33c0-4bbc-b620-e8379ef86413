import { z } from "zod";
import { DeviceIconKey } from "./DeviceIcons";
import { DeviceControlSettings } from "./devices/DeviceControlSettings";
import { ThermostatControlSettings } from "./devices/ThermostatControlSettings";

export const UnifiedActionSettings = z.union([
  DeviceControlSettings,
  ThermostatControlSettings,
]);
export type UnifiedActionSettings = z.infer<typeof UnifiedActionSettings>;

export const doorSensorViaIds = ["onOpen", "onClose"] as const;
export const DoorSensorViaId = z.enum(doorSensorViaIds);
export type DoorSensorViaId = z.infer<typeof DoorSensorViaId>;

export const DoorSensorVia = z.object({
  name: z.string(),
  icon: DeviceIconKey.optional(),
  lightIcon: DeviceIconKey.optional(),
  lightName: z.string(),
  showLabel: z.boolean(),
  onUpClick: z.record(z.string(), UnifiedActionSettings),
});
export type DoorSensorVia = z.infer<typeof DoorSensorVia>;

export const DoorSensorAction = z.object({
  enabled: z.boolean(),
  name: z.string(),
  icon: DeviceIconKey.optional(),
  showLabel: z.boolean(),
  offDelay: z.number(),
  onUpClick: z.record(z.string(), UnifiedActionSettings),
});
export type DoorSensorAction = z.infer<typeof DoorSensorAction>;

export function aDoorSensorAction(
  params: Partial<DoorSensorAction>,
): DoorSensorAction {
  return {
    enabled: false,
    showLabel: false,
    name: "Door Sensor Action",
    icon: undefined,
    offDelay: 0,
    onUpClick: {},
    ...params,
  };
}

export const DoorSensor = z.object({
  id: z.string(),
  type: z.literal("doorSensor"),
  title: z.string(),
  onOpen: DoorSensorAction,
  onClose: DoorSensorAction,
});
export type DoorSensor = z.infer<typeof DoorSensor>;
