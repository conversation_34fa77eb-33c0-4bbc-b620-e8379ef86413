import { z } from "zod";
import { DeviceIconKey } from "../DeviceIcons";
import { BaseDevice } from "./BaseDevice";

export const SomfyShadesDevice = z.object({
  id: z.string(),
  name: z.string(),
  icon: DeviceIconKey.optional(),
  sortIndex: z.number(),
  showLabel: z.boolean().default(true),
});
export type SomfyShadesDevice = z.infer<typeof SomfyShadesDevice>;

// Device type that extends BaseDevice for use in the Device union
export const SomfyShadesBaseDevice = BaseDevice.extend({
  type: z.literal("somfyShades"),
  name: z.string(),
  icon: DeviceIconKey.optional(),
  nodeId: z.string(),
  viaId: z.string(), // References the shade device in the base station
});
export type SomfyShadesBaseDevice = z.infer<typeof SomfyShadesBaseDevice>;

export const BaseStation = z.object({
  id: z.string(),
  type: z.literal("baseStation"),
  title: z.string(),
  somfyShades: z.record(z.string(), SomfyShadesDevice).default({}),
});
export type BaseStation = z.infer<typeof BaseStation>;
