import { z } from "zod";
import { randomId } from "../../lib/randomId";

export const DeviceControlSettings = z.object({
  id: z.string(),
  deviceId: z.string(),
  sortIndex: z.number(),
  onValue: z.number(),
  offValue: z.number(),
  dimSpeed: z.number().describe("Dimming speed in seconds"),
  targetValue: z.number(),
  delay: z
    .number()
    .default(0)
    .describe("Delay in miliseconds before action executes"),
  type: z.literal("lighting").prefault("lighting"),
});
export type DeviceControlSettings = z.infer<typeof DeviceControlSettings>;

export function aDeviceControlSettings(
  params: Partial<DeviceControlSettings>,
): DeviceControlSettings {
  return {
    id: `device-control-settings-${randomId()}`,
    deviceId: `device-id-${randomId()}`,
    dimSpeed: 0,
    sortIndex: 0,
    targetValue: 0,
    onValue: 0,
    offValue: 0,
    delay: 0,
    type: "lighting",
    ...params,
  };
}
