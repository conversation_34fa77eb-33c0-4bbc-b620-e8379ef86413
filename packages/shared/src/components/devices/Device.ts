import { z } from "zod";
import { RoomDimmerDevice } from "../RoomDimmer";
import { RoomSwitchDevice } from "../RoomSwitch";
import { SomoFanDevice } from "../SomoFan";
import { SomoIrControllerDevice } from "../SomoIrController";
import { SomoThermostatDevice } from "../SomoThermostat";
import { SomfyShadesBaseDevice } from "./BaseStation";
import {
  RelayOutputDevice,
  ZeroToTenVoltDimmerDevice,
} from "./CanBusController";
import { SomoShadesDevice } from "./SomoShades";

export const Device = z.union([
  // TODO: move to top-level (Controllables)
  RoomSwitchDevice,
  RoomDimmerDevice,
  RelayOutputDevice,
  SomfyShadesBaseDevice,
  SomoIrControllerDevice,
  SomoFanDevice,
  SomoThermostatDevice,
  SomoShadesDevice,
  ZeroToTenVoltDimmerDevice,
]);
export type Device = z.infer<typeof Device>;
