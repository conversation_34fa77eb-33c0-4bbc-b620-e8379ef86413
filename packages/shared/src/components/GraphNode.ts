import { Node } from "@xyflow/react";
import z from "zod";
import { randomId } from "../lib/randomId";
import { CommentNodeData } from "./CommentNode";
import { Light } from "./controllables/Light";
import { OutletDimmerLight } from "./controllables/OutletDimmerLight";
import { SomoIrController, SomoThermostat } from "./devices";
import { BaseStation } from "./devices/BaseStation";
import {
  CanBusController,
  ToggleCanBusController,
} from "./devices/CanBusController";
import { SomoShades } from "./devices/SomoShades";
import { DoorSensor } from "./DoorSensor";
import { Image } from "./Image";
import { OutletDimmer } from "./OutletDimmer";
import { PresenceSensor } from "./PresenceSensor";
import { RoomDimmer } from "./RoomDimmer";
import { RoomSwitch } from "./RoomSwitch";
import { Scene } from "./Scene";
import { SectionNodeData } from "./SectionNode";
import { ServicePad } from "./ServicePad";
import { SomoFan } from "./SomoFan";
import { VirtualButton } from "./VirtualButton";

/**
 * Container nodes contain the UI details of the related Data, such as the
 * via elements that can be connected to devices.
 */
const containerNodeTypes = [
  "baseStationContainer",
  "canbusControllerContainer",
  "doorSensorContainer",
  "image",
  "light",
  "outletDimmerContainer",
  "outletDimmerLight",
  "presenceSensorContainer",
  "roomDimmerContainer",
  "somoFanContainer",
  "roomSwitchContainer",
  "scene",
  "servicePadContainer",
  "somoShadesContainer",
  "somoThermostatContainer",
  "somoIrControllerContainer",
  "virtualButtonContainer",
] as const;
export type ContainerNodeTypes = (typeof containerNodeTypes)[number];

export const DataByContainerNodeType = {
  baseStationContainer: BaseStation,
  canbusControllerContainer: CanBusController,
  doorSensorContainer: DoorSensor,
  somoFanContainer: SomoFan,
  image: Image,
  light: Light,
  outletDimmerContainer: OutletDimmer,
  outletDimmerLight: OutletDimmerLight,
  presenceSensorContainer: PresenceSensor,
  roomDimmerContainer: RoomDimmer,
  roomSwitchContainer: RoomSwitch,
  scene: Scene,
  servicePadContainer: ServicePad,
  somoShadesContainer: SomoShades,
  somoThermostatContainer: SomoThermostat,
  somoIrControllerContainer: SomoIrController,
  virtualButtonContainer: VirtualButton,
} as const satisfies Record<ContainerNodeTypes, z.ZodSchema>;
export type DataByContainerNodeType = {
  [K in ContainerNodeTypes]: z.infer<(typeof DataByContainerNodeType)[K]>;
};

export const LayoutContainerNodeData = z.object({ dataId: z.string() });
export type LayoutContainerNodeData = z.infer<typeof LayoutContainerNodeData>;
export type LayoutContainerNode<
  Type extends ContainerNodeTypes = ContainerNodeTypes,
> = Node<LayoutContainerNodeData, Type>;

export function isContainer(node: GraphNode) {
  return containerNodeTypes.includes(node.type as ContainerNodeTypes);
}

/**
 * Anchor nodes are nodes that always show up on graph.
 * User can select them to make the container nodes appear.
 */
const anchorNodeTypes = [
  "baseStationAnchor",
  "canbusControllerAnchor",
  "doorSensorAnchor",
  "roomDimmerAnchor",
  "roomSwitchAnchor",
  "outletDimmerAnchor",
  "presenceSensorAnchor",
  "servicePadAnchor",
  "somoFanAnchor",
  "somoIrControllerAnchor",
  "somoShadesAnchor",
  "somoThermostatAnchor",
  "virtualButtonAnchor",
] as const;
export type AnchorNodeTypes = (typeof anchorNodeTypes)[number];

export const LayoutAnchorNodeData = z.record(z.string(), z.unknown());
export type LayoutAnchorNodeData = z.infer<typeof LayoutAnchorNodeData>;
export type LayoutAnchorNode<Type extends AnchorNodeTypes = AnchorNodeTypes> =
  Node<LayoutAnchorNodeData, Type>;

export function isAnchor(node: GraphNode) {
  return anchorNodeTypes.includes(node.type as AnchorNodeTypes);
}

/**
 * Map each Node `.type` to its related `.data` type.
 *
 * @example
 *   `Node<ImageNodeData, "image">` => `image: ImageNodeData`
 */
type NodesDataByType = {
  baseStationContainer: LayoutContainerNodeData;
  canbusControllerContainer: LayoutContainerNodeData;
  doorSensorContainer: LayoutContainerNodeData;
  image: LayoutContainerNodeData;
  light: LayoutContainerNodeData;
  roomDimmerContainer: LayoutContainerNodeData;
  roomSwitchContainer: LayoutContainerNodeData;
  scene: LayoutContainerNodeData;
  outletDimmerContainer: LayoutContainerNodeData;
  outletDimmerLight: LayoutContainerNodeData;
  presenceSensorContainer: LayoutContainerNodeData;
  somoFanContainer: LayoutContainerNodeData;
  somoIrControllerContainer: LayoutContainerNodeData;
  somoShadesContainer: LayoutContainerNodeData;
  somoThermostatContainer: LayoutContainerNodeData;
  servicePadContainer: LayoutContainerNodeData;
  virtualButtonContainer: LayoutContainerNodeData;

  baseStationAnchor: LayoutAnchorNodeData;
  canbusControllerAnchor: LayoutAnchorNodeData;
  doorSensorAnchor: LayoutAnchorNodeData;
  roomDimmerAnchor: LayoutAnchorNodeData;
  roomSwitchAnchor: LayoutAnchorNodeData;
  outletDimmerAnchor: LayoutAnchorNodeData;
  presenceSensorAnchor: LayoutAnchorNodeData;
  servicePadAnchor: LayoutAnchorNodeData;
  somoFanAnchor: LayoutAnchorNodeData;
  somoIrControllerAnchor: LayoutAnchorNodeData;
  somoShadesAnchor: LayoutAnchorNodeData;
  somoThermostatAnchor: LayoutAnchorNodeData;
  virtualButtonAnchor: LayoutAnchorNodeData;

  // 👇 What's left to refactor
  comment: CommentNodeData;
  section: SectionNodeData;
};

// Derive @xyflow/react's `Node` types based on the bindings above
export type GraphNodesByType = {
  [K in keyof NodesDataByType]: Node<NodesDataByType[K], K>;
};

export type GraphNode =
  | LayoutAnchorNode
  | LayoutContainerNode
  // TODO: once we have migrated all nodes, we can get rid of GraphNodesByType
  | GraphNodesByType[keyof GraphNodesByType];

/**
 * List of ReactFlow keys we want to store in Y.js.
 * Some keys are not shared between clients (e.g., selected is a local state).
 */
export const reactFlowKeysToPersist: (keyof Node)[] = [
  "id",
  "type",
  "ariaLabel",

  // Relations to other nodes
  "parentId",
  "expandParent",
  "extent",

  // Style
  "width",
  "height",
  "position",
  "className",
  "style",
  "zIndex",

  // Custom data
  "data",

  // We don't store if node is selected… BUT we store if it's able to!
  "selectable",
  "connectable",
  "focusable",
  "draggable",
  "deletable",
];

/**
 * Helper function to filter nodes by type that gives you autocompletion +
 * type guard the filtered nodes.
 *
 * @example
 *   const nodes = filterNodes(nodes, "image");
 *   // ^ => Node<ImageNodeData, "image">[]
 */
export function filterNodes<Type extends keyof GraphNodesByType>(
  nodes: GraphNode[],
  type: Type,
): GraphNodesByType[Type][] {
  return nodes.filter((n): n is GraphNodesByType[Type] => n.type === type);
}

// #region Generic helpers for @xyflow nodes

export function isManipulated(node: Node) {
  return node.selected || node.dragging;
}

export function isIdle(node: Node) {
  return !isManipulated(node);
}

export function isNodeOrParentSelected(allNodes: Node[], node: Node) {
  return node.selected || Boolean(findParent(allNodes, node)?.selected);
}

export function findParent(allNodes: Node[], node: Node) {
  if (!node.parentId) {
    return null;
  }

  return allNodes.find((n) => n.id === node.parentId);
}

// #endregion

// #region Factories
export function aSectionNode(
  params: Partial<GraphNodesByType["section"]>,
  data?: Partial<SectionNodeData>,
): GraphNodesByType["section"] {
  return {
    id: `section-node-${randomId()}`,
    type: "section",
    position: { x: 0, y: 0 },
    data: {
      title: "Section",
      devices: {},
      ...data,
    },
    ...params,
  };
}

export function aToggleCanBusController(
  params: Partial<ToggleCanBusController>,
): ToggleCanBusController {
  return {
    id: `toggle-canbus-controller-${randomId()}`,
    type: "toggle",
    label: "Toggle",
    sortIndex: 0,
    onUpClick: {},
    onUpHold: {},
    onDownClick: {},
    onDownHold: {},
    onUpPress: {},
    onUpRelease: {},
    onDownPress: {},
    onDownRelease: {},
    onUpHoldRelease: {},
    onDownHoldRelease: {},
    ...params,
  };
}
