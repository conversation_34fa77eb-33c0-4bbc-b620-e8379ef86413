import { z } from "zod";
import { DeviceIconKey } from "./DeviceIcons";
import { BaseDevice } from "./devices/BaseDevice";
import { DeviceControlSettings } from "./devices/DeviceControlSettings";

export const roomSwitchViaIds = ["via1", "via2", "via3"] as const;
export const RoomSwitchViaId = z.enum(roomSwitchViaIds);
export type RoomSwitchViaId = z.infer<typeof RoomSwitchViaId>;

export const RoomSwitchVia = z.object({
  enabled: z.boolean(),
  hasLoad: z.boolean(),
  name: z.string(),
  icon: DeviceIconKey.optional(),
  lightName: z.string(),
  lightIcon: DeviceIconKey.optional(),
  showLabel: z.boolean(),
  onUpClick: z.record(z.string(), DeviceControlSettings),
});
export type RoomSwitchVia = z.infer<typeof RoomSwitchVia>;

export function aRoomSwitchVia(params: Partial<RoomSwitchVia>): RoomSwitchVia {
  return {
    name: "Room Switch Via",
    enabled: false,
    hasLoad: false,
    showLabel: false,
    lightName: "Light",
    icon: undefined,
    lightIcon: undefined,
    onUpClick: {},
    ...params,
  };
}

export const RoomSwitchDevice = BaseDevice.extend({
  type: z.literal("roomSwitch"),
  nodeId: z.string(),
  viaId: RoomSwitchViaId,
});
export type RoomSwitchDevice = z.infer<typeof RoomSwitchDevice>;

export const RoomSwitch = z.object({
  id: z.string(),
  type: z.literal("roomSwitch"),
  title: z.string(),
  via1: RoomSwitchVia,
  via2: RoomSwitchVia,
  via3: RoomSwitchVia,
});
export type RoomSwitch = z.infer<typeof RoomSwitch>;
