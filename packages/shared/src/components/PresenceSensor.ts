import { z } from "zod";
import { DeviceIconKey } from "./DeviceIcons";
import { DeviceControlSettings } from "./devices/DeviceControlSettings";

export const presenceSensorViaIds = ["onActivate", "onDeactivate"] as const;
export const PresenceSensorViaId = z.enum(presenceSensorViaIds);
export type PresenceSensorViaId = z.infer<typeof PresenceSensorViaId>;

export const PresenceSensorVia = z.object({
  name: z.string(),
  icon: DeviceIconKey.optional(),
  lightIcon: DeviceIconKey.optional(),
  lightName: z.string(),
  showLabel: z.boolean(),
  onUpClick: z.record(z.string(), DeviceControlSettings),
});
export type PresenceSensorVia = z.infer<typeof PresenceSensorVia>;

export const PresenceSensorAction = z.object({
  enabled: z.boolean(),
  name: z.string(),
  icon: DeviceIconKey.optional(),
  showLabel: z.boolean(),
  offDelay: z.number(),
  cancelOnActivityDuringDelay: z.boolean().prefault(false),
  onUpClick: z.record(z.string(), DeviceControlSettings),
});
export type PresenceSensorAction = z.infer<typeof PresenceSensorAction>;

export function aPresenceSensorAction(
  params: Partial<PresenceSensorAction>,
): PresenceSensorAction {
  return {
    enabled: false,
    showLabel: false,
    name: "Presence Sensor Action",
    icon: undefined,
    offDelay: 0,
    cancelOnActivityDuringDelay: false,
    onUpClick: {},
    ...params,
  };
}

export const PresenceSensor = z.object({
  id: z.string(),
  type: z.literal("presenceSensor"),
  title: z.string(),
  onActivate: PresenceSensorAction,
  onDeactivate: PresenceSensorAction,
});
export type PresenceSensor = z.infer<typeof PresenceSensor>;
