import { z } from "zod";
import { DeviceIconKey } from "./DeviceIcons";
import { DeviceControlSettings } from "./devices/DeviceControlSettings";
import { CurvePoint, CurveType } from "./DimmingCurve";

export const outletDimmerViaIds = ["via", "viaUp", "viaDown"] as const;
export const OutletDimmerViaId = z.enum(outletDimmerViaIds);
export type OutletDimmerViaId = z.infer<typeof OutletDimmerViaId>;

export const OutletDimmerVia = z.object({
  name: z.string(),
  icon: DeviceIconKey.optional(),
  showLabel: z.boolean(),
  onUpClick: z
    .record(z.string(), DeviceControlSettings)
    .refine((record) => Object.keys(record).length <= 1, {
      error: "Only one action is allowed.",
    }),
});
export type OutletDimmerVia = z.infer<typeof OutletDimmerVia>;

export function aOutletDimmerVia(
  params: Partial<OutletDimmerVia>,
): OutletDimmerVia {
  return {
    name: "Outlet Dimmer Via",
    showLabel: false,
    icon: undefined,
    onUpClick: {},
    ...params,
  };
}

export const OutletDimmerButton = OutletDimmerVia.extend({
  enabled: z.boolean(),
});
export type OutletDimmerButton = z.infer<typeof OutletDimmerButton>;

export function aOutletDimmerButton(
  params: Partial<OutletDimmerButton>,
): OutletDimmerButton {
  return {
    enabled: false,
    name: "Outlet Dimmer Button",
    showLabel: false,
    icon: undefined,
    onUpClick: {},
    ...params,
  };
}

export const OutletDimmer = z.object({
  id: z.string(),
  type: z.literal("outletDimmer"),
  name: z.string(),
  via: OutletDimmerVia,
  viaUp: OutletDimmerButton,
  viaDown: OutletDimmerButton,
  dimSpeed: z.number().describe("Dimming speed in seconds"),
  dimmingCurve: z.object({
    points: z.array(CurvePoint),
    type: CurveType,
  }),
});
export type OutletDimmer = z.infer<typeof OutletDimmer>;
