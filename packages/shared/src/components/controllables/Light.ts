import { z } from "zod";
import { randomId } from "../../lib/randomId";
import { DeviceIconKey } from "../DeviceIcons";
import { CurvePoint, CurveType } from "../DimmingCurve";

export const lightFixtureTypes = [
  "Tunable White",
  "EST",
  "DF_12",
  "ELV",
  "D4",
  "Analog",
] as const;
export const LightFixtureType = z.enum(lightFixtureTypes);
export type LightFixtureType = z.infer<typeof LightFixtureType>;

export const LightFixture = z.object({
  id: z.string(),
  type: LightFixtureType,
  channel: z.number(),
  minBrightness: z.number(),
  maxBrightness: z.number(),
});
export type LightFixture = z.infer<typeof LightFixture>;

export function aLightFixture(params: Partial<LightFixture>): LightFixture {
  return {
    id: `fixture-${randomId()}`,
    type: "Tunable White",
    channel: 1,
    minBrightness: 0,
    maxBrightness: 100,
    ...params,
  };
}

export const Light = z.object({
  id: z.string(),
  type: z.literal("light"),
  name: z.string(),
  showLabel: z.boolean(),
  icon: DeviceIconKey.optional(),
  fixtures: z.record(z.string(), LightFixture),
  isDimmable: z.boolean(),
  defaultDimmingSpeed: z.number(),
  defaultDimmingCurve: z.object({
    points: z.array(CurvePoint),
    type: CurveType,
  }),
});
export type Light = z.infer<typeof Light>;
