import z from "zod";
import { <PERSON>ceIconKey } from "../DeviceIcons";
import { CurvePoint, CurveType } from "../DimmingCurve";

export const OutletDimmerLight = z.object({
  id: z.string(),
  type: z.literal("outletDimmerLight"),
  name: z.string(),
  showLabel: z.boolean(),
  icon: DeviceIconKey.optional(),
  controllerId: z
    .string()
    .describe("Data ID of the Outlet Dimmer it belongs to"),
  defaultDimmingSpeed: z.number(),
  defaultDimmingCurve: z.object({
    points: z.array(CurvePoint),
    type: CurveType,
  }),
});
export type OutletDimmerLight = z.infer<typeof OutletDimmerLight>;
