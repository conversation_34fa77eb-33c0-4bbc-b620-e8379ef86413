import { z } from "zod";
import { DeviceIconKey } from "./DeviceIcons";

export const servicePadViaIds = [
  "makeUpRoomButton",
  "doNotDisturbButton",
  "doorbellButton",
] as const;
export const ServicePadViaId = z.enum(servicePadViaIds);
export type ServicePadViaId = z.infer<typeof ServicePadViaId>;

export const ServicePadAction = z.object({
  enabled: z.boolean(),
  name: z.string(),
  icon: DeviceIconKey.optional(),
  showLabel: z.boolean(),
});
export type ServicePadAction = z.infer<typeof ServicePadAction>;

export function aServicePadAction(
  params: Partial<ServicePadAction>,
): ServicePadAction {
  return {
    enabled: false,
    showLabel: false,
    name: "Service Pad Action",
    icon: undefined,
    ...params,
  };
}

export const ServicePad = z.object({
  id: z.string(),
  type: z.literal("servicePad"),
  title: z.string(),
  mode: z
    .enum(["servicePad", "doorbell"])
    .describe("Service Pad or just Doorbell"),
  makeUpRoomButton: ServicePadAction,
  doNotDisturbButton: ServicePadAction,
  doorbellButton: ServicePadAction,
});
export type ServicePad = z.infer<typeof ServicePad>;
