import { z } from "zod";
import { DeviceIconKey } from "./DeviceIcons";
import { DeviceControlSettings } from "./devices/DeviceControlSettings";

export const virtualButtonViaIds = ["via"] as const;
export const VirtualButtonViaId = z.enum(virtualButtonViaIds);
export type VirtualButtonViaId = z.infer<typeof VirtualButtonViaId>;

export const VirtualButtonVia = z.object({
  enabled: z.boolean(),
  name: z.string(),
  icon: DeviceIconKey.optional(),
  lightName: z.string(),
  lightIcon: DeviceIconKey.optional(),
  showLabel: z.boolean(),
  onUpClick: z.record(z.string(), DeviceControlSettings),
});
export type VirtualButtonVia = z.infer<typeof VirtualButtonVia>;

/*
export const VirtualButtonContainerNodeData = z.object({
  title: z.string(),
  via: VirtualButtonVia,
});
export type VirtualButtonContainerNodeData = z.infer<
  typeof VirtualButtonContainerNodeData
>;
*/

export const VirtualButton = z.object({
  id: z.string(),
  type: z.literal("virtualButton"),
  title: z.string(),
  via: VirtualButtonVia,
});
export type VirtualButton = z.infer<typeof VirtualButton>;
