import { z } from "zod";
import { DeviceControlSettings } from "./devices";

export const sceneViaIds = ["onActivate", "onDeactivate"] as const;
export const SceneViaIds = z.enum(sceneViaIds);
export type SceneViaIds = z.infer<typeof SceneViaIds>;

export const SceneVia = z.object({
  enabled: z.boolean(),
  onUpClick: z.record(z.string(), DeviceControlSettings),
});
export type SceneVia = z.infer<typeof SceneVia>;

export const SceneInput = z.object({
  source: z.string(),
  sourceHandle: z.string(),
});
export type SceneInput = z.infer<typeof SceneInput>;

export const Scene = z.object({
  id: z.string(),
  type: z.literal("scene"),
  title: z.string(),
  inputs: z.array(SceneInput),
  onActivate: SceneVia,
  onDeactivate: SceneVia,
});
export type Scene = z.infer<typeof Scene>;
