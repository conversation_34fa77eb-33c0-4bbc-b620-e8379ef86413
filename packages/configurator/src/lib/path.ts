/**
 * Create dot-notation paths for the full data structure.
 *
 * @example
 * ```ts
 * type Data = { foo: { bar: string; baz: number } };
 * type Path = DeepPaths<Data>; // "foo" | "foo.bar" | "foo.baz"
 * ```
 */
export type DeepPaths<T> = T extends
  | string
  | number
  | boolean
  | Date
  | RegExp
  | Buffer
  | Uint8Array
  | ((...args: unknown[]) => unknown)
  | null
  | undefined
  ? never
  : T extends ReadonlyArray<infer _U>
    ? never
    : T extends object
      ? {
          [K in Extract<keyof T, string>]: T[K] extends object
            ? T[K] extends (...args: unknown[]) => unknown
              ? K
              : K | `${K}.${DeepPaths<T[K]>}`
            : K;
        }[Extract<keyof T, string>]
      : never;

/**
 * Get the value at a given dot-notation path.
 *
 * @example
 * ```ts
 * type Data = { foo: { bar: string; baz: number } };
 * type Value = DeepPathValue<Data, "foo.baz">; // "number"
 * ```
 */
export type DeepPathValue<
  T,
  P extends string,
> = P extends `${infer K}.${infer Rest}`
  ? K extends keyof T
    ? DeepPathValue<T[K], Rest>
    : never
  : P extends keyof T
    ? T[P]
    : never;
