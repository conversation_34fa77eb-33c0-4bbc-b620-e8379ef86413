export function omit<T extends object, K extends keyof T>(
  object: T,
  keysToExclude: K[],
): Omit<T, K> {
  const newObject = { ...object };
  for (const key of keysToExclude) {
    delete newObject[key];
  }
  return newObject;
}

export function pick<T extends object, K extends keyof T>(
  object: T,
  keysToInclude: K[],
): Pick<T, K> {
  const newObject = {} as Pick<T, K>;
  for (const key of keysToInclude) {
    newObject[key] = object[key];
  }
  return newObject;
}
