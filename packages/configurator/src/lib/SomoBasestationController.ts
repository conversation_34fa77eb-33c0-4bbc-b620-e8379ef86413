import {
  BasestationUpdateMessage,
  getActiveConfigurationForBasestation,
} from "@somo/shared";
import { SerialPort } from "web-serial-polyfill";
import { EventEmitter } from "./EventEmitter";
import { SerialPortClient } from "./SerialPortClient";
import { crc16Calculate } from "./crc16";

// Add Web Serial API type declarations
declare global {
  interface Navigator {
    serial: {
      getPorts(): Promise<SerialPort[]>;
      requestPort(options?: {
        filters?: Array<{ usbVendorId?: number }>;
      }): Promise<SerialPort>;
    };
  }
}

interface BasestationControllerEvents {
  on(event: "connected", listener: () => void): this;
  on(event: "disconnected", listener: () => void): this;
  on(event: "response", listener: (response: string) => void): this;
  on(event: "error", listener: (err?: Error) => void): this;
  on(event: "serialData", listener: (data: string) => void): this;

  off(event: "connected", listener: () => void): this;
  off(event: "disconnected", listener: () => void): this;
  off(event: "response", listener: (response: string) => void): this;
  off(event: "error", listener: (err?: Error) => void): this;
  off(event: "serialData", listener: (data: string) => void): this;

  addListener(event: "connected", listener: () => void): this;
  addListener(event: "disconnected", listener: () => void): this;
  addListener(event: "response", listener: (response: string) => void): this;
  addListener(event: "error", listener: (err?: Error) => void): this;
  addListener(event: "serialData", listener: (data: string) => void): this;

  removeListener(event: "connected", listener: () => void): this;
  removeListener(event: "disconnected", listener: () => void): this;
  removeListener(event: "response", listener: (response: string) => void): this;
  removeListener(event: "error", listener: (err?: Error) => void): this;
  removeListener(event: "serialData", listener: (data: string) => void): this;

  removeAllListeners(
    event?: "connected" | "disconnected" | "response" | "error" | "serialData",
  ): this;
}

export class SomoBasestationController
  extends EventEmitter
  implements BasestationControllerEvents
{
  private serialPort: SerialPortClient;
  private useStreamingTransfer = true; // Default to streaming mode for minimal memory usage

  constructor(port: SerialPort) {
    super();
    this.serialPort = new SerialPortClient(port);
    this.setupEventListeners();
  }

  /**
   * Set whether to use streaming transfer (true streaming with ring buffer)
   */
  setUseStreamingTransfer(useStreaming: boolean) {
    this.useStreamingTransfer = useStreaming;
    console.log(
      `[basestation] Streaming transfer mode: ${useStreaming ? "enabled" : "disabled"}`,
    );
  }

  private setupEventListeners() {
    this.serialPort.on("connected", () => {
      console.log("[basestation] Connected to Somo Basestation");
      console.log(
        `[basestation] Transfer mode: ${this.useStreamingTransfer ? "streaming (256-byte ring buffer)" : "single command (legacy)"}`,
      );
      this.emit("connected");
    });

    this.serialPort.on("disconnected", () => {
      console.log("[basestation] Disconnected from Somo Basestation");
      this.emit("disconnected");
    });

    this.serialPort.on("data", (data: string) => {
      this.handleData(data);
    });

    this.serialPort.on("error", (error: Error) => {
      console.error("[basestation|error]", error);
      this.emit("error", error);
    });
  }

  private handleData(line: string) {
    // Log all serial output to developer console
    console.log("[basestation|serial]", line);

    // Emit all serial data for monitoring
    this.emit("serialData", line);

    // Parse specific responses
    if (line.includes("[SERIAL]")) {
      this.emit("response", line);
    }
  }

  /**
   * Check if Web Serial API is supported
   */
  static isSupported(): boolean {
    return "serial" in navigator;
  }

  /**
   * Get list of available serial ports that match Pico devices
   */
  static async getAvailablePorts(): Promise<SerialPort[]> {
    if (!SomoBasestationController.isSupported()) {
      throw new Error("Web Serial API is not supported in this browser");
    }

    const ports = await navigator.serial.getPorts();
    // Filter for Somo Basestation devices based on vendor/product IDs
    return ports.filter((port: SerialPort) => {
      const info = port.getInfo();
      // Raspberry Pi Foundation vendor ID is 0x2E8A (used by Somo Basestation)
      return info.usbVendorId === 0x2e8a || !info.usbVendorId;
    });
  }

  /**
   * Request a new serial port from the user
   */
  static async requestPort(): Promise<SerialPort | null> {
    if (!SomoBasestationController.isSupported()) {
      throw new Error("Web Serial API is not supported in this browser");
    }

    try {
      // Request port with filters for Raspberry Pi Pico
      const port = await navigator.serial.requestPort({
        filters: [
          { usbVendorId: 0x2e8a }, // Raspberry Pi vendor ID
        ],
      });
      return port;
    } catch (error) {
      if (error instanceof DOMException && error.name === "NotFoundError") {
        // User cancelled the prompt
        return null;
      }
      throw error;
    }
  }

  /**
   * Send a raw command to the basestation
   */
  async sendCommand(command: string): Promise<void> {
    if (!command.endsWith("\n")) {
      command += "\n";
    }
    await this.serialPort.write(command);
  }

  private buildNodeQrMappings(configuration: {
    roomSwitchIdToQRCodeMapping?: { id: string; qrCode: string }[];
    roomDimmerIdToQRCodeMapping?: { id: string; qrCode: string }[];
    outletDimmerIdToQRCodeMapping?: { id: string; qrCode: string }[];
    canBusControllerIdToQRCodeMapping?: { id: string; qrCode: string }[];
    doorSensorIdToQRCodeMapping?: { id: string; qrCode: string }[];
    presenceSensorIdToQRCodeMapping?: { id: string; qrCode: string }[];
  }) {
    return [
      ...(configuration.roomSwitchIdToQRCodeMapping ?? []),
      ...(configuration.roomDimmerIdToQRCodeMapping ?? []),
      ...(configuration.outletDimmerIdToQRCodeMapping ?? []),
      ...(configuration.canBusControllerIdToQRCodeMapping ?? []),
      ...(configuration.doorSensorIdToQRCodeMapping ?? []),
      ...(configuration.presenceSensorIdToQRCodeMapping ?? []),
    ].map((m) => ({ deviceId: m.id, qrCode: m.qrCode }));
  }

  /**
   * Send configuration update to the basestation using streaming protocol (true streaming with ring buffer)
   */
  async sendConfigurationUpdateStreaming(params: {
    roomId: string;
    version: string;
    nodes: any[];
    edges: any[];
    configuration: {
      rf?: { channel: number; network: number };
      wifi?: { ssid: string; password: string };
      serverAddress?: string;
      mac?: { useMacAddress: boolean; macAddress: string };
      dhcp?: {
        staticIp: boolean;
        ipAddress: string;
        subnetMask: string;
        gateway: string;
        dnsServer: string;
      };
      roomSwitchIdToQRCodeMapping?: { id: string; qrCode: string }[];
      roomDimmerIdToQRCodeMapping?: { id: string; qrCode: string }[];
      outletDimmerIdToQRCodeMapping?: { id: string; qrCode: string }[];
      canBusControllerIdToQRCodeMapping?: { id: string; qrCode: string }[];
      doorSensorIdToQRCodeMapping?: { id: string; qrCode: string }[];
      presenceSensorIdToQRCodeMapping?: { id: string; qrCode: string }[];
      somfyShadesDeviceIdMapping?: {
        baseStationId: string;
        shadeKey: string;
        deviceId: number;
      }[];
    };
    qrCode: string;
  }): Promise<void> {
    console.log("[basestation] Sending configuration update (streaming)");

    // Convert configuration QR mappings to nodeQrMappings format
    const nodeQrMappings = this.buildNodeQrMappings(params.configuration);

    // Get the active configuration
    const { activeConfiguration } = getActiveConfigurationForBasestation({
      name: params.qrCode,
      roomId: params.roomId,
      version: params.version,
      nodes: params.nodes,
      nodeQrMappings,
      rfConfig: params.configuration.rf,
      wifiConfig: params.configuration.wifi,
      serverAddress: params.configuration.serverAddress,
      macConfig: params.configuration.mac,
      dhcpConfig: params.configuration.dhcp,
      somfyShadesDeviceIdMapping:
        params.configuration.somfyShadesDeviceIdMapping,
    });

    // Encode the configuration
    const data = BasestationUpdateMessage.encode({
      qrCode: params.qrCode,
      config: activeConfiguration.config,
    }).finish();

    // Calculate CRC16
    const crc16 = crc16Calculate(data);

    // Calculate chunk parameters
    const CHUNK_SIZE = 16; // 16 bytes per chunk for streaming
    const totalSize = data.length;
    const chunkCount = Math.ceil(totalSize / CHUNK_SIZE);

    console.log(
      `[basestation] Streaming configuration size: ${totalSize} bytes, chunks: ${chunkCount}, CRC16: 0x${crc16.toString(16).padStart(4, "0")}`,
    );

    // Start streaming transfer
    await this.sendCommand(
      `STREAM_CONFIG_START|${totalSize}|${chunkCount}|${crc16.toString(16)}`,
    );

    // Wait for confirmation
    await new Promise((resolve) => setTimeout(resolve, 100));

    // Send chunks
    for (let i = 0; i < chunkCount; i++) {
      const start = i * CHUNK_SIZE;
      const end = Math.min(start + CHUNK_SIZE, totalSize);
      const chunk = data.slice(start, end);

      // Convert chunk to base64
      const base64Chunk = btoa(String.fromCharCode(...chunk));

      console.log(
        `[basestation] Streaming chunk ${i + 1}/${chunkCount} (${chunk.length} bytes)`,
      );

      await this.sendCommand(`STREAM_CONFIG_CHUNK|${i}|${base64Chunk}`);

      // Small delay between chunks to prevent buffer overflow
      await new Promise((resolve) => setTimeout(resolve, 50));
    }

    // Wait for completion
    return new Promise((resolve) => {
      const timeout = setTimeout(() => {
        console.log(
          "[basestation] Streaming configuration update completed (timeout)",
        );
        resolve();
      }, 5000); // Longer timeout for streaming

      const responseHandler = (response: string) => {
        if (
          response.includes("Streaming decode completed successfully") ||
          response.includes("Configuration applied successfully") ||
          response.includes("Failed to process stream chunk") ||
          response.includes("ERROR")
        ) {
          clearTimeout(timeout);
          this.removeListener("response", responseHandler);
          console.log(
            "[basestation] Streaming configuration update response:",
            response,
          );
          resolve();
        }
      };

      this.on("response", responseHandler);
    });
  }

  /**
   * Send configuration update to the basestation
   * Automatically selects between streaming and single command based on settings
   */
  async sendConfigurationUpdate(params: {
    roomId: string;
    version: string;
    nodes: any[];
    edges: any[];
    configuration: {
      rf?: { channel: number; network: number };
      wifi?: { ssid: string; password: string };
      serverAddress?: string;
      mac?: { useMacAddress: boolean; macAddress: string };
      dhcp?: {
        staticIp: boolean;
        ipAddress: string;
        subnetMask: string;
        gateway: string;
        dnsServer: string;
      };
      roomSwitchIdToQRCodeMapping?: { id: string; qrCode: string }[];
      roomDimmerIdToQRCodeMapping?: { id: string; qrCode: string }[];
      outletDimmerIdToQRCodeMapping?: { id: string; qrCode: string }[];
      canBusControllerIdToQRCodeMapping?: { id: string; qrCode: string }[];
      doorSensorIdToQRCodeMapping?: { id: string; qrCode: string }[];
      presenceSensorIdToQRCodeMapping?: { id: string; qrCode: string }[];
      somfyShadesDeviceIdMapping?: {
        baseStationId: string;
        shadeKey: string;
        deviceId: number;
      }[];
    };
    qrCode: string;
  }): Promise<void> {
    // Check if we should use streaming transfer
    if (this.useStreamingTransfer) {
      return this.sendConfigurationUpdateStreaming(params);
    }

    // Otherwise use legacy single command
    return this.sendConfigurationUpdateLegacy(params);
  }

  /**
   * Send configuration update to the basestation (legacy single command)
   */
  private async sendConfigurationUpdateLegacy(params: {
    roomId: string;
    version: string;
    nodes: any[];
    edges: any[];
    configuration: {
      rf?: { channel: number; network: number };
      wifi?: { ssid: string; password: string };
      serverAddress?: string;
      mac?: { useMacAddress: boolean; macAddress: string };
      dhcp?: {
        staticIp: boolean;
        ipAddress: string;
        subnetMask: string;
        gateway: string;
        dnsServer: string;
      };
      roomSwitchIdToQRCodeMapping?: { id: string; qrCode: string }[];
      roomDimmerIdToQRCodeMapping?: { id: string; qrCode: string }[];
      outletDimmerIdToQRCodeMapping?: { id: string; qrCode: string }[];
      canBusControllerIdToQRCodeMapping?: { id: string; qrCode: string }[];
      doorSensorIdToQRCodeMapping?: { id: string; qrCode: string }[];
      presenceSensorIdToQRCodeMapping?: { id: string; qrCode: string }[];
      somfyShadesDeviceIdMapping?: {
        baseStationId: string;
        shadeKey: string;
        deviceId: number;
      }[];
    };
    qrCode: string;
  }): Promise<void> {
    console.log(
      "[basestation] Sending configuration update (legacy single command)",
    );

    // Convert configuration QR mappings to nodeQrMappings format
    const nodeQrMappings = this.buildNodeQrMappings(params.configuration);

    // Get the active configuration
    const { activeConfiguration } = getActiveConfigurationForBasestation({
      name: params.qrCode,
      roomId: params.roomId,
      version: params.version,
      nodes: params.nodes,
      nodeQrMappings,
      rfConfig: params.configuration.rf,
      wifiConfig: params.configuration.wifi,
      serverAddress: params.configuration.serverAddress,
      macConfig: params.configuration.mac,
      dhcpConfig: params.configuration.dhcp,
      somfyShadesDeviceIdMapping:
        params.configuration.somfyShadesDeviceIdMapping,
    });

    // Encode the configuration
    const data = BasestationUpdateMessage.encode({
      qrCode: params.qrCode,
      config: activeConfiguration.config,
    }).finish();

    const dataWithAction = new Uint8Array(data.length);
    dataWithAction.set(data, 0);

    // Use base64 encoding to avoid newline issues in binary data
    const base64Data = btoa(String.fromCharCode(...dataWithAction));
    console.log("[basestation] Base64 data:", base64Data);
    const command = `FLASH_CONFIG|${base64Data}`;

    console.log(
      `[basestation] Sending command: FLASH_CONFIG|<${dataWithAction.length} bytes as base64>`,
    );

    // Send as text command - base64 is safe for line-based processing
    await this.sendCommand(command);

    // Wait a bit for the response
    return new Promise((resolve) => {
      const timeout = setTimeout(() => {
        console.log("[basestation] Configuration update sent (timeout)");
        resolve();
      }, 2000);

      const responseHandler = (response: string) => {
        if (
          response.includes("Configuration sent to event queue") ||
          response.includes("Failed to send configuration")
        ) {
          clearTimeout(timeout);
          this.removeListener("response", responseHandler);
          console.log("[basestation] Configuration update response:", response);
          resolve();
        }
      };

      this.on("response", responseHandler);
    });
  }

  /**
   * Send reboot command to the basestation
   */
  async sendReboot(): Promise<void> {
    console.log("[basestation] Sending reboot command");
    await this.sendCommand("reboot");
  }

  /**
   * Send status command to the basestation
   */
  async sendStatus(): Promise<void> {
    console.log("[basestation] Sending status command");
    await this.sendCommand("status");
  }

  /**
   * Send help command to the basestation
   */
  async sendHelp(): Promise<void> {
    console.log("[basestation] Sending help command");
    await this.sendCommand("help");
  }

  /**
   * Perform handshake with basestation and get QR code and firmware version
   */
  async performHandshake(): Promise<{ qr: string; version: string } | null> {
    console.log("[basestation] Performing handshake");

    // Try handshake with retry logic
    for (let attempt = 1; attempt <= 2; attempt++) {
      const result = await this.attemptHandshake();
      if (result) {
        return result;
      }
      if (attempt === 1) {
        console.log("[basestation] Handshake failed, retrying...");
        await new Promise((resolve) => setTimeout(resolve, 500));
      }
    }

    return null;
  }

  private async attemptHandshake(): Promise<{
    qr: string;
    version: string;
  } | null> {
    return new Promise((resolve) => {
      const timeout = setTimeout(() => {
        console.log("[basestation] Handshake timeout");
        resolve(null);
      }, 3000); // 3 second timeout per attempt

      const responseHandler = (data: string) => {
        // Look for text response from GET_QR command: <qrcode>|<version>
        // Ignore log messages that start with "["
        if (!data.startsWith("[") && data.includes("|")) {
          const parts = data.split("|");
          if (parts.length === 2) {
            const qr = parts[0]; // QR code (may be empty string)
            const version = parts[1].trim(); // Version (trim whitespace)

            clearTimeout(timeout);
            this.removeListener("serialData", responseHandler);
            console.log("[basestation] Handshake successful:", { qr, version });
            resolve({ qr, version });
          }
        }
      };

      this.on("serialData", responseHandler);

      // Only send command if port is still writable
      if (this.serialPort.isOpen) {
        this.sendCommand("GET_QR").catch(() => {
          // Ignore errors if port was closed
          clearTimeout(timeout);
          resolve(null);
        });
      } else {
        clearTimeout(timeout);
        resolve(null);
      }
    });
  }

  /**
   * Set QR code on the basestation
   */
  async setQRCode(qrCode: string): Promise<void> {
    console.log("[basestation] Setting QR code:", qrCode);
    await this.sendCommand(`SET_QR|${qrCode}`);
  }

  /**
   * Set WiFi credentials on the basestation and update ESP32
   */
  async setWifiCredentials(ssid: string, password: string): Promise<void> {
    console.log("[basestation] Setting WiFi credentials:", ssid);
    await this.sendCommand(`WIFI|${ssid}|${password}`);
  }

  /**
   * Set WebSocket server address on the basestation and update ESP32
   */
  async setServerAddress(serverAddress: string): Promise<void> {
    const address = serverAddress.trim();
    // Basic scheme check
    if (!/^wss?:\/\//i.test(address)) {
      throw new Error("Server address must start with ws:// or wss://");
    }
    // Disallow protocol delimiter characters used by the firmware parser
    if (/[|\r\n]/.test(address)) {
      throw new Error(
        'Server address cannot contain "|" or newline characters',
      );
    }
    // Optional: enforce maximum length (keep in sync with nanopb header size)
    // TODO: replace 192 with the actual max from basestation-config.pb.h for server_address
    if (address.length > 192) {
      throw new Error("Server address is too long");
    }
    console.log("[basestation] Setting server address:", address);
    await this.sendCommand(`SERVER|${address}`);
  }

  /**
   * Send configuration update to ESP32
   */
  async updateESP32Config(): Promise<void> {
    console.log("[basestation] Sending configuration update to ESP32");
    await this.sendCommand("update_esp32");
  }

  /**
   * Close the connection
   */
  async close(): Promise<void> {
    await this.serialPort.disconnect();
  }

  /**
   * Check if connected
   */
  isConnected(): boolean {
    return this.serialPort.isOpen;
  }

  /**
   * Send raw binary data (for bootloader communication)
   */
  async sendBinary(data: Uint8Array): Promise<void> {
    await this.serialPort.writeBinary(data);
  }

  /**
   * Get direct access to serial port for bootloader
   */
  getSerialPort(): SerialPort {
    return this.serialPort.getPort();
  }

  /**
   * Release serial streams without closing port (for bootloader mode)
   */
  async releaseStreams(): Promise<void> {
    await this.serialPort.releaseStreams();
  }
}
