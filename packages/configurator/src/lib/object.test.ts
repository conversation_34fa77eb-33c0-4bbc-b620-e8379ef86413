import { describe, expect, it } from "vitest";
import { omit, pick } from "./object";

describe("omit", () => {
  it("should return a new object with the specified keys omitted", () => {
    const originalObject = {
      a: 1,
      b: 2,
      c: 3,
    };

    const newObject = omit(originalObject, ["b", "c"]);

    expect(newObject).toEqual({ a: 1 });
  });
});

describe("pick", () => {
  it("should return a new object with only the specified keys", () => {
    const originalObject = {
      a: 1,
      b: 2,
      c: 3,
    };

    const newObject = pick(originalObject, ["a", "b"]);

    expect(newObject).toEqual({ a: 1, b: 2 });
  });
});
