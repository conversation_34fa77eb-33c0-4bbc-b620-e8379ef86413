import { toYjsType } from "@/lib/yjsUtils";
import { useEffect, useState } from "react";
import * as Y from "yjs";
// TODO: these belong to the sld-graph folder, not the layout-graph/ one
import { getSldEdgesMap, getSldNodesMap } from "../graph/Graph";
// TODO: ideally, we should have an independent ReactFlowContext for the Sld graph
import { useReactFlowContext } from "../graph/ReactFlowContext";

// TODO: pass Zod schemas to make functions type-safe instead of using `any`

export function useGetSldEdgeData(edgeId: string) {
  const [data, setData] = useState<any | null>(null);
  const { yDoc } = useReactFlowContext();

  useEffect(() => {
    const sldEdgesMap = getSldEdgesMap(yDoc);
    if (!sldEdgesMap) {
      setData(null);
      return;
    }

    const edgeMap = sldEdgesMap.get(edgeId);
    if (!edgeMap) {
      setData(null);
      return;
    }

    // Initialize value immediately, then watch for changes
    setData(edgeMap.toJSON());

    const observer = () => {
      setData(edgeMap.toJSON());
    };

    edgeMap.observeDeep(observer);

    return () => {
      edgeMap.unobserveDeep(observer);
    };
  }, [yDoc, edgeId]);

  return data;
}

export function useGetSldEdges() {
  const [data, setData] = useState<any[]>([]);
  const { yDoc } = useReactFlowContext();

  useEffect(() => {
    const sldEdgesMap = getSldEdgesMap(yDoc);
    if (!sldEdgesMap) {
      setData([]);
      return;
    }

    // Initialize value immediately, then watch for changes
    setData(Object.values(sldEdgesMap.toJSON()));

    const observer = () => {
      setData(Object.values(sldEdgesMap.toJSON()));
    };

    sldEdgesMap.observeDeep(observer);

    return () => {
      sldEdgesMap.unobserveDeep(observer);
    };
  }, [yDoc]);

  return data;
}

export function useGetSldNodeData<T = any>(nodeId: string): T | null {
  const [data, setData] = useState<T | null>(null);
  const { yDoc } = useReactFlowContext();

  useEffect(() => {
    const nodeDataMap = getSldNodeDataMap<T>(yDoc, nodeId);
    if (!nodeDataMap) {
      return;
    }

    // Initialize value immediately, then watch for changes
    setData(nodeDataMap.toJSON() as T);

    const observer = () => {
      setData(nodeDataMap.toJSON() as T);
    };
    nodeDataMap.observeDeep(observer);

    return () => {
      nodeDataMap.unobserveDeep(observer);
    };
  }, [yDoc, nodeId]);

  return data;
}

export function useGetSldYXmlFragment(nodeId: string) {
  const [data, setData] = useState<Y.XmlFragment>(new Y.XmlFragment());
  const { yDoc } = useReactFlowContext();

  useEffect(() => {
    const nodeDataMap = getSldNodeDataMap(yDoc, nodeId);
    if (!nodeDataMap) {
      return;
    }

    // Initialize value immediately, then watch for changes
    setData(nodeDataMap.get("text"));

    const observer = () => {
      setData(nodeDataMap.get("text"));
    };
    nodeDataMap.observeDeep(observer);

    return () => {
      nodeDataMap.unobserveDeep(observer);
    };
  }, [yDoc, nodeId]);

  return data;
}

function getSldNodeDataMap<T = any>(
  yDoc: Y.Doc,
  nodeId: string,
): Y.Map<T> | null {
  const sldNodesMap = getSldNodesMap(yDoc);
  if (!sldNodesMap) {
    console.log("SLD sync: SLD nodes map not found, nodeId:", nodeId);
    return null;
  }

  const nodeMap = sldNodesMap.get(nodeId);
  if (!nodeMap) {
    console.log("SLD sync: Node not found in SLD nodes map, nodeId:", nodeId);
    return null;
  }

  const nodeDataMap = nodeMap.get("data");
  if (!nodeDataMap) {
    console.log("SLD sync: Node data map not found, nodeId:", nodeId);
    return null;
  }

  return nodeDataMap;
}

export function useGetSldNodes() {
  const [data, setData] = useState<any[]>([]);
  const { yDoc } = useReactFlowContext();

  useEffect(() => {
    const sldNodesMap = getSldNodesMap(yDoc);
    if (!sldNodesMap) {
      setData([]);
      return;
    }

    // Initialize value immediately, then watch for changes
    setData(Object.values(sldNodesMap.toJSON()));

    const observer = () => {
      setData(Object.values(sldNodesMap.toJSON()));
    };

    sldNodesMap.observeDeep(observer);

    return () => {
      sldNodesMap.unobserveDeep(observer);
    };
  }, [yDoc]);

  return data;
}

export function useUpdateSldNodeData(nodeId: string) {
  const { yDoc } = useReactFlowContext();

  return (newData: any) => {
    if (!yDoc) {
      return;
    }

    const sldNodesMap = getSldNodesMap(yDoc);
    if (!sldNodesMap) {
      return;
    }

    const nodeMap = sldNodesMap.get(nodeId);
    if (!nodeMap) {
      return;
    }

    const nodeDataMap = nodeMap.get("data");
    if (!nodeDataMap) {
      return;
    }

    Object.entries(newData).forEach(([key, value]) => {
      const yValue = toYjsType(value);
      nodeDataMap.set(key, yValue);
    });
  };
}

export function useUpdateSldEdgeData(edgeId: string) {
  const { yDoc } = useReactFlowContext();

  return (newData: any) => {
    if (!yDoc) {
      return;
    }

    const sldEdgesMap = getSldEdgesMap(yDoc);
    if (!sldEdgesMap) {
      return;
    }

    const edgeMap = sldEdgesMap.get(edgeId);
    if (!edgeMap) {
      return;
    }

    Object.entries(newData).forEach(([key, value]) => {
      const yValue = toYjsType(value);
      edgeMap.set(key, yValue);
    });
  };
}
