import { ServicePadIcon } from "@/components/devices/service-pad/ServicePadIcon";
import { SldComponent } from "../sld/types";
import { GridConfig } from "@/lib/gridConfig";
import { useGetData } from "@/components/data/useData";
import { useEffect } from "react";
import {
  Handle,
  Position,
  useNodeId,
  useUpdateNodeInternals,
} from "@xyflow/react";

// Utility function to get connector styles
function getRfServicePadConnectorStyle(handleId: string): {
  stroke: string;
  strokeWidth: number;
  strokeOpacity: number;
} {
  switch (handleId) {
    case "makeUpRoomButton":
      return {
        stroke: "#3b82f6", // Blue
        strokeWidth: 2.5,
        strokeOpacity: 0.7,
      };
    case "doorbellButton":
      return {
        stroke: "#8b5cf6", // Purple
        strokeWidth: 2.5,
        strokeOpacity: 0.7,
      };
    case "doNotDisturbButton":
      return {
        stroke: "#ef4444", // Red
        strokeWidth: 2.5,
        strokeOpacity: 0.7,
      };
    case "live":
      return {
        stroke: "#dc2626", // Red
        strokeWidth: 2,
        strokeOpacity: 0.7,
      };
    case "ground":
    case "ground-input":
    case "ground-output":
      return {
        stroke: "#10b981", // Green
        strokeWidth: 2,
        strokeOpacity: 0.9,
      };
    case "neutral":
      return {
        stroke: "#6b7280", // Gray
        strokeWidth: 3,
        strokeOpacity: 0.9,
      };
    default:
      return {
        stroke: "#4D9FC9", // Default blue
        strokeWidth: 2,
        strokeOpacity: 0.9,
      };
  }
}

// Connector configuration using GridConfig
const CONNECTORS = [
  {
    id: "makeUpRoomButton",
    top: `${GridConfig.getHandleOffset(1)}px`,
    color: "#3b82f6",
    label: "Service",
    textColor: "text-blue-600",
  },
  {
    id: "doorbellButton",
    top: `${GridConfig.getHandleOffset(2)}px`,
    color: "#8b5cf6",
    label: "Doorbell",
    textColor: "text-purple-600",
  },
  {
    id: "doNotDisturbButton",
    top: `${GridConfig.getHandleOffset(3)}px`,
    color: "#ef4444",
    label: "Privacy",
    textColor: "text-red-600",
  },
  {
    id: "live",
    top: `${GridConfig.getHandleOffset(4)}px`,
    color: "#dc2626",
    label: "Line",
    textColor: "text-red-600",
  },
  {
    id: "ground",
    top: `${GridConfig.getHandleOffset(5)}px`,
    color: "#10b981",
    label: "Ground",
    textColor: "text-green-600",
  },
  {
    id: "neutral",
    top: `${GridConfig.getHandleOffset(6)}px`,
    color: "#6b7280",
    label: "Neutral (Opt)",
    textColor: "text-gray-600",
  },
];

// Reusable connector component
const Connector = ({
  id,
  top,
  color,
  label,
  textColor,
}: {
  id: string;
  top: string;
  color: string;
  label: string;
  textColor: string;
}) => (
  <>
    <Handle
      type="source"
      position={Position.Right}
      id={id}
      isConnectable={true}
      className="w-5 h-5 border-2 border-white rounded-full"
      style={{
        top,
        transform: "translateY(-50%)",
        backgroundColor: color,
      }}
    />
    <div
      className={`absolute right-2 transform -translate-y-1/2 text-xs font-semibold text-right whitespace-normal ${textColor}`}
      style={{
        top,
        lineHeight: "1.2",
        width: "90px",
        wordBreak: "normal",
      }}
    >
      {label}
    </div>
  </>
);

export const RfServicePadSLD: SldComponent = ({ data, selected }) => {
  const gridBasedHeight = 7 * GridConfig.GRID_SIZE;
  const gridBasedWidth = 12 * GridConfig.GRID_SIZE;

  const nodeId = useNodeId();
  const actualData = useGetData(data.dataId, "servicePad");

  const updateNodeInternals = useUpdateNodeInternals();
  useEffect(() => {
    if (!nodeId) {
      return;
    }
    updateNodeInternals(nodeId);
  }, [nodeId, updateNodeInternals, actualData]);

  if (!actualData) {
    console.warn("Service Pad: No data found for dataId:", data.dataId);
    return (
      <div className="w-20 h-20 bg-gray-200 border border-gray-300 rounded flex items-center justify-center text-xs text-gray-500">
        Loading...
      </div>
    );
  }

  return (
    <div
      className={`bg-white border-2 rounded-lg p-2 shadow-md cursor-move relative ${
        selected ? "border-blue-500 bg-blue-50" : "border-gray-300"
      }`}
      style={{
        minWidth: `${gridBasedWidth}px`,
        minHeight: `${gridBasedHeight}px`,
      }}
    >
      {/* Right side connectors */}
      {CONNECTORS.map((connector) => (
        <Connector key={connector.id} {...connector} />
      ))}

      {/* Service Pad Icon - Left aligned */}
      <div className="flex justify-start mb-2">
        <ServicePadIcon className="w-8 h-8 text-gray-700" />
      </div>

      {/* Device Name - Main identifier for SLD view */}
      <div
        className="text-sm font-semibold text-left mb-2 text-gray-800 leading-tight pr-16 whitespace-normal break-words"
        style={{
          width: "140px", // Fixed width - will force breaking when needed
          overflowWrap: "break-word",
          wordWrap: "break-word",
        }}
      >
        {actualData?.title}
      </div>

      {/* Device Type - Secondary info */}
      <div className="text-xs text-gray-500 text-left mb-2">
        {"Room Service"}
        {<br />}
        {"Pad"}
      </div>
    </div>
  );
};

// Export the edge style function
RfServicePadSLD.getEdgeStyle = getRfServicePadConnectorStyle;
RfServicePadSLD.overrideEdgeColor = true;
