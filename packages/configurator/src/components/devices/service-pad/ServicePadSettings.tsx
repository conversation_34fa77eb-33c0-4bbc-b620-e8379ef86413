import { useData } from "@/components/data/useData";
import { useReactFlowContext } from "@/components/graph/ReactFlowContext";
import { LampPopover } from "@/components/graph/settings/LampPopover";
import {
  SettingsGroup,
  SettingsGroupContent,
  SettingsGroupHeader,
  SettingsLabel,
  SettingsRow,
} from "@/components/graph/settings/SettingsGroup";
import { SettingsInput } from "@/components/graph/settings/SettingsInput";
import { DeviceIconKey } from "@/components/icons/DeviceIcons";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import {
  GraphNodesByType,
  ServicePad,
  ServicePadAction,
  ServicePadViaId,
} from "@somo/shared";
import { useState } from "react";
import { match } from "ts-pattern";

export function ServicePadSettings({
  node,
}: {
  node: GraphNodesByType["servicePadContainer"];
}) {
  const { readOnly } = useReactFlowContext();
  const { data, updateNestedData } = useData(node.data.dataId, "servicePad");
  const [modeValue, setModeValue] = useState(data?.mode);

  if (!data) {
    return <div>Loading...</div>;
  }

  return (
    <div>
      <SettingsGroup>
        <SettingsGroupContent>
          <SettingsRow>
            <div className="font-mono text-xs text-gray-500">ID: {node.id}</div>
          </SettingsRow>
          <SettingsRow>
            <SettingsLabel htmlFor="name">Name</SettingsLabel>
            <SettingsInput
              id="name"
              value={data.title}
              onEndEdit={(value) => updateNestedData("title", value)}
              disabled={readOnly}
            />
          </SettingsRow>
        </SettingsGroupContent>
      </SettingsGroup>

      <SettingsGroup>
        <SettingsGroupHeader className="flex flex-row justify-between items-center -mb-2 pt-4">
          <span className="flex flex-grow">Service Pad Settings</span>
        </SettingsGroupHeader>
        <SettingsGroupContent>
          <SettingsRow className="flex flex-row justify-between items-center">
            <SettingsLabel htmlFor="hvacModes" className="flex-shrink-0">
              Service Pad Mode
            </SettingsLabel>
            <ToggleGroup
              value={modeValue}
              onValueChange={(values: ServicePad["mode"]) => {
                if (values) {
                  updateNestedData("mode", values);
                  setModeValue(values);
                }
              }}
              type="single"
              disabled={readOnly}
            >
              <ToggleGroupItem
                value="servicePad"
                aria-label="Select Service Pad"
              >
                Service Pad
              </ToggleGroupItem>
              <ToggleGroupItem value="doorbell" aria-label="Select Doorbell">
                Doorbell
              </ToggleGroupItem>
            </ToggleGroup>
          </SettingsRow>
        </SettingsGroupContent>
      </SettingsGroup>

      <SettingsGroup className="mb-4">
        <SettingsGroupHeader className="flex flex-row justify-between items-center -mb-2 pt-4">
          <span className="flex flex-grow">Service Pad Actions</span>
        </SettingsGroupHeader>
        <SettingsGroupContent>
          {match(data.mode)
            .with("servicePad", () => (
              <>
                <SettingsRow>
                  <AdditionalButtonRow
                    button={data.makeUpRoomButton}
                    viaId="makeUpRoomButton"
                    label="Make Up Room"
                    onIconChange={(iconKey) => {
                      updateNestedData("makeUpRoomButton.icon", iconKey);
                    }}
                    onNameChange={(value) => {
                      updateNestedData("makeUpRoomButton.name", value);
                    }}
                    onShowLabelChange={(checked) => {
                      updateNestedData("makeUpRoomButton.showLabel", checked);
                    }}
                  />
                </SettingsRow>

                <SettingsRow className="mt-4">
                  <AdditionalButtonRow
                    button={data.doNotDisturbButton}
                    viaId="doNotDisturbButton"
                    label="Do Not Disturb"
                    onIconChange={(iconKey) => {
                      updateNestedData("doNotDisturbButton.icon", iconKey);
                    }}
                    onNameChange={(value) => {
                      updateNestedData("doNotDisturbButton.name", value);
                    }}
                    onShowLabelChange={(checked) => {
                      updateNestedData("doNotDisturbButton.showLabel", checked);
                    }}
                  />
                </SettingsRow>
              </>
            ))
            .with("doorbell", () => (
              <SettingsRow className="mt-4">
                <AdditionalButtonRow
                  button={data.doorbellButton}
                  viaId="doorbellButton"
                  label="Doorbell Button"
                  onIconChange={(iconKey) => {
                    updateNestedData("doorbellButton.icon", iconKey);
                  }}
                  onNameChange={(value) => {
                    updateNestedData("doorbellButton.name", value);
                  }}
                  onShowLabelChange={(checked) => {
                    updateNestedData("doorbellButton.showLabel", checked);
                  }}
                />
              </SettingsRow>
            ))
            .exhaustive()}
        </SettingsGroupContent>
      </SettingsGroup>
    </div>
  );
}

// Updated interface for the additional buttons
interface AdditionalButtonRowProps {
  button: ServicePadAction;
  viaId: ServicePadViaId;
  label: string;
  onIconChange: (iconKey: DeviceIconKey | undefined) => void;
  onNameChange: (name: string) => void;
  onShowLabelChange: (checked: boolean) => void;
}

function AdditionalButtonRow({
  button,
  viaId,
  label,
  onIconChange,
  onNameChange,
  onShowLabelChange,
}: AdditionalButtonRowProps) {
  const { readOnly } = useReactFlowContext();

  return (
    <div className="flex flex-col gap-2">
      <div className="flex flex-row gap-2 text-xs font-bold text-gray-800 mt-2 items-center justify-between">
        <Label className="text-gray-700">{label}</Label>
      </div>

      {button.enabled && (
        <>
          <div className="flex flex-row gap-2 text-xs font-bold text-gray-800 mt-2 items-center">
            <LampPopover
              activeIconKey={button.icon}
              onIconClick={onIconChange}
              disabled={readOnly}
            />

            <SettingsInput
              className="text-sm font-normal h-10"
              value={button.name}
              onEndEdit={onNameChange}
              disabled={readOnly}
            />
            <div className="flex flex-row items-center gap-1 ml-3 w-[90px] flex-shrink-0">
              <Checkbox
                id={`${viaId}-showLabel`}
                checked={button.showLabel}
                onCheckedChange={onShowLabelChange}
                disabled={readOnly}
              />
              <Label
                htmlFor={`${viaId}-showLabel`}
                className="text-gray-500 text-xs font-semibold truncate"
              >
                Show label
              </Label>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
