import { useGetData } from "@/components/data/useData";
import { DeviceIcons } from "@/components/icons/DeviceIcons";
import { SomoButton } from "@/components/icons/SomoButton";
import { useExecutionContext } from "@/contexts/ExecutionContext";
import { cn } from "@/lib/classNames";
import { LayoutContainerNode, ServicePad, ServicePadViaId } from "@somo/shared";
import {
  Handle,
  NodeProps,
  Position,
  useReactFlow,
  useUpdateNodeInternals,
} from "@xyflow/react";
import equal from "fast-deep-equal/react";
import React, { useEffect, useRef } from "react";

export const ServicePadContainerNode = React.memo(RegularNode, equal);
export const ServicePadContainerNodeSimulator = React.memo(
  SimulatorNode,
  equal,
);

function RegularNode(props: NodeProps<LayoutContainerNode>) {
  const data = useGetData(props.data.dataId, "servicePad");
  if (!data) {
    return <></>;
  }
  const propsWithData = { ...props, data: { ...props.data, ...data } };
  return (
    <BaseNode {...propsWithData}>
      <ViaButton {...propsWithData} viaId="makeUpRoomButton" />
      <ViaButton {...propsWithData} viaId="doorbellButton" />
      <ViaButton {...propsWithData} viaId="doNotDisturbButton" />
    </BaseNode>
  );
}

function SimulatorNode(props: NodeProps<LayoutContainerNode>) {
  const data = useGetData(props.data.dataId, "servicePad");
  if (!data) {
    return <></>;
  }
  const propsWithData = { ...props, data: { ...props.data, ...data } };
  return (
    <BaseNode {...propsWithData}>
      <SimulatorViaButton {...propsWithData} viaId="makeUpRoomButton" />
      <SimulatorViaButton {...propsWithData} viaId="doorbellButton" />
      <SimulatorViaButton {...propsWithData} viaId="doNotDisturbButton" />
    </BaseNode>
  );
}

type PropsWithData = NodeProps<LayoutContainerNode> & {
  data: ServicePad;
};

function ViaButton(props: PropsWithData & { viaId: ServicePadViaId }) {
  const via = props.data[props.viaId];
  if (!via.enabled) {
    return <></>;
  }

  if (
    props.data.mode === "doorbell" &&
    (props.viaId === "makeUpRoomButton" || props.viaId === "doNotDisturbButton")
  ) {
    return <></>;
  }

  if (props.data.mode === "servicePad" && props.viaId === "doorbellButton") {
    return <></>;
  }

  const IconComponent = via.icon ? DeviceIcons[via.icon] : null;

  return (
    <div className="flex flex-col items-center w-full">
      <div className="relative size-5 flex items-center justify-center">
        <SomoButton
          className={cn(
            "text-white size-5 flex-shrink-0",
            IconComponent && "opacity-0",
          )}
        />
        <div className="absolute">
          {IconComponent && <IconComponent className="text-white size-5" />}
        </div>
      </div>

      {via.showLabel && (
        <div className="text-[8px] text-white font-light truncate w-full text-center px-0.5">
          {via.name}
        </div>
      )}
    </div>
  );
}

/**
 * The Simulator button is similar to the default one, but it is clickable
 * and will emit the relevant events to test the button behavior.
 */
function SimulatorViaButton(props: PropsWithData & { viaId: ServicePadViaId }) {
  const { servicePadStatus, sendServicePadCommand } = useExecutionContext();
  const isOn =
    (props.viaId === "makeUpRoomButton" &&
      servicePadStatus.value === "makeUpRoom") ||
    (props.viaId === "doNotDisturbButton" &&
      servicePadStatus.value === "doNotDisturb");

  const via = props.data[props.viaId];
  if (!via.enabled) {
    return <></>;
  }

  if (
    props.data.mode === "doorbell" &&
    (props.viaId === "makeUpRoomButton" || props.viaId === "doNotDisturbButton")
  ) {
    return <></>;
  }

  if (props.data.mode === "servicePad" && props.viaId === "doorbellButton") {
    return <></>;
  }

  const IconComponent = via.icon ? DeviceIcons[via.icon] : null;

  return (
    <div
      className="flex flex-col items-center w-full group relative"
      onClick={() => sendServicePadCommand(props.id, props.viaId, via)}
    >
      <div className="relative size-5 flex items-center justify-center">
        <SomoButton
          className={cn(
            "size-5 text-white flex-shrink-0 group-hover:text-blue-300",
            isOn ? "text-yellow-400" : "text-white",
            IconComponent && "opacity-0",
          )}
        />
        <div className="absolute">
          {IconComponent && (
            <IconComponent
              className={cn(
                "size-5 group-hover:text-blue-300",
                isOn ? "text-yellow-400" : "text-white",
              )}
            />
          )}
        </div>
      </div>

      {via.showLabel && (
        <div
          className={cn(
            "text-[8px] font-light truncate w-full text-center px-0.5 group-hover:text-blue-300",
            isOn ? "text-yellow-400" : "text-white",
          )}
        >
          {via.name}
        </div>
      )}
    </div>
  );
}

function BaseNode({
  id,
  selected,
  data,
  height,
  children,
}: PropsWithData & {
  children: React.ReactNode;
}) {
  const updateNodeInternals = useUpdateNodeInternals();
  const reactFlowInstance = useReactFlow();
  const ref = useRef<HTMLDivElement>(null);
  const zoom = reactFlowInstance.getViewport().zoom;

  useEffect(() => {
    updateNodeInternals(id);
    const size = ref.current?.getBoundingClientRect();
    if (size && size.height > 0 && height !== Math.floor(size.height / zoom)) {
      reactFlowInstance.updateNode(id, {
        height: Math.floor(size.height / zoom),
      });
    }
  }, [
    updateNodeInternals,
    id,
    height,
    data.makeUpRoomButton?.enabled,
    data.makeUpRoomButton?.showLabel,
    data.doorbellButton?.enabled,
    data.doorbellButton?.showLabel,
    data.doNotDisturbButton?.enabled,
    data.doNotDisturbButton?.showLabel,
    selected,
    zoom,
  ]);

  return (
    <div
      ref={ref}
      className={cn(
        "flex items-center justify-center bg-gray-600 rounded-md shadow px-1 py-2",
        "min-w-[60px] min-h-[30px]",
        selected && "outline outline-2 outline-blue-500 outline-offset-1",
      )}
    >
      <Handle
        type="target"
        position={Position.Bottom}
        isConnectable={false}
        style={{
          position: "absolute",
          background: "transparent",
          border: 0,
          color: "transparent",
          width: "2px",
          height: "2px",
          bottom: "2.5px",
        }}
      />
      <div className="flex flex-col items-center gap-2 w-full px-0.5">
        {children}
      </div>
    </div>
  );
}
