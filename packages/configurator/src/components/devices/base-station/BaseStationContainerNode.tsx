import { useGetData } from "@/components/data/useData";
import { cn } from "@/lib/classNames";
import { BaseStation, LayoutContainerNode } from "@somo/shared";
import {
  Handle,
  NodeProps,
  Position,
  useReactFlow,
  useUpdateNodeInternals,
} from "@xyflow/react";
import equal from "fast-deep-equal/react";
import React, { useEffect, useRef } from "react";

export const BaseStationContainerNode = React.memo(RegularNode, equal);
export const BaseStationContainerNodeSimulator = React.memo(
  SimulatorNode,
  equal,
);

function RegularNode(props: NodeProps<LayoutContainerNode>) {
  const data = useGetData(props.data.dataId, "baseStation");
  if (!data) {
    return <></>;
  }
  const propsWithData = { ...props, data: { ...props.data, ...data } };

  return (
    <BaseNode {...propsWithData}>
      <div className="flex flex-col items-center justify-center gap-1 px-1">
        <div className="text-[10px] text-white font-light text-center max-w-[100px]">
          {/* Only basestation name for now, will disappear when we add devices */}
          {data.title}
        </div>
      </div>
    </BaseNode>
  );
}

function SimulatorNode(props: NodeProps<LayoutContainerNode>) {
  const data = useGetData(props.data.dataId, "baseStation");
  if (!data) {
    return <></>;
  }
  const propsWithData = { ...props, data: { ...props.data, ...data } };

  return (
    <BaseNode {...propsWithData}>
      <div className="flex flex-col items-center justify-center gap-1 px-1">
        <div className="text-[10px] text-white font-light text-center max-w-[100px]">
          {/* Only basestation name for now, will disappear when we add devices */}
          {data.title}
        </div>
      </div>
    </BaseNode>
  );
}

type PropsWithData = NodeProps<LayoutContainerNode> & {
  data: BaseStation;
};

function BaseNode({
  id,
  selected,
  height,
  width,
  children,
}: PropsWithData & {
  children: React.ReactNode;
}) {
  const updateNodeInternals = useUpdateNodeInternals();
  const reactFlowInstance = useReactFlow();
  const ref = useRef<HTMLDivElement>(null);
  const zoom = reactFlowInstance.getViewport().zoom;

  useEffect(() => {
    updateNodeInternals(id);

    const size = ref.current?.getBoundingClientRect();
    if (
      size &&
      ((size.height > 0 && height !== Math.floor(size.height / zoom)) ||
        (size.width > 0 && width !== Math.floor(size.width / zoom)))
    ) {
      reactFlowInstance.updateNode(id, {
        height: Math.floor(size.height / zoom),
        width: Math.floor(size.width / zoom),
      });
    }
  }, [updateNodeInternals, id, height, width, selected, zoom]);

  return (
    <div
      ref={ref}
      className={cn(
        "inline-flex items-center justify-center bg-gradient-to-b from-gray-600 to-gray-700 rounded-lg shadow-lg px-2 py-2 min-w-[80px] min-h-[40px] border border-gray-500",
        selected &&
          "outline outline-2 outline-blue-400 outline-offset-2 shadow-blue-200/50",
      )}
    >
      <Handle
        type="target"
        position={Position.Bottom}
        isConnectable={false}
        style={{
          background: "transparent",
          border: 0,
          color: "transparent",
          width: "2px",
          height: "2px",
          bottom: "2.5px",
        }}
      />
      <div className="flex items-center justify-center">{children}</div>
    </div>
  );
}
