import { useData } from "@/components/data/useData";
import { useReactFlowContext } from "@/components/graph/ReactFlowContext";
import {
  SettingsGroup,
  SettingsGroupContent,
  SettingsGroupHeader,
  SettingsLabel,
  SettingsRow,
} from "@/components/graph/settings/SettingsGroup";
import { SettingsInput } from "@/components/graph/settings/SettingsInput";
import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { cn } from "@/lib/classNames";
import { GraphNodesByType, randomId, SomfyShadesDevice } from "@somo/shared";
import { ChevronDown, PlusIcon, Trash2Icon } from "lucide-react";
import { useState } from "react";

export function BaseStationSettings({
  node,
}: {
  node: GraphNodesByType["baseStationContainer"];
}) {
  const { readOnly } = useReactFlowContext();
  const {
    data: baseStationData,
    updateNestedData,
    deleteNestedData,
  } = useData(node.data.dataId, "baseStation");
  const [shadesCollapsedState, setShadesCollapsedState] = useState<
    Record<string, boolean>
  >({});

  if (!baseStationData) {
    return <></>;
  }

  return (
    <div>
      <SettingsGroup>
        <SettingsGroupContent>
          <SettingsRow>
            <div className="font-mono text-xs text-gray-500">ID: {node.id}</div>
          </SettingsRow>
          <SettingsRow>
            <SettingsLabel htmlFor="name">Name</SettingsLabel>
            <SettingsInput
              id="name"
              value={baseStationData.title}
              onEndEdit={(value) => {
                if (typeof value !== "string") {
                  return;
                }
                updateNestedData("title", value);
              }}
              disabled={readOnly}
            />
          </SettingsRow>
        </SettingsGroupContent>
      </SettingsGroup>

      <SettingsGroup>
        <SettingsGroupHeader className="flex items-center justify-between">
          <span>Somfy Shades</span>
          {!readOnly &&
            Object.keys(baseStationData.somfyShades || {}).length < 16 && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="size-5"
                    onClick={() => {
                      const newRandomId = `somfy-shade-${randomId()}`;
                      updateNestedData(`somfyShades.${newRandomId}`, {
                        id: newRandomId,
                        name: "Shades",
                        sortIndex: Object.keys(
                          baseStationData.somfyShades || {},
                        ).length,
                        showLabel: true,
                        icon: "shade" as const,
                      });
                    }}
                  >
                    <PlusIcon className="size-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Add Somfy Shades</TooltipContent>
              </Tooltip>
            )}
        </SettingsGroupHeader>
        <SettingsGroupContent>
          {(
            Object.values(
              baseStationData.somfyShades || {},
            ) as SomfyShadesDevice[]
          ).map((shade) => (
            <SettingsRow key={shade.id} className="flex flex-col gap-2">
              <div
                className={cn(
                  "flex flex-row items-center justify-between cursor-pointer hover:bg-gray-50 -mx-2 px-2 py-1 rounded",
                )}
                onClick={() => {
                  setShadesCollapsedState((prev) => ({
                    ...prev,
                    [shade.id]: !prev[shade.id],
                  }));
                }}
              >
                <div className="flex items-center gap-2">
                  <ChevronDown
                    className={cn(
                      "size-4 transition-transform duration-300 text-gray-500",
                      shadesCollapsedState[shade.id] && "-rotate-90",
                    )}
                  />
                  <div className="flex flex-col">
                    <div className="text-sm font-medium">{shade.name}</div>
                    <div className="text-xs text-gray-500">Somfy Shades</div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <div className="size-3 rounded-full bg-green-400" />
                  {!readOnly && (
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="size-5"
                          onClick={(e) => {
                            e.stopPropagation();
                            deleteNestedData(`somfyShades.${shade.id}`);
                          }}
                        >
                          <Trash2Icon className="size-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>Remove Shade</TooltipContent>
                    </Tooltip>
                  )}
                </div>
              </div>

              {!shadesCollapsedState[shade.id] && (
                <div className="ml-6 space-y-2">
                  <SettingsRow>
                    <SettingsLabel htmlFor={`shade-name-${shade.id}`}>
                      Name
                    </SettingsLabel>
                    <SettingsInput
                      id={`shade-name-${shade.id}`}
                      value={shade.name}
                      onEndEdit={(value) => {
                        if (typeof value !== "string") {
                          return;
                        }
                        updateNestedData(`somfyShades.${shade.id}.name`, value);
                      }}
                      disabled={readOnly}
                    />
                  </SettingsRow>
                </div>
              )}
            </SettingsRow>
          ))}
          {Object.keys(baseStationData.somfyShades || {}).length === 0 && (
            <div className="text-sm text-gray-500 text-center py-4">
              No Somfy Shades configured. Click the + button to add one.
            </div>
          )}
          {Object.keys(baseStationData.somfyShades || {}).length >= 16 && (
            <div className="text-sm text-gray-500 text-center py-2">
              Maximum of 16 Somfy Shades reached.
            </div>
          )}
        </SettingsGroupContent>
      </SettingsGroup>
    </div>
  );
}
