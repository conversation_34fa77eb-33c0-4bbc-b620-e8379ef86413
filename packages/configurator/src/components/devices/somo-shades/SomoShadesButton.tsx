import { useGetData } from "@/components/data/useData";
import { useReactFlowContext } from "@/components/graph/ReactFlowContext";
import { DeviceIcons } from "@/components/icons/DeviceIcons";
import { useExecutionContext } from "@/contexts/ExecutionContext";
import { cn } from "@/lib/classNames";
import { filterNodes, SomoShadesDevice, SomoShadesVia } from "@somo/shared";
import { Handle, Position } from "@xyflow/react";
import { match } from "ts-pattern";

interface Props {
  direction: "left" | "right";
  device: SomoShadesDevice;
}

export function SomoShadesButton(props: Props) {
  const { readOnly } = useReactFlowContext();
  return (
    <BaseButton
      {...props}
      isConnectable={!readOnly}
      className="text-white"
      renderLabel={(load) => (
        <div className="text-xs flex-shrink-0 mr-2">{load.loadName}</div>
      )}
    />
  );
}

export function SomoShadesButtonSimulator(props: Props) {
  const { device } = props;
  const { isShadesOpen, getShadesProgress, sendShadesCommand } =
    useExecutionContext();

  return (
    <BaseButton
      {...props}
      isConnectable={false}
      className={
        isShadesOpen(props.device.id, props.device.viaId)
          ? "bg-yellow-100 text-black shadow-md border-yellow-300 "
          : "bg-gray-600 text-white border-gray-700"
      }
      onClick={() => sendShadesCommand(device.nodeId, device.viaId)}
      renderLabel={(load) => (
        <div className="flex flex-col items-start flex-1">
          <div className="text-xs flex-shrink-0 mr-2">{load.loadName}</div>
          <div className="flex-shrink-0 mr-2 text-[9px]">
            {getShadesProgress(props.device.id, props.device.viaId)}%
          </div>
        </div>
      )}
    />
  );
}

function BaseButton({
  device,
  direction,
  isConnectable,
  renderLabel,
  onClick,
  className = "",
}: Props & {
  isConnectable: boolean;
  renderLabel: (load: SomoShadesVia) => React.ReactNode;
  onClick?: () => void;
  className?: string;
}) {
  const { nodes } = useReactFlowContext();

  const shadesNode = filterNodes(nodes, "somoShadesContainer").find(
    (node) => node.id === device.nodeId,
  );
  if (!shadesNode) {
    return (
      <div className="text-xs font-mono h-[40px] flex items-center justify-center px-2">
        Shades/Curtains missing
      </div>
    );
  }

  const data = useGetData(shadesNode?.data.dataId, "somoShades");
  if (!data) {
    return <>Loading...</>;
  }

  const load = device.viaId === "via" ? data.via : undefined;

  if (!load) {
    return (
      <div className="text-xs font-mono h-[40px] flex items-center justify-center px-2">
        Shades/Curtains missing
      </div>
    );
  }

  const { loadIcon } = data.via;
  const ActiveIconComponent = loadIcon ? DeviceIcons[loadIcon] : null;

  return (
    <div
      onClick={onClick}
      className={cn(
        "size-10 bg-gray-600 rounded-md shadow-md flex items-center justify-center min-w-[40px] gap-2 flex-shrink-0 px-2 relative",
        device.showLabel && "w-auto",
        className,
      )}
    >
      {ActiveIconComponent && (
        <ActiveIconComponent className="size-6 flex-shrink-0" />
      )}
      {device.showLabel && renderLabel(load)}
      <Handle
        type="target"
        id={device.id}
        isConnectable={isConnectable}
        position={match(device.anchorPosition)
          .with("bottom", () => Position.Bottom)
          .with("top", () => Position.Top)
          .with("left", () => Position.Left)
          .with("right", () => Position.Right)
          .with(undefined, () =>
            direction === "left" ? Position.Left : Position.Right,
          )
          .exhaustive()}
        style={{
          background: "white",
          border: "1px solid #4b5563",
          color: "transparent",
          width: 6,
          height: 6,
          minHeight: 6,
          minWidth: 6,
          borderRadius: "50%",
          boxShadow: "0 1px 2px rgba(0, 0, 0, 0.1)",
        }}
      />
    </div>
  );
}
