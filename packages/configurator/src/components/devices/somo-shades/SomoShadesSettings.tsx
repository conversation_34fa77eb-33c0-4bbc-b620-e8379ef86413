import { useData } from "@/components/data/useData";
import { useReactFlowContext } from "@/components/graph/ReactFlowContext";
import { ActionSettingsWithHeader } from "@/components/graph/settings/ActionSettings";
import { LampPopover } from "@/components/graph/settings/LampPopover";
import {
  SettingsGroup,
  SettingsGroupContent,
  SettingsGroupHeader,
  SettingsLabel,
  SettingsRow,
} from "@/components/graph/settings/SettingsGroup";
import { SettingsInput } from "@/components/graph/settings/SettingsInput";
import { DeviceIconKey } from "@/components/icons/DeviceIcons";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { clamp } from "@/lib/math";
import {
  GraphNodesByType,
  randomId,
  SomoShadesButton,
  SomoShadesVia,
  SomoShadesViaId,
} from "@somo/shared";
import { useConnectableDevices } from "../useConnectableDevices";

export function SomoShadesSettings({
  node,
}: {
  node: GraphNodesByType["somoShadesContainer"];
}) {
  const { readOnly } = useReactFlowContext();
  const { data, updateNestedData } = useData(node.data.dataId, "somoShades");

  if (!data) {
    return <div>Loading...</div>;
  }

  return (
    <div>
      <SettingsGroup>
        <SettingsGroupContent>
          <SettingsRow>
            <div className="font-mono text-xs text-gray-500">ID: {node.id}</div>
          </SettingsRow>
          <SettingsRow>
            <SettingsLabel htmlFor="name">Name</SettingsLabel>
            <SettingsInput
              id="name"
              value={data.title}
              onEndEdit={(value) => {
                if (typeof value !== "string") {
                  return;
                }
                updateNestedData("title", value);
              }}
              disabled={readOnly}
            />
          </SettingsRow>
        </SettingsGroupContent>
      </SettingsGroup>

      <SettingsGroup>
        <SettingsGroupHeader className="flex flex-row justify-between items-center -mb-2 pt-4">
          <span className="flex flex-grow">Shades Settings</span>
        </SettingsGroupHeader>
        <SettingsGroupContent>
          <SettingsRow className="flex flex-row justify-between items-center">
            <SettingsLabel htmlFor="raceTime" className="flex-shrink-0">
              Time to fully open/close (sec)
            </SettingsLabel>
            <SettingsInput
              id="raceTime"
              value={data.raceTime.toString()}
              onEndEdit={(value) => {
                let numValue = parseFloat(value);
                if (isNaN(numValue)) {
                  numValue = 0;
                }
                updateNestedData(
                  "raceTime",
                  clamp(numValue, { min: 0.1, max: 90 }),
                );
              }}
              inputClassName="text-center"
              className="w-[100px]"
              disabled={readOnly}
            />
          </SettingsRow>
        </SettingsGroupContent>
      </SettingsGroup>

      <SettingsGroup className="mb-4">
        <SettingsGroupHeader className="flex flex-row justify-between items-center -mb-2 pt-4">
          <span className="flex flex-grow">Control</span>
        </SettingsGroupHeader>
        <SettingsGroupContent>
          <SettingsRow>
            <ConnectionRow
              via={data.via}
              onLoadNameChange={(value) => {
                updateNestedData("via.loadName", value);
              }}
              onLoadIconChange={(iconKey) => {
                updateNestedData("via.loadIcon", iconKey);
              }}
            />
          </SettingsRow>
          <SettingsRow>
            <ShadesButtonRow
              node={node}
              button={data.viaUp}
              viaId="viaUp"
              label="Up Button"
              onIconChange={(iconKey) => {
                updateNestedData("viaUp.icon", iconKey);
              }}
              onNameChange={(value) => {
                updateNestedData("viaUp.name", value);
              }}
              onShowLabelChange={(checked) => {
                updateNestedData("viaUp.showLabel", checked);
              }}
              onEnabledChange={(checked) => {
                updateNestedData("viaUp.enabled", checked);
              }}
            />
          </SettingsRow>

          <SettingsRow className="mt-4">
            <ShadesButtonRow
              node={node}
              button={data.viaDown}
              viaId="viaDown"
              label="Down Button"
              onIconChange={(iconKey) => {
                updateNestedData("viaDown.icon", iconKey);
              }}
              onNameChange={(value) => {
                updateNestedData("viaDown.name", value);
              }}
              onShowLabelChange={(checked) => {
                updateNestedData("viaDown.showLabel", checked);
              }}
              onEnabledChange={(checked) => {
                updateNestedData("viaDown.enabled", checked);
              }}
            />
          </SettingsRow>
        </SettingsGroupContent>
      </SettingsGroup>
    </div>
  );
}

interface ConnectionRowProps {
  via: SomoShadesVia;
  onLoadNameChange: (loadName: string) => void;
  onLoadIconChange: (iconKey: DeviceIconKey | undefined) => void;
}

function ConnectionRow({
  via,
  onLoadNameChange,
  onLoadIconChange,
}: ConnectionRowProps) {
  const { readOnly } = useReactFlowContext();

  return (
    <div className="flex flex-col gap-2">
      <div className="flex flex-row gap-2 text-xs font-bold text-gray-800 mt-2 items-center">
        <LampPopover
          activeIconKey={via.loadIcon}
          onIconClick={onLoadIconChange}
          disabled={readOnly}
        />
        <SettingsInput
          className="text-sm font-normal h-10"
          value={via.loadName}
          onEndEdit={onLoadNameChange}
          disabled={readOnly}
        />
      </div>
    </div>
  );
}

// Updated interface for the additional buttons
interface ShadesButtonRowProps {
  node: GraphNodesByType["somoShadesContainer"];
  button: SomoShadesButton;
  viaId: SomoShadesViaId;
  label: string;
  onIconChange: (iconKey: DeviceIconKey | undefined) => void;
  onNameChange: (name: string) => void;
  onShowLabelChange: (checked: boolean) => void;
  onEnabledChange: (checked: boolean) => void;
}

function ShadesButtonRow({
  node,
  button,
  viaId,
  label,
  onIconChange,
  onNameChange,
  onShowLabelChange,
}: ShadesButtonRowProps) {
  const { nodes, readOnly } = useReactFlowContext();

  const { updateNestedData, deleteNestedData } = useData(
    node.data.dataId,
    "somoShades",
  );
  const dataKey = `${viaId}.onUpClick` as const;

  const actions = Object.values(button.onUpClick ?? {}).sort(
    (a, b) => a.sortIndex - b.sortIndex,
  );

  const connectableDevices = useConnectableDevices({
    nodes,
    nodeType: node.type,
    actions,
  }).filter((device) => device.type === "somoShades");

  return (
    <div className="flex flex-col gap-2">
      <div className="flex flex-row gap-2 text-xs font-bold text-gray-800 mt-2 items-center justify-between">
        <Label className="text-gray-700">{label}</Label>
      </div>

      {button.enabled && (
        <>
          <div className="flex flex-row gap-2 text-xs font-bold text-gray-800 mt-2 items-center">
            <LampPopover
              activeIconKey={button.icon}
              onIconClick={onIconChange}
              disabled={readOnly}
            />

            <SettingsInput
              className="text-sm font-normal h-10"
              value={button.name}
              onEndEdit={onNameChange}
              disabled={readOnly}
            />
            <div className="flex flex-row items-center gap-1 ml-3 w-[90px] flex-shrink-0">
              <Checkbox
                id={`${viaId}-showLabel`}
                checked={button.showLabel}
                onCheckedChange={onShowLabelChange}
                disabled={readOnly}
              />
              <Label
                htmlFor={`${viaId}-showLabel`}
                className="text-gray-500 text-xs font-semibold truncate"
              >
                Show label
              </Label>
            </div>
          </div>

          <ActionSettingsWithHeader
            node={node}
            type="nothing"
            label="On click"
            sourceHandle={viaId}
            devices={connectableDevices}
            actions={actions}
            missingDeviceText="Add shades/curtains to a section first"
            onDelete={(id) => deleteNestedData(`${dataKey}.${id}`)}
            onDeviceChange={(id, value) => {
              updateNestedData(`${dataKey}.${id}.deviceId`, value);
            }}
            onAddAction={(deviceId) => {
              const newActionId = randomId();
              const mapSize = Object.values(button.onUpClick).length;
              updateNestedData(`${dataKey}.${newActionId}`, {
                id: newActionId,
                sortIndex: mapSize,
                deviceId,
                raceTime: 0.2,
                targetValue: 100,
                onValue: 100,
                offValue: 0,
              });
            }}
          />
        </>
      )}
    </div>
  );
}
