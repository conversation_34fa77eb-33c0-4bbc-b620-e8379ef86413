import { cn } from "@/lib/classNames";
import { LayoutAnchorNode } from "@somo/shared";
import {
  Handle,
  NodeProps,
  Position,
  useUpdateNodeInternals,
} from "@xyflow/react";
import equal from "fast-deep-equal/react";
import { Blinds } from "lucide-react";
import React, { useEffect } from "react";

export const SomoShadesAnchorNode = React.memo(InternalNode, equal);

function InternalNode({ id, selected }: NodeProps<LayoutAnchorNode>) {
  const updateNodeInternals = useUpdateNodeInternals();

  useEffect(() => {
    const timeout = setTimeout(() => {
      updateNodeInternals(id);
    }, 1);
    return () => clearTimeout(timeout);
  }, [updateNodeInternals, id]);

  return (
    <div className="w-full h-full flex items-center justify-center">
      <div
        className={cn(
          "size-4 bg-white rounded-[2px] mx-auto border border-white outline outline-[2px] outline-offset-[0px] outline-gray-400 shadow",
          "outline outline-1 outline-offset-[0px] outline-gray-300 bg-gray-600",
          selected && "outline-2 outline-blue-400 bg-black",
        )}
      />
      <Handle
        type="source"
        position={Position.Top}
        isConnectable={false}
        style={{
          background: "transparent",
          border: 0,
          top: "50%",
          left: "50%",
          right: "auto",
          bottom: "auto",
          transform: "translate(-50%, -50%)",
        }}
      />
      <div className="text-white absolute">
        <Blinds className="size-2" />
      </div>
    </div>
  );
}
