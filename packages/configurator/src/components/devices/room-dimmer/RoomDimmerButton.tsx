import { useGetData } from "@/components/data/useData";
import { useReactFlowContext } from "@/components/graph/ReactFlowContext";
import { DeviceIcons } from "@/components/icons/DeviceIcons";
import { useExecutionContext } from "@/contexts/ExecutionContext";
import { cn } from "@/lib/classNames";
import { filterNodes, RoomDimmerDevice, RoomDimmerVia } from "@somo/shared";
import { Handle, Position } from "@xyflow/react";
import { match } from "ts-pattern";

interface Props {
  direction: "left" | "right";
  device: RoomDimmerDevice;
}

export function RoomDimmerButton(props: Props) {
  const { readOnly } = useReactFlowContext();
  return (
    <BaseButton
      {...props}
      isConnectable={!readOnly}
      className="text-white"
      renderLabel={(light) => (
        <div className="text-xs flex-shrink-0 mr-2">{light.lightName}</div>
      )}
    />
  );
}

export function RoomDimmerButtonSimulator(props: Props) {
  const { isLightOn, getBrightness, sendDimmerCommand } = useExecutionContext();

  return (
    <BaseButton
      {...props}
      isConnectable={false}
      className={
        isLightOn(props.device.id)
          ? "bg-yellow-100 text-black shadow-md border-yellow-300 "
          : "bg-gray-600 text-white border-gray-700"
      }
      onPointerDown={() =>
        sendDimmerCommand(props.device.nodeId, props.device.viaId, "onUpHold")
      }
      onPointerUp={() =>
        sendDimmerCommand(props.device.nodeId, props.device.viaId, "onUpClick")
      }
      renderLabel={(light) => (
        <div className="flex flex-col items-start flex-1">
          <div className="text-xs flex-shrink-0 mr-2">{light.lightName}</div>
          <div className="flex-shrink-0 mr-2 text-[9px]">
            {getBrightness(props.device.id)}%
          </div>
        </div>
      )}
    />
  );
}

function BaseButton({
  device,
  direction,
  isConnectable,
  renderLabel,
  onPointerDown,
  onPointerUp,
  className = "",
}: Props & {
  isConnectable: boolean;
  renderLabel: (light: RoomDimmerVia) => React.ReactNode;
  onPointerDown?: () => void;
  onPointerUp?: () => void;
  className?: string;
}) {
  const { nodes } = useReactFlowContext();

  if (device.viaId !== "via") {
    return <></>;
  }

  const dimmerNode = filterNodes(nodes, "roomDimmerContainer").find(
    (node) => node.id === device.nodeId,
  );
  if (!dimmerNode) {
    return (
      <div className="text-xs font-mono h-[40px] flex items-center justify-center px-2">
        Light missing
      </div>
    );
  }

  const data = useGetData(dimmerNode?.data.dataId, "roomDimmer");
  if (!data) {
    return <></>;
  }

  const { lightIcon } = data.via;
  const ActiveIconComponent = lightIcon ? DeviceIcons[lightIcon] : null;

  return (
    <div
      onPointerDown={onPointerDown}
      onPointerUp={onPointerUp}
      className={cn(
        "size-10 bg-gray-600 rounded-md shadow-md flex items-center justify-center min-w-[40px] gap-2 flex-shrink-0 px-2 relative",
        device.showLabel && "w-auto",
        className,
      )}
    >
      {ActiveIconComponent && (
        <ActiveIconComponent className="size-6 flex-shrink-0" />
      )}
      {device.showLabel && renderLabel(data.via)}
      <Handle
        type="target"
        id={device.id}
        isConnectable={isConnectable}
        position={match(device.anchorPosition)
          .with("bottom", () => Position.Bottom)
          .with("top", () => Position.Top)
          .with("left", () => Position.Left)
          .with("right", () => Position.Right)
          .with(undefined, () =>
            direction === "left" ? Position.Left : Position.Right,
          )
          .exhaustive()}
        style={{
          background: "white",
          border: "1px solid #4b5563",
          color: "transparent",
          width: 6,
          height: 6,
          minHeight: 6,
          minWidth: 6,
          borderRadius: "50%",
          boxShadow: "0 1px 2px rgba(0, 0, 0, 0.1)",
        }}
      />
    </div>
  );
}
