import { useData } from "@/components/data/useData";
import {
  getDeviceDimmingSpeed,
  getHoldDimmingSpeed,
} from "@/components/devices/deviceUtils";
import { useReactFlowContext } from "@/components/graph/ReactFlowContext";
import { ActionSettingsWithHeader } from "@/components/graph/settings/ActionSettings";
import { DimmingCurve } from "@/components/graph/settings/DimmingCurve";
import { LampPopover } from "@/components/graph/settings/LampPopover";
import {
  SettingsGroup,
  SettingsGroupContent,
  SettingsGroupHeader,
  SettingsLabel,
  SettingsRow,
} from "@/components/graph/settings/SettingsGroup";
import { SettingsInput } from "@/components/graph/settings/SettingsInput";
import { DeviceIconKey } from "@/components/icons/DeviceIcons";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { clamp } from "@/lib/math";
import {
  CurvePoint,
  CurveType,
  LayoutContainerNode,
  randomId,
  RoomDimmerAction,
  RoomDimmerButton,
  RoomDimmerVia,
  RoomDimmerViaId,
} from "@somo/shared";
import { useEffect } from "react";
import { match } from "ts-pattern";
import { useConnectableDevices } from "../useConnectableDevices";

export function RoomDimmerSettings({
  node,
}: {
  node: LayoutContainerNode<"roomDimmerContainer">;
}) {
  const { readOnly } = useReactFlowContext();
  const { data, updateNestedData } = useData(node.data.dataId, "roomDimmer");

  useEffect(() => {
    if (!data) {
      return;
    }
    if (data.dimmingCurve.points.length < 2) {
      updateNestedData("dimmingCurve", {
        type: "linear",
        points: [
          { x: 0, y: 0 },
          { x: 1, y: 1 },
        ],
      });
    }
  }, [data?.dimmingCurve.points, updateNestedData]);

  const updateDimmingCurve = (newPoints: CurvePoint[], newType: CurveType) => {
    updateNestedData("dimmingCurve", {
      type: newType,
      points: newPoints,
    });
  };

  if (!data) {
    return <div>Loading...</div>;
  }

  return (
    <div>
      <SettingsGroup>
        <SettingsGroupContent>
          <SettingsRow>
            <div className="font-mono text-xs text-gray-500">ID: {node.id}</div>
          </SettingsRow>
          <SettingsRow>
            <SettingsLabel htmlFor="name">Name</SettingsLabel>
            <SettingsInput
              id="name"
              value={data.title}
              onEndEdit={(value) => {
                updateNestedData("title", value);
              }}
              disabled={readOnly}
            />
          </SettingsRow>
        </SettingsGroupContent>
      </SettingsGroup>

      <SettingsGroup>
        <SettingsGroupHeader className="flex flex-row justify-between items-center -mb-2 pt-4">
          <span className="flex flex-grow">Dimmer Settings</span>
        </SettingsGroupHeader>
        <SettingsGroupContent>
          <SettingsRow className="flex flex-row justify-between items-center">
            <SettingsLabel htmlFor="dimSpeed" className="flex-shrink-0">
              Default Dim Speed (sec)
            </SettingsLabel>
            <SettingsInput
              id="dimSpeed"
              value={data.dimSpeed.toString()}
              onEndEdit={(value) => {
                let numValue = parseFloat(value);
                if (isNaN(numValue)) {
                  numValue = 0;
                }
                const clampedValue = clamp(numValue, { min: 0.1, max: 5 });
                updateNestedData("dimSpeed", clampedValue);
              }}
              inputClassName="text-center"
              className="w-[100px]"
              disabled={readOnly}
            />
          </SettingsRow>
        </SettingsGroupContent>
      </SettingsGroup>

      <SettingsGroup>
        <SettingsGroupContent>
          <DimmingCurve
            points={data.dimmingCurve.points}
            onChange={(newPoints) =>
              updateDimmingCurve(newPoints, data.dimmingCurve.type)
            }
            curveType={data.dimmingCurve.type}
            onCurveTypeChange={(newType) =>
              updateDimmingCurve(data.dimmingCurve.points, newType)
            }
            className="mt-2"
            disabled={readOnly}
          />
        </SettingsGroupContent>
      </SettingsGroup>

      <SettingsGroup className="mb-4">
        <SettingsGroupHeader className="flex flex-row justify-between items-center -mb-2 pt-4">
          <span className="flex flex-grow">Control</span>
        </SettingsGroupHeader>
        <SettingsGroupContent>
          <SettingsRow>
            <ConnectionRow
              node={node}
              via={data.via}
              onIconChange={(iconKey) => {
                updateNestedData("via.icon", iconKey);
              }}
              onNameChange={(value) => {
                updateNestedData("via.name", value);
              }}
              onShowLabelChange={(checked) => {
                updateNestedData("via.showLabel", checked);
              }}
              onLightNameChange={(value) => {
                updateNestedData("via.lightName", value);
              }}
              onLightIconChange={(iconKey) => {
                updateNestedData("via.lightIcon", iconKey);
              }}
            />
          </SettingsRow>
        </SettingsGroupContent>
      </SettingsGroup>

      <SettingsGroup className="mb-4">
        <SettingsGroupHeader className="flex flex-row justify-between items-center -mb-2 pt-4">
          <span className="flex flex-grow">Additional Buttons</span>
        </SettingsGroupHeader>
        <SettingsGroupContent>
          <SettingsRow>
            <AdditionalButtonRow
              node={node}
              button={data.viaUp}
              viaId="viaUp"
              label="Up Button"
              onIconChange={(iconKey) => {
                updateNestedData("viaUp.icon", iconKey);
              }}
              onNameChange={(value) => {
                updateNestedData("viaUp.name", value);
              }}
              onShowLabelChange={(checked) => {
                updateNestedData("viaUp.showLabel", checked);
              }}
              onEnabledChange={(checked) => {
                updateNestedData("viaUp.enabled", checked);
              }}
            />
          </SettingsRow>

          <SettingsRow className="mt-4">
            <AdditionalButtonRow
              node={node}
              button={data.viaDown}
              viaId="viaDown"
              label="Down Button"
              onIconChange={(iconKey) => {
                updateNestedData("viaDown.icon", iconKey);
              }}
              onNameChange={(value) => {
                updateNestedData("viaDown.name", value);
              }}
              onShowLabelChange={(checked) => {
                updateNestedData("viaDown.showLabel", checked);
              }}
              onEnabledChange={(checked) => {
                updateNestedData("viaDown.enabled", checked);
              }}
            />
          </SettingsRow>
        </SettingsGroupContent>
      </SettingsGroup>
    </div>
  );
}

interface ConnectionRowProps {
  node: LayoutContainerNode<"roomDimmerContainer">;
  via: RoomDimmerVia;
  onIconChange: (iconKey: DeviceIconKey | undefined) => void;
  onNameChange: (name: string) => void;
  onLightNameChange: (lightName: string) => void;
  onShowLabelChange: (checked: boolean) => void;
  onLightIconChange: (iconKey: DeviceIconKey | undefined) => void;
}

function ConnectionRow({
  via,
  node,
  onIconChange,
  onNameChange,
  onLightNameChange,
  onShowLabelChange,
  onLightIconChange,
}: ConnectionRowProps) {
  const { readOnly } = useReactFlowContext();

  return (
    <div className="flex flex-col gap-4">
      <div className="flex flex-row gap-2 text-xs font-bold text-gray-800 mt-2 items-center">
        <LampPopover
          activeIconKey={via.lightIcon}
          onIconClick={onLightIconChange}
        />
        <SettingsInput
          className="text-sm font-normal h-10"
          value={via.lightName}
          onEndEdit={onLightNameChange}
          disabled={readOnly}
        />
      </div>

      <div className="flex flex-row gap-2 text-xs font-bold text-gray-800 items-center">
        <LampPopover
          activeIconKey={via.icon}
          onIconClick={onIconChange}
          disabled={readOnly}
        />

        <SettingsInput
          className="text-sm font-normal h-10"
          value={via.name}
          onEndEdit={onNameChange}
          disabled={readOnly}
        />
        <div className="flex flex-row items-center gap-1 ml-3 w-[90px] flex-shrink-0">
          <Checkbox
            id="showLabel"
            checked={via.showLabel}
            onCheckedChange={onShowLabelChange}
            disabled={readOnly}
          />
          <Label
            htmlFor="showLabel"
            className="text-gray-500 text-xs font-semibold flex-shrink-0 truncate"
          >
            Show label
          </Label>
        </div>
      </div>

      <ViaActionSettings node={node} via={via} actionType="onUpClick" />
      <ViaActionSettings node={node} via={via} actionType="onUpHold" />
    </div>
  );
}

function ViaActionSettings({
  node,
  via,
  actionType,
}: {
  node: LayoutContainerNode<"roomDimmerContainer">;
  via: RoomDimmerVia;
  actionType: RoomDimmerAction;
}) {
  const { nodes } = useReactFlowContext();
  const { updateNestedData, deleteNestedData } = useData(
    node.data.dataId,
    "roomDimmer",
  );
  const dataKey = `via.${actionType}` as const;

  const actions = Object.values(via[actionType] ?? {}).sort(
    (a, b) => a.sortIndex - b.sortIndex,
  );

  const connectableDevices = useConnectableDevices({
    nodes,
    nodeType: node.type,
    actions,
  });

  return (
    <ActionSettingsWithHeader
      type="onOff"
      label={match(actionType)
        .with("onUpClick", () => "On click")
        .with("onUpHold", () => "On hold")
        .exhaustive()}
      node={node}
      sourceHandle="via"
      devices={connectableDevices}
      actions={actions}
      onDelete={(id) => deleteNestedData(`${dataKey}.${id}`)}
      onDeviceChange={(id, deviceId) => {
        updateNestedData(`${dataKey}.${id}.deviceId`, deviceId);
      }}
      onDelayChange={(id, value) => {
        updateNestedData(`${dataKey}.${id}.delay`, value);
      }}
      onDimSpeedChange={(id, value) => {
        updateNestedData(`${dataKey}.${id}.dimSpeed`, value);
      }}
      onOnValueChange={(id, value) => {
        updateNestedData(`${dataKey}.${id}.onValue`, value);
      }}
      onOffValueChange={(id, value) => {
        updateNestedData(`${dataKey}.${id}.offValue`, value);
      }}
      onAddAction={(deviceId) => {
        const newActionId = randomId();
        const mapSize = Object.values(via[actionType]).length;
        updateNestedData(`${dataKey}.${newActionId}`, {
          id: newActionId,
          sortIndex: mapSize,
          type: "lighting",
          deviceId,
          dimSpeed: match(actionType)
            .with("onUpClick", () =>
              getDeviceDimmingSpeed(deviceId, connectableDevices, nodes),
            )
            .with("onUpHold", () => getHoldDimmingSpeed())
            .exhaustive(),
          targetValue: 100,
          onValue: 100,
          offValue: 0,
          delay: 0,
        });
      }}
    />
  );
}

// Updated interface for the additional buttons
interface AdditionalButtonRowProps {
  node: LayoutContainerNode<"roomDimmerContainer">;
  button: RoomDimmerButton;
  viaId: RoomDimmerViaId;
  label: string;
  onIconChange: (iconKey: DeviceIconKey | undefined) => void;
  onNameChange: (name: string) => void;
  onShowLabelChange: (checked: boolean) => void;
  onEnabledChange: (checked: boolean) => void;
}

function AdditionalButtonRow({
  node,
  button,
  viaId,
  label,
  onIconChange,
  onNameChange,
  onShowLabelChange,
  onEnabledChange,
}: AdditionalButtonRowProps) {
  const { readOnly } = useReactFlowContext();

  return (
    <div className="flex flex-col gap-4">
      <div className="flex flex-row gap-2 text-xs font-bold text-gray-800 mt-2 items-center justify-between">
        <Label className="text-gray-700">{label}</Label>
        <div className="flex flex-row items-center gap-1 w-[90px] flex-shrink-0">
          <Checkbox
            id={`${viaId}-enabled`}
            checked={button.enabled}
            onCheckedChange={() => onEnabledChange(!button.enabled)}
            disabled={readOnly}
          />
          <Label
            htmlFor={`${viaId}-enabled`}
            className="text-gray-500 text-xs font-semibold truncate"
          >
            Enabled
          </Label>
        </div>
      </div>

      {button.enabled && (
        <>
          <div className="flex flex-row gap-2 text-xs font-bold text-gray-800 items-center">
            <LampPopover
              activeIconKey={button.icon}
              onIconClick={onIconChange}
              disabled={readOnly}
            />

            <SettingsInput
              className="text-sm font-normal h-10"
              value={button.name}
              onEndEdit={onNameChange}
              disabled={readOnly}
            />
            <div className="flex flex-row items-center gap-1 ml-3 w-[90px] flex-shrink-0">
              <Checkbox
                id={`${viaId}-showLabel`}
                checked={button.showLabel}
                onCheckedChange={onShowLabelChange}
                disabled={readOnly}
              />
              <Label
                htmlFor={`${viaId}-showLabel`}
                className="text-gray-500 text-xs font-semibold truncate"
              >
                Show label
              </Label>
            </div>
          </div>

          <ButtonActionSettings
            node={node}
            button={button}
            viaId={viaId}
            actionType="onUpClick"
          />
          <ButtonActionSettings
            node={node}
            button={button}
            viaId={viaId}
            actionType="onUpHold"
          />
        </>
      )}
    </div>
  );
}

function ButtonActionSettings({
  node,
  button,
  viaId,
  actionType,
}: {
  node: LayoutContainerNode<"roomDimmerContainer">;
  button: RoomDimmerButton;
  viaId: RoomDimmerViaId;
  actionType: RoomDimmerAction;
}) {
  const { nodes } = useReactFlowContext();
  const { updateNestedData, deleteNestedData } = useData(
    node.data.dataId,
    "roomDimmer",
  );
  const dataKey = `${viaId}.${actionType}` as const;

  const actions = Object.values(button[actionType] ?? {}).sort(
    (a, b) => a.sortIndex - b.sortIndex,
  );

  const connectableDevices = useConnectableDevices({
    nodes,
    nodeType: node.type,
    actions,
  });

  return (
    <ActionSettingsWithHeader
      type="targetValue"
      label={match(actionType)
        .with("onUpClick", () => "On click")
        .with("onUpHold", () => "On hold")
        .exhaustive()}
      node={node}
      sourceHandle={viaId}
      devices={connectableDevices}
      actions={actions}
      onDelete={(id) => deleteNestedData(`${dataKey}.${id}`)}
      onDeviceChange={(id, deviceId) => {
        updateNestedData(`${dataKey}.${id}.deviceId`, deviceId);
      }}
      onDelayChange={(id, value) => {
        updateNestedData(`${dataKey}.${id}.delay`, value);
      }}
      onDimSpeedChange={(id, value) => {
        updateNestedData(`${dataKey}.${id}.dimSpeed`, value);
      }}
      onTargetValueChange={(id, value) => {
        updateNestedData(`${dataKey}.${id}.targetValue`, value);
      }}
      onAddAction={(deviceId) => {
        const newActionId = randomId();
        const mapSize = Object.values(button[actionType]).length;
        updateNestedData(`${dataKey}.${newActionId}`, {
          id: newActionId,
          sortIndex: mapSize,
          deviceId,
          dimSpeed: match(actionType)
            .with("onUpClick", () =>
              getDeviceDimmingSpeed(deviceId, connectableDevices, nodes),
            )
            .with("onUpHold", () => getHoldDimmingSpeed())
            .exhaustive(),
          targetValue: 100,
          onValue: 100,
          offValue: 0,
          delay: 0,
          type: "lighting",
        });
      }}
    />
  );
}
