import { useData } from "@/components/data/useData";
import { getDeviceDimmingSpeed } from "@/components/devices/deviceUtils";
import { useReactFlowContext } from "@/components/graph/ReactFlowContext";
import { ActionSettings } from "@/components/graph/settings/ActionSettings";
import { LampPopover } from "@/components/graph/settings/LampPopover";
import {
  SettingsGroup,
  SettingsGroupContent,
  SettingsGroupHeader,
  SettingsLabel,
  SettingsRow,
} from "@/components/graph/settings/SettingsGroup";
import { SettingsInput } from "@/components/graph/settings/SettingsInput";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Label } from "@/components/ui/label";
import {
  <PERSON>lt<PERSON>,
  Toolt<PERSON>Content,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { TooltipDropdownMenu } from "@/components/ui/tooltip-dropdown-menu";
import { clamp } from "@/lib/math";
import {
  aThermostatControlSettings,
  DoorSensorAction,
  DoorSensorViaId,
  GraphNodesByType,
  LayoutContainerNode,
  randomId,
} from "@somo/shared";
import { CircleAlertIcon, PlusIcon } from "lucide-react";
import { useConnectableDevices } from "../useConnectableDevices";

// Updated interface for the additional buttons
interface AdditionalButtonRowProps {
  id: DoorSensorViaId;
  label: string;
  button: DoorSensorAction;
  node: LayoutContainerNode<"doorSensorContainer">;
}

function AdditionalButtonRow({
  id,
  label,
  button,
  node,
}: AdditionalButtonRowProps) {
  const { nodes, readOnly } = useReactFlowContext();
  const { data, updateNestedData, deleteNestedData } = useData(
    node.data.dataId,
    "doorSensor",
  );

  const actions = Object.values(button.onUpClick ?? {}).sort(
    (a, b) => a.sortIndex - b.sortIndex,
  );
  const deviceActions = actions.filter(
    (action) => action.type === "lighting" || !("type" in action),
  );
  const thermostatActions = actions.filter(
    (action) => action.type === "thermostat",
  );

  const connectableDevices = useConnectableDevices({
    nodes,
    nodeType: node.type,
    actions: deviceActions,
  }).filter(
    (node) =>
      node.type !== "somoThermostat" && node.type !== "somoIrController",
  );

  const connectableThermostats = useConnectableDevices({
    nodes,
    nodeType: node.type,
    actions: thermostatActions,
  }).filter((node) => node.type === "somoThermostat");

  const connectableIrControllers = useConnectableDevices({
    nodes,
    nodeType: node.type,
    actions: thermostatActions,
  }).filter((node) => node.type === "somoIrController");

  const firstUnusedDeviceId = connectableDevices.find((d) => !d.isUsed)?.id;
  const firstUnusedThermostatId = connectableThermostats.find(
    (d) => !d.isUsed,
  )?.id;
  const firstUnusedIrControllerId = connectableIrControllers.find(
    (d) => !d.isUsed,
  )?.id;
  const firstUnusedHvacId =
    firstUnusedThermostatId || firstUnusedIrControllerId;

  const dimSpeed = getDeviceDimmingSpeed(
    firstUnusedDeviceId,
    connectableDevices,
    nodes,
  );

  if (!data) {
    return <></>;
  }

  return (
    <div className="flex flex-col gap-2">
      <div className="flex flex-row gap-2 text-xs font-bold text-gray-800 mt-2 items-center justify-between">
        <Label className="text-gray-700">{label}</Label>
        <div className="flex flex-row items-center gap-1 w-[90px] flex-shrink-0">
          <Checkbox
            id={`${id}-enabled`}
            checked={button.enabled}
            onCheckedChange={() =>
              updateNestedData(`${id}.enabled`, !button.enabled)
            }
            disabled={readOnly}
          />
          <Label
            htmlFor={`${id}-enabled`}
            className="text-gray-500 text-xs font-semibold truncate"
          >
            Enabled
          </Label>
        </div>
      </div>

      {button.enabled && (
        <>
          <div className="flex flex-row gap-2 text-xs font-bold text-gray-800 mt-2 items-center">
            <LampPopover
              activeIconKey={button.icon}
              onIconClick={(iconKey) => updateNestedData(`${id}.icon`, iconKey)}
              disabled={readOnly}
            />

            <SettingsInput
              className="text-sm font-normal h-10"
              value={button.name}
              onEndEdit={(name) => updateNestedData(`${id}.name`, name)}
              disabled={readOnly}
            />
            <div className="flex flex-row items-center gap-1 ml-3 w-[90px] flex-shrink-0">
              <Checkbox
                id={`${id}-showLabel`}
                checked={button.showLabel}
                onCheckedChange={(checked) =>
                  updateNestedData(`${id}.showLabel`, Boolean(checked))
                }
                disabled={readOnly}
              />
              <Label
                htmlFor={`${id}-showLabel`}
                className="text-gray-500 text-xs font-semibold truncate"
              >
                Show label
              </Label>
            </div>
          </div>
          <SettingsRow className="flex flex-row justify-between items-center gap-2 pr-2.5 mt-2">
            <SettingsInput
              className="w-auto"
              inputClassName="w-[80px] text-center"
              label="Delay (sec)"
              value={button.offDelay.toString()}
              disabled={readOnly}
              onEndEdit={(value) => {
                let numValue = parseInt(value, 10);
                if (isNaN(numValue)) {
                  numValue = 0;
                }
                // 1800 = 30 minutes seems a reasonable max value
                const newValue = clamp(numValue, { min: 0, max: 1_800 });
                updateNestedData(`${id}.offDelay`, newValue);
              }}
            />
          </SettingsRow>

          <SettingsRow className="flex flex-row justify-between items-center gap-2 pl-1 pr-2.5 mt-2">
            <SettingsLabel>Execute</SettingsLabel>
            {!readOnly && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <TooltipDropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="size-6 data-[state=open]:bg-gray-100"
                      >
                        <PlusIcon className="size-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="mr-5">
                      <DropdownMenuLabel>Add action</DropdownMenuLabel>
                      {firstUnusedDeviceId ? (
                        <DropdownMenuItem
                          onClick={() => {
                            const actionId = `device-control-${randomId()}`;
                            const mapSize = Object.values(
                              data[id].onUpClick,
                            ).length;
                            updateNestedData(`${id}.onUpClick.${actionId}`, {
                              id: actionId,
                              deviceId: firstUnusedDeviceId,
                              sortIndex: mapSize,
                              type: "lighting",
                              dimSpeed,
                              targetValue: 100,
                              onValue: 100,
                              offValue: 0,
                              delay: 0,
                            });
                          }}
                        >
                          Device Control
                        </DropdownMenuItem>
                      ) : (
                        <DropdownMenuItem disabled>
                          <div className="flex items-center text-red-600">
                            <CircleAlertIcon className="mr-2 size-4" />
                            Create a device first
                          </div>
                        </DropdownMenuItem>
                      )}

                      <DropdownMenuSeparator />

                      <DropdownMenuLabel>HVAC Control</DropdownMenuLabel>
                      {firstUnusedHvacId ? (
                        <DropdownMenuItem
                          onClick={() => {
                            if (!firstUnusedHvacId) {
                              console.warn("No HVAC device found");
                              return;
                            }

                            const newActionId = randomId();
                            const mapSize = Object.values(
                              data[id].onUpClick,
                            ).length;
                            updateNestedData(
                              `${id}.onUpClick.${newActionId}`,
                              aThermostatControlSettings({
                                id: newActionId,
                                deviceId: firstUnusedHvacId,
                                sortIndex: mapSize,
                                setpoint: 22,
                                mode: "auto",
                                fanSpeed: "auto",
                                type: "thermostat",
                              }),
                            );
                          }}
                        >
                          HVAC Control
                        </DropdownMenuItem>
                      ) : (
                        <DropdownMenuItem disabled>
                          <div className="flex items-center text-red-600">
                            <CircleAlertIcon className="mr-2 size-4" />
                            Create an HVAC device first
                          </div>
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </TooltipDropdownMenu>
                </TooltipTrigger>
                <TooltipContent>Add action</TooltipContent>
              </Tooltip>
            )}
          </SettingsRow>
          {actions.length === 0 && (
            <div className="text-gray-500 text-xs px-1">
              No actions defined.
            </div>
          )}
          <div className="flex flex-col gap-2 px-3">
            {actions.map((action) => {
              const dataKey = `${id}.onUpClick.${action.id}` as const;

              if (action.type === "thermostat") {
                return (
                  <ActionSettings
                    key={action.id}
                    type="thermostat"
                    action={action}
                    devices={[
                      ...connectableThermostats,
                      ...connectableIrControllers,
                    ]}
                    onDelete={() => deleteNestedData(dataKey)}
                    onDeviceChange={(deviceId) =>
                      updateNestedData(`${dataKey}.deviceId`, deviceId)
                    }
                    onSetPointChange={(value) =>
                      // @ts-expect-error - Can't infer value type because of UnifiedActionSettings
                      updateNestedData(`${dataKey}.setpoint`, value)
                    }
                    onModeChange={(value) =>
                      // @ts-expect-error - Can't infer value type because of UnifiedActionSettings
                      updateNestedData(`${dataKey}.mode`, value)
                    }
                    onFanSpeedChange={(value) =>
                      // @ts-expect-error - Can't infer value type because of UnifiedActionSettings
                      updateNestedData(`${dataKey}.fanSpeed`, value)
                    }
                  />
                );
              } else {
                return (
                  <ActionSettings
                    key={action.id}
                    type="targetValue"
                    action={action}
                    devices={connectableDevices}
                    onDelete={() => deleteNestedData(dataKey)}
                    onDeviceChange={(deviceId) =>
                      updateNestedData(`${dataKey}.deviceId`, deviceId)
                    }
                    onDelayChange={(value) =>
                      // @ts-expect-error - Can't infer value type because of UnifiedActionSettings
                      updateNestedData(`${dataKey}.delay`, value)
                    }
                    onDimSpeedChange={(value) =>
                      // @ts-expect-error - Can't infer value type because of UnifiedActionSettings
                      updateNestedData(`${dataKey}.dimSpeed`, value)
                    }
                    onTargetValueChange={(value) =>
                      // @ts-expect-error - Can't infer value type because of UnifiedActionSettings
                      updateNestedData(`${dataKey}.targetValue`, value)
                    }
                  />
                );
              }
            })}
          </div>
        </>
      )}
    </div>
  );
}

export function DoorSensorSettings({
  node,
}: {
  node: GraphNodesByType["doorSensorContainer"];
}) {
  const { reactFlowInstance, readOnly } = useReactFlowContext();
  const { data } = useData(node.data.dataId, "doorSensor");

  if (!data) {
    return <></>;
  }

  return (
    <div>
      <SettingsGroup>
        <SettingsGroupContent>
          <SettingsRow>
            <div className="font-mono text-xs text-gray-500">ID: {node.id}</div>
          </SettingsRow>
          <SettingsRow>
            <SettingsLabel htmlFor="name">Name</SettingsLabel>
            <SettingsInput
              id="name"
              value={data.title}
              onEndEdit={(value) => {
                reactFlowInstance?.updateNodeData(node.id, {
                  title: value,
                });
              }}
              disabled={readOnly}
            />
          </SettingsRow>
        </SettingsGroupContent>
      </SettingsGroup>
      <SettingsGroup className="mb-4">
        <SettingsGroupHeader className="flex flex-row justify-between items-center -mb-2 pt-4">
          <span className="flex flex-grow">Sensor Actions</span>
        </SettingsGroupHeader>
        <SettingsGroupContent>
          <SettingsRow>
            <AdditionalButtonRow
              id="onOpen"
              label="On Open"
              button={data.onOpen}
              node={node}
            />
          </SettingsRow>

          <SettingsRow className="mt-4">
            <AdditionalButtonRow
              id="onClose"
              label="On Close"
              button={data.onClose}
              node={node}
            />
          </SettingsRow>
        </SettingsGroupContent>
      </SettingsGroup>
    </div>
  );
}
