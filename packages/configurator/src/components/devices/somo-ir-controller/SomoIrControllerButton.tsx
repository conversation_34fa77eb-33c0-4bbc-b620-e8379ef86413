import { useGetData } from "@/components/data/useData";
import { useReactFlowContext } from "@/components/graph/ReactFlowContext";
import { DeviceIcons } from "@/components/icons/DeviceIcons";
import { useExecutionContext } from "@/contexts/ExecutionContext";
import { cn } from "@/lib/classNames";
import {
  filterNodes,
  SomoIrControllerDevice,
  SomoIrControllerVia,
} from "@somo/shared";
import { Handle, Position } from "@xyflow/react";
import { match, P } from "ts-pattern";

interface Props {
  direction: "left" | "right";
  device: SomoIrControllerDevice;
}

export function SomoIrControllerButton(props: Props) {
  const { readOnly } = useReactFlowContext();
  return (
    <BaseButton
      {...props}
      isConnectable={!readOnly}
      className="text-white"
      renderLabel={(irController) => (
        <div className="text-xs flex-shrink-0 mr-2">
          {irController.hvacName}
        </div>
      )}
    />
  );
}

export function SomoIrControllerButtonSimulator(props: Props) {
  const { device } = props;
  const { irControllerStates, sendIrControllerCommand } = useExecutionContext();
  const { nodes } = useReactFlowContext();

  const node = filterNodes(nodes, "somoIrControllerContainer").find(
    (n) => n.id === device.nodeId,
  );

  if (!node) {
    return (
      <div className="text-xs font-mono h-[40px] flex items-center justify-center px-2">
        IR Controller missing
      </div>
    );
  }

  const data = useGetData(node?.data.dataId, "somoIrController");
  if (!data) {
    return <></>;
  }

  const deviceState = irControllerStates[device.id];

  if (!deviceState) {
    return (
      <div className="text-xs text-white flex-shrink-0 mr-2 bg-gray-600 rounded-md px-2 py-1">
        Device not connected
      </div>
    );
  }

  const isOn = deviceState.setpoint > 0;

  const unit = data.temperatureUnit ?? "C";
  const defaultRange =
    unit === "F" ? { min: 41, max: 95 } : { min: 5, max: 35 };
  const min = data.minTemp ?? defaultRange.min;
  const max = data.maxTemp ?? defaultRange.max;
  const stepSize = data.stepSize ?? 1;

  const command = match(device.viaId)
    .with(P.string.startsWith("mode"), () => "mode")
    .with(P.string.startsWith("via"), () => "setpoint")
    .with(P.string.startsWith("fan"), () => "fanSpeed")
    .exhaustive() as "mode" | "setpoint" | "fanSpeed";

  return (
    <BaseButton
      {...props}
      isConnectable={false}
      className={
        isOn
          ? "bg-yellow-100 text-black shadow-md border-yellow-300 "
          : "bg-gray-600 text-white border-gray-700"
      }
      onClick={() =>
        sendIrControllerCommand(
          command,
          device.nodeId,
          device.viaId,
          min,
          max,
          stepSize,
        )
      }
      renderLabel={(irController) => (
        <div className="flex flex-col items-start flex-1">
          <div className="text-xs flex-shrink-0 mr-2">
            {irController.hvacName}
          </div>
          <div className="flex-shrink-0 mr-2 text-[9px]">
            IR: {deviceState.mode} / {deviceState.fanSpeed} -{" "}
            {deviceState.setpoint} °{unit}
          </div>
        </div>
      )}
    />
  );
}

function BaseButton({
  device,
  direction,
  isConnectable,
  renderLabel,
  onClick,
  className = "",
}: Props & {
  isConnectable: boolean;
  renderLabel: (irController: SomoIrControllerVia) => React.ReactNode;
  onClick?: () => void;
  className?: string;
}) {
  const { nodes } = useReactFlowContext();

  const node = filterNodes(nodes, "somoIrControllerContainer").find(
    (node) => node.id === device.nodeId,
  );
  const data = node ? useGetData(node.data.dataId, "somoIrController") : null;
  if (!data) {
    return <></>;
  }

  const irController = device.viaId === "via" ? data[device.viaId] : undefined;

  if (!irController) {
    return (
      <div className="text-xs font-mono h-[40px] flex items-center justify-center px-2">
        IR Controller missing
      </div>
    );
  }

  const { hvacIcon } = irController;
  const ActiveIconComponent = hvacIcon ? DeviceIcons[hvacIcon] : null;

  return (
    <div
      onClick={onClick}
      className={cn(
        "h-[40px] w-[40px] bg-gray-600 rounded-md shadow-md flex items-center justify-center min-w-[40px] gap-2 flex-shrink-0 px-2 relative",
        device.showLabel && "w-auto",
        className,
      )}
    >
      {ActiveIconComponent && (
        <ActiveIconComponent className="size-6 flex-shrink-0" />
      )}
      {device.showLabel && renderLabel(irController)}
      <Handle
        type="target"
        id={device.id}
        isConnectable={isConnectable}
        position={match(device.anchorPosition)
          .with("bottom", () => Position.Bottom)
          .with("top", () => Position.Top)
          .with("left", () => Position.Left)
          .with("right", () => Position.Right)
          .with(undefined, () =>
            direction === "left" ? Position.Left : Position.Right,
          )
          .exhaustive()}
        style={{
          background: "white",
          border: "1px solid #4b5563",
          color: "transparent",
          width: 6,
          height: 6,
          minHeight: 6,
          minWidth: 6,
          borderRadius: "50%",
          boxShadow: "0 1px 2px rgba(0, 0, 0, 0.1)",
        }}
      />
    </div>
  );
}
