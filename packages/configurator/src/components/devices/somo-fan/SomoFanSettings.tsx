import { useData } from "@/components/data/useData";
import { useReactFlowContext } from "@/components/graph/ReactFlowContext";
import { ActionSettingsWithHeader } from "@/components/graph/settings/ActionSettings";
import { LampPopover } from "@/components/graph/settings/LampPopover";
import {
  SettingsGroup,
  SettingsGroupContent,
  SettingsGroupHeader,
  SettingsLabel,
  SettingsRow,
} from "@/components/graph/settings/SettingsGroup";
import { SettingsInput } from "@/components/graph/settings/SettingsInput";
import { DeviceIconKey } from "@/components/icons/DeviceIcons";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import {
  GraphNodesByType,
  randomId,
  SomoFanButton,
  SomoFanVia,
  SomoFanViaId,
} from "@somo/shared";
import { useState } from "react";
import { useConnectableDevices } from "../useConnectableDevices";

export function SomoFanSettings({
  node,
}: {
  node: GraphNodesByType["somoFanContainer"];
}) {
  const { readOnly } = useReactFlowContext();
  const { data, updateNestedData, deleteNestedData } = useData(
    node.data.dataId,
    "somoFan",
  );
  const [fanTypeValue, setFanTypeValue] = useState(data?.fanType);

  if (!data) {
    return <div>Loading...</div>;
  }

  return (
    <div>
      <SettingsGroup>
        <SettingsGroupContent>
          <SettingsRow>
            <div className="font-mono text-xs text-gray-500">ID: {node.id}</div>
          </SettingsRow>
          <SettingsRow>
            <SettingsLabel htmlFor="name">Name</SettingsLabel>
            <SettingsInput
              id="name"
              value={data.title || "Fan Control"}
              onEndEdit={(value) => updateNestedData("title", value)}
              disabled={readOnly}
            />
          </SettingsRow>
        </SettingsGroupContent>
      </SettingsGroup>

      <SettingsGroup>
        <SettingsGroupHeader className="flex flex-row justify-between items-center -mb-2 pt-4">
          <span className="flex flex-grow">Fan Settings</span>
        </SettingsGroupHeader>
        <SettingsGroupContent>
          <SettingsRow className="flex flex-row justify-between items-center">
            <SettingsLabel htmlFor="hvacModes" className="flex-shrink-0">
              Fan Control Type
            </SettingsLabel>
            <ToggleGroup
              value={fanTypeValue}
              onValueChange={(values: "onoff" | "3speed") => {
                if (values) {
                  const previousFanType = fanTypeValue;
                  setFanTypeValue(values);
                  updateNestedData("fanType", values);

                  // Clear actions from buttons that will be hidden
                  if (previousFanType === "onoff" && values === "3speed") {
                    console.log("Clearing actions from via button");
                    deleteNestedData("via.onUpClick");
                  } else if (
                    previousFanType === "3speed" &&
                    values === "onoff"
                  ) {
                    console.log("Clearing actions from speed buttons");
                    deleteNestedData("viaHigh.onUpClick");
                    deleteNestedData("viaMed.onUpClick");
                    deleteNestedData("viaLow.onUpClick");
                  }
                }
              }}
              type="single"
              disabled={readOnly}
            >
              <ToggleGroupItem value="onoff" aria-label="Toggle On Off">
                On/Off
              </ToggleGroupItem>
              <ToggleGroupItem value="3speed" aria-label="Toggle 3 Speed">
                3-Speed
              </ToggleGroupItem>
            </ToggleGroup>
          </SettingsRow>
        </SettingsGroupContent>
      </SettingsGroup>

      <SettingsGroup className="mb-4">
        <SettingsGroupHeader className="flex flex-row justify-between items-center -mb-2 pt-4">
          <span className="flex flex-grow">Control</span>
        </SettingsGroupHeader>
        <SettingsGroupContent>
          <SettingsRow>
            <ConnectionRow
              via={data?.via}
              node={node}
              fanType={data?.fanType}
              onIconChange={(iconKey) => {
                updateNestedData("via.icon", iconKey);
              }}
              onNameChange={(value) => {
                updateNestedData("via.name", value);
              }}
              onShowLabelChange={(checked) => {
                updateNestedData("via.showLabel", checked);
              }}
              onLoadNameChange={(value) => {
                updateNestedData("via.loadName", value);
              }}
              onLoadIconChange={(iconKey) => {
                updateNestedData("via.loadIcon", iconKey);
              }}
            />
          </SettingsRow>
          {data.fanType === "3speed" && (
            <>
              <SettingsRow>
                <FanButtonRow
                  button={data.viaHigh}
                  node={node}
                  viaId="viaHigh"
                  label="Fan High"
                  onIconChange={(iconKey) => {
                    updateNestedData("viaHigh.icon", iconKey);
                  }}
                  onNameChange={(value) => {
                    updateNestedData("viaHigh.name", value);
                  }}
                  onShowLabelChange={(checked) => {
                    updateNestedData("viaHigh.showLabel", checked);
                  }}
                />
              </SettingsRow>

              <SettingsRow className="mt-4">
                <FanButtonRow
                  button={data.viaMed}
                  node={node}
                  viaId="viaMed"
                  label="Fan Medium"
                  onIconChange={(iconKey) => {
                    updateNestedData("viaMed.icon", iconKey);
                  }}
                  onNameChange={(value) => {
                    updateNestedData("viaMed.name", value);
                  }}
                  onShowLabelChange={(checked) => {
                    updateNestedData("viaMed.showLabel", checked);
                  }}
                />
              </SettingsRow>

              <SettingsRow className="mt-4">
                <FanButtonRow
                  button={data.viaLow}
                  node={node}
                  viaId="viaLow"
                  label="Fan Low"
                  onIconChange={(iconKey) => {
                    updateNestedData("viaLow.icon", iconKey);
                  }}
                  onNameChange={(value) => {
                    updateNestedData("viaLow.name", value);
                  }}
                  onShowLabelChange={(checked) => {
                    updateNestedData("viaLow.showLabel", checked);
                  }}
                />
              </SettingsRow>
            </>
          )}
        </SettingsGroupContent>
      </SettingsGroup>
    </div>
  );
}

interface ConnectionRowProps {
  node: GraphNodesByType["somoFanContainer"];
  via: SomoFanVia;
  fanType: "onoff" | "3speed";
  onIconChange: (iconKey: DeviceIconKey | undefined) => void;
  onNameChange: (name: string) => void;
  onLoadNameChange: (loadName: string) => void;
  onShowLabelChange: (checked: boolean) => void;
  onLoadIconChange: (iconKey: DeviceIconKey | undefined) => void;
}

function ConnectionRow({
  via,
  node,
  fanType,
  onIconChange,
  onNameChange,
  onLoadNameChange,
  onShowLabelChange,
  onLoadIconChange,
}: ConnectionRowProps) {
  const { nodes, readOnly } = useReactFlowContext();

  const { updateNestedData, deleteNestedData } = useData(
    node.data.dataId,
    "somoFan",
  );
  const dataKey = "via.onUpClick" as const;

  const actions = Object.values(via.onUpClick ?? {}).sort(
    (a, b) => a.sortIndex - b.sortIndex,
  );

  const connectableDevices = useConnectableDevices({
    nodes,
    nodeType: node.type,
    actions,
  }).filter((node) => node.type === "somoFan");

  return (
    <div className="flex flex-col gap-2">
      <div className="flex flex-row gap-2 text-xs font-bold text-gray-800 mt-2 items-center">
        <LampPopover
          activeIconKey={via.loadIcon}
          onIconClick={onLoadIconChange}
          disabled={readOnly}
        />
        <SettingsInput
          className="text-sm font-normal h-10"
          value={via.loadName}
          onEndEdit={onLoadNameChange}
          disabled={readOnly}
        />
      </div>

      {fanType === "onoff" && (
        <>
          <div className="flex flex-row gap-2 text-xs font-bold text-gray-800 mt-2 items-center">
            <LampPopover
              activeIconKey={via.icon}
              onIconClick={onIconChange}
              disabled={readOnly}
            />

            <SettingsInput
              className="text-sm font-normal h-10"
              value={via.name}
              onEndEdit={onNameChange}
              disabled={readOnly}
            />
            <div className="flex flex-row items-center gap-1 ml-3 w-[90px] flex-shrink-0">
              <Checkbox
                id="showLabel"
                checked={via.showLabel}
                onCheckedChange={onShowLabelChange}
                disabled={readOnly}
              />
              <Label
                htmlFor="showLabel"
                className="text-gray-500 text-xs font-semibold flex-shrink-0 truncate"
              >
                Show label
              </Label>
            </div>
          </div>

          <ActionSettingsWithHeader
            node={node}
            type="nothing"
            label="On click"
            sourceHandle="via"
            devices={connectableDevices}
            actions={actions}
            missingDeviceText="Add a fan to a section first"
            onDelete={(id) => deleteNestedData(`${dataKey}.${id}`)}
            onDeviceChange={(id, value) => {
              updateNestedData(`${dataKey}.${id}.deviceId`, value);
            }}
            onAddAction={(deviceId) => {
              const newActionId = randomId();
              const mapSize = Object.values(via.onUpClick).length;
              updateNestedData(`${dataKey}.${newActionId}`, {
                id: newActionId,
                sortIndex: mapSize,
                deviceId,
                targetValue: 100,
                onValue: 100,
                offValue: 0,
              });
            }}
          />
        </>
      )}
    </div>
  );
}

// Updated interface for the additional buttons
interface FanButtonRowProps {
  node: GraphNodesByType["somoFanContainer"];
  button: SomoFanButton;
  viaId: SomoFanViaId;
  label: string;
  onIconChange: (iconKey: DeviceIconKey | undefined) => void;
  onNameChange: (name: string) => void;
  onShowLabelChange: (checked: boolean) => void;
}

function FanButtonRow({
  node,
  button,
  viaId,
  label,
  onIconChange,
  onNameChange,
  onShowLabelChange,
}: FanButtonRowProps) {
  const { nodes, readOnly } = useReactFlowContext();

  const { updateNestedData, deleteNestedData } = useData(
    node.data.dataId,
    "somoFan",
  );
  const dataKey = `${viaId}.onUpClick` as const;

  const actions = Object.values(button.onUpClick ?? {}).sort(
    (a, b) => a.sortIndex - b.sortIndex,
  );

  const connectableDevices = useConnectableDevices({
    nodes,
    nodeType: node.type,
    actions,
  }).filter((node) => node.type === "somoFan");

  return (
    <div className="flex flex-col gap-2">
      <div className="flex flex-row gap-2 text-xs font-bold text-gray-800 mt-2 items-center justify-between">
        <Label className="text-gray-700">{label}</Label>
      </div>

      {button.enabled && (
        <>
          <div className="flex flex-row gap-2 text-xs font-bold text-gray-800 mt-2 items-center">
            <LampPopover
              activeIconKey={button.icon}
              onIconClick={onIconChange}
              disabled={readOnly}
            />

            <SettingsInput
              className="text-sm font-normal h-10"
              value={button.name}
              onEndEdit={onNameChange}
              disabled={readOnly}
            />
            <div className="flex flex-row items-center gap-1 ml-3 w-[90px] flex-shrink-0">
              <Checkbox
                id={`${viaId}-showLabel`}
                checked={button.showLabel}
                onCheckedChange={onShowLabelChange}
                disabled={readOnly}
              />
              <Label
                htmlFor={`${viaId}-showLabel`}
                className="text-gray-500 text-xs font-semibold truncate"
              >
                Show label
              </Label>
            </div>
          </div>

          <ActionSettingsWithHeader
            node={node}
            type="nothing"
            label="On click"
            sourceHandle={viaId}
            devices={connectableDevices}
            actions={actions}
            missingDeviceText="Add a fan to a section first"
            onDelete={(id) => deleteNestedData(`${dataKey}.${id}`)}
            onDeviceChange={(id, value) => {
              updateNestedData(`${dataKey}.${id}.deviceId`, value);
            }}
            onAddAction={(deviceId) => {
              const newActionId = randomId();
              const mapSize = Object.values(button.onUpClick).length;
              updateNestedData(`${dataKey}.${newActionId}`, {
                id: newActionId,
                sortIndex: mapSize,
                deviceId,
                targetValue: 100,
                onValue: 100,
                offValue: 0,
              });
            }}
          />
        </>
      )}
    </div>
  );
}
