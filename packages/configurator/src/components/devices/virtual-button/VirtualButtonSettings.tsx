import { useData } from "@/components/data/useData";
import { getDeviceDimmingSpeed } from "@/components/devices/deviceUtils";
import { useReactFlowContext } from "@/components/graph/ReactFlowContext";
import { ActionSettingsWithHeader } from "@/components/graph/settings/ActionSettings";
import { LampPopover } from "@/components/graph/settings/LampPopover";
import {
  SettingsGroup,
  SettingsGroupContent,
  SettingsGroupHeader,
  SettingsLabel,
  SettingsRow,
} from "@/components/graph/settings/SettingsGroup";
import { SettingsInput } from "@/components/graph/settings/SettingsInput";
import { DeviceIconKey } from "@/components/icons/DeviceIcons";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import {
  LayoutContainerNode,
  randomId,
  VirtualButtonVia,
  VirtualButtonViaId,
} from "@somo/shared";
import { useConnectableDevices } from "../useConnectableDevices";

export function VirtualButtonSettings({
  node,
}: {
  node: LayoutContainerNode<"virtualButtonContainer">;
}) {
  const { readOnly } = useReactFlowContext();

  const { data, updateNestedData } = useData(node.data.dataId, "virtualButton");
  if (!data) {
    return <div>Loading...</div>;
  }

  return (
    <div>
      <SettingsGroup>
        <SettingsGroupContent>
          <SettingsRow>
            <div className="font-mono text-xs text-gray-500">ID: {node.id}</div>
          </SettingsRow>
          <SettingsRow>
            <SettingsLabel htmlFor="name">Name</SettingsLabel>
            <SettingsInput
              id="name"
              value={data.title}
              onEndEdit={(value) => {
                updateNestedData("title", value);
              }}
              disabled={readOnly}
            />
          </SettingsRow>
        </SettingsGroupContent>
      </SettingsGroup>
      <ViaSettingsGroup node={node} viaId="via" name="Via" />
    </div>
  );
}

function ViaSettingsGroup({
  node,
  viaId,
  name,
}: {
  node: LayoutContainerNode<"virtualButtonContainer">;
  viaId: VirtualButtonViaId;
  name: string;
}) {
  const { readOnly } = useReactFlowContext();

  const { data, updateNestedData } = useData(node.data.dataId, "virtualButton");
  const via = data?.[viaId];
  if (!via) {
    return null;
  }

  return (
    <SettingsGroup>
      <SettingsGroupHeader className="flex flex-row justify-between items-center -mb-2 pt-4">
        <span className="flex flex-grow">{name}</span>
        <div className="flex flex-row items-center gap-1 w-[90px] flex-shrink-0">
          <Checkbox
            id={`${viaId}-enabled`}
            checked={via.enabled}
            onCheckedChange={(checked) => {
              updateNestedData(`${viaId}.enabled`, Boolean(checked));
            }}
            disabled={readOnly}
          />
          <Label
            htmlFor={`${viaId}-enabled`}
            className="text-gray-500 text-xs font-semibold flex-shrink-0 truncate"
          >
            Enabled
          </Label>
        </div>
      </SettingsGroupHeader>
      <SettingsGroupContent>
        <SettingsRow>
          <ConnectionRow
            node={node}
            via={via}
            viaId={viaId}
            onIconChange={(iconKey) => {
              updateNestedData(`${viaId}.icon`, iconKey);
            }}
            onNameChange={(value) => {
              updateNestedData(`${viaId}.name`, value);
            }}
            onShowLabelChange={(checked) => {
              updateNestedData(`${viaId}.showLabel`, checked);
            }}
            onLightNameChange={(value) => {
              updateNestedData(`${viaId}.lightName`, value);
            }}
            onLightIconChange={(iconKey) => {
              updateNestedData(`${viaId}.lightIcon`, iconKey);
            }}
          />
        </SettingsRow>
      </SettingsGroupContent>
    </SettingsGroup>
  );
}

interface ConnectionRowProps {
  node: LayoutContainerNode<"virtualButtonContainer">;
  via: VirtualButtonVia;
  viaId: VirtualButtonViaId;
  onIconChange: (iconKey: DeviceIconKey | undefined) => void;
  onNameChange: (name: string) => void;
  onLightNameChange: (lightName: string) => void;
  onShowLabelChange: (checked: boolean) => void;
  onLightIconChange: (iconKey: DeviceIconKey | undefined) => void;
}

function ConnectionRow({
  node,
  via,
  viaId,
  onIconChange,
  onNameChange,
  onLightNameChange,
  onShowLabelChange,
  onLightIconChange,
}: ConnectionRowProps) {
  const { nodes, readOnly } = useReactFlowContext();
  const { updateNestedData, deleteNestedData } = useData(
    node.data.dataId,
    "virtualButton",
  );
  const dataKey = `${viaId}.onUpClick` as const;

  const actions = Object.values(via.onUpClick ?? {}).sort(
    (a, b) => a.sortIndex - b.sortIndex,
  );

  const connectableDevices = useConnectableDevices({
    nodes,
    nodeType: node.type,
    actions,
  });

  if (!via.enabled) {
    return null;
  }

  return (
    <div className="flex flex-col gap-2">
      <div className="flex flex-row gap-2 text-xs font-bold text-gray-800 mt-2 items-center">
        <LampPopover
          activeIconKey={via.lightIcon}
          onIconClick={onLightIconChange}
          disabled={readOnly}
        />
        <SettingsInput
          className="text-sm font-normal h-10"
          value={via.lightName}
          onEndEdit={onLightNameChange}
          disabled={readOnly}
        />
      </div>
      <div className="flex flex-row gap-2 text-xs font-bold text-gray-800 mt-2 items-center">
        <LampPopover activeIconKey={via.icon} onIconClick={onIconChange} />

        <SettingsInput
          className="text-sm font-normal h-10"
          value={via.name}
          onEndEdit={onNameChange}
          disabled={readOnly}
        />
        <div className="flex flex-row items-center gap-1 ml-3 w-[90px] flex-shrink-0">
          <Checkbox
            id={`${viaId}-showLabel`}
            checked={via.showLabel}
            onCheckedChange={onShowLabelChange}
            disabled={readOnly}
          />
          <Label
            htmlFor={`${viaId}-showLabel`}
            className="text-gray-500 text-xs font-semibold flex-shrink-0 truncate"
          >
            Show label
          </Label>
        </div>
      </div>

      <ActionSettingsWithHeader
        type="onOff"
        label="On click"
        node={node}
        sourceHandle={viaId}
        devices={connectableDevices}
        actions={actions}
        onDelete={(id) => deleteNestedData(`${dataKey}.${id}`)}
        onDeviceChange={(id, deviceId) => {
          updateNestedData(`${dataKey}.${id}.deviceId`, deviceId);
        }}
        onDelayChange={(id, value) => {
          updateNestedData(`${dataKey}.${id}.delay`, value);
        }}
        onDimSpeedChange={(id, value) => {
          updateNestedData(`${dataKey}.${id}.dimSpeed`, value);
        }}
        onOnValueChange={(id, value) => {
          updateNestedData(`${dataKey}.${id}.onValue`, value);
        }}
        onOffValueChange={(id, value) => {
          updateNestedData(`${dataKey}.${id}.offValue`, value);
        }}
        onAddAction={(deviceId) => {
          const newActionId = randomId();
          const mapSize = Object.values(via.onUpClick).length;
          updateNestedData(`${dataKey}.${newActionId}`, {
            id: newActionId,
            sortIndex: mapSize,
            deviceId,
            dimSpeed: getDeviceDimmingSpeed(
              deviceId,
              connectableDevices,
              nodes,
            ),
            targetValue: 100,
            onValue: 100,
            offValue: 0,
            delay: 0,
            type: "lighting",
          });
        }}
      />
    </div>
  );
}
