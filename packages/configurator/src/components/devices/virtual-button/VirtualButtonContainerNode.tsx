import { useGetData } from "@/components/data/useData";
import {
  useHandlePosition,
  useHandleStyle,
} from "@/components/graph/position/useHandlePosition";
import { useReactFlowContext } from "@/components/graph/ReactFlowContext";
import { DeviceIcons } from "@/components/icons/DeviceIcons";
import { SomoButton } from "@/components/icons/SomoButton";
import { useExecutionContext } from "@/contexts/ExecutionContext";
import { cn } from "@/lib/classNames";
import {
  LayoutContainerNode,
  VirtualButton,
  VirtualButtonViaId,
} from "@somo/shared";
import {
  Handle,
  NodeProps,
  Position,
  useReactFlow,
  useUpdateNodeInternals,
} from "@xyflow/react";
import equal from "fast-deep-equal/react";
import React, { useEffect, useRef } from "react";

export const VirtualButtonContainerNode = React.memo(RegularNode, equal);
export const VirtualButtonContainerNodeSimulator = React.memo(
  SimulatorNode,
  equal,
);

function RegularNode(props: NodeProps<LayoutContainerNode>) {
  const data = useGetData(props.data.dataId, "virtualButton");
  if (!data) {
    return <></>;
  }

  const propsWithData = { ...props, data: { ...props.data, ...data } };
  return (
    <BaseNode {...propsWithData}>
      <ViaButton {...propsWithData} viaId="via" />
    </BaseNode>
  );
}

function SimulatorNode(props: NodeProps<LayoutContainerNode>) {
  const data = useGetData(props.data.dataId, "virtualButton");
  if (!data) {
    return <></>;
  }

  const propsWithData = { ...props, data: { ...props.data, ...data } };
  return (
    <BaseNode {...propsWithData}>
      <SimulatorViaButton {...propsWithData} viaId="via" />
    </BaseNode>
  );
}

type PropsWithData = NodeProps<LayoutContainerNode> & {
  data: VirtualButton;
};

function ViaButton(
  props: PropsWithData & {
    viaId: VirtualButtonViaId;
  },
) {
  const { readOnly } = useReactFlowContext();
  const position = useHandlePosition(props, props.viaId);
  const style = position ? useHandleStyle(position) : undefined;
  const via = props.data[props.viaId];
  if (!via.enabled) {
    return <></>;
  }

  const IconComponent = via.icon ? DeviceIcons[via.icon] : null;

  return (
    <div className="flex flex-col items-center w-full">
      <div className="relative size-5 flex items-center justify-center">
        <Handle
          id={props.viaId}
          type="source"
          isConnectable={!readOnly}
          position={position}
          style={style}
        />
        <SomoButton
          className={cn(
            "text-white size-5 flex-shrink-0",
            IconComponent && "opacity-0",
          )}
        />
        <div className="absolute">
          {IconComponent && <IconComponent className="text-white size-5" />}
        </div>
      </div>

      {via.showLabel && (
        <div className="text-[8px] text-white font-light truncate w-full text-center px-0.5">
          {via.name}
        </div>
      )}
    </div>
  );
}

/**
 * The Simulator button is similar to the default one, but it is clickable
 * and will emit the relevant events to test the button behavior.
 */
function SimulatorViaButton(
  props: PropsWithData & {
    viaId: VirtualButtonViaId;
  },
) {
  const { sendVirtualButtonCommand } = useExecutionContext();

  const position = useHandlePosition(props, props.viaId);
  const via = props.data[props.viaId];
  if (!via.enabled) {
    return <></>;
  }

  const IconComponent = via.icon ? DeviceIcons[via.icon] : null;

  return (
    <div
      className="flex flex-col items-center w-full group relative"
      onClick={() => sendVirtualButtonCommand(props.id)}
    >
      <div className="relative size-5 flex items-center justify-center">
        <Handle
          id={props.viaId}
          type="source"
          isConnectable={false}
          position={position}
          style={{
            border: "none",
            background: "transparent",
          }}
        />
        <SomoButton
          className={cn(
            "size-5 flex-shrink-0 group-hover:text-blue-300 text-white",
            IconComponent && "opacity-0",
          )}
        />
        <div className="absolute">
          {IconComponent && (
            <IconComponent className="size-5 group-hover:text-blue-300 text-white" />
          )}
        </div>
      </div>

      {via.showLabel && (
        <div className="text-[8px] font-light truncate w-full text-center px-0.5 group-hover:text-blue-300 text-white">
          {via.name}
        </div>
      )}
    </div>
  );
}

function BaseNode({
  id,
  selected,
  data,
  height,
  children,
}: PropsWithData & {
  children: React.ReactNode;
}) {
  const updateNodeInternals = useUpdateNodeInternals();
  const reactFlowInstance = useReactFlow();
  const ref = useRef<HTMLDivElement>(null);
  const zoom = reactFlowInstance.getViewport().zoom;

  useEffect(() => {
    updateNodeInternals(id);

    const size = ref.current?.getBoundingClientRect();
    if (size && size.height > 0 && height !== Math.floor(size.height / zoom)) {
      reactFlowInstance.updateNode(id, {
        height: Math.floor(size.height / zoom),
      });
    }
  }, [
    updateNodeInternals,
    id,
    height,
    selected,
    zoom,
    data.via.enabled,
    data.via.showLabel,
  ]);

  return (
    <div
      ref={ref}
      className={cn(
        "flex items-center justify-center bg-gray-600 rounded-md shadow px-1 py-2",
        "min-w-[60px] min-h-[30px]",
        selected && "outline outline-2 outline-blue-500 outline-offset-1",
      )}
    >
      <Handle
        type="target"
        position={Position.Bottom}
        isConnectable={false}
        style={{
          position: "absolute",
          background: "transparent",
          border: 0,
          color: "transparent",
          width: "2px",
          height: "2px",
          bottom: "2.5px",
        }}
      />
      <div className="flex flex-col items-center gap-1 w-full px-0.5">
        {children}
      </div>
    </div>
  );
}
