import { useData } from "@/components/data/useData";
import { useReactFlowContext } from "@/components/graph/ReactFlowContext";
import { ActionSettingsWithHeader } from "@/components/graph/settings/ActionSettings";
import { LampPopover } from "@/components/graph/settings/LampPopover";
import {
  SettingsGroup,
  SettingsGroupContent,
  SettingsGroupHeader,
  SettingsLabel,
  SettingsRow,
} from "@/components/graph/settings/SettingsGroup";
import { SettingsInput } from "@/components/graph/settings/SettingsInput";
import { DeviceIconKey } from "@/components/icons/DeviceIcons";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import {
  GraphNodesByType,
  PresenceSensorAction,
  PresenceSensorViaId,
  randomId,
} from "@somo/shared";
import { getDeviceDimmingSpeed } from "../deviceUtils";
import { useConnectableDevices } from "../useConnectableDevices";

export function PresenceSensorSettings({
  node,
}: {
  node: GraphNodesByType["presenceSensorContainer"];
}) {
  const { readOnly } = useReactFlowContext();
  const { data, updateNestedData } = useData(
    node.data.dataId,
    "presenceSensor",
  );
  if (!data) {
    return <></>;
  }

  return (
    <div>
      <SettingsGroup>
        <SettingsGroupContent>
          <SettingsRow>
            <div className="font-mono text-xs text-gray-500">ID: {node.id}</div>
          </SettingsRow>
          <SettingsRow>
            <SettingsLabel htmlFor="name">Name</SettingsLabel>
            <SettingsInput
              id="name"
              value={data.title}
              onEndEdit={(value) => {
                updateNestedData("title", value);
              }}
              disabled={readOnly}
            />
          </SettingsRow>
        </SettingsGroupContent>
      </SettingsGroup>

      <SettingsGroup className="mb-4">
        <SettingsGroupHeader className="flex flex-row justify-between items-center -mb-2 pt-4">
          <span className="flex flex-grow">Sensor Actions</span>
        </SettingsGroupHeader>
        <SettingsGroupContent>
          <SettingsRow>
            <AdditionalButtonRow
              node={node}
              button={data.onActivate}
              viaId="onActivate"
              label="On Activate"
              onIconChange={(iconKey) => {
                updateNestedData("onActivate.icon", iconKey);
              }}
              onNameChange={(value) => {
                updateNestedData("onActivate.name", value);
              }}
              onShowLabelChange={(checked) => {
                updateNestedData("onActivate.showLabel", checked);
              }}
              onEnabledChange={(checked) => {
                updateNestedData("onActivate.enabled", checked);
              }}
            />
          </SettingsRow>

          <SettingsRow className="mt-4">
            <AdditionalButtonRow
              node={node}
              button={data.onDeactivate}
              viaId="onDeactivate"
              label="On Deactivate"
              onIconChange={(iconKey) => {
                updateNestedData("onDeactivate.icon", iconKey);
              }}
              onNameChange={(value) => {
                updateNestedData("onDeactivate.name", value);
              }}
              onShowLabelChange={(checked) => {
                updateNestedData("onDeactivate.showLabel", checked);
              }}
              onEnabledChange={(checked) => {
                updateNestedData("onDeactivate.enabled", checked);
              }}
            />
          </SettingsRow>
        </SettingsGroupContent>
      </SettingsGroup>
    </div>
  );
}

// Updated interface for the additional buttons
interface AdditionalButtonRowProps {
  node: GraphNodesByType["presenceSensorContainer"];
  button: PresenceSensorAction;
  viaId: PresenceSensorViaId;
  label: string;
  onIconChange: (iconKey: DeviceIconKey | undefined) => void;
  onNameChange: (name: string) => void;
  onShowLabelChange: (checked: boolean) => void;
  onEnabledChange: (checked: boolean) => void;
}

function AdditionalButtonRow({
  node,
  button,
  viaId,
  label,
  onIconChange,
  onNameChange,
  onShowLabelChange,
  onEnabledChange,
}: AdditionalButtonRowProps) {
  const { nodes, readOnly } = useReactFlowContext();
  const { updateNestedData, deleteNestedData } = useData(
    node.data.dataId,
    "presenceSensor",
  );
  const dataKey = `${viaId}.onUpClick` as const;

  const actions = Object.values(button.onUpClick ?? {}).sort(
    (a, b) => a.sortIndex - b.sortIndex,
  );

  const connectableDevices = useConnectableDevices({
    nodes,
    nodeType: node.type,
    actions,
  });

  return (
    <div className="flex flex-col gap-2">
      <div className="flex flex-row gap-2 text-xs font-bold text-gray-800 mt-2 items-center justify-between">
        <Label className="text-gray-700">{label}</Label>
        <div className="flex flex-row items-center gap-1 w-[90px] flex-shrink-0">
          <Checkbox
            id={`${viaId}-enabled`}
            checked={button.enabled}
            onCheckedChange={onEnabledChange}
            disabled={readOnly}
          />
          <Label
            htmlFor={`${viaId}-enabled`}
            className="text-gray-500 text-xs font-semibold truncate"
          >
            Enabled
          </Label>
        </div>
      </div>

      {button.enabled && (
        <>
          <div className="flex flex-row gap-2 text-xs font-bold text-gray-800 mt-2 items-center">
            <LampPopover
              activeIconKey={button.icon}
              onIconClick={onIconChange}
              disabled={readOnly}
            />

            <SettingsInput
              className="text-sm font-normal h-10"
              value={button.name}
              onEndEdit={onNameChange}
              disabled={readOnly}
            />
            <div className="flex flex-row items-center gap-1 ml-3 w-[90px] flex-shrink-0">
              <Checkbox
                id={`${viaId}-showLabel`}
                checked={button.showLabel}
                onCheckedChange={onShowLabelChange}
                disabled={readOnly}
              />
              <Label
                htmlFor={`${viaId}-showLabel`}
                className="text-gray-500 text-xs font-semibold truncate"
              >
                Show label
              </Label>
            </div>
          </div>

          <SettingsRow className="flex flex-row justify-between items-center gap-2 pr-2.5 mt-2">
            <SettingsInput
              className="w-auto"
              inputClassName="w-[80px] text-center"
              label="Delay (sec)"
              value={button.offDelay.toString()}
              onEndEdit={(value) => {
                let numValue = parseInt(value, 10);
                if (isNaN(numValue)) {
                  numValue = 0;
                }
                updateNestedData(`${viaId}.offDelay`, numValue);
              }}
              disabled={readOnly}
            />
          </SettingsRow>

          <SettingsRow className="flex flex-row items-center gap-2 pr-2.5 mt-2">
            <Checkbox
              id={`${viaId}-cancel-on-activity`}
              checked={button.cancelOnActivityDuringDelay}
              onCheckedChange={(checked) => {
                updateNestedData(
                  `${viaId}.cancelOnActivityDuringDelay`,
                  Boolean(checked.valueOf()),
                );
              }}
              disabled={readOnly}
            />
            <Label
              htmlFor={`${viaId}-cancel-on-activity`}
              className="text-gray-500 text-xs font-semibold truncate"
            >
              Cancel on activity
            </Label>
          </SettingsRow>

          <ActionSettingsWithHeader
            type="targetValue"
            label="Execute"
            node={node}
            sourceHandle={viaId}
            devices={connectableDevices}
            actions={actions}
            onDelete={(id) => deleteNestedData(`${dataKey}.${id}`)}
            onDelayChange={(id, value) => {
              updateNestedData(`${dataKey}.${id}.delay`, value);
            }}
            onDeviceChange={(id, value) => {
              updateNestedData(`${dataKey}.${id}.deviceId`, value);
            }}
            onDimSpeedChange={(id, value) => {
              updateNestedData(`${dataKey}.${id}.dimSpeed`, value);
            }}
            onTargetValueChange={(id, value) => {
              updateNestedData(`${dataKey}.${id}.targetValue`, value);
            }}
            onAddAction={(deviceId) => {
              const newActionId = randomId();
              const mapSize = Object.values(button.onUpClick).length;
              updateNestedData(`${dataKey}.${newActionId}`, {
                id: newActionId,
                sortIndex: mapSize,
                deviceId,
                dimSpeed: getDeviceDimmingSpeed(
                  deviceId,
                  connectableDevices,
                  nodes,
                ),
                targetValue: 100,
                onValue: 100,
                offValue: 0,
                delay: 0,
                type: "lighting",
              });
            }}
          />
        </>
      )}
    </div>
  );
}
