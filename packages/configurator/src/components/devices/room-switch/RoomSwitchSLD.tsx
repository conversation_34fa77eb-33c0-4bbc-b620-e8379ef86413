import { RoomSwitchIcon } from "@/components/devices/room-switch/RoomSwitchIcon";
import { useGetData } from "@/components/data/useData";
import { GridConfig } from "@/lib/gridConfig";
import {
  Handle,
  Position,
  useNodeId,
  useUpdateNodeInternals,
} from "@xyflow/react";
import { useEffect } from "react";
import { SldComponent } from "../sld/types";

// Utility function to get connector styles
function getRoomSwitchConnectorStyle(handleId: string): {
  stroke: string;
  strokeWidth: number;
  strokeOpacity: number;
} {
  switch (handleId) {
    case "via3":
    case "via2":
    case "via1":
      return {
        stroke: "#eab308", // Yellow
        strokeWidth: 2.5,
        strokeOpacity: 0.7,
      };
    case "live":
      return {
        stroke: "#dc2626", // Red
        strokeWidth: 2,
        strokeOpacity: 0.7,
      };
    case "ground":
    case "ground-input":
    case "ground-output":
      return {
        stroke: "#10b981", // Green
        strokeWidth: 2,
        strokeOpacity: 0.9,
      };
    case "neutral":
      return {
        stroke: "#6b7280", // Gray
        strokeWidth: 3,
        strokeOpacity: 0.9,
      };
    default:
      return {
        stroke: "#4D9FC9", // Default blue
        strokeWidth: 2,
        strokeOpacity: 0.9,
      };
  }
}

// Connector configuration using GridConfig
const CONNECTORS = [
  {
    id: "via3",
    top: `${GridConfig.getHandleOffset(1)}px`,
    color: "#eab308",
    label: "Light 3",
    textColor: "text-yellow-600",
  },
  {
    id: "via2",
    top: `${GridConfig.getHandleOffset(2)}px`,
    color: "#eab308",
    label: "Light 2",
    textColor: "text-yellow-600",
  },
  {
    id: "via1",
    top: `${GridConfig.getHandleOffset(3)}px`,
    color: "#eab308",
    label: "Light 1",
    textColor: "text-yellow-600",
  },
  {
    id: "live",
    top: `${GridConfig.getHandleOffset(4)}px`,
    color: "#dc2626",
    label: "Line",
    textColor: "text-red-600",
  },
  {
    id: "ground",
    top: `${GridConfig.getHandleOffset(5)}px`,
    color: "#10b981",
    label: "Ground",
    textColor: "text-green-600",
  },
  {
    id: "neutral",
    top: `${GridConfig.getHandleOffset(6)}px`,
    color: "#6b7280",
    label: "Neutral (Opt)",
    textColor: "text-gray-600",
  },
];

// Reusable connector component
const Connector = ({
  id,
  top,
  color,
  label,
  textColor,
}: {
  id: string;
  top: string;
  color: string;
  label: string;
  textColor: string;
}) => (
  <>
    <Handle
      type="source"
      position={Position.Right}
      id={id}
      isConnectable={true}
      className="w-5 h-5 border-2 border-white rounded-full"
      style={{
        top,
        transform: "translateY(-50%)",
        backgroundColor: color,
      }}
    />
    <div
      className={`absolute right-2 transform -translate-y-1/2 text-xs font-semibold text-right whitespace-normal ${textColor}`}
      style={{
        top,
        lineHeight: "1.2",
        width: "90px",
        wordBreak: "normal",
      }}
    >
      {label}
    </div>
  </>
);

export const RoomSwitchSLD: SldComponent = ({ data, selected = false }) => {
  const nodeId = useNodeId();
  const roomSwitchData = useGetData(data.dataId, "roomSwitch");

  const updateNodeInternals = useUpdateNodeInternals();
  useEffect(() => {
    if (!nodeId) {
      return;
    }
    updateNodeInternals(nodeId);
  }, [nodeId, updateNodeInternals, roomSwitchData]);

  if (!roomSwitchData) {
    console.warn(
      "RoomSwitchSLD: No roomSwitch data found for dataId:",
      data.dataId,
    );
    return (
      <div className="w-20 h-20 bg-gray-200 border border-gray-300 rounded flex items-center justify-center text-xs text-gray-500">
        Loading...
      </div>
    );
  }

  const gridBasedHeight = 7 * GridConfig.GRID_SIZE;
  const gridBasedWidth = 11 * GridConfig.GRID_SIZE;

  return (
    <div
      className={`bg-white border-2 rounded-lg p-2 shadow-md cursor-move relative ${
        selected ? "border-blue-500 bg-blue-50" : "border-gray-300"
      }`}
      style={{
        minWidth: `${gridBasedWidth}px`,
        minHeight: `${gridBasedHeight}px`,
      }}
    >
      {/* Right side connectors */}
      {CONNECTORS.map((connector) => (
        <Connector key={connector.id} {...connector} />
      ))}

      {/* Switch Icon - Left aligned */}
      <div className="flex justify-start mb-2">
        <RoomSwitchIcon className="w-8 h-8 text-gray-700" />
      </div>

      {/* Device Name - Main identifier for SLD view */}
      <div
        className="text-sm font-semibold text-left mb-2 text-gray-800 leading-tight pr-16 whitespace-normal break-words"
        style={{
          width: `${gridBasedWidth}px`,
          overflowWrap: "break-word",
          wordWrap: "break-word",
        }}
      >
        {roomSwitchData.title}
      </div>

      {/* Device Type - Secondary info */}
      <div className="text-xs text-gray-500 text-left mb-2">Room Switch</div>
    </div>
  );
};

// Export the edge style function
RoomSwitchSLD.getEdgeStyle = getRoomSwitchConnectorStyle;
RoomSwitchSLD.overrideEdgeColor = true;
