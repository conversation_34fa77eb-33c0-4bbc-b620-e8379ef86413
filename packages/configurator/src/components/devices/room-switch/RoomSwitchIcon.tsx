export function RoomSwitchIcon({ className }: { className?: string }) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M4.64516 1.54839H19.3548C21.0651 1.54839 22.4516 2.93486 22.4516 4.64516V9.76471C22.4516 10.0571 22.6886 10.2941 22.981 10.2941H23.4706C23.763 10.2941 24 10.0571 24 9.76471V4.64516C24 2.07971 21.9203 0 19.3548 0H4.64516C2.07971 0 0 2.07971 0 4.64516V9.76471C0 10.0571 0.237026 10.2941 0.529412 10.2941H1.01897C1.31136 10.2941 1.54839 10.0571 1.54839 9.76471V4.64516C1.54839 2.93486 2.93486 1.54839 4.64516 1.54839Z"
        fill="currentColor"
      />
      <path
        d="M1.54839 14.2353C1.54839 13.9429 1.31136 13.7059 1.01898 13.7059H0.529412C0.237026 13.7059 0 13.9429 0 14.2353V19.3548C0 21.9203 2.07971 24 4.64516 24H19.3548C21.9203 24 24 21.9203 24 19.3548V14.2353C24 13.9429 23.763 13.7059 23.4706 13.7059H22.981C22.6886 13.7059 22.4516 13.9429 22.4516 14.2353V19.3548C22.4516 21.0651 21.0651 22.4516 19.3548 22.4516H4.64516C2.93486 22.4516 1.54839 21.0651 1.54839 19.3548V14.2353Z"
        fill="currentColor"
      />
      <path
        d="M12 17.3335H12.0075"
        stroke="currentColor"
        strokeWidth="1.33333"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M5.33398 9.87994C7.16739 8.24009 9.54088 7.3335 12.0007 7.3335C14.4604 7.3335 16.8339 8.24009 18.6673 9.87994"
        stroke="currentColor"
        strokeWidth="1.33333"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7.33398 12.5727C8.58018 11.3512 10.2556 10.667 12.0007 10.667C13.7457 10.667 15.4211 11.3512 16.6673 12.5727"
        stroke="currentColor"
        strokeWidth="1.33333"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9.66602 14.9526C10.2891 14.3419 11.1268 13.9998 11.9993 13.9998C12.8719 13.9998 13.7096 14.3419 14.3327 14.9526"
        stroke="currentColor"
        strokeWidth="1.33333"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
