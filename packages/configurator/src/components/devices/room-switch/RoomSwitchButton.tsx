import { useGetData } from "@/components/data/useData";
import { useReactFlowContext } from "@/components/graph/ReactFlowContext";
import { DeviceIcons } from "@/components/icons/DeviceIcons";
import { useExecutionContext } from "@/contexts/ExecutionContext";
import { cn } from "@/lib/classNames";
import { filterNodes, RoomSwitchDevice } from "@somo/shared";
import { <PERSON>le, Position } from "@xyflow/react";
import { match } from "ts-pattern";

interface Props {
  direction: "left" | "right";
  device: RoomSwitchDevice;
}

export function RoomSwitchButton(props: Props) {
  const { readOnly } = useReactFlowContext();
  return (
    <BaseButton {...props} isConnectable={!readOnly} className="text-white" />
  );
}

export function RoomSwitchButtonSimulator(props: Props) {
  const { isLightOn, sendSwitchCommand } = useExecutionContext();

  return (
    <BaseButton
      {...props}
      isConnectable={false}
      onClick={() => sendSwitchCommand(props.device.nodeId, props.device.viaId)}
      className={
        isLightOn(props.device.id)
          ? "bg-yellow-100 text-black shadow-md border-yellow-300 "
          : "bg-gray-600 text-white border-gray-700"
      }
    />
  );
}

function BaseButton({
  device,
  direction,
  isConnectable,
  onClick,
  className = "",
}: Props & {
  isConnectable: boolean;
  onClick?: () => void;
  className?: string;
}) {
  const { nodes } = useReactFlowContext();

  const switchNode = filterNodes(nodes, "roomSwitchContainer").find(
    (node) => node.id === device.nodeId,
  );

  if (!switchNode) {
    return (
      <div className="text-xs font-mono h-[40px] flex items-center justify-center px-2">
        Switch missing
      </div>
    );
  }

  const data = useGetData(switchNode?.data.dataId, "roomSwitch");
  if (!data) {
    return <></>;
  }

  const light = data[device.viaId];
  const { lightIcon, lightName } = light;
  const ActiveIconComponent = lightIcon ? DeviceIcons[lightIcon] : null;

  return (
    <div
      onClick={onClick}
      className={cn(
        "size-10 bg-gray-600 rounded-md shadow-md flex items-center justify-center min-w-[40px] gap-2 flex-shrink-0 px-2 relative",
        device.showLabel && "w-auto",
        className,
      )}
    >
      {ActiveIconComponent && (
        <ActiveIconComponent className="size-6 flex-shrink-0" />
      )}
      {device.showLabel && (
        <div className="text-xs flex-shrink-0 mr-2">{lightName}</div>
      )}
      <Handle
        type="target"
        id={device.id}
        isConnectable={isConnectable}
        position={match(device.anchorPosition)
          .with("bottom", () => Position.Bottom)
          .with("top", () => Position.Top)
          .with("left", () => Position.Left)
          .with("right", () => Position.Right)
          .with(undefined, () =>
            direction === "left" ? Position.Left : Position.Right,
          )
          .exhaustive()}
        style={{
          background: "white",
          border: "1px solid #4b5563",
          color: "transparent",
          width: 6,
          height: 6,
          minHeight: 6,
          minWidth: 6,
          borderRadius: "50%",
          boxShadow: "0 1px 2px rgba(0, 0, 0, 0.1)",
        }}
      />
    </div>
  );
}
