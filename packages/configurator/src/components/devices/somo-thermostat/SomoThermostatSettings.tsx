import { useData } from "@/components/data/useData";
import { useReactFlowContext } from "@/components/graph/ReactFlowContext";
import { LampPopover } from "@/components/graph/settings/LampPopover";
import {
  SettingsGroup,
  SettingsGroupContent,
  SettingsGroupHeader,
  SettingsLabel,
  SettingsRow,
} from "@/components/graph/settings/SettingsGroup";
import { SettingsInput } from "@/components/graph/settings/SettingsInput";
import { DeviceIconKey } from "@/components/icons/DeviceIcons";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { clamp } from "@/lib/math";
import {
  GraphNodesByType,
  SomoThermostat,
  SomoThermostatVia,
} from "@somo/shared";

interface ThermostatConnectionRowProps {
  via: SomoThermostatVia;
  onHvacIconChange: (iconKey: DeviceIconKey | undefined) => void;
  onHvacNameChange: (name: string) => void;
  readOnly: boolean;
}

function ThermostatConnectionRow({
  via,
  onHvacIconChange,
  onHvacNameChange,
  readOnly,
}: ThermostatConnectionRowProps) {
  return (
    <div className="flex flex-col gap-2">
      <div className="flex flex-row gap-2 text-xs font-bold text-gray-800 mt-2 items-center">
        <LampPopover
          activeIconKey={via.hvacIcon}
          onIconClick={onHvacIconChange}
          disabled={readOnly}
        />
        <SettingsInput
          className="text-sm font-normal h-10"
          value={via.hvacName}
          onEndEdit={onHvacNameChange}
          disabled={readOnly}
        />
      </div>
    </div>
  );
}

export function SomoThermostatSettings({
  node,
}: {
  node: GraphNodesByType["somoThermostatContainer"];
}) {
  const { reactFlowInstance, readOnly } = useReactFlowContext();

  const { data, updateNestedData } = useData(
    node.data.dataId,
    "somoThermostat",
  );
  if (!data) {
    return <div>Loading...</div>;
  }

  const celsiusToFahrenheit = (celsius: number) => (celsius * 9) / 5 + 32;
  const fahrenheitToCelsius = (fahrenheit: number) =>
    ((fahrenheit - 32) * 5) / 9;

  const updateTemperatureUnit = (
    newTempUnit: SomoThermostat["temperatureUnit"],
  ) => {
    const currentUnit = data.temperatureUnit;
    const currentMinTemp = data.minTemp;
    const currentMaxTemp = data.maxTemp;

    if (currentUnit !== newTempUnit) {
      let newMinTemp: number;
      let newMaxTemp: number;

      if (currentUnit === "C" && newTempUnit === "F") {
        newMinTemp = Math.round(celsiusToFahrenheit(currentMinTemp));
        newMaxTemp = Math.round(celsiusToFahrenheit(currentMaxTemp));
      } else {
        newMinTemp = Math.round(fahrenheitToCelsius(currentMinTemp));
        newMaxTemp = Math.round(fahrenheitToCelsius(currentMaxTemp));
      }

      const clampValues = getClampValues(newTempUnit);

      updateNestedData("temperatureUnit", newTempUnit);
      updateNestedData("minTemp", clamp(newMinTemp, clampValues));
      updateNestedData("maxTemp", clamp(newMaxTemp, clampValues));
    }
  };

  function getClampValues(unit: SomoThermostat["temperatureUnit"]) {
    return unit === "C" ? { min: 5, max: 35 } : { min: 41, max: 95 };
  }

  return (
    <div>
      <SettingsGroup>
        <SettingsGroupContent>
          <SettingsRow>
            <div className="font-mono text-xs text-gray-500">ID: {node.id}</div>
          </SettingsRow>
          <SettingsRow>
            <SettingsLabel htmlFor="name">Name</SettingsLabel>
            <SettingsInput
              id="name"
              value={data.title}
              onEndEdit={(value) => {
                if (typeof value !== "string") {
                  return;
                }
                reactFlowInstance?.updateNodeData(node.id, {
                  title: value,
                });
              }}
              disabled={readOnly}
            />
          </SettingsRow>
        </SettingsGroupContent>
      </SettingsGroup>

      <SettingsGroup>
        <SettingsGroupHeader className="flex flex-row justify-between items-center -mb-2 pt-4">
          <span className="flex flex-grow">Thermostat Settings</span>
        </SettingsGroupHeader>
        <SettingsGroupContent>
          <SettingsRow>
            <ThermostatConnectionRow
              via={data.via}
              onHvacIconChange={(iconKey) => {
                updateNestedData("via.hvacIcon", iconKey);
              }}
              onHvacNameChange={(value) => {
                updateNestedData("via.hvacName", value);
              }}
              readOnly={readOnly}
            />
          </SettingsRow>
          <SettingsRow className="flex flex-row justify-between items-center">
            <SettingsLabel htmlFor="tempUnit" className="flex-shrink-0">
              Temperature Unit
            </SettingsLabel>
            <RadioGroup
              defaultValue={data.temperatureUnit}
              onValueChange={updateTemperatureUnit}
              className="grid-flow-col"
              disabled={readOnly}
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="C" id="r1" />
                <Label htmlFor="r1">Celsius</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="F" id="r2" />
                <Label htmlFor="r2">Fahrenheit</Label>
              </div>
            </RadioGroup>
          </SettingsRow>
          <SettingsRow className="flex flex-row justify-between items-center">
            <SettingsLabel htmlFor="minTemp" className="flex-shrink-0">
              Min. Allowed Temperature (degrees)
            </SettingsLabel>
            <SettingsInput
              id="minTemp"
              value={data.minTemp.toString()}
              onEndEdit={(value) => {
                let numValue = parseFloat(value);
                if (isNaN(numValue)) {
                  numValue = 0;
                }
                updateNestedData(
                  "minTemp",
                  clamp(numValue, getClampValues(data.temperatureUnit)),
                );
              }}
              inputClassName="text-center"
              className="w-[100px]"
              disabled={readOnly}
            />
          </SettingsRow>
          <SettingsRow className="flex flex-row justify-between items-center">
            <SettingsLabel htmlFor="maxTemp" className="flex-shrink-0">
              Max. Allowed Temperature (degrees)
            </SettingsLabel>
            <SettingsInput
              id="maxTemp"
              value={data.maxTemp.toString()}
              onEndEdit={(value) => {
                let numValue = parseFloat(value);
                if (isNaN(numValue)) {
                  numValue = 0;
                }
                updateNestedData(
                  "maxTemp",
                  clamp(numValue, getClampValues(data.temperatureUnit)),
                );
              }}
              inputClassName="text-center"
              className="w-[100px]"
              disabled={readOnly}
            />
          </SettingsRow>
          <SettingsRow className="flex flex-row justify-between items-center">
            <SettingsLabel htmlFor="stepSize" className="flex-shrink-0">
              Setpoint Step Size
            </SettingsLabel>
            <SettingsInput
              id="stepSize"
              value={data.stepSize?.toString() ?? "1"}
              onEndEdit={(value) => {
                let numValue = parseFloat(value);
                if (isNaN(numValue) || numValue <= 0) {
                  numValue = 1;
                }
                updateNestedData("stepSize", numValue);
              }}
              inputClassName="text-center"
              className="w-[100px]"
              disabled={readOnly}
            />
          </SettingsRow>
          <SettingsRow className="flex flex-row justify-between items-center">
            <SettingsLabel htmlFor="hvacModes" className="flex-shrink-0">
              Allowed HVAC Modes
            </SettingsLabel>
            <ToggleGroup
              defaultValue={data.allowedModes}
              onValueChange={(values) => {
                updateNestedData(
                  "allowedModes",
                  values as ("fan" | "heat" | "cool" | "auto")[],
                );
              }}
              type="multiple"
              disabled={readOnly}
            >
              <ToggleGroupItem value="cool" aria-label="Toggle cool">
                Cool
              </ToggleGroupItem>
              <ToggleGroupItem value="heat" aria-label="Toggle heat">
                Heat
              </ToggleGroupItem>
              <ToggleGroupItem value="fan" aria-label="Toggle fan">
                Fan
              </ToggleGroupItem>
              <ToggleGroupItem value="auto" aria-label="Toggle auto">
                Auto
              </ToggleGroupItem>
            </ToggleGroup>
          </SettingsRow>
          <SettingsRow className="flex flex-row justify-between items-center">
            <SettingsLabel htmlFor="fanSpeeds" className="flex-shrink-0">
              Allowed Fan Speeds
            </SettingsLabel>
            <ToggleGroup
              defaultValue={data.allowedFanSpeeds}
              onValueChange={(values) => {
                updateNestedData(
                  "allowedFanSpeeds",
                  values as ("auto" | "low" | "medium" | "high")[],
                );
              }}
              type="multiple"
              disabled={readOnly}
            >
              <ToggleGroupItem value="low" aria-label="Toggle low">
                Low
              </ToggleGroupItem>
              <ToggleGroupItem value="medium" aria-label="Toggle medium">
                Medium
              </ToggleGroupItem>
              <ToggleGroupItem value="high" aria-label="Toggle high">
                High
              </ToggleGroupItem>
              <ToggleGroupItem value="auto" aria-label="Toggle auto">
                Auto
              </ToggleGroupItem>
            </ToggleGroup>
          </SettingsRow>
        </SettingsGroupContent>
      </SettingsGroup>
    </div>
  );
}
