import { useGetData } from "@/components/data/useData";
import {
  useHandlePosition,
  useHandleStyle,
} from "@/components/graph/position/useHandlePosition";
import { DeviceIcons } from "@/components/icons/DeviceIcons";
import { SomoButton } from "@/components/icons/SomoButton";
import { useExecutionContext } from "@/contexts/ExecutionContext";
import { cn } from "@/lib/classNames";
import {
  LayoutContainerNode,
  SomoThermostat,
  SomoThermostatViaId,
} from "@somo/shared";
import {
  Handle,
  NodeProps,
  Position,
  useReactFlow,
  useUpdateNodeInternals,
} from "@xyflow/react";
import equal from "fast-deep-equal/react";
import React, { useEffect, useRef } from "react";
import { match, P } from "ts-pattern";

export const SomoThermostatContainerNode = React.memo(RegularNode, equal);
export const SomoThermostatContainerNodeSimulator = React.memo(
  SimulatorNode,
  equal,
);

function RegularNode(props: NodeProps<LayoutContainerNode>) {
  const data = useGetData(props.data.dataId, "somoThermostat");
  if (!data) {
    return <></>;
  }
  const propsWithData = { ...props, data: { ...props.data, ...data } };
  return (
    <BaseNode {...propsWithData}>
      <div className="flex flex-row items-start w-full">
        {/* Up/Down buttons in a column */}
        <div className="flex flex-col gap-2">
          <ViaButton {...propsWithData} viaId="viaUp" />
          <ViaButton {...propsWithData} viaId="viaDown" />
        </div>
        <div className="flex flex-col gap-2">
          {/* Mode buttons in a row, to the right */}
          <div className="flex flex-row gap-2">
            <ViaButton {...propsWithData} viaId="modeCool" />
            <ViaButton {...propsWithData} viaId="modeHeat" />
            <ViaButton {...propsWithData} viaId="modeFan" />
            <ViaButton {...propsWithData} viaId="modeAuto" />
          </div>
          {/* Mode buttons in a row, below the mode buttons */}
          <div className="flex flex-row gap-2">
            <ViaButton {...propsWithData} viaId="fanLow" />
            <ViaButton {...propsWithData} viaId="fanMedium" />
            <ViaButton {...propsWithData} viaId="fanHigh" />
            <ViaButton {...propsWithData} viaId="fanAuto" />
          </div>
        </div>
      </div>
    </BaseNode>
  );
}

function SimulatorNode(props: NodeProps<LayoutContainerNode>) {
  const data = useGetData(props.data.dataId, "somoThermostat");
  if (!data) {
    return <></>;
  }
  const propsWithData = { ...props, data: { ...props.data, ...data } };
  return (
    <BaseNode {...propsWithData}>
      <div className="flex flex-row items-start w-full">
        {/* Up/Down buttons in a column */}
        <div className="flex flex-col gap-2">
          <SimulatorViaButton {...propsWithData} viaId="viaUp" />
          <SimulatorViaButton {...propsWithData} viaId="viaDown" />
        </div>
        {/* Mode buttons in a row, to the right */}
        <div className="flex flex-col gap-2">
          <div className="flex flex-row gap-2">
            <SimulatorViaButton {...propsWithData} viaId="modeCool" />
            <SimulatorViaButton {...propsWithData} viaId="modeHeat" />
            <SimulatorViaButton {...propsWithData} viaId="modeFan" />
            <SimulatorViaButton {...propsWithData} viaId="modeAuto" />
          </div>
          {/* Mode buttons in a row, below the mode buttons */}
          <div className="flex flex-row gap-2">
            <SimulatorViaButton {...propsWithData} viaId="fanLow" />
            <SimulatorViaButton {...propsWithData} viaId="fanMedium" />
            <SimulatorViaButton {...propsWithData} viaId="fanHigh" />
            <SimulatorViaButton {...propsWithData} viaId="fanAuto" />
          </div>
        </div>
      </div>
    </BaseNode>
  );
}

type PropsWithData = NodeProps<LayoutContainerNode> & {
  data: SomoThermostat;
};

function ViaButton(props: PropsWithData & { viaId: SomoThermostatViaId }) {
  const position = useHandlePosition(props, props.viaId);
  const style = position ? useHandleStyle(position) : undefined;
  const via = props.data[props.viaId];
  if ("enabled" in via && !via.enabled) {
    return <></>;
  }

  const IconComponent = via.icon ? DeviceIcons[via.icon] : null;

  return (
    <div className="flex flex-col items-center w-full">
      <div className="relative h-[20px] w-[20px] flex items-center justify-center">
        <Handle
          id={props.viaId}
          type="source"
          isConnectable={false}
          position={position}
          style={style}
        />
        <SomoButton
          className={cn(
            "text-white w-[19px] h-[19px] flex-shrink-0",
            IconComponent && "opacity-0",
          )}
        />
        <div className="absolute">
          {IconComponent && (
            <IconComponent className="text-white w-[19px] h-[19px]" />
          )}
        </div>
      </div>

      {via.showLabel && (
        <div className="text-[8px] text-white font-light truncate w-full text-center px-0.5">
          {via.name}
        </div>
      )}
    </div>
  );
}

/**
 * The Simulator button is similar to the default one, but it is clickable
 * and will emit the relevant events to test the button behavior.
 */
function SimulatorViaButton(
  props: PropsWithData & {
    viaId: SomoThermostatViaId;
  },
) {
  const {
    getThermostatSetpoint,
    getThermostatMode,
    getThermostatFanSpeed,
    sendThermostatCommand,
  } = useExecutionContext();
  const brightness = getThermostatSetpoint(props.id, props.viaId);
  const mode = getThermostatMode(props.id, props.viaId);
  const fanSpeed = getThermostatFanSpeed(props.id, props.viaId);
  const isOn = match(props.viaId)
    .with(P.string.startsWith("via"), () => brightness > 0)
    .with(
      P.string.startsWith("mode"),
      () => mode === props.viaId.replace("mode", "").toLowerCase(),
    )
    .with(
      P.string.startsWith("fan"),
      () => fanSpeed === props.viaId.replace("fan", "").toLowerCase(),
    )
    .exhaustive();

  const position = useHandlePosition(props, props.viaId);
  const via = props.data[props.viaId];
  if ("enabled" in via && !via.enabled) {
    return <></>;
  }

  const command = match(props.viaId)
    .with(P.string.startsWith("mode"), () => "mode")
    .with(P.string.startsWith("via"), () => "setpoint")
    .with(P.string.startsWith("fan"), () => "fanSpeed")
    .exhaustive() as "mode" | "setpoint" | "fanSpeed";

  const IconComponent = via.icon ? DeviceIcons[via.icon] : null;

  return (
    <div
      className="flex flex-col items-center w-full group relative"
      onClick={() =>
        sendThermostatCommand(
          command,
          props.id,
          props.viaId,
          props.data.minTemp,
          props.data.maxTemp,
          props.data.stepSize,
        )
      }
    >
      <div className="relative h-[20px] w-[20px] flex items-center justify-center">
        <Handle
          id={props.viaId}
          type="source"
          isConnectable={false}
          position={position}
          style={{
            border: "none",
            background: "transparent",
          }}
        />
        <SomoButton
          className={cn(
            "w-[19px] h-[19px] flex-shrink-0 group-hover:text-blue-300",
            isOn ? "text-yellow-400" : "text-white",
            IconComponent && "opacity-0",
          )}
        />
        <div className="absolute">
          {IconComponent && (
            <IconComponent
              className={cn(
                "w-[19px] h-[19px] group-hover:text-blue-300",
                isOn ? "text-yellow-400" : "text-white",
              )}
            />
          )}
        </div>
      </div>

      {via.showLabel && (
        <div
          className={cn(
            "text-[8px] font-light truncate w-full text-center px-0.5 group-hover:text-blue-300",
            isOn ? "text-yellow-400" : "text-white",
          )}
        >
          {via.name}
        </div>
      )}
    </div>
  );
}

function BaseNode({
  id,
  selected,
  data,
  height,
  children,
}: PropsWithData & { children: React.ReactNode }) {
  const updateNodeInternals = useUpdateNodeInternals();
  const reactFlowInstance = useReactFlow();
  const ref = useRef<HTMLDivElement>(null);
  const zoom = reactFlowInstance.getViewport().zoom;

  useEffect(() => {
    updateNodeInternals(id);

    const size = ref.current?.getBoundingClientRect();
    if (size && size.height > 0 && height !== Math.floor(size.height / zoom)) {
      reactFlowInstance.updateNode(id, {
        height: Math.floor(size.height / zoom),
      });
    }
  }, [
    updateNodeInternals,
    id,
    height,
    selected,
    zoom,
    data.via.showLabel,
    data.viaUp.enabled,
    data.viaUp.showLabel,
    data.viaDown.enabled,
    data.viaDown.showLabel,
    data.modeCool.enabled,
    data.modeCool.showLabel,
    data.modeHeat.enabled,
    data.modeHeat.showLabel,
    data.modeAuto.enabled,
    data.modeAuto.showLabel,
    data.modeFan.enabled,
    data.modeFan.showLabel,
  ]);

  return (
    <div
      ref={ref}
      className={cn(
        "flex items-center justify-center bg-gray-600 rounded-md shadow px-1 py-2",
        "min-h-[30px] w-auto",
        "max-w-full",
        "overflow-visible",
        selected && "outline outline-2 outline-blue-500 outline-offset-1",
      )}
    >
      <Handle
        type="target"
        position={Position.Bottom}
        isConnectable={false}
        style={{
          position: "absolute",
          background: "transparent",
          border: 0,
          color: "transparent",
          width: "2px",
          height: "2px",
          bottom: "2.5px",
        }}
      />
      <div className="flex flex-col items-center gap-2 w-auto px-0.5 flex-grow">
        {children}
      </div>
    </div>
  );
}
