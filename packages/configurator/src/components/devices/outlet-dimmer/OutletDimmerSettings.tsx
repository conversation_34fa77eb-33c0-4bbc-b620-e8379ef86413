import { useData } from "@/components/data/useData";
import { useReactFlowContext } from "@/components/graph/ReactFlowContext";
import { DimmingCurve } from "@/components/graph/settings/DimmingCurve";
import {
  SettingsGroup,
  SettingsGroupContent,
  SettingsGroupHeader,
  SettingsLabel,
  SettingsRow,
} from "@/components/graph/settings/SettingsGroup";
import { SettingsInput } from "@/components/graph/settings/SettingsInput";
import { clamp } from "@/lib/math";
import { LayoutContainerNode } from "@somo/shared";

export function OutletDimmerSettings({
  node,
}: {
  node: LayoutContainerNode<"outletDimmerContainer">;
}) {
  const { readOnly } = useReactFlowContext();
  const { data, updateNestedData } = useData(node.data.dataId, "outletDimmer");

  if (!data) {
    return <div>Loading...</div>;
  }

  return (
    <div>
      <SettingsGroup>
        <SettingsGroupContent>
          <SettingsRow>
            <div className="font-mono text-xs text-gray-500">ID: {node.id}</div>
          </SettingsRow>
          <SettingsRow>
            <SettingsLabel htmlFor="name">Name</SettingsLabel>
            <SettingsInput
              id="name"
              value={data.name}
              onEndEdit={(value) => updateNestedData("name", value)}
              disabled={readOnly}
            />
          </SettingsRow>
        </SettingsGroupContent>
      </SettingsGroup>

      <SettingsGroup>
        <SettingsGroupHeader className="flex flex-row justify-between items-center -mb-2 pt-4">
          <span className="flex flex-grow">Outlet Dimmer Settings</span>
        </SettingsGroupHeader>
        <SettingsGroupContent>
          <SettingsRow className="flex flex-row justify-between items-center">
            <SettingsLabel htmlFor="dimSpeed" className="flex-shrink-0">
              Dim Speed (sec)
            </SettingsLabel>
            <SettingsInput
              id="dimSpeed"
              value={data.dimSpeed.toString()}
              onEndEdit={(value) => {
                let numValue = parseFloat(value);
                if (isNaN(numValue)) {
                  numValue = 0;
                }
                updateNestedData(
                  "dimSpeed",
                  clamp(numValue, { min: 0.1, max: 5 }),
                );
              }}
              inputClassName="text-center"
              className="w-[100px]"
              disabled={readOnly}
            />
          </SettingsRow>
        </SettingsGroupContent>
      </SettingsGroup>

      <SettingsGroup>
        <SettingsGroupContent>
          <DimmingCurve
            points={data.dimmingCurve.points}
            onChange={(newPoints) =>
              updateNestedData("dimmingCurve.points", newPoints)
            }
            curveType={data.dimmingCurve.type}
            onCurveTypeChange={(newType) =>
              updateNestedData("dimmingCurve.type", newType)
            }
            className="mt-2"
            disabled={readOnly}
          />
        </SettingsGroupContent>
      </SettingsGroup>

      {/* TODO: make it possible to create/delete 1 ODL from here */}
      {/* <SettingsGroup className="mb-4">
        <SettingsGroupHeader className="flex flex-row justify-between items-center -mb-2 pt-4">
          <span className="flex flex-grow">Control</span>
        </SettingsGroupHeader>
        <SettingsGroupContent>
          <SettingsRow>
            <ConnectionRow
              via={data.via}
              onLightNameChange={(value) => {
                updateNestedData("via.lightName", value);
              }}
              onLightIconChange={(iconKey) => {
                updateNestedData("via.lightIcon", iconKey);
              }}
            />
          </SettingsRow>
        </SettingsGroupContent>
      </SettingsGroup> */}
    </div>
  );
}

// interface ConnectionRowProps {
//   via: OutletDimmerVia;
//   onLightNameChange: (lightName: string) => void;
//   onLightIconChange: (iconKey: DeviceIconKey | undefined) => void;
// }

// function ConnectionRow({
//   via,
//   onLightNameChange,
//   onLightIconChange,
// }: ConnectionRowProps) {
//   const { readOnly } = useReactFlowContext();

//   return (
//     <div className="flex flex-col gap-2">
//       <div className="flex flex-row gap-2 text-xs font-bold text-gray-800 mt-2 items-center">
//         <LampPopover
//           activeIconKey={via.lightIcon}
//           onIconClick={onLightIconChange}
//           disabled={readOnly}
//         />
//         <SettingsInput
//           className="text-sm font-normal h-10"
//           value={via.lightName}
//           onEndEdit={onLightNameChange}
//           disabled={readOnly}
//         />
//       </div>
//     </div>
//   );
// }
