import { createDataFromNode } from "@/components/data/createData";
import { getData, useData } from "@/components/data/useData";
import { useReactFlowContext } from "@/components/graph/ReactFlowContext";
import { DimmingCurve } from "@/components/graph/settings/DimmingCurve";
import {
  SettingsGroup,
  SettingsGroupContent,
  SettingsGroupHeader,
  SettingsLabel,
  SettingsRow,
} from "@/components/graph/settings/SettingsGroup";
import { SettingsInput } from "@/components/graph/settings/SettingsInput";
import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { clamp } from "@/lib/math";
import { LayoutContainerNode, filterNodes, randomId } from "@somo/shared";
import { ArrowRightSquareIcon, PlusIcon, Trash2Icon } from "lucide-react";
import { useMemo } from "react";

export function OutletDimmerSettings({
  node,
}: {
  node: LayoutContainerNode<"outletDimmerContainer">;
}) {
  const { readOnly, nodes, yDoc, reactFlowInstance } = useReactFlowContext();
  const { data, updateNestedData } = useData(node.data.dataId, "outletDimmer");

  const relatedLight = useMemo(() => {
    if (!data) {
      return null;
    }

    const lightNodes = filterNodes(nodes, "outletDimmerLight");
    for (const lightNode of lightNodes) {
      const lightData = getData(
        yDoc,
        lightNode.data.dataId,
        "outletDimmerLight",
      );
      if (lightData?.controllerId === data.id) {
        return { node: lightNode, data: lightData };
      }
    }
    return null;
  }, [nodes, yDoc, data?.id]);

  const createOutletDimmerLight = () => {
    if (!data || !yDoc || !reactFlowInstance) {
      return;
    }

    const lightNode: LayoutContainerNode<"outletDimmerLight"> = {
      id: randomId(),
      type: "outletDimmerLight",
      data: { dataId: "replaced at runtime" },
      position: {
        x: node.position.x + 200,
        y: node.position.y,
      },
      width: 16,
      height: 16,
      selected: true,
      selectable: true,
      draggable: true,
      deletable: true,
    };

    const dataId = createDataFromNode(yDoc, lightNode, {
      controllerId: data.id,
      name: `${data.name} Light`,
    });

    if (dataId) {
      lightNode.data.dataId = dataId;
      reactFlowInstance.setNodes((nodes) => [...nodes, lightNode]);
    }
  };

  const selectLight = () => {
    if (!relatedLight || !reactFlowInstance) {
      return;
    }

    // Select and center on the light node
    reactFlowInstance.setNodes((nodes) =>
      nodes.map((n) => ({
        ...n,
        selected: n.id === relatedLight.node.id,
      })),
    );

    reactFlowInstance.setCenter(
      relatedLight.node.position.x,
      relatedLight.node.position.y,
      {
        duration: 800,
        zoom: reactFlowInstance.getViewport().zoom ?? 1,
      },
    );
  };

  const deleteLight = () => {
    if (!relatedLight || !reactFlowInstance) {
      return;
    }

    // TODO: is it enough to delete from Data too?
    reactFlowInstance.setNodes((nodes) =>
      nodes.filter((n) => n.id !== relatedLight.node.id),
    );
  };

  if (!data) {
    return <div>Loading...</div>;
  }

  return (
    <div>
      <SettingsGroup>
        <SettingsGroupContent>
          <SettingsRow>
            <div className="font-mono text-xs text-gray-500">ID: {node.id}</div>
          </SettingsRow>
          <SettingsRow>
            <SettingsLabel htmlFor="name">Name</SettingsLabel>
            <SettingsInput
              id="name"
              value={data.name}
              onEndEdit={(value) => updateNestedData("name", value)}
              disabled={readOnly}
            />
          </SettingsRow>
        </SettingsGroupContent>
      </SettingsGroup>

      <SettingsGroup>
        <SettingsGroupHeader className="flex flex-row justify-between items-center -mb-2 pt-4">
          <span className="flex flex-grow">Outlet Dimmer Settings</span>
        </SettingsGroupHeader>
        <SettingsGroupContent>
          <SettingsRow className="flex flex-row justify-between items-center">
            <SettingsLabel htmlFor="dimSpeed" className="flex-shrink-0">
              Dim Speed (sec)
            </SettingsLabel>
            <SettingsInput
              id="dimSpeed"
              value={data.dimSpeed.toString()}
              onEndEdit={(value) => {
                let numValue = parseFloat(value);
                if (isNaN(numValue)) {
                  numValue = 0;
                }
                updateNestedData(
                  "dimSpeed",
                  clamp(numValue, { min: 0.1, max: 5 }),
                );
              }}
              inputClassName="text-center"
              className="w-[100px]"
              disabled={readOnly}
            />
          </SettingsRow>
        </SettingsGroupContent>
      </SettingsGroup>

      <SettingsGroup>
        <SettingsGroupContent>
          <DimmingCurve
            points={data.dimmingCurve.points}
            onChange={(newPoints) =>
              updateNestedData("dimmingCurve.points", newPoints)
            }
            curveType={data.dimmingCurve.type}
            onCurveTypeChange={(newType) =>
              updateNestedData("dimmingCurve.type", newType)
            }
            className="mt-2"
            disabled={readOnly}
          />
        </SettingsGroupContent>
      </SettingsGroup>

      <SettingsGroup className="mb-4">
        <SettingsGroupHeader className="flex flex-row justify-between items-center -mb-2 pt-4">
          <span className="flex flex-grow">Control</span>
        </SettingsGroupHeader>
        <SettingsGroupContent>
          <SettingsRow>
            {relatedLight ? (
              <div className="flex flex-row gap-0.5 text-xs font-bold text-gray-800 cursor-pointer items-center w-full">
                <div
                  className="flex-grow flex gap-2 hover:text-gray-400"
                  onClick={selectLight}
                >
                  <span>{relatedLight.data.name}</span>
                  <ArrowRightSquareIcon
                    className="size-4 flex-shrink-0 mr-1 opacity-100 hover:opacity-40 cursor-pointer"
                    onClick={selectLight}
                  />
                </div>

                {!readOnly && (
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="flex-shrink-0 -mr-1.5"
                        onClick={deleteLight}
                      >
                        <Trash2Icon className="size-4 flex-shrink-0" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Delete Light</TooltipContent>
                  </Tooltip>
                )}
              </div>
            ) : (
              <div className="flex flex-row gap-2 items-center w-full">
                <span className="text-xs text-gray-500 flex-grow">
                  No outlet dimmer light connected
                </span>
                {!readOnly && (
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="flex-shrink-0"
                        onClick={createOutletDimmerLight}
                      >
                        <PlusIcon className="size-4 flex-shrink-0" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Create Light</TooltipContent>
                  </Tooltip>
                )}
              </div>
            )}
          </SettingsRow>
        </SettingsGroupContent>
      </SettingsGroup>
    </div>
  );
}
