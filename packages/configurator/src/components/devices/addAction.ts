import { objectToYMap } from "@/lib/yjsUtils";
import {
  DoorSensorVia,
  MomentaryCanBusController,
  OutletDimmerVia,
  PresenceSensorVia,
  randomId,
  RoomDimmerVia,
  RoomSwitchVia,
  SceneVia,
  SomoFanVia,
  SomoIrControllerVia,
  SomoShadesVia,
  SomoThermostatVia,
  ToggleCanBusController,
  VirtualButtonVia,
} from "@somo/shared";
import * as Y from "yjs";

/**
 * Add action to a device via map.
 *
 * This function ensures the operation is type-safe.
 */
export function addAction(viaMap: Y.Map<any>, config: Config) {
  let newActionMap = viaMap.get(config.key) as Y.Map<any> | undefined;
  if (!newActionMap) {
    newActionMap = new Y.Map();
    viaMap.set(config.key, newActionMap);
  }

  const newAction = {
    id: randomId(),
    sortIndex: newActionMap.size,
    ...config.params,
  };

  newActionMap.set(newAction.id, objectToYMap(newAction));
}

type Config =
  | {
      device: "virtualButton";
      key: "onUpClick";
      params: ActionParams<VirtualButtonVia, "onUpClick">;
    }
  | {
      device: "toggleCanBusController";
      key: "onDownClick";
      params: ActionParams<ToggleCanBusController, "onDownClick">;
    }
  | {
      device: "toggleCanBusController";
      key: "onDownHold";
      params: ActionParams<ToggleCanBusController, "onDownHold">;
    }
  | {
      device: "toggleCanBusController";
      key: "onUpClick";
      params: ActionParams<ToggleCanBusController, "onUpClick">;
    }
  | {
      device: "toggleCanBusController";
      key: "onUpHold";
      params: ActionParams<ToggleCanBusController, "onUpHold">;
    }
  | {
      device: "toggleCanBusController";
      key: "onUpPress";
      params: ActionParams<ToggleCanBusController, "onUpPress">;
    }
  | {
      device: "toggleCanBusController";
      key: "onUpRelease";
      params: ActionParams<ToggleCanBusController, "onUpRelease">;
    }
  | {
      device: "toggleCanBusController";
      key: "onDownPress";
      params: ActionParams<ToggleCanBusController, "onDownPress">;
    }
  | {
      device: "toggleCanBusController";
      key: "onDownRelease";
      params: ActionParams<ToggleCanBusController, "onDownRelease">;
    }
  | {
      device: "toggleCanBusController";
      key: "onUpHoldRelease";
      params: ActionParams<ToggleCanBusController, "onUpHoldRelease">;
    }
  | {
      device: "toggleCanBusController";
      key: "onDownHoldRelease";
      params: ActionParams<ToggleCanBusController, "onDownHoldRelease">;
    }
  | {
      device: "momentaryCanBusController";
      key: "onUpClick";
      params: ActionParams<MomentaryCanBusController, "onUpClick">;
    }
  | {
      device: "momentaryCanBusController";
      key: "onUpHold";
      params: ActionParams<MomentaryCanBusController, "onUpHold">;
    }
  | {
      device: "momentaryCanBusController";
      key: "onUpPress";
      params: ActionParams<MomentaryCanBusController, "onUpPress">;
    }
  | {
      device: "momentaryCanBusController";
      key: "onUpRelease";
      params: ActionParams<MomentaryCanBusController, "onUpRelease">;
    }
  | {
      device: "momentaryCanBusController";
      key: "onUpHoldRelease";
      params: ActionParams<MomentaryCanBusController, "onUpHoldRelease">;
    }
  | {
      device: "roomSwitch";
      key: "onUpClick";
      params: ActionParams<RoomSwitchVia, "onUpClick">;
    }
  | {
      device: "roomDimmer";
      key: "onUpClick";
      params: ActionParams<RoomDimmerVia, "onUpClick">;
    }
  | {
      device: "roomDimmer";
      key: "onUpHold";
      params: ActionParams<RoomDimmerVia, "onUpHold">;
    }
  | {
      device: "outletDimmer";
      key: "onUpClick";
      params: ActionParams<OutletDimmerVia, "onUpClick">;
    }
  | {
      device: "somoShades";
      key: "onUpClick";
      params: ActionParams<SomoShadesVia, "onUpClick">;
    }
  | {
      device: "somoFan";
      key: "onUpClick";
      params: ActionParams<SomoFanVia, "onUpClick">;
    }
  | {
      device: "somoThermostat";
      key: "onUpClick";
      params: ActionParams<SomoThermostatVia, "onUpClick">;
    }
  | {
      device: "somoIrController";
      key: "onUpClick";
      params: ActionParams<SomoIrControllerVia, "onUpClick">;
    }
  | {
      device: "presenceSensor";
      key: "onUpClick";
      params: ActionParams<PresenceSensorVia, "onUpClick">;
    }
  | {
      device: "doorSensor";
      key: "onUpClick";
      params: ActionParams<DoorSensorVia, "onUpClick">;
    }
  | {
      device: "reedSwitchSensor";
      key: "onOpen";
      params: ActionParams<
        { onOpen: Record<string, any>; onClose: Record<string, any> },
        "onOpen"
      >;
    }
  | {
      device: "reedSwitchSensor";
      key: "onClose";
      params: ActionParams<
        { onOpen: Record<string, any>; onClose: Record<string, any> },
        "onClose"
      >;
    }
  | {
      device: "pirSensor";
      key: "onActivate";
      params: ActionParams<
        { onActivate: Record<string, any>; onDeactivate: Record<string, any> },
        "onActivate"
      >;
    }
  | {
      device: "pirSensor";
      key: "onDeactivate";
      params: ActionParams<
        { onActivate: Record<string, any>; onDeactivate: Record<string, any> },
        "onDeactivate"
      >;
    }
  | {
      device: "thermostat";
      key: "thermostatActions";
      params: {
        deviceId: string;
        setpoint: number;
        mode: "heat" | "cool" | "fan" | "auto";
        fanSpeed: "low" | "medium" | "high" | "auto";
        sortIndex?: number;
      };
    }
  | {
      device: "scene";
      key: "onUpClick";
      params: ActionParams<SceneVia, "onUpClick">;
    };

type RecordKeys<T> = {
  [K in keyof T]: T[K] extends Record<string, unknown> ? K : never;
}[keyof T];

type ActionParams<T extends Record<string, unknown>, K extends RecordKeys<T>> =
  T[K] extends Record<string, unknown>
    ? Omit<T[K][string], "id" | "sortIndex">
    : never;
