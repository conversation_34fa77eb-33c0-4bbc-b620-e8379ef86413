import groundIcon from "@/components/icons/SLD/ground.png";
import { Hand<PERSON>, Position } from "@xyflow/react";
import { SldNodeData } from "./types";

const GroundSLDComponent: React.FC<{
  data: SldNodeData;
  selected?: boolean;
}> = ({ data: _data, selected = false }) => {
  return (
    <div
      className={`relative flex flex-col items-center justify-center border-2 rounded-sm p-2 w-14 h-14 ${
        selected
          ? "border-blue-500 shadow-lg scale-105"
          : "border-gray-200 hover:border-gray-300"
      }`}
    >
      {/* Small handle at the top for connections */}
      <Handle
        type="source"
        position={Position.Top}
        id="ground-output"
        className="w-2 h-2 bg-green-600 border border-white"
        style={{ top: "-2px" }}
        isConnectable={true}
      />

      <div className="flex flex-col items-center w-full h-full">
        <img src={groundIcon} alt="Ground" className="w-6 h-6 object-contain" />
        <div className="text-xs font-medium text-green-600">Gnd</div>
      </div>
    </div>
  );
};

// Edge style function for ground connections
function getGroundEdgeStyle(_handleId: string): {
  stroke: string;
  strokeWidth: number;
  strokeOpacity: number;
} {
  return {
    stroke: "#10b981", // Green color for all ground edges
    strokeWidth: 2,
    strokeOpacity: 0.8,
  };
}

export const GroundSLD = GroundSLDComponent;
