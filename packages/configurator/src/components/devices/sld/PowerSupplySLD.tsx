import { SldNodeData } from "./types";

const PowerSupplySLDComponent: React.FC<{
  data: SldNodeData;
  selected?: boolean;
}> = ({ data: _data, selected = false }) => {
  return (
    <div
      className={`flex flex-col items-center justify-center w-full h-full bg-white border-2 rounded-lg p-2 transition-all ${
        selected
          ? "border-blue-500 shadow-lg scale-105"
          : "border-gray-300 hover:border-gray-400"
      }`}
    >
      <div className="text-xs font-semibold text-center text-gray-800 mb-1">
        Power Supply
      </div>
      <div className="flex flex-col items-center">
        <div className="w-8 h-6 bg-red-600 rounded flex items-center justify-center">
          <span className="text-white text-xs font-bold">+</span>
        </div>
        <div className="w-8 h-6 bg-black rounded mt-1 flex items-center justify-center">
          <span className="text-white text-xs font-bold">-</span>
        </div>
      </div>
    </div>
  );
};

export const PowerSupplySLD = PowerSupplySLDComponent;
