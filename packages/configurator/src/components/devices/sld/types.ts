import z from "zod";
import { Node, NodeProps } from "@xyflow/react";
export const SldNodeData = z.object({ dataId: z.string() });
export type SldNodeData = z.infer<typeof SldNodeData>;

// Edge style function type
export type EdgeStyleFunction = (handleId: string) => {
  stroke: string;
  strokeWidth: number;
  strokeOpacity: number;
};

// Generic SldComponent type that can handle various prop patterns
export type SldComponent = React.ComponentType<NodeProps<SldNode>> & {
  getEdgeStyle?: EdgeStyleFunction;
  overrideEdgeColor?: boolean;
};

export type SldNode = Node<SldNodeData>;
