import lineIcon from "@/components/icons/SLD/line.png";
import { Handle, Position } from "@xyflow/react";
import { SldNodeData } from "./types";

const LineSLDComponent: React.FC<{ data: SldNodeData; selected?: boolean }> = ({
  data: _data,
  selected = false,
}) => {
  return (
    <div
      className={`relative flex flex-col items-center justify-center border-2 rounded-sm p-2 w-14 h-14 ${
        selected
          ? "border-blue-500 shadow-lg scale-105"
          : "border-gray-200 hover:border-gray-300"
      }`}
    >
      {/* Single handle at the bottom for both input and output connections */}
      <Handle
        type="source"
        position={Position.Bottom}
        id="line-connection"
        className="w-2 h-2 bg-red-600 border border-white"
        style={{ bottom: "-2px" }}
        isConnectable={true}
      />

      <div className="flex flex-col items-center w-full h-full">
        <img
          src={lineIcon}
          alt="120V Line"
          className="w-6 h-6 object-contain"
        />
        <div className="text-xs font-medium text-red-600">120V</div>
      </div>
    </div>
  );
};

// Edge style function for line connections
function getLineEdgeStyle(_handleId: string): {
  stroke: string;
  strokeWidth: number;
  strokeOpacity: number;
} {
  return {
    stroke: "#dc2626", // Red color for all 120V line edges
    strokeWidth: 2.3,
    strokeOpacity: 0.8,
  };
}

export const LineSLD = LineSLDComponent;
