import { <PERSON><PERSON>, <PERSON>si<PERSON> } from "@xyflow/react";
import { DeviceIcons } from "@/components/icons/DeviceIcons";
import { SldNodeData } from "./types";

// Utility function to get edge styles for lamp connections
function getLampEdgeStyle(handleId: string): {
  stroke: string;
  strokeWidth: number;
  strokeOpacity: number;
} {
  switch (handleId) {
    case "line":
      return {
        stroke: "#dc2626", // Red for line
        strokeWidth: 2,
        strokeOpacity: 0.9,
      };
    case "neutral":
      return {
        stroke: "#6b7280", // Gray for neutral
        strokeWidth: 2,
        strokeOpacity: 0.9,
      };
    case "ground":
      return {
        stroke: "#10b981", // Green for ground
        strokeWidth: 2,
        strokeOpacity: 0.9,
      };
    default:
      return {
        stroke: "#4D9FC9", // Default blue
        strokeWidth: 2,
        strokeOpacity: 0.9,
      };
  }
}

// Connector configuration for lamp
const CONNECTORS = [
  {
    id: "line",
    top: "8px",
    color: "#dc2626",
    label: "Line",
    textColor: "text-red-600",
  },
  {
    id: "neutral",
    top: "24px",
    color: "#6b7280",
    label: "Neutral",
    textColor: "text-gray-600",
  },
  {
    id: "ground",
    top: "40px",
    color: "#10b981",
    label: "Ground",
    textColor: "text-green-600",
  },
];

// Reusable connector component
const Connector = ({
  id,
  top,
  color,
  label,
  textColor,
}: {
  id: string;
  top: string;
  color: string;
  label: string;
  textColor: string;
}) => (
  <>
    <Handle
      type="source"
      position={Position.Right}
      id={id}
      isConnectable={true}
      className="w-3 h-3 border border-white rounded-full"
      style={{
        top,
        transform: "translateY(-50%)",
        backgroundColor: color,
      }}
    />
    <div
      className={`absolute right-2 transform -translate-y-1/2 text-xs font-semibold text-right ${textColor}`}
      style={{
        top,
        lineHeight: "1.2",
        width: "50px",
      }}
    >
      {label}
    </div>
  </>
);

const LampSLDComponent: React.FC<{ data: SldNodeData; selected?: boolean }> = ({
  data: _data,
  selected = false,
}) => {
  const LampIcon = DeviceIcons.ceilingLamp;

  return (
    <div
      className={`bg-white border-2 rounded-lg p-2 shadow-md min-w-[170px] min-h-[80px] cursor-move relative ${
        selected ? "border-blue-500 bg-blue-50" : "border-gray-300"
      }`}
    >
      {/* Right side connectors */}
      {CONNECTORS.map((connector) => (
        <Connector key={connector.id} {...connector} />
      ))}

      {/* Lamp Icon - Left aligned */}
      <div className="flex justify-start mb-2">
        <LampIcon className="w-8 h-8 text-gray-700" />
      </div>

      {/* Device Type - Main identifier for SLD view */}
      <div className="text-sm font-semibold text-left mb-2 text-gray-800 break-words leading-tight pr-16">
        Lamp
      </div>
    </div>
  );
};

export const LampSLD = LampSLDComponent;
