import { useGetSldEdges } from "@/components/sld-graph/useSldGraph";
import {
  Handle,
  NodeProps,
  Position,
  useNodeId,
  useReactFlow,
  useStore,
  useUpdateNodeInternals,
} from "@xyflow/react";
import { useCallback, useEffect, useRef } from "react";
import { SldNode } from "./types";

// Map angle (0..360) to side: 0=right, 90=bottom, 180=left, 270=top
function sideFromAngle(angle360: number): Position {
  // Round to 1 decimal place to reduce sensitivity to small changes
  const roundedAngle = Math.round(angle360 * 10) / 10;
  const a = ((roundedAngle % 360) + 360) % 360;

  if (a >= 315 || a < 45) {
    return Position.Right;
  } // [-45, 45) - pointing right
  if (a >= 45 && a < 135) {
    return Position.Bottom;
  } // [45, 135) - pointing down
  if (a >= 135 && a < 225) {
    return Position.Left;
  } // [135, 225) - pointing left
  if (a >= 225 && a < 315) {
    return Position.Top;
  } // [225, 315) - pointing up
  // Fallback (should not happen with normalized angles)
  return Position.Bottom;
}

// Map Position to handle ID
function getHandleId(position: Position): string {
  switch (position) {
    case Position.Top:
      return "ghost-top";
    case Position.Right:
      return "ghost-right";
    case Position.Bottom:
      return "ghost-bottom";
    case Position.Left:
      return "ghost-left";
    default:
      return "ghost-bottom";
  }
}

// Calculate exact handle position based on node position and handle ID
function calculateHandlePosition(
  node: any,
  handleId: string,
): { x: number; y: number } {
  const nodeX = node.position.x;
  const nodeY = node.position.y;
  const nodeWidth = node.width ?? 0;
  const nodeHeight = node.height ?? 0;

  // Extract position from handle ID (e.g., "ghost-top" -> "top")
  const positionStr = handleId.split("-")[1]; // "ghost-top" -> "top"

  switch (positionStr) {
    case "top":
      return { x: nodeX + nodeWidth / 2, y: nodeY };
    case "right":
      return { x: nodeX + nodeWidth, y: nodeY + nodeHeight / 2 };
    case "bottom":
      return { x: nodeX + nodeWidth / 2, y: nodeY + nodeHeight };
    case "left":
      return { x: nodeX, y: nodeY + nodeHeight / 2 };
    default:
      // Fallback to center if handle ID format is unexpected
      return { x: nodeX + nodeWidth / 2, y: nodeY + nodeHeight / 2 };
  }
}

const GhostNodeSLDComponent: React.FC<NodeProps<SldNode>> = ({
  data,
  selected = false,
  dragging = false,
}) => {
  const { getNode, screenToFlowPosition } = useReactFlow();
  const edges = useGetSldEdges();
  const nodeId = useNodeId();
  const updateNodeInternals = useUpdateNodeInternals();
  const isConnecting = useStore((s) => !!s.connectionClickStartHandle);
  const externalDragActive = !!(data as any)?.externalDragActive;
  const reconnectEdge = (data as any)?.reconnectEdge;
  const pendingReconnections = useRef(new Set<string>());

  const recalcSide = useCallback(() => {
    if (!nodeId) {
      return;
    }

    const ghostNode = getNode(nodeId);
    if (!ghostNode) {
      return;
    }

    const halfW = (ghostNode.width ?? 16) / 2;
    const halfH = (ghostNode.height ?? 16) / 2;
    const ghostCenter = {
      x: (ghostNode.position?.x ?? 0) + halfW,
      y: (ghostNode.position?.y ?? 0) + halfH,
    };

    const connectedEdges = edges.filter(
      (e) => e.source === nodeId || e.target === nodeId,
    );

    if (connectedEdges.length === 0) {
      return;
    }

    // Process each connected edge to determine optimal handle
    connectedEdges.forEach((edge) => {
      const otherNodeId = edge.source === nodeId ? edge.target : edge.source;
      const otherHandleId =
        edge.source === nodeId ? edge.targetHandle : edge.sourceHandle;
      const currentGhostHandleId =
        edge.source === nodeId ? edge.sourceHandle : edge.targetHandle;

      if (!otherNodeId || !otherHandleId) {
        return;
      }

      // Get the other node using React Flow's API
      const otherNode = getNode(otherNodeId);
      if (!otherNode) {
        return;
      }

      // Calculate exact handle position based on node position and handle location
      const handleCenterFlow = calculateHandlePosition(
        otherNode,
        otherHandleId,
      );

      const deltaX = handleCenterFlow.x - ghostCenter.x;
      const deltaY = handleCenterFlow.y - ghostCenter.y;

      const angleRad = Math.atan2(deltaY, deltaX);
      const angleDeg = (angleRad * 180) / Math.PI;
      // Properly normalize angle: atan2 gives -180 to +180, we want 0 to 360
      const angle360 = angleDeg < 0 ? angleDeg + 360 : angleDeg;

      const optimalSide = sideFromAngle(angle360);
      const optimalHandleId = getHandleId(optimalSide);

      // Reconnect edge if it's using a suboptimal handle
      if (currentGhostHandleId !== optimalHandleId) {
        // console.log(`Edge ${edge.id}: raw=${angleDeg.toFixed(1)}°, normalized=${angle360.toFixed(1)}°, optimal=${optimalHandleId}, current=${currentGhostHandleId}`);

        if (reconnectEdge && !pendingReconnections.current.has(edge.id)) {
          // Mark this edge as pending reconnection to prevent duplicate attempts
          pendingReconnections.current.add(edge.id);

          // Determine which handle to update based on whether ghost is source or target
          const newSourceHandle =
            edge.source === nodeId ? optimalHandleId : undefined;
          const newTargetHandle =
            edge.target === nodeId ? optimalHandleId : undefined;

          // Use setTimeout to avoid race conditions during rapid recalculations
          setTimeout(() => {
            const success = reconnectEdge(
              edge.id,
              newSourceHandle,
              newTargetHandle,
            );
            // Remove from pending set regardless of success/failure
            pendingReconnections.current.delete(edge.id);
            if (success) {
              // console.log(`Successfully reconnected edge ${edge.id} to ${optimalHandleId}`);
            }
          }, 0);
        } else if (!reconnectEdge) {
          console.warn("reconnectEdge function not available");
        }
      }
    });
  }, [nodeId, getNode, edges, screenToFlowPosition, reconnectEdge]);

  // Recalculate while the node is being dragged/selected, a connection is dragged, or externalDragActive is true
  useEffect(() => {
    if (!dragging && !selected && !isConnecting && !externalDragActive) {
      return;
    }

    let raf: number;
    const tick = () => {
      recalcSide();
      raf = requestAnimationFrame(tick);
    };
    raf = requestAnimationFrame(tick);
    return () => cancelAnimationFrame(raf);
  }, [dragging, selected, isConnecting, externalDragActive, recalcSide]);

  // Also recalculate whenever edges change (e.g., when a new connection is made)
  useEffect(() => {
    if (!nodeId) {
      return;
    }

    // Check if this ghost node has any edges connected to it
    const connectedEdge = edges.find(
      (e) => e.source === nodeId || e.target === nodeId,
    );
    if (connectedEdge) {
      // Use a small timeout to ensure DOM elements are rendered
      const timeout = setTimeout(() => {
        recalcSide();
      }, 0);
      return () => clearTimeout(timeout);
    }
    return undefined;
  }, [edges, nodeId, recalcSide]);

  // Force React Flow to recompute handle internals when edges change
  useEffect(() => {
    if (!nodeId) {
      return;
    }
    const t = setTimeout(() => updateNodeInternals(nodeId), 0);
    return () => clearTimeout(t);
  }, [edges, nodeId, updateNodeInternals]);

  return (
    <div
      style={{
        width: 16,
        height: 16,
        backgroundColor: "transparent",
        border: "none",
        borderRadius: "50%",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        cursor: "default",
        position: "relative",
        zIndex: -1,
      }}
    >
      {/* Small visible dot at center */}
      <div
        style={{
          position: "absolute",
          width: 6,
          height: 6,
          background: "#2196F3",
          border: "1px solid #1976D2",
          borderRadius: "50%",
          left: "50%",
          top: "50%",
          transform: "translate(-50%, -50%)",
          opacity: 1,
        }}
      />

      {/* Four handles positioned at center with different Position values */}
      <Handle
        id="ghost-top"
        type="source"
        position={Position.Top}
        isConnectable={true}
        style={{
          background: "#2196F3",
          border: "2px solid #1976D2",
          width: 6,
          height: 6,
          zIndex: 10,
          opacity: 1,
          transition: "opacity 0.15s ease",
          left: "50%",
          top: "50%",
          transform: "translate(-50%, -50%)",
        }}
      />
      <Handle
        id="ghost-right"
        type="source"
        position={Position.Right}
        isConnectable={true}
        style={{
          background: "#2196F3",
          border: "2px solid #1976D2",
          width: 6,
          height: 6,
          zIndex: 10,
          opacity: 1,
          transition: "opacity 0.15s ease",
          left: "50%",
          top: "50%",
          transform: "translate(-50%, -50%)",
        }}
      />
      <Handle
        id="ghost-bottom"
        type="source"
        position={Position.Bottom}
        isConnectable={true}
        style={{
          background: "#2196F3",
          border: "2px solid #1976D2",
          width: 6,
          height: 6,
          zIndex: 10,
          opacity: 1,
          transition: "opacity 0.15s ease",
          left: "50%",
          top: "50%",
          transform: "translate(-50%, -50%)",
        }}
      />
      <Handle
        id="ghost-left"
        type="source"
        position={Position.Left}
        isConnectable={true}
        style={{
          background: "#2196F3",
          border: "2px solid #1976D2",
          width: 6,
          height: 6,
          zIndex: 10,
          opacity: 1,
          transition: "opacity 0.15s ease",
          left: "50%",
          top: "50%",
          transform: "translate(-50%, -50%)",
        }}
      />
    </div>
  );
};

export const GhostNodeSLD = GhostNodeSLDComponent;
