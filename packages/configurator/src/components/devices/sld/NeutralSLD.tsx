import neutralIcon from "@/components/icons/SLD/neutral.png";
import { Hand<PERSON>, Position } from "@xyflow/react";
import { SldNodeData } from "./types";

const NeutralSLDComponent: React.FC<{
  data: SldNodeData;
  selected?: boolean;
}> = ({ data: _data, selected = false }) => {
  return (
    <div
      className={`relative flex flex-col items-center justify-center border-2 rounded-sm p-2 w-14 h-14 ${
        selected
          ? "border-blue-500 shadow-lg scale-105"
          : "border-gray-200 hover:border-gray-300"
      }`}
    >
      {/* Small handle at the bottom for connections */}
      <Handle
        type="source"
        position={Position.Bottom}
        id="neutral-output"
        className="w-2 h-2 bg-blue-600 border border-white"
        style={{ bottom: "-2px" }}
        isConnectable={true}
      />

      <div className="flex flex-col items-center w-full h-full">
        <img
          src={neutralIcon}
          alt="Neutral"
          className="w-6 h-6 object-contain"
        />
        <div className="text-xs font-medium text-blue-600">N</div>
      </div>
    </div>
  );
};

// Edge style function for neutral connections
function getNeutralEdgeStyle(_handleId: string): {
  stroke: string;
  strokeWidth: number;
  strokeOpacity: number;
} {
  return {
    stroke: "#6b7280", // Grey color for all neutral edges
    strokeWidth: 2,
    strokeOpacity: 0.9,
  };
}

export const NeutralSLD = NeutralSLDComponent;
