import { getData, useGetData } from "@/components/data/useData";
import { useReactFlowContext } from "@/components/graph/ReactFlowContext";
import { useHandleStyle } from "@/components/graph/position/useHandlePosition";
import { SomoButton } from "@/components/icons/SomoButton";
import { useExecutionContext } from "@/contexts/ExecutionContext";
import { cn } from "@/lib/classNames";
import {
  LayoutContainerNode,
  MomentaryCanBusController,
  ToggleCanBusController,
} from "@somo/shared";
import {
  Handle,
  NodeProps,
  Position,
  useReactFlow,
  useUpdateNodeInternals,
} from "@xyflow/react";
import equal from "fast-deep-equal/react";
import { CircleDot } from "lucide-react";
import React, { useEffect, useMemo, useRef } from "react";

export const CanBusControllerContainerNode = React.memo(RegularNode, equal);
export const CanBusControllerContainerNodeSimulator = React.memo(
  SimulatorNode,
  equal,
);

function RegularNode(props: NodeProps<LayoutContainerNode>) {
  const { yDoc } = useReactFlowContext();
  const data = getData(yDoc, props.data.dataId, "canbusController");
  if (!data) {
    return <></>;
  }

  const sortedControllers = useMemo(() => {
    const controllers = Object.values(data.controllers);
    return controllers.sort((a, b) => {
      // Extract port numbers from portId (e.g., "3pin-port-1" -> 1)
      const aPortMatch = a.portId?.match(/(\d+)pin-port-(\d+)/);
      const bPortMatch = b.portId?.match(/(\d+)pin-port-(\d+)/);

      if (!aPortMatch || !bPortMatch) {
        return 0;
      }

      const aPinCount = parseInt(aPortMatch[1]);
      const bPinCount = parseInt(bPortMatch[1]);
      const aPortNum = parseInt(aPortMatch[2]);
      const bPortNum = parseInt(bPortMatch[2]);

      // Sort by pin count first (3-pin before 2-pin), then by port number
      if (aPinCount !== bPinCount) {
        return bPinCount - aPinCount;
      }
      return aPortNum - bPortNum;
    });
  }, [data.controllers]);

  // Sort reed switch sensors by port order (2-pin ports by port number)
  const sortedReedSwitchSensors = useMemo(() => {
    const reedSwitchSensors = Object.values(data.reedSwitchSensors || {});
    return reedSwitchSensors.sort((a, b) => {
      // Extract port numbers from portId (e.g., "2pin-port-1" -> 1)
      const aPortMatch = a.portId?.match(/2pin-port-(\d+)/);
      const bPortMatch = b.portId?.match(/2pin-port-(\d+)/);

      if (!aPortMatch || !bPortMatch) {
        return 0;
      }

      const aPortNum = parseInt(aPortMatch[1]);
      const bPortNum = parseInt(bPortMatch[1]);

      return aPortNum - bPortNum;
    });
  }, [data.reedSwitchSensors]);

  // Sort PIR sensors by port order (ADC ports by port number)
  const sortedPirSensors = useMemo(() => {
    const pirSensors = Object.values(data.pirSensors || {});
    return pirSensors.sort((a, b) => {
      // Extract port numbers from portId (e.g., "adc-port-1" -> 1)
      const aPortMatch = a.portId?.match(/adc-port-(\d+)/);
      const bPortMatch = b.portId?.match(/adc-port-(\d+)/);

      if (!aPortMatch || !bPortMatch) {
        return 0;
      }

      const aPortNum = parseInt(aPortMatch[1]);
      const bPortNum = parseInt(bPortMatch[1]);

      return aPortNum - bPortNum;
    });
  }, [data.pirSensors]);

  const updateNodeInternals = useUpdateNodeInternals();

  useEffect(() => {
    updateNodeInternals(props.id);
  }, [
    sortedControllers.length,
    sortedReedSwitchSensors.length,
    sortedPirSensors.length,
    props.id,
  ]);

  return (
    <BaseNode {...props}>
      {sortedControllers.map((controller) => (
        <div
          key={controller.id}
          className="flex flex-col items-center justify-center gap-1 px-1"
        >
          <div className="text-[8px] text-white font-light text-center max-w-[60px]">
            {controller.label}
          </div>
          {controller.type === "momentary" ? (
            <MomentaryViaButton controller={controller} />
          ) : (
            <ToggleViaButton controller={controller} />
          )}
        </div>
      ))}

      {sortedReedSwitchSensors.map((sensor) => (
        <div
          key={sensor.id}
          className="flex flex-col items-center justify-center gap-1 px-1"
        >
          <div className="text-[8px] text-white font-light text-center max-w-[60px]">
            {sensor.label}
          </div>
          <ReedSwitchSensorViaButton sensor={sensor} />
        </div>
      ))}

      {sortedPirSensors.map((sensor) => (
        <div
          key={sensor.id}
          className="flex flex-col items-center justify-center gap-1 px-1"
        >
          <div className="text-[8px] text-white font-light text-center max-w-[60px]">
            {sensor.label}
          </div>
          <PirSensorViaButton sensor={sensor} />
        </div>
      ))}
    </BaseNode>
  );
}

function SimulatorNode(props: NodeProps<LayoutContainerNode>) {
  const data = useGetData(props.data.dataId, "canbusController");
  if (!data) {
    return <></>;
  }
  // Sort controllers by port order (3-pin ports first, then by port number)
  const sortedControllers = useMemo(() => {
    const controllers = Object.values(data.controllers);
    return controllers.sort((a, b) => {
      // Extract port numbers from portId (e.g., "3pin-port-1" -> 1)
      const aPortMatch = a.portId?.match(/(\d+)pin-port-(\d+)/);
      const bPortMatch = b.portId?.match(/(\d+)pin-port-(\d+)/);

      if (!aPortMatch || !bPortMatch) {
        return 0;
      }

      const aPinCount = parseInt(aPortMatch[1]);
      const bPinCount = parseInt(bPortMatch[1]);
      const aPortNum = parseInt(aPortMatch[2]);
      const bPortNum = parseInt(bPortMatch[2]);

      // Sort by pin count first (3-pin before 2-pin), then by port number
      if (aPinCount !== bPinCount) {
        return bPinCount - aPinCount; // 3-pin first
      }
      return aPortNum - bPortNum;
    });
  }, [data.controllers]);

  // Sort 2-pin ports by port number
  const sortedReedSwitchSensors = useMemo(() => {
    const reedSwitchSensors = Object.values(data.reedSwitchSensors || {});
    return reedSwitchSensors.sort((a, b) => {
      // Extract port numbers from portId (e.g., "2pin-port-1" -> 1)
      const aPortMatch = a.portId?.match(/2pin-port-(\d+)/);
      const bPortMatch = b.portId?.match(/2pin-port-(\d+)/);

      if (!aPortMatch || !bPortMatch) {
        return 0;
      }

      const aPortNum = parseInt(aPortMatch[1]);
      const bPortNum = parseInt(bPortMatch[1]);

      return aPortNum - bPortNum;
    });
  }, [data.reedSwitchSensors]);

  // Get PIR sensors (no sorting needed since there's only one ADC port)
  const pirSensors = Object.values(data.pirSensors || {});

  return (
    <BaseNode {...props}>
      {sortedControllers.map((controller) => (
        <div
          key={controller.id}
          className="flex flex-col items-center justify-center gap-1 px-1"
        >
          <div className="text-[8px] text-white font-light text-center max-w-[60px]">
            {controller.label}
          </div>
          {controller.type === "momentary" ? (
            <MomentarySimulatorViaButton
              containerId={props.id}
              controller={controller}
            />
          ) : (
            <>
              <ToggleSimulatorViaButton
                containerId={props.id}
                controller={controller}
                direction="up"
              />
              <ToggleSimulatorViaButton
                containerId={props.id}
                controller={controller}
                direction="down"
              />
            </>
          )}
        </div>
      ))}

      {sortedReedSwitchSensors.map((sensor) => (
        <div
          key={sensor.id}
          className="flex flex-col items-center justify-center gap-1 px-1"
        >
          <div className="text-[8px] text-white font-light text-center max-w-[60px]">
            {sensor.label}
          </div>
          <ReedSwitchSensorSimulatorViaButton
            containerId={props.id}
            sensor={sensor}
            action="open"
          />
          <ReedSwitchSensorSimulatorViaButton
            containerId={props.id}
            sensor={sensor}
            action="close"
          />
        </div>
      ))}

      {pirSensors.map((sensor) => (
        <div
          key={sensor.id}
          className="flex flex-col items-center justify-center gap-1 px-1"
        >
          <div className="text-[8px] text-white font-light text-center max-w-[60px]">
            {sensor.label}
          </div>
          <PirSensorSimulatorViaButton
            containerId={props.id}
            sensor={sensor}
            action="activate"
          />
          <PirSensorSimulatorViaButton
            containerId={props.id}
            sensor={sensor}
            action="deactivate"
          />
        </div>
      ))}
    </BaseNode>
  );
}

function MomentaryViaButton({
  controller,
}: {
  controller: MomentaryCanBusController;
}) {
  const { readOnly } = useReactFlowContext();
  return (
    <div className="flex flex-col items-center w-full">
      <div className="relative size-5 flex items-center justify-center">
        <Handle
          id={controller.id}
          type="source"
          isConnectable={!readOnly}
          position={Position.Top}
          style={useHandleStyle(Position.Top)}
        />
        <CircleDot className="text-white size-5" />
      </div>
    </div>
  );
}

/**
 * The Simulator button is similar to the default one, but it is clickable
 * and will emit the relevant events to test the button behavior.
 */
function MomentarySimulatorViaButton({
  containerId,
  controller,
}: {
  containerId: string;
  controller: MomentaryCanBusController;
}) {
  const { isControllerOn, sendMomentaryCommand } = useExecutionContext();
  const isOn = isControllerOn(containerId, controller.id);

  return (
    <div
      className="flex flex-col items-center w-full group relative"
      onPointerDown={() => {
        sendMomentaryCommand(containerId, controller.id, "onUpHold");
      }}
      onPointerUp={() => {
        sendMomentaryCommand(containerId, controller.id, "onUpClick");
      }}
    >
      <Handle
        id={controller.id}
        type="source"
        isConnectable={false}
        position={Position.Top}
        style={{
          border: "none",
          background: "transparent",
        }}
      />
      <CircleDot
        className={cn(
          "size-5 group-hover:text-blue-300",
          isOn ? "text-yellow-400" : "text-white",
        )}
      />
    </div>
  );
}

function ToggleViaButton({
  controller,
}: {
  controller: ToggleCanBusController;
}) {
  const { readOnly } = useReactFlowContext();
  return (
    <div className="flex flex-col items-center w-full">
      <div className="relative size-5 flex items-center justify-center">
        <Handle
          id={controller.id}
          type="source"
          isConnectable={!readOnly}
          position={Position.Top}
          style={useHandleStyle(Position.Top)}
        />
        <SomoButton className="text-white size-5 flex-shrink-0" />
      </div>
    </div>
  );
}

function ToggleSimulatorViaButton({
  containerId,
  controller,
  direction,
}: {
  containerId: string;
  controller: ToggleCanBusController;
  direction: "up" | "down";
}) {
  const { isControllerOn, sendToggleCommand } = useExecutionContext();
  const isOn = isControllerOn(containerId, controller.id);

  return (
    <div
      className="flex flex-col items-center w-full group relative"
      onPointerDown={() => {
        const viaId = direction === "up" ? "onUpHold" : "onDownHold";
        sendToggleCommand(containerId, controller.id, viaId);
      }}
      onPointerUp={() => {
        const viaId = direction === "up" ? "onUpClick" : "onDownClick";
        sendToggleCommand(containerId, controller.id, viaId);
      }}
    >
      <div className="relative size-5 flex items-center justify-center">
        <Handle
          id={controller.id}
          type="source"
          isConnectable={false}
          position={Position.Top}
          style={{
            border: "none",
            background: "transparent",
          }}
        />
        <SomoButton
          className={cn(
            "size-5 flex-shrink-0 group-hover:text-blue-300",
            isOn ? "text-yellow-400" : "text-white",
          )}
        />
      </div>
      <div className="text-[8px] text-white font-light truncate w-full text-center px-0.5">
        {direction === "up" ? "Up" : "Down"}
      </div>
    </div>
  );
}

function ReedSwitchSensorViaButton({
  sensor,
}: {
  sensor: { id: string; label: string };
}) {
  const { readOnly } = useReactFlowContext();

  return (
    <div className="flex flex-col items-center w-full">
      <div className="relative size-5 flex items-center justify-center">
        <Handle
          id={sensor.id}
          type="source"
          isConnectable={!readOnly}
          position={Position.Top}
          style={useHandleStyle(Position.Top)}
        />
        <SomoButton className="text-white size-5 flex-shrink-0" />
      </div>
    </div>
  );
}

function PirSensorViaButton({
  sensor,
}: {
  sensor: { id: string; label: string };
}) {
  const { readOnly } = useReactFlowContext();

  return (
    <div className="flex flex-col items-center w-full">
      <div className="relative size-5 flex items-center justify-center">
        <Handle
          id={sensor.id}
          type="source"
          isConnectable={!readOnly}
          position={Position.Top}
          style={useHandleStyle(Position.Top)}
        />
        <SomoButton className="text-white size-5 flex-shrink-0" />
      </div>
    </div>
  );
}

function ReedSwitchSensorSimulatorViaButton({
  containerId,
  sensor,
  action,
}: {
  containerId: string;
  sensor: { id: string; label: string };
  action: "open" | "close";
}) {
  const {
    isReedSwitchSensorOpen,
    openReedSwitchSensor,
    closeReedSwitchSensor,
  } = useExecutionContext();
  const isOpen = isReedSwitchSensorOpen(containerId, sensor.id);

  const handleClick = () => {
    if (action === "open") {
      openReedSwitchSensor(containerId, sensor.id);
    } else if (action === "close") {
      closeReedSwitchSensor(containerId, sensor.id);
    }
  };

  return (
    <div
      className="flex flex-col items-center w-full group cursor-pointer"
      onClick={handleClick}
    >
      <div className="relative size-5 flex items-center justify-center">
        <Handle
          id={sensor.id}
          type="source"
          isConnectable={false}
          position={Position.Top}
          style={{
            border: "none",
            background: "transparent",
          }}
        />
        <SomoButton
          className={cn(
            "size-5 flex-shrink-0 group-hover:text-blue-300",
            (action === "open" && isOpen) || (action === "close" && !isOpen)
              ? "text-yellow-400"
              : "text-white",
          )}
        />
      </div>
      <div className="text-[8px] text-white font-light truncate w-full text-center px-0.5">
        {action === "open" ? "Open" : "Close"}
      </div>
    </div>
  );
}

function PirSensorSimulatorViaButton({
  containerId,
  sensor,
  action,
}: {
  containerId: string;
  sensor: { id: string; label: string };
  action: "activate" | "deactivate";
}) {
  const { isPirSensorActive, activatePirSensor, deactivatePirSensor } =
    useExecutionContext();
  const isActive = isPirSensorActive(containerId, sensor.id);

  const handleClick = () => {
    if (action === "activate") {
      activatePirSensor(containerId, sensor.id);
    } else if (action === "deactivate") {
      deactivatePirSensor(containerId, sensor.id);
    }
  };

  return (
    <div
      className="flex flex-col items-center w-full group cursor-pointer"
      onClick={handleClick}
    >
      <div className="relative size-5 flex items-center justify-center">
        <Handle
          id={sensor.id}
          type="source"
          isConnectable={false}
          position={Position.Top}
          style={{
            border: "none",
            background: "transparent",
          }}
        />
        <SomoButton
          className={cn(
            "size-5 flex-shrink-0 group-hover:text-blue-300",
            (action === "activate" && isActive) ||
              (action === "deactivate" && !isActive)
              ? "text-yellow-400"
              : "text-white",
          )}
        />
      </div>
      <div className="text-[8px] text-white font-light truncate w-full text-center px-0.5">
        {action === "activate" ? "Activate" : "Deactivate"}
      </div>
    </div>
  );
}

function BaseNode({
  id,
  selected,
  height,
  width,
  children,
}: NodeProps<LayoutContainerNode> & {
  children: React.ReactNode;
}) {
  const updateNodeInternals = useUpdateNodeInternals();
  const reactFlowInstance = useReactFlow();
  const ref = useRef<HTMLDivElement>(null);
  const zoom = reactFlowInstance.getViewport().zoom;

  useEffect(() => {
    updateNodeInternals(id);

    const size = ref.current?.getBoundingClientRect();
    if (
      size &&
      ((size.height > 0 && height !== Math.floor(size.height / zoom)) ||
        (size.width > 0 && width !== Math.floor(size.width / zoom)))
    ) {
      reactFlowInstance.updateNode(id, {
        height: Math.floor(size.height / zoom),
        width: Math.floor(size.width / zoom),
      });
    }
  }, [updateNodeInternals, id, height, width, selected, zoom]);

  return (
    <div
      ref={ref}
      className={cn(
        "inline-flex items-center justify-center bg-gradient-to-b from-gray-600 to-gray-700 rounded-lg shadow-lg px-2 py-2 min-w-[80px] min-h-[40px] border border-gray-500",
        selected &&
          "outline outline-2 outline-blue-400 outline-offset-2 shadow-blue-200/50",
      )}
    >
      <Handle
        type="target"
        position={Position.Bottom}
        isConnectable={false}
        style={{
          background: "transparent",
          border: 0,
          color: "transparent",
          width: "2px",
          height: "2px",
          bottom: "2.5px",
        }}
      />
      <div className="flex items-center justify-center">{children}</div>
    </div>
  );
}
