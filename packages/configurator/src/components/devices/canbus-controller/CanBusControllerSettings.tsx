import { useData } from "@/components/data/useData";
import { useReactFlowContext } from "@/components/graph/ReactFlowContext";
import { ActionSettingsWithHeader } from "@/components/graph/settings/ActionSettings";
import { LampPopover } from "@/components/graph/settings/LampPopover";
import {
  SettingsGroup,
  SettingsGroupContent,
  SettingsGroupHeader,
  SettingsLabel,
  SettingsRow,
} from "@/components/graph/settings/SettingsGroup";
import { SettingsInput } from "@/components/graph/settings/SettingsInput";
import { DeviceIconKey } from "@/components/icons/DeviceIcons";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Label } from "@/components/ui/label";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { TooltipDropdownMenu } from "@/components/ui/tooltip-dropdown-menu";
import { cn } from "@/lib/classNames";
import {
  CanBusControllerController,
  GraphNodesByType,
  MomentaryCanBusController,
  MomentaryCanBusControllerViaId,
  PirSensor,
  randomId,
  ReedSwitchSensor,
  RelayConnector,
  ToggleCanBusController,
  ToggleCanBusControllerViaId,
  ZeroToTenVoltDimmer,
} from "@somo/shared";
import { ChevronDown, PlusIcon, Trash2Icon } from "lucide-react";
import { useEffect, useMemo, useState } from "react";
import { match } from "ts-pattern";
import { getDeviceDimmingSpeed, getHoldDimmingSpeed } from "../deviceUtils";
import { useConnectableDevices } from "../useConnectableDevices";

export function CanBusControllerSettings({
  node,
}: {
  node: GraphNodesByType["canbusControllerContainer"];
}) {
  const { readOnly } = useReactFlowContext();
  const { data, updateNestedData, deleteNestedData } = useData(
    node.data.dataId,
    "canbusController",
  );
  // Add port collapsed state
  const [portCollapsedState, setPortCollapsedState] = useState<{
    [portId: string]: boolean;
  }>({});

  const [sortedControllers, setSortableControllers] = useState<
    (MomentaryCanBusController | ToggleCanBusController)[]
  >([]);

  useEffect(() => {
    if (!data) {
      return;
    }
    // get all objects from node.data.controllers
    const controllers = Object.values(data.controllers ?? {});
    // sort devices by sortIndex
    controllers.sort((a, b) => a.sortIndex - b.sortIndex);
    setSortableControllers(controllers);
  }, [data?.controllers]);

  if (!data) {
    return <></>;
  }

  return (
    <div>
      <SettingsGroup>
        <SettingsGroupContent>
          <SettingsRow>
            <div className="font-mono text-xs text-gray-500">ID: {node.id}</div>
          </SettingsRow>
          <SettingsRow>
            <SettingsLabel htmlFor="name">Name</SettingsLabel>
            <SettingsInput
              id="name"
              value={data.title}
              onEndEdit={(value) => {
                if (typeof value !== "string") {
                  return;
                }
                updateNestedData("title", value);
              }}
              disabled={readOnly}
            />
          </SettingsRow>
        </SettingsGroupContent>
      </SettingsGroup>

      <SettingsGroup>
        <SettingsGroupHeader>
          <span>3-Pin Ports</span>
        </SettingsGroupHeader>
        <SettingsGroupContent>
          {Object.values(data.threePinPorts || {}).map((port) => {
            const connectedController = Object.values(
              data.controllers || {},
            ).find((controller) => controller.portId === port.id);

            return (
              <SettingsRow key={port.id} className="flex flex-col gap-2">
                <div
                  className={cn(
                    "flex flex-row items-center justify-between cursor-pointer hover:bg-gray-50 -mx-2 px-2 py-1 rounded",
                    connectedController && "cursor-pointer",
                  )}
                  onClick={() => {
                    if (connectedController) {
                      setPortCollapsedState((prev) => ({
                        ...prev,
                        [port.id]: !prev[port.id],
                      }));
                    }
                  }}
                >
                  <div className="flex items-center gap-2">
                    {connectedController && (
                      <ChevronDown
                        className={cn(
                          "size-4 transition-transform duration-300 text-gray-500",
                          portCollapsedState[port.id] && "-rotate-90",
                        )}
                      />
                    )}
                    <div className="flex flex-col">
                      <div className="text-sm font-medium">{port.label}</div>
                      <div className="text-xs text-gray-500">
                        {connectedController
                          ? `${connectedController.type === "toggle" ? "Toggle" : "Momentary"}: ${connectedController.label}`
                          : "Not connected"}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div
                      className={cn(
                        "size-3 rounded-full",
                        connectedController ? "bg-green-400" : "bg-gray-400",
                      )}
                    />
                    {!connectedController && !readOnly && (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <TooltipDropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="size-5"
                              >
                                <PlusIcon className="size-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent className="mr-5">
                              <DropdownMenuLabel>
                                Add device to this port
                              </DropdownMenuLabel>
                              <DropdownMenuItem
                                onClick={() => {
                                  if (data.controllers) {
                                    const index = sortedControllers.length;
                                    const controller: CanBusControllerController =
                                      {
                                        id: randomId(),
                                        label: "Toggle",
                                        sortIndex: index,
                                        type: "toggle",
                                        portId: port.id,
                                        onUpClick: {},
                                        onUpHold: {},
                                        onDownClick: {},
                                        onDownHold: {},
                                        onUpPress: {},
                                        onUpRelease: {},
                                        onDownPress: {},
                                        onDownRelease: {},
                                        onUpHoldRelease: {},
                                        onDownHoldRelease: {},
                                      };
                                    // Update port status
                                    updateNestedData(
                                      `controllers.${controller.id}`,
                                      controller,
                                    );

                                    if (
                                      data.threePinPorts &&
                                      data.threePinPorts[port.id]
                                    ) {
                                      updateNestedData(
                                        `threePinPorts.${port.id}`,
                                        {
                                          ...port,
                                          connected: true,
                                          deviceType: "toggle",
                                          controllerId: controller.id,
                                        },
                                      );
                                    }
                                  }
                                }}
                              >
                                Toggle Button
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => {
                                  if (data.controllers) {
                                    const index = sortedControllers.length;
                                    const controller: CanBusControllerController =
                                      {
                                        id: randomId(),
                                        label: "Momentary",
                                        sortIndex: index,
                                        type: "momentary",
                                        portId: port.id,
                                        onUpClick: {},
                                        onUpHold: {},
                                        onUpPress: {},
                                        onUpRelease: {},
                                        onUpHoldRelease: {},
                                      };
                                    // Update port status
                                    updateNestedData(
                                      `controllers.${controller.id}`,
                                      controller,
                                    );

                                    if (
                                      data.threePinPorts &&
                                      data.threePinPorts[port.id]
                                    ) {
                                      updateNestedData(
                                        `threePinPorts.${port.id}`,
                                        {
                                          ...port,
                                          connected: true,
                                          deviceType: "momentary",
                                          controllerId: controller.id,
                                        },
                                      );
                                    }
                                  }
                                }}
                              >
                                Momentary Button
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </TooltipDropdownMenu>
                        </TooltipTrigger>
                        <TooltipContent>Add device</TooltipContent>
                      </Tooltip>
                    )}
                    {connectedController && !readOnly && (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="size-5"
                            onClick={(e) => {
                              e.stopPropagation(); // Prevent collapse/expand when clicking remove
                              // Remove controller
                              deleteNestedData(
                                `controllers.${connectedController.id}`,
                              );

                              updateNestedData(`threePinPorts.${port.id}`, {
                                ...port,
                                connected: false,
                                deviceType: "none",
                                controllerId: undefined,
                              });
                            }}
                          >
                            <Trash2Icon className="size-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>Remove Device</TooltipContent>
                      </Tooltip>
                    )}
                  </div>
                </div>

                {connectedController && !portCollapsedState[port.id] && (
                  <ConnectedControllerSettings
                    node={node}
                    controller={connectedController}
                  />
                )}
              </SettingsRow>
            );
          })}
        </SettingsGroupContent>
      </SettingsGroup>

      <SettingsGroup>
        <SettingsGroupHeader>
          <span>2-Pin Ports</span>
        </SettingsGroupHeader>
        <SettingsGroupContent>
          {Object.values(data.twoPinPorts || {}).map((port) => {
            const connectedSensor = Object.values(
              data.reedSwitchSensors || {},
            ).find((sensor) => sensor.portId === port.id);

            return (
              <SettingsRow key={port.id} className="flex flex-col gap-2">
                <div
                  className={cn(
                    "flex flex-row items-center justify-between cursor-pointer hover:bg-gray-50 -mx-2 px-2 py-1 rounded",
                    connectedSensor && "cursor-pointer",
                  )}
                  onClick={() => {
                    if (connectedSensor) {
                      setPortCollapsedState((prev) => ({
                        ...prev,
                        [port.id]: !prev[port.id],
                      }));
                    }
                  }}
                >
                  <div className="flex items-center gap-2">
                    {connectedSensor && (
                      <ChevronDown
                        className={cn(
                          "size-4 transition-transform duration-300 text-gray-500",
                          portCollapsedState[port.id] && "-rotate-90",
                        )}
                      />
                    )}
                    <div className="flex flex-col">
                      <div className="text-sm font-medium">{port.label}</div>
                      <div className="text-xs text-gray-500">
                        {connectedSensor
                          ? `Reed Switch: ${connectedSensor.label}`
                          : "Not connected"}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div
                      className={cn(
                        "size-3 rounded-full",
                        connectedSensor ? "bg-green-400" : "bg-gray-400",
                      )}
                    />
                    {!connectedSensor && !readOnly && (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <TooltipDropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="size-5"
                              >
                                <PlusIcon className="size-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent className="mr-5">
                              <DropdownMenuLabel>
                                Add device to this port
                              </DropdownMenuLabel>
                              <DropdownMenuItem
                                onClick={() => {
                                  // Create new reed switch sensor
                                  const newSensorId = `reed-switch-sensor-${randomId()}`;
                                  const sensor = {
                                    id: newSensorId,
                                    label: `Reed Switch`,
                                    portId: port.id,
                                    onOpen: {},
                                    onClose: {},
                                  };
                                  updateNestedData(
                                    `reedSwitchSensors.${newSensorId}`,
                                    sensor,
                                  );

                                  // Update port status
                                  if (
                                    data.twoPinPorts &&
                                    data.twoPinPorts[port.id]
                                  ) {
                                    updateNestedData(`twoPinPorts.${port.id}`, {
                                      id: port.id,
                                      label: port.label,
                                      connected: true,
                                      deviceType: "reedSwitch",
                                      sensorId: newSensorId,
                                    });
                                  }
                                }}
                              >
                                Reed Switch Sensor
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </TooltipDropdownMenu>
                        </TooltipTrigger>
                        <TooltipContent>Add device</TooltipContent>
                      </Tooltip>
                    )}
                    {connectedSensor && !readOnly && (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="size-5"
                            onClick={(e) => {
                              e.stopPropagation();
                              // Remove sensor
                              deleteNestedData(
                                `reedSwitchSensors.${connectedSensor.id}`,
                              );
                              updateNestedData(`twoPinPorts.${port.id}`, {
                                ...port,
                                connected: false,
                                deviceType: "none",
                                sensorId: undefined,
                              });
                            }}
                          >
                            <Trash2Icon className="size-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>Remove Device</TooltipContent>
                      </Tooltip>
                    )}
                  </div>
                </div>

                {connectedSensor && !portCollapsedState[port.id] && (
                  <ConnectedReedSwitchSensorSettings
                    sensor={connectedSensor}
                    node={node}
                  />
                )}
              </SettingsRow>
            );
          })}
        </SettingsGroupContent>
      </SettingsGroup>

      <SettingsGroup>
        <SettingsGroupHeader>
          <span>ADC Ports</span>
        </SettingsGroupHeader>
        <SettingsGroupContent>
          {Object.values(data.adcPorts || {}).map((port) => {
            const connectedSensor = Object.values(data.pirSensors || {}).find(
              (sensor) => sensor.portId === port.id,
            );

            return (
              <SettingsRow key={port.id} className="flex flex-col gap-2">
                <div
                  className={cn(
                    "flex flex-row items-center justify-between cursor-pointer hover:bg-gray-50 -mx-2 px-2 py-1 rounded",
                    connectedSensor && "cursor-pointer",
                  )}
                  onClick={() => {
                    if (connectedSensor) {
                      setPortCollapsedState((prev) => ({
                        ...prev,
                        [port.id]: !prev[port.id],
                      }));
                    }
                  }}
                >
                  <div className="flex items-center gap-2">
                    {connectedSensor && (
                      <ChevronDown
                        className={cn(
                          "size-4 transition-transform duration-300 text-gray-500",
                          portCollapsedState[port.id] && "-rotate-90",
                        )}
                      />
                    )}
                    <div className="flex flex-col">
                      <div className="text-sm font-medium">{port.label}</div>
                      <div className="text-xs text-gray-500">
                        {connectedSensor
                          ? `PIR Sensor: ${connectedSensor.label}`
                          : "Not connected"}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div
                      className={cn(
                        "size-3 rounded-full",
                        connectedSensor ? "bg-green-400" : "bg-gray-400",
                      )}
                    />
                    {!connectedSensor && !readOnly && (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <TooltipDropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="size-5"
                              >
                                <PlusIcon className="size-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent className="mr-5">
                              <DropdownMenuLabel>
                                Add device to this port
                              </DropdownMenuLabel>
                              <DropdownMenuItem
                                onClick={() => {
                                  // Create new PIR sensor
                                  const newSensorId = `pir-sensor-${randomId()}`;
                                  const sensor = {
                                    id: newSensorId,
                                    label: `PIR Sensor`,
                                    portId: port.id,
                                    onActivate: {},
                                    onDeactivate: {},
                                    onActivateDelay: 0,
                                    onDeactivateDelay: 0,
                                  };
                                  updateNestedData(
                                    `pirSensors.${newSensorId}`,
                                    sensor,
                                  );

                                  // Update port status
                                  updateNestedData(`adcPorts.${port.id}`, {
                                    id: port.id,
                                    label: port.label,
                                    connected: true,
                                    deviceType: "pirSensor",
                                    sensorId: newSensorId,
                                  });
                                }}
                              >
                                PIR Sensor
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </TooltipDropdownMenu>
                        </TooltipTrigger>
                        <TooltipContent>Add device</TooltipContent>
                      </Tooltip>
                    )}
                    {connectedSensor && !readOnly && (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="size-5"
                            onClick={(e) => {
                              e.stopPropagation();
                              // Remove sensor
                              deleteNestedData(
                                `pirSensors.${connectedSensor.id}`,
                              );

                              // Update port status
                              updateNestedData(`adcPorts.${port.id}`, {
                                id: port.id,
                                label: port.label,
                                connected: false,
                                deviceType: "none",
                              });
                            }}
                          >
                            <Trash2Icon className="size-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>Remove Device</TooltipContent>
                      </Tooltip>
                    )}
                  </div>
                </div>

                {connectedSensor && !portCollapsedState[port.id] && (
                  <ConnectedPirSensorSettings
                    node={node}
                    sensor={connectedSensor}
                  />
                )}
              </SettingsRow>
            );
          })}
        </SettingsGroupContent>
      </SettingsGroup>

      <SettingsGroup>
        <SettingsGroupHeader>
          <span>0-10V Ports</span>
        </SettingsGroupHeader>
        <SettingsGroupContent>
          {Object.values(data.zeroToTenVoltPorts || {}).map((port) => {
            const connectedDimmer = Object.values(
              data.zeroToTenVoltDimmers || {},
            ).find((dimmer) => dimmer.portId === port.id);

            return (
              <SettingsRow key={port.id} className="flex flex-col gap-2">
                <div
                  className={cn(
                    "flex flex-row items-center justify-between cursor-pointer hover:bg-gray-50 -mx-2 px-2 py-1 rounded",
                    connectedDimmer && "cursor-pointer",
                  )}
                  onClick={() => {
                    if (connectedDimmer) {
                      setPortCollapsedState((prev) => ({
                        ...prev,
                        [port.id]: !prev[port.id],
                      }));
                    }
                  }}
                >
                  <div className="flex items-center gap-2">
                    {connectedDimmer && (
                      <ChevronDown
                        className={cn(
                          "size-4 transition-transform duration-300 text-gray-500",
                          portCollapsedState[port.id] && "-rotate-90",
                        )}
                      />
                    )}
                    <div className="flex flex-col">
                      <div className="text-sm font-medium">{port.label}</div>
                      <div className="text-xs text-gray-500">
                        {connectedDimmer
                          ? `0-10V Dimmer: ${connectedDimmer.name}`
                          : "Not connected"}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div
                      className={cn(
                        "size-3 rounded-full",
                        connectedDimmer ? "bg-green-400" : "bg-gray-400",
                      )}
                    />
                    {!connectedDimmer && !readOnly && (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="size-5"
                            onClick={() => {
                              // Create new 0-10V dimmer
                              const newDimmerId = `0-10v-dimmer-${randomId()}`;
                              const dimmer = {
                                id: newDimmerId,
                                name: "0-10V Dimmer",
                                portId: port.id,
                                dimmingType: "sinking" as const,
                                sortIndex: 0,
                                showLabel: true,
                                icon: "ceilingLamp" as const,
                                useRelay: false,
                                minBrightness: 0,
                                maxBrightness: 100,
                                defaultDimmingSpeed: 0.2,
                              };
                              // Update port status
                              updateNestedData(
                                `zeroToTenVoltDimmers.${newDimmerId}`,
                                dimmer,
                              );
                              updateNestedData(
                                `zeroToTenVoltPorts.${port.id}`,
                                {
                                  id: port.id,
                                  label: port.label,
                                  connected: true,
                                  deviceType: "zeroToTenVoltDimmer",
                                  dimmerId: newDimmerId,
                                },
                              );
                            }}
                          >
                            <PlusIcon className="size-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>Add 0-10V Dimmer</TooltipContent>
                      </Tooltip>
                    )}
                    {connectedDimmer && !readOnly && (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="size-5"
                            onClick={(e) => {
                              e.stopPropagation();

                              // Remove Dimmer
                              deleteNestedData(
                                `zeroToTenVoltDimmers.${connectedDimmer.id}`,
                              );

                              updateNestedData(
                                `zeroToTenVoltPorts.${port.id}`,
                                {
                                  id: port.id,
                                  label: port.label,
                                  connected: false,
                                  deviceType: "none",
                                },
                              );

                              if (connectedDimmer.relayConnectorId) {
                                const relayConnector =
                                  data.relayConnectors?.[
                                    connectedDimmer.relayConnectorId
                                  ];
                                updateNestedData(
                                  `relayConnectors.${connectedDimmer.relayConnectorId}`,
                                  {
                                    ...relayConnector,
                                    connected: false,
                                    deviceType: "none",
                                    dimmerId: undefined,
                                  },
                                );
                              }
                            }}
                          >
                            <Trash2Icon className="size-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>Remove Device</TooltipContent>
                      </Tooltip>
                    )}
                  </div>
                </div>

                {connectedDimmer && !portCollapsedState[port.id] && (
                  <ConnectedZeroToTenVoltDimmerSettings
                    dimmer={connectedDimmer}
                    node={node}
                  />
                )}
              </SettingsRow>
            );
          })}
        </SettingsGroupContent>
      </SettingsGroup>

      <SettingsGroup>
        <SettingsGroupHeader>
          <span>Output Ports</span>
        </SettingsGroupHeader>
        <SettingsGroupContent>
          {Object.values(data.relayConnectors || {}).map((connector) => {
            const connectedDevice = Object.values(data.relayOutputs || {}).find(
              (device) => device.relayConnectorId === connector.id,
            );

            const connectedDimmer = Object.values(
              data.zeroToTenVoltDimmers || {},
            ).find((dimmer) => dimmer.relayConnectorId === connector.id);

            return (
              <SettingsRow key={connector.id} className="flex flex-col gap-2">
                <div
                  className={cn(
                    "flex flex-row items-center justify-between cursor-pointer hover:bg-gray-50 -mx-2 px-2 py-1 rounded",
                    (connectedDevice || connectedDimmer) && "cursor-pointer",
                  )}
                  onClick={() => {
                    if (connectedDevice || connectedDimmer) {
                      setPortCollapsedState((prev) => ({
                        ...prev,
                        [connector.id]: !prev[connector.id],
                      }));
                    }
                  }}
                >
                  <div className="flex items-center gap-2">
                    {(connectedDevice || connectedDimmer) && (
                      <ChevronDown
                        className={cn(
                          "size-4 transition-transform duration-300 text-gray-500",
                          portCollapsedState[connector.id] && "-rotate-90",
                        )}
                      />
                    )}
                    <div className="flex flex-col">
                      <div className="text-sm font-medium">
                        {connector.label}
                      </div>
                      <div className="text-xs text-gray-500">
                        {connectedDevice
                          ? `Relay Output: ${connectedDevice.name}`
                          : connectedDimmer
                            ? `Used by 0-10V Dimmer: ${connectedDimmer.name}`
                            : "Not connected"}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div
                      className={cn(
                        "size-3 rounded-full",
                        connectedDevice || connectedDimmer
                          ? "bg-green-400"
                          : "bg-gray-400",
                      )}
                    />
                    {!connectedDevice && !connectedDimmer && !readOnly && (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="size-5"
                            onClick={() => {
                              // Create new relay output
                              const newDeviceId = `relay-output-${randomId()}`;
                              const device = {
                                id: newDeviceId,
                                name: "Relay Output",
                                relayConnectorId: connector.id,
                                sortIndex: 0,
                                showLabel: true,
                                icon: "ceilingLamp" as const,
                              };

                              updateNestedData(
                                `relayOutputs.${newDeviceId}`,
                                device,
                              );

                              // Update connector status
                              updateNestedData(
                                `relayConnectors.${connector.id}`,
                                {
                                  ...connector,
                                  connected: true,
                                  deviceType: "relayOutput",
                                  relayOutputId: newDeviceId,
                                },
                              );
                            }}
                          >
                            <PlusIcon className="size-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>Add Relay Output</TooltipContent>
                      </Tooltip>
                    )}
                    {connectedDevice && !readOnly && (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="size-5"
                            onClick={(e) => {
                              e.stopPropagation();
                              // Remove device

                              deleteNestedData(
                                `relayOutputs.${connectedDevice.id}`,
                              );
                              updateNestedData(
                                `relayConnectors.${connector.id}`,
                                {
                                  ...connector,
                                  connected: false,
                                  deviceType: "none",
                                  relayOutputId: undefined,
                                },
                              );
                            }}
                          >
                            <Trash2Icon className="size-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>Remove Device</TooltipContent>
                      </Tooltip>
                    )}
                  </div>
                </div>

                {connectedDevice && !portCollapsedState[connector.id] && (
                  <ConnectedRelayOutputSettings
                    connectedDevice={connectedDevice}
                    node={node}
                  />
                )}
              </SettingsRow>
            );
          })}
        </SettingsGroupContent>
      </SettingsGroup>
    </div>
  );
}

function ConnectedControllerSettings({
  node,
  controller,
}: {
  node: GraphNodesByType["canbusControllerContainer"];
  controller: CanBusControllerController;
}) {
  const { readOnly } = useReactFlowContext();
  const { updateNestedData } = useData(node.data.dataId, "canbusController");

  return (
    <div className="ml-4 space-y-2 border-l-2 border-gray-200 pl-4">
      <SettingsInput
        label="Device Name"
        value={controller.label}
        onEndEdit={(value) => {
          if (typeof value !== "string") {
            return;
          }
          updateNestedData(`controllers.${controller.id}`, {
            ...controller,
            label: value,
          });
        }}
        disabled={readOnly}
      />

      {controller.type === "toggle" && (
        <>
          <ToggleControllerActionSettings
            node={node}
            controller={controller}
            actionType="onUpPress"
          />
          <ToggleControllerActionSettings
            node={node}
            controller={controller}
            actionType="onUpRelease"
          />
          <ToggleControllerActionSettings
            node={node}
            controller={controller}
            actionType="onUpClick"
          />
          <ToggleControllerActionSettings
            node={node}
            controller={controller}
            actionType="onUpHold"
          />
          <ToggleControllerActionSettings
            node={node}
            controller={controller}
            actionType="onUpHoldRelease"
          />
          <ToggleControllerActionSettings
            node={node}
            controller={controller}
            actionType="onDownPress"
          />
          <ToggleControllerActionSettings
            node={node}
            controller={controller}
            actionType="onDownRelease"
          />
          <ToggleControllerActionSettings
            node={node}
            controller={controller}
            actionType="onDownClick"
          />
          <ToggleControllerActionSettings
            node={node}
            controller={controller}
            actionType="onDownHold"
          />
          <ToggleControllerActionSettings
            node={node}
            controller={controller}
            actionType="onDownHoldRelease"
          />
        </>
      )}

      {controller.type === "momentary" && (
        <>
          <MomentaryControllerActionSettings
            node={node}
            controller={controller}
            actionType="onUpPress"
          />
          <MomentaryControllerActionSettings
            node={node}
            controller={controller}
            actionType="onUpRelease"
          />
          <MomentaryControllerActionSettings
            node={node}
            controller={controller}
            actionType="onUpClick"
          />
          <MomentaryControllerActionSettings
            node={node}
            controller={controller}
            actionType="onUpHold"
          />
          <MomentaryControllerActionSettings
            node={node}
            controller={controller}
            actionType="onUpHoldRelease"
          />
        </>
      )}
    </div>
  );
}

function ToggleControllerActionSettings({
  node,
  controller,
  actionType,
}: {
  node: GraphNodesByType["canbusControllerContainer"];
  controller: ToggleCanBusController;
  actionType: ToggleCanBusControllerViaId;
}) {
  const { nodes } = useReactFlowContext();
  const { deleteNestedData, updateNestedData } = useData(
    node.data.dataId,
    "canbusController",
  );
  const dataKey = `controllers.${controller.id}.${actionType}` as const;

  const actions = Object.values(controller[actionType] ?? {}).sort(
    (a, b) => a.sortIndex - b.sortIndex,
  );

  const connectableDevices = useConnectableDevices({
    nodes,
    nodeType: "canbusControllerContainer",
    actions,
  });

  return (
    <ActionSettingsWithHeader
      type="targetValue"
      label={match(actionType)
        .with("onUpClick", () => "On up click")
        .with("onUpHold", () => "On up hold")
        .with("onDownClick", () => "On down click")
        .with("onDownHold", () => "On down hold")
        .with("onUpPress", () => "On up press")
        .with("onUpRelease", () => "On up release")
        .with("onDownPress", () => "On down press")
        .with("onDownRelease", () => "On down release")
        .with("onUpHoldRelease", () => "On up hold release")
        .with("onDownHoldRelease", () => "On down hold release")
        .exhaustive()}
      node={node}
      sourceHandle={controller.id}
      actions={actions}
      devices={connectableDevices}
      onDelete={(id) => {
        deleteNestedData(`${dataKey}.${id}`);
      }}
      onDelayChange={(id, value) => {
        updateNestedData(`${dataKey}.${id}.delay`, value);
      }}
      onDeviceChange={(id, deviceId) => {
        updateNestedData(`${dataKey}.${id}.deviceId`, deviceId);
      }}
      onDimSpeedChange={(id, value) => {
        updateNestedData(`${dataKey}.${id}.dimSpeed`, value);
      }}
      onTargetValueChange={(id, value) => {
        updateNestedData(`${dataKey}.${id}.targetValue`, value);
      }}
      onAddAction={(deviceId) => {
        const newActionId = randomId();
        const mapSize = Object.values(controller[actionType]).length;
        updateNestedData(`${dataKey}.${newActionId}`, {
          id: newActionId,
          sortIndex: mapSize,
          deviceId,
          dimSpeed: match(actionType)
            .with(
              "onUpClick",
              "onDownClick",
              "onUpPress",
              "onUpRelease",
              "onDownPress",
              "onDownRelease",
              "onUpHoldRelease",
              "onDownHoldRelease",
              () => getDeviceDimmingSpeed(deviceId, connectableDevices, nodes),
            )
            .with("onUpHold", "onDownHold", () => getHoldDimmingSpeed())
            .exhaustive(),
          targetValue: match(actionType)
            .with(
              "onUpClick",
              "onUpHold",
              "onUpPress",
              "onUpHoldRelease",
              () => 100,
            )
            .with(
              "onDownClick",
              "onDownHold",
              "onDownPress",
              "onDownHoldRelease",
              () => 0,
            )
            .with("onUpRelease", "onDownRelease", () => 100)
            .exhaustive(),
          onValue: 100,
          offValue: 0,
          delay: 0,
          type: "lighting",
        });
      }}
    />
  );
}

function MomentaryControllerActionSettings({
  node,
  controller,
  actionType,
}: {
  node: GraphNodesByType["canbusControllerContainer"];
  controller: MomentaryCanBusController;
  actionType: MomentaryCanBusControllerViaId;
}) {
  const { nodes } = useReactFlowContext();
  const { deleteNestedData, updateNestedData } = useData(
    node.data.dataId,
    "canbusController",
  );
  const dataKey = `controllers.${controller.id}.${actionType}` as const;

  const actions = Object.values(controller[actionType] ?? {}).sort(
    (a, b) => a.sortIndex - b.sortIndex,
  );

  const connectableDevices = useConnectableDevices({
    nodes,
    nodeType: "canbusControllerContainer",
    actions,
  });

  return (
    <ActionSettingsWithHeader
      type="targetValue"
      label={match(actionType)
        .with("onUpClick", () => "On click")
        .with("onUpHold", () => "On hold")
        .with("onUpPress", () => "On press")
        .with("onUpRelease", () => "On release")
        .with("onUpHoldRelease", () => "On hold release")
        .exhaustive()}
      node={node}
      sourceHandle={controller.id}
      actions={actions}
      devices={connectableDevices}
      onDelete={(id) => {
        deleteNestedData(`${dataKey}.${id}`);
      }}
      onDelayChange={(id, value) => {
        updateNestedData(`${dataKey}.${id}.delay`, value);
      }}
      onDeviceChange={(id, deviceId) => {
        updateNestedData(`${dataKey}.${id}.deviceId`, deviceId);
      }}
      onDimSpeedChange={(id, value) => {
        updateNestedData(`${dataKey}.${id}.dimSpeed`, value);
      }}
      onTargetValueChange={(id, value) => {
        updateNestedData(`${dataKey}.${id}.targetValue`, value);
      }}
      onAddAction={(deviceId) => {
        const newActionId = randomId();
        const mapSize = Object.values(controller[actionType]).length;
        updateNestedData(`${dataKey}.${newActionId}`, {
          id: newActionId,
          sortIndex: mapSize,
          deviceId,
          dimSpeed: getDeviceDimmingSpeed(deviceId, connectableDevices, nodes),
          targetValue: 100,
          onValue: 100,
          offValue: 0,
          delay: 0,
          type: "lighting",
        });
      }}
    />
  );
}

function ConnectedReedSwitchSensorSettings({
  node,
  sensor,
}: {
  node: GraphNodesByType["canbusControllerContainer"];
  sensor: ReedSwitchSensor;
}) {
  const { readOnly } = useReactFlowContext();
  const { updateNestedData } = useData(node.data.dataId, "canbusController");

  return (
    <div className="ml-4 space-y-2 border-l-2 border-gray-200 pl-4">
      <SettingsInput
        label="Device Name"
        value={sensor.label}
        onEndEdit={(value) => {
          if (typeof value !== "string") {
            return;
          }
          updateNestedData(`reedSwitchSensors.${sensor.id}`, {
            ...sensor,
            label: value,
          });
        }}
        disabled={readOnly}
      />

      <ReedSwitchSensorActionSettings
        node={node}
        sensor={sensor}
        actionType="onOpen"
      />
      <ReedSwitchSensorActionSettings
        node={node}
        sensor={sensor}
        actionType="onClose"
      />
    </div>
  );
}

function ConnectedPirSensorSettings({
  node,
  sensor,
}: {
  node: GraphNodesByType["canbusControllerContainer"];
  sensor: PirSensor;
}) {
  const { readOnly } = useReactFlowContext();
  const { updateNestedData } = useData(node.data.dataId, "canbusController");

  //  const sensor = data?.pirSensors?.[sensor.id];

  return (
    <div className="ml-4 space-y-2 border-l-2 border-gray-200 pl-4">
      <SettingsInput
        label="Device Name"
        value={sensor.label}
        onEndEdit={(value) => {
          if (typeof value !== "string") {
            return;
          }
          updateNestedData(`pirSensors.${sensor.id}.label`, value);
        }}
        disabled={readOnly}
      />

      <PirSensorActionSettings
        node={node}
        sensor={sensor}
        actionType="onActivate"
      />
      <PirSensorActionSettings
        node={node}
        sensor={sensor}
        actionType="onDeactivate"
      />
    </div>
  );
}

function PirSensorActionSettings({
  node,
  sensor,
  actionType,
}: {
  node: GraphNodesByType["canbusControllerContainer"];
  sensor: PirSensor;
  actionType: "onActivate" | "onDeactivate";
}) {
  const { nodes } = useReactFlowContext();
  const { updateNestedData, deleteNestedData } = useData(
    node.data.dataId,
    "canbusController",
  );
  const dataKey = `pirSensors.${sensor.id}.${actionType}` as const;

  const actions = Object.values(sensor[actionType] ?? {}).sort(
    (a, b) => a.sortIndex - b.sortIndex,
  );

  const connectableDevices = useConnectableDevices({
    nodes,
    nodeType: "canbusControllerContainer",
    actions,
  });

  return (
    <ActionSettingsWithHeader
      type="targetValue"
      label={match(actionType)
        .with("onActivate", () => "On activate")
        .with("onDeactivate", () => "On deactivate")
        .exhaustive()}
      node={node}
      sourceHandle={sensor.id}
      actions={actions}
      devices={connectableDevices}
      onDelete={(id) => {
        deleteNestedData(`${dataKey}.${id}`);
      }}
      onDelayChange={(id, value) => {
        updateNestedData(`${dataKey}.${id}.delay`, value);
      }}
      onDeviceChange={(id, deviceId) => {
        updateNestedData(`${dataKey}.${id}.deviceId`, deviceId);
      }}
      onDimSpeedChange={(id, value) => {
        updateNestedData(`${dataKey}.${id}.dimSpeed`, value);
      }}
      onTargetValueChange={(id, value) => {
        updateNestedData(`${dataKey}.${id}.targetValue`, value);
      }}
      onAddAction={(deviceId) => {
        const newActionId = randomId();
        const mapSize = Object.values(sensor[actionType]).length;
        updateNestedData(`${dataKey}.${newActionId}`, {
          id: newActionId,
          sortIndex: mapSize,
          deviceId,
          dimSpeed: getDeviceDimmingSpeed(deviceId, connectableDevices, nodes),
          targetValue: match(actionType)
            .with("onActivate", () => 100)
            .with("onDeactivate", () => 0)
            .exhaustive(),
          onValue: 100,
          offValue: 0,
          delay: 0,
          type: "lighting",
        });
      }}
    />
  );
}

function ReedSwitchSensorActionSettings({
  node,
  sensor,
  actionType,
}: {
  node: GraphNodesByType["canbusControllerContainer"];
  sensor: ReedSwitchSensor;
  actionType: "onOpen" | "onClose";
}) {
  const { nodes } = useReactFlowContext();
  const { updateNestedData, deleteNestedData } = useData(
    node.data.dataId,
    "canbusController",
  );
  const dataKey = `reedSwitchSensors.${sensor.id}.${actionType}` as const;

  const actions = Object.values(sensor[actionType] ?? {}).sort(
    (a, b) => a.sortIndex - b.sortIndex,
  );

  const connectableDevices = useConnectableDevices({
    nodes,
    nodeType: "canbusControllerContainer",
    actions,
  });

  return (
    <ActionSettingsWithHeader
      type="targetValue"
      label={match(actionType)
        .with("onOpen", () => "On open")
        .with("onClose", () => "On close")
        .exhaustive()}
      node={node}
      sourceHandle={sensor.id}
      actions={actions}
      devices={connectableDevices}
      onDelete={(id) => {
        deleteNestedData(`${dataKey}.${id}`);
      }}
      onDelayChange={(id, value) => {
        updateNestedData(`${dataKey}.${id}.delay`, value);
      }}
      onDeviceChange={(id, deviceId) => {
        updateNestedData(`${dataKey}.${id}.deviceId`, deviceId);
      }}
      onDimSpeedChange={(id, value) => {
        updateNestedData(`${dataKey}.${id}.dimSpeed`, value);
      }}
      onTargetValueChange={(id, value) => {
        updateNestedData(`${dataKey}.${id}.targetValue`, value);
      }}
      onAddAction={(deviceId) => {
        const newActionId = randomId();
        const mapSize = Object.values(sensor[actionType]).length;
        updateNestedData(`${dataKey}.${newActionId}`, {
          id: newActionId,
          sortIndex: mapSize,
          deviceId,
          dimSpeed: getDeviceDimmingSpeed(deviceId, connectableDevices, nodes),
          targetValue: match(actionType)
            .with("onOpen", () => 100)
            .with("onClose", () => 0)
            .exhaustive(),
          onValue: 100,
          offValue: 0,
          delay: 0,
          type: "lighting",
        });
      }}
    />
  );
}

function ConnectedZeroToTenVoltDimmerSettings({
  dimmer,
  node,
}: {
  dimmer: ZeroToTenVoltDimmer;
  node: GraphNodesByType["canbusControllerContainer"];
}) {
  const { readOnly } = useReactFlowContext();
  const { data, updateNestedData } = useData(
    node.data.dataId,
    "canbusController",
  );

  const availableRelayConnectors = useMemo(() => {
    if (!data) {
      return [];
    }
    const relayConnectors = data.relayConnectors || {};
    const relayOutputs = data.relayOutputs || {};
    const connectors: RelayConnector[] = [];

    Object.values(relayConnectors).forEach((connector) => {
      // Check if this connector is occupied by a relay output
      const isOccupiedByRelayOutput = Object.values(relayOutputs).some(
        (output) => output.relayConnectorId === connector.id,
      );

      // A connector is available if:
      // 1. It's not connected to anything AND not occupied by a relay output, OR
      // 2. It's already connected to this specific dimmer
      const isAvailable =
        (connector.deviceType === "none" &&
          !connector.connected &&
          !isOccupiedByRelayOutput) ||
        (connector.deviceType === "zeroToTenVoltDimmer" &&
          connector.dimmerId === dimmer.id);

      if (isAvailable) {
        connectors.push(connector);
      }
    });

    return connectors;
  }, [data?.relayConnectors, dimmer.id]);

  const hasAvailableRelayConnectors = availableRelayConnectors.length > 0;
  const canUseRelay = hasAvailableRelayConnectors || dimmer.useRelay;

  const handleRelayToggle = (checked: boolean) => {
    if (readOnly) {
      return;
    }

    if (checked && hasAvailableRelayConnectors) {
      // Assign first available relay connector
      const firstAvailable = availableRelayConnectors[0];
      updateNestedData(`zeroToTenVoltDimmers.${dimmer.id}.useRelay`, true);
      updateNestedData(
        `zeroToTenVoltDimmers.${dimmer.id}.relayConnectorId`,
        firstAvailable.id,
      );

      // Update relay connector status
      updateNestedData(`relayConnectors.${firstAvailable.id}.connected`, true);
      updateNestedData(
        `relayConnectors.${firstAvailable.id}.deviceType`,
        "zeroToTenVoltDimmer",
      );
      updateNestedData(
        `relayConnectors.${firstAvailable.id}.dimmerId`,
        dimmer.id,
      );
    } else if (!checked && dimmer.relayConnectorId) {
      // Release the assigned relay connector
      updateNestedData(`zeroToTenVoltDimmers.${dimmer.id}.useRelay`, false);
      updateNestedData(
        `zeroToTenVoltDimmers.${dimmer.id}.relayConnectorId`,
        undefined,
      );
      updateNestedData(
        `relayConnectors.${dimmer.relayConnectorId}.connected`,
        false,
      );
      updateNestedData(
        `relayConnectors.${dimmer.relayConnectorId}.deviceType`,
        "none",
      );
      updateNestedData(
        `relayConnectors.${dimmer.relayConnectorId}.dimmerId`,
        undefined,
      );
    }
  };

  if (!data) {
    return <></>;
  }

  return (
    <div className="ml-4 space-y-2 border-l-2 border-gray-200 pl-4">
      <div className="flex flex-row gap-2 text-xs font-bold text-gray-800 mt-2 items-center">
        <LampPopover
          activeIconKey={dimmer.icon}
          onIconClick={(iconKey: DeviceIconKey | undefined) => {
            updateNestedData(`zeroToTenVoltDimmers.${dimmer.id}.icon`, iconKey);
          }}
          disabled={readOnly}
        />
        <SettingsInput
          className="text-sm font-normal h-10"
          value={dimmer.name}
          onEndEdit={(value) => {
            if (typeof value !== "string") {
              return;
            }
            updateNestedData(`zeroToTenVoltDimmers.${dimmer.id}.name`, value);
          }}
          disabled={readOnly}
        />
      </div>

      <SettingsRow>
        <SettingsLabel>Dimming Type</SettingsLabel>
        <select
          value={dimmer.dimmingType}
          onChange={(e) => {
            if (readOnly) {
              return;
            }
            const dimmingType = e.target.value as "sinking" | "sourcing";
            updateNestedData(
              `zeroToTenVoltDimmers.${dimmer.id}.dimmingType`,
              dimmingType,
            );
          }}
          disabled={readOnly}
          className="px-2 py-1 border rounded text-sm"
        >
          <option value="sinking">Sinking</option>
          <option value="sourcing">Sourcing</option>
        </select>
      </SettingsRow>

      <SettingsRow>
        <div className="flex items-center gap-2">
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="flex items-center gap-2">
                <Checkbox
                  id={`relay-${dimmer.id}`}
                  checked={dimmer.useRelay || false}
                  disabled={readOnly || !canUseRelay}
                  onCheckedChange={(checked) =>
                    handleRelayToggle(checked as boolean)
                  }
                />
                <Label
                  htmlFor={`relay-${dimmer.id}`}
                  className="text-gray-500 text-xs font-semibold"
                >
                  Use Relay
                </Label>
              </div>
            </TooltipTrigger>
            {!canUseRelay && (
              <TooltipContent>
                No relay connectors available. All 4 relay connectors are
                currently in use.
              </TooltipContent>
            )}
          </Tooltip>
        </div>
      </SettingsRow>

      <div className="flex flex-row items-center gap-2 flex-wrap">
        <SettingsInput
          className="w-auto flex-shrink-0"
          inputClassName="w-[60px] text-center flex-shrink-0"
          label="Min. brightness"
          value={(dimmer.minBrightness ?? 0).toString()}
          disabled={readOnly}
          onEndEdit={(value) => {
            if (readOnly) {
              return;
            }
            const minBrightness = parseInt(value);
            if (isNaN(minBrightness)) {
              return;
            }
            updateNestedData(
              `zeroToTenVoltDimmers.${dimmer.id}.minBrightness`,
              minBrightness,
            );
          }}
        />
        <SettingsInput
          className="w-auto flex-shrink-0"
          inputClassName="w-[60px] text-center flex-shrink-0"
          label="Max. brightness"
          value={(dimmer.maxBrightness ?? 100).toString()}
          disabled={readOnly}
          onEndEdit={(value) => {
            if (readOnly) {
              return;
            }
            const maxBrightness = parseInt(value);
            if (isNaN(maxBrightness)) {
              return;
            }
            updateNestedData(
              `zeroToTenVoltDimmers.${dimmer.id}.maxBrightness`,
              maxBrightness,
            );
          }}
        />
      </div>

      <SettingsInput
        className="w-auto"
        inputClassName="w-[60px] text-center"
        label="Default dim speed (sec)"
        value={(dimmer.defaultDimmingSpeed ?? 0.2).toString()}
        disabled={readOnly}
        onEndEdit={(value) => {
          if (readOnly) {
            return;
          }
          const dimSpeed = parseFloat(value);
          if (isNaN(dimSpeed)) {
            return;
          }
          updateNestedData(
            `zeroToTenVoltDimmers.${dimmer.id}.defaultDimmingSpeed`,
            dimSpeed,
          );
        }}
      />
    </div>
  );
}

function ConnectedRelayOutputSettings({
  connectedDevice,
  node,
}: {
  connectedDevice: { id: string; name: string; icon?: string };
  node: GraphNodesByType["canbusControllerContainer"];
}) {
  const { readOnly } = useReactFlowContext();
  const { data, updateNestedData } = useData(
    node.data.dataId,
    "canbusController",
  );

  if (!data) {
    return <></>;
  }

  const device = data.relayOutputs?.[connectedDevice.id];

  return (
    <div className="ml-4 space-y-2 border-l-2 border-gray-200 pl-4">
      <div className="flex flex-row gap-2 text-xs font-bold text-gray-800 mt-2 items-center">
        <LampPopover
          activeIconKey={device.icon as DeviceIconKey}
          onIconClick={(iconKey: DeviceIconKey | undefined) => {
            updateNestedData(
              `relayOutputs.${connectedDevice.id}.icon`,
              iconKey,
            );
          }}
          disabled={readOnly}
        />
        <SettingsInput
          className="text-sm font-normal h-10"
          value={connectedDevice.name}
          onEndEdit={(value) => {
            if (typeof value !== "string") {
              return;
            }
            updateNestedData(`relayOutputs.${connectedDevice.id}.name`, value);
          }}
          disabled={readOnly}
        />
      </div>
    </div>
  );
}
