import {
  Handle,
  Position,
  useNodeId,
  useUpdateNodeInternals,
} from "@xyflow/react";
import { CanBusControllerIcon } from "./CanBusControllerIcon";
import { SldComponent, SldNodeData } from "../sld/types";
import { GridConfig } from "@/lib/gridConfig";
import { useGetData } from "@/components/data/useData";
import { useEffect } from "react";

// Utility function to get connector styles for CanBo controller
function getCanBoConnectorStyle(handleId: string): {
  stroke: string;
  strokeWidth: number;
  strokeOpacity: number;
} {
  switch (handleId) {
    case "knob-input":
      return {
        stroke: "#8b5cf6", // Purple for knob input
        strokeWidth: 2,
        strokeOpacity: 0.7,
      };
    case "sw3":
    case "sw4":
    case "sw1":
    case "sw2":
      return {
        stroke: "#eab308", // Yellow for switches
        strokeWidth: 2.5,
        strokeOpacity: 0.7,
      };
    case "r1":
    case "r2":
    case "r3":
    case "r4":
      return {
        stroke: "#dc2626", // Red for relays
        strokeWidth: 2,
        strokeOpacity: 0.7,
      };
    case "d1":
    case "d3":
      return {
        stroke: "#06b6d4", // Cyan for door sensors
        strokeWidth: 2,
        strokeOpacity: 0.7,
      };
    case "5v-aux":
    case "24v-aux":
      return {
        stroke: "#dc2626", // Red for power
        strokeWidth: 3,
        strokeOpacity: 0.9,
      };
    case "0-10v-src":
    case "0-10v-sink":
      return {
        stroke: "#dc2626", // Red for 0-10V dimmer
        strokeWidth: 2,
        strokeOpacity: 0.7,
      };
    case "io1":
    case "io2":
      return {
        stroke: "#10b981", // Green for SomoBus I/O
        strokeWidth: 2.5,
        strokeOpacity: 0.8,
      };
    default:
      return {
        stroke: "#4D9FC9", // Default blue
        strokeWidth: 2,
        strokeOpacity: 0.9,
      };
  }
}

// Left side connectors (top to bottom) using GridConfig
const LEFT_CONNECTORS = [
  {
    id: "knob-input",
    type: "source" as const,
    top: `${GridConfig.getHandleOffset(1)}px`,
    color: "#8b5cf6",
    label: "Knob",
    fullLabel: "Knob Input",
    textColor: "text-purple-600",
  },
  {
    id: "sw3",
    type: "source" as const,
    top: `${GridConfig.getHandleOffset(2)}px`,
    color: "#eab308",
    label: "SW3",
    fullLabel: "Light Switch 3",
    textColor: "text-yellow-600",
  },
  {
    id: "sw4",
    type: "source" as const,
    top: `${GridConfig.getHandleOffset(3)}px`,
    color: "#eab308",
    label: "SW4",
    fullLabel: "Light Switch 4",
    textColor: "text-yellow-600",
  },
  {
    id: "r1",
    type: "source" as const,
    top: `${GridConfig.getHandleOffset(4)}px`,
    color: "#dc2626",
    label: "R1",
    fullLabel: "Relay 1",
    textColor: "text-red-600",
  },
  {
    id: "r2",
    type: "source" as const,
    top: `${GridConfig.getHandleOffset(5)}px`,
    color: "#dc2626",
    label: "R2",
    fullLabel: "Relay 2",
    textColor: "text-red-600",
  },
];

// Bottom connectors (left to right) using GridConfig with 2x spacing
const BOTTOM_CONNECTORS = [
  {
    id: "d1",
    type: "source" as const,
    left: `${GridConfig.getHandleOffset(2)}px`,
    color: "#06b6d4",
    label: "D1",
    fullLabel: "Door Sensor 1",
    textColor: "text-cyan-600",
  },
  {
    id: "d3",
    type: "source" as const,
    left: `${GridConfig.getHandleOffset(4)}px`,
    color: "#06b6d4",
    label: "D3",
    fullLabel: "Door Sensor 3",
    textColor: "text-cyan-600",
  },
  {
    id: "5v-aux",
    type: "source" as const,
    left: `${GridConfig.getHandleOffset(6)}px`,
    color: "#dc2626",
    label: "+5V",
    fullLabel: "5V Aux Power",
    textColor: "text-red-600",
  },
  {
    id: "0-10v-src",
    type: "source" as const,
    left: `${GridConfig.getHandleOffset(8)}px`,
    color: "#dc2626",
    label: "0-10V\nSRC",
    fullLabel: "0-10V Dimmer Input Src",
    textColor: "text-red-600",
  },
  {
    id: "0-10v-sink",
    type: "source" as const,
    left: `${GridConfig.getHandleOffset(10)}px`,
    color: "#dc2626",
    label: "0-10V\nSink",
    fullLabel: "0-10V Dimmer Input Sink",
    textColor: "text-red-600",
  },
];

// Right side connectors (top to bottom) using GridConfig
const RIGHT_CONNECTORS = [
  {
    id: "sw1",
    type: "source" as const,
    top: `${GridConfig.getHandleOffset(1)}px`,
    color: "#eab308",
    label: "SW1",
    fullLabel: "Light Switch Input 1",
    textColor: "text-yellow-600",
  },
  {
    id: "sw2",
    type: "source" as const,
    top: `${GridConfig.getHandleOffset(2)}px`,
    color: "#eab308",
    label: "SW2",
    fullLabel: "Light Switch Input 2",
    textColor: "text-yellow-600",
  },
  {
    id: "24v-aux",
    type: "source" as const,
    top: `${GridConfig.getHandleOffset(3)}px`,
    color: "#dc2626",
    label: "+24V",
    fullLabel: "24V Aux Power",
    textColor: "text-red-600",
  },
  {
    id: "r3",
    type: "source" as const,
    top: `${GridConfig.getHandleOffset(4)}px`,
    color: "#dc2626",
    label: "R3",
    fullLabel: "Relay Output 3",
    textColor: "text-red-600",
  },
  {
    id: "r4",
    type: "source" as const,
    top: `${GridConfig.getHandleOffset(5)}px`,
    color: "#dc2626",
    label: "R4",
    fullLabel: "Relay Output 4",
    textColor: "text-red-600",
  },
];

// Top connectors (left to right) using GridConfig with 2x spacing
const TOP_CONNECTORS = [
  {
    id: "io1",
    type: "source" as const,
    left: `${GridConfig.getHandleOffset(4)}px`,
    color: "#10b981",
    label: "I/O 1",
    fullLabel: "SomoBus I/O 1",
    textColor: "text-green-600",
  },
  {
    id: "io2",
    type: "source" as const,
    left: `${GridConfig.getHandleOffset(8)}px`,
    color: "#10b981",
    label: "I/O 2",
    fullLabel: "SomoBus I/O 2",
    textColor: "text-green-600",
  },
];

// Reusable connector component for side connectors
const SideConnector = ({
  id,
  type,
  position,
  top,
  color,
  label,
  fullLabel,
  textColor,
}: {
  id: string;
  type: "target" | "source";
  position: Position;
  top: string;
  color: string;
  label: string;
  fullLabel: string;
  textColor: string;
}) => (
  <>
    <Handle
      type={type}
      position={position}
      id={id}
      isConnectable={true}
      className="w-5 h-5 border-2 border-white rounded-full"
      style={{
        top,
        transform: "translateY(-50%)",
        backgroundColor: color,
      }}
      title={fullLabel} // Tooltip for full label
    />
    <div
      className={`absolute transform -translate-y-1/2 text-[10px] font-semibold whitespace-normal ${textColor} ${
        position === Position.Left ? "left-2 text-left" : "right-2 text-right"
      }`}
      style={{
        top,
        lineHeight: "1.2",
        width: "90px",
        wordBreak: "normal",
      }}
      title={fullLabel} // Tooltip for full label
    >
      {label}
    </div>
  </>
);

// Reusable connector component for top/bottom connectors
const HorizontalConnector = ({
  id,
  type,
  position,
  left,
  color,
  label,
  fullLabel,
  textColor,
}: {
  id: string;
  type: "target" | "source";
  position: Position;
  left: string;
  color: string;
  label: string;
  fullLabel: string;
  textColor: string;
}) => (
  <>
    <Handle
      type={type}
      position={position}
      id={id}
      isConnectable={true}
      className="w-5 h-5 border-2 border-white rounded-full"
      style={{
        left,
        transform: "translateX(-50%)",
        backgroundColor: color,
      }}
      title={fullLabel} // Tooltip for full label
    />
    <div
      className={`absolute transform -translate-x-1/2 ${
        label.includes("0-10V") ? "text-[8px]" : "text-[10px]"
      } font-semibold text-center whitespace-pre-line ${textColor} ${
        position === Position.Top ? "top-2" : "bottom-2"
      }`}
      style={{
        left,
        lineHeight: "1.2",
        width: "60px",
        wordBreak: "normal",
      }}
      title={fullLabel} // Tooltip for full label
    >
      {label}
    </div>
  </>
);

export const CanBoSLD: SldComponent = ({ data, selected = false }) => {
  const gridBasedHeight = 7 * GridConfig.GRID_SIZE;
  const gridBasedWidth = 12 * GridConfig.GRID_SIZE;

  const nodeId = useNodeId();
  const actualData = useGetData(data.dataId, "canbusController");

  const updateNodeInternals = useUpdateNodeInternals();
  useEffect(() => {
    if (!nodeId) {
      return;
    }
    updateNodeInternals(nodeId);
  }, [nodeId, updateNodeInternals, actualData]);

  if (!actualData) {
    console.warn("CanBo Controller: No data found for dataId:", data.dataId);
    return (
      <div className="w-20 h-20 bg-gray-200 border border-gray-300 rounded flex items-center justify-center text-xs text-gray-500">
        Loading...
      </div>
    );
  }

  return (
    <div
      className={`bg-white border-2 rounded-lg p-4 shadow-md cursor-move relative ${
        selected ? "border-blue-500 bg-blue-50" : "border-gray-300"
      }`}
      style={{
        minWidth: `${gridBasedWidth}px`,
        minHeight: `${gridBasedHeight}px`,
      }}
    >
      {/* Left side connectors */}
      {LEFT_CONNECTORS.map((connector) => (
        <SideConnector
          key={connector.id}
          {...connector}
          position={Position.Left}
        />
      ))}

      {/* Right side connectors */}
      {RIGHT_CONNECTORS.map((connector) => (
        <SideConnector
          key={connector.id}
          {...connector}
          position={Position.Right}
        />
      ))}

      {/* Bottom connectors */}
      {BOTTOM_CONNECTORS.map((connector) => (
        <HorizontalConnector
          key={connector.id}
          {...connector}
          position={Position.Bottom}
        />
      ))}

      {/* Top connectors */}
      {TOP_CONNECTORS.map((connector) => (
        <HorizontalConnector
          key={connector.id}
          {...connector}
          position={Position.Top}
        />
      ))}

      {/* Controller Icon - Center aligned */}
      <div className="flex justify-center mb-2">
        <CanBusControllerIcon className="w-8 h-8 text-gray-700" />
      </div>

      {/* Device Name - Main identifier for SLD view */}
      <div
        className="text-xs font-semibold text-center mb-1 text-gray-600 leading-tight whitespace-normal break-words"
        style={{
          width: `${gridBasedWidth - 32}px`,
          overflowWrap: "break-word",
          wordWrap: "break-word",
          margin: "0 auto",
        }}
      >
        {actualData?.title}
      </div>

      {/* Device Type - Secondary info */}
      <div
        className="text-[10px] text-gray-500 text-center mb-2"
        style={{
          width: `${gridBasedWidth}px`,
          margin: "0 auto",
        }}
      >
        {"CanBo"}
        {<br />}
        {"Controller"}
      </div>
    </div>
  );
};

// Export the edge style function
CanBoSLD.getEdgeStyle = getCanBoConnectorStyle;
CanBoSLD.overrideEdgeColor = true;
