import { RelayOutput, RelayOutputDevice, filterNodes } from "@somo/shared";
import { <PERSON><PERSON>, Position } from "@xyflow/react";

import { getData } from "@/components/data/useData";
import { useReactFlowContext } from "@/components/graph/ReactFlowContext";
import { DeviceIcons } from "@/components/icons/DeviceIcons";
import { cn } from "@/lib/classNames";
import { match } from "ts-pattern";

interface Props {
  direction: "left" | "right";
  device: RelayOutputDevice;
}

export function RelayOutputButton(props: Props) {
  const { readOnly } = useReactFlowContext();
  return (
    <BaseButton
      {...props}
      isConnectable={!readOnly}
      className="text-white"
      renderLabel={(output) => (
        <div className="text-xs flex-shrink-0 mr-2">{output.name}</div>
      )}
    />
  );
}

export function RelayOutputButtonSimulator(props: Props) {
  return (
    <BaseButton
      {...props}
      isConnectable={false}
      className="bg-gray-600 text-white border-gray-700"
      renderLabel={(output) => (
        <div className="flex flex-col items-start flex-1">
          <div className="text-xs flex-shrink-0 mr-2">{output.name}</div>
          <div className="flex-shrink-0 mr-2 text-[9px]">On/Off</div>
        </div>
      )}
    />
  );
}

function BaseButton({
  device,
  direction,
  isConnectable,
  renderLabel,
  onClick,
  className = "",
}: Props & {
  isConnectable: boolean;
  renderLabel: (output: RelayOutput) => React.ReactNode;
  onClick?: () => void;
  className?: string;
}) {
  const { nodes, yDoc } = useReactFlowContext();

  const canbusNode = filterNodes(nodes, "canbusControllerContainer").find(
    (node) => node.id === device.nodeId,
  );
  if (!canbusNode) {
    return <></>;
  }
  const canbusData = getData(yDoc, canbusNode?.data.dataId, "canbusController");
  const relayOutput = canbusData?.relayOutputs?.[device.viaId];

  if (!relayOutput) {
    return (
      <div className="text-xs font-mono h-[40px] flex items-center justify-center px-2">
        Output missing
      </div>
    );
  }

  const { icon } = relayOutput;
  const ActiveIconComponent = icon
    ? DeviceIcons[icon as keyof typeof DeviceIcons]
    : null;

  return (
    <div
      onClick={onClick}
      className={cn(
        "size-10 bg-gray-600 rounded-md shadow-md flex items-center justify-center min-w-[40px] gap-2 flex-shrink-0 px-2 relative",
        device.showLabel && "w-auto",
        className,
      )}
    >
      {ActiveIconComponent && (
        <ActiveIconComponent className="size-6 flex-shrink-0" />
      )}
      {device.showLabel && renderLabel(relayOutput)}
      <Handle
        type="target"
        id={device.id}
        isConnectable={isConnectable}
        position={match(device.anchorPosition)
          .with("bottom", () => Position.Bottom)
          .with("top", () => Position.Top)
          .with("left", () => Position.Left)
          .with("right", () => Position.Right)
          .with(undefined, () =>
            direction === "left" ? Position.Left : Position.Right,
          )
          .exhaustive()}
        style={{
          background: "white",
          border: "1px solid #4b5563",
          color: "transparent",
          width: 6,
          height: 6,
          minHeight: 6,
          minWidth: 6,
          borderRadius: "50%",
          boxShadow: "0 1px 2px rgba(0, 0, 0, 0.1)",
        }}
      />
    </div>
  );
}
