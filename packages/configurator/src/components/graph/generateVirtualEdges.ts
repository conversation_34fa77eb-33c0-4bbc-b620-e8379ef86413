import { uniqueBy } from "@/lib/array";
import {
  ContainerNodeTypes,
  doorSensorViaIds,
  filterNodes,
  GraphNode,
  GraphNodesByType,
  isNodeOrParentSelected,
  outletDimmerViaIds,
  presenceSensorViaIds,
  roomDimmerViaIds,
  roomSwitchViaIds,
  somoFanViaIds,
  somoIrControllerViaIds,
  somoShadesViaIds,
  somoThermostatViaIds,
  virtualButtonViaIds,
} from "@somo/shared";
import { Edge } from "@xyflow/react";
import { match } from "ts-pattern";
import * as Y from "yjs";
import z from "zod";
import { DataSchemaByType, DataType } from "../data/Data";
import { getData } from "../data/useData";

/**
 * On top of the ReactFlow edges, we do create some on the fly based on the
 * state of the graph. E.g., if a scene node is selected, we show edges to the
 * connected inputs and devices.
 *
 * This function computes the list of virtual edges that should be drawn.
 */
export function generateVirtualEdges(yDoc: Y.Doc, nodes: GraphNode[]): Edge[] {
  const edges: Edge[] = [];
  const sectionNodes = filterNodes(nodes, "section");

  const newEdges = [
    ...createDeviceEdges(
      yDoc,
      nodes,
      "virtualButtonContainer",
      "virtualButton",
      virtualButtonViaIds,
    ),

    ...createDeviceEdges(
      yDoc,
      nodes,
      "roomSwitchContainer",
      "roomSwitch",
      roomSwitchViaIds,
    ),
    ...createDeviceEdges(
      yDoc,
      nodes,
      "roomDimmerContainer",
      "roomDimmer",
      roomDimmerViaIds,
    ),
    ...createDeviceEdges(
      yDoc,
      nodes,
      "outletDimmerContainer",
      "outletDimmer",
      outletDimmerViaIds,
    ),
    ...createDeviceEdges(
      yDoc,
      nodes,
      "presenceSensorContainer",
      "presenceSensor",
      presenceSensorViaIds,
    ),
    ...createDeviceEdges(
      yDoc,
      nodes,
      "doorSensorContainer",
      "doorSensor",
      doorSensorViaIds,
    ),
    ...createDeviceEdges(
      yDoc,
      nodes,
      "somoShadesContainer",
      "somoShades",
      somoShadesViaIds,
    ),
    ...createDeviceEdges(
      yDoc,
      nodes,
      "somoFanContainer",
      "somoFan",
      somoFanViaIds,
    ),
    ...createDeviceEdges(
      yDoc,
      nodes,
      "somoThermostatContainer",
      "somoThermostat",
      somoThermostatViaIds,
    ),
    ...createDeviceEdges(
      yDoc,
      nodes,
      "somoIrControllerContainer",
      "somoIrController",
      somoIrControllerViaIds,
    ),
    ...createDeviceEdges(
      yDoc,
      nodes,
      "somoThermostatContainer",
      "somoThermostat",
      somoThermostatViaIds,
    ),
    ...createDeviceEdges(
      yDoc,
      nodes,
      "somoIrControllerContainer",
      "somoIrController",
      somoIrControllerViaIds,
    ),

    ...canBusControllerEdges(yDoc, nodes),
  ]
    .map((edge) => withDeviceTargetOrNull(nodes, edge))
    .concat(sceneInputsEdges(yDoc, nodes))
    .filter((d) => d !== null)
    .map(toEdge);

  newEdges.forEach((newEdge) => {
    if (edges.find((e) => e.id === newEdge.id)) {
      return;
    }
    edges.push(newEdge);
  });

  // Highlight all scenes connections when selected
  filterNodes(nodes, "scene")
    .filter((node) => node.selected && !node.dragging)
    .forEach((node) => {
      // draw an edge from node to each of the source handles anchors
      const data = getData(yDoc, node.data.dataId, "scene");
      if (!data) {
        return;
      }

      for (const input of data.inputs) {
        // TODO: have the find match dataIds once all node types are migrated
        const sourceControllerNode = nodes.find(
          (n) => "dataId" in n.data && n.data.dataId === input.source,
        );
        const sourceAnchorNode = nodes.find(
          (n) => n.id === sourceControllerNode?.parentId,
        );
        if (!sourceAnchorNode) {
          continue;
        }

        edges.push(
          toEdge({
            source: sourceAnchorNode.id,
            sourceHandle: "",
            target: node.id,
            targetHandle: "",
          }),
        );
      }

      const { onActivate, onDeactivate } = data;
      const onActivateDeviceIds = onActivate.enabled
        ? Object.values(onActivate.onUpClick)
        : [];
      const onDeactivateDeviceIds = onDeactivate.enabled
        ? Object.values(onDeactivate.onUpClick)
        : [];
      // draw an edge from node to earch of the target sections
      const allDeviceIds = [
        ...onActivateDeviceIds,
        ...onDeactivateDeviceIds,
      ].map((device) => device.deviceId);
      // draw an edge from scene to each device
      for (const deviceId of allDeviceIds) {
        const sectionNode = sectionNodes.find((n) =>
          Object.values(n.data.devices).some((d) => d.id === deviceId),
        );
        if (!sectionNode) {
          continue;
        }
        edges.push(
          toEdge({
            source: node.id,
            sourceHandle: "",
            target: sectionNode.id,
            targetHandle: deviceId,
          }),
        );
      }
    });

  // Highlight all devices connections when a section node is selected
  const selectedSectionNodes = sectionNodes.filter((n) => n.selected);
  for (const selectedSectionNode of selectedSectionNodes) {
    // find a controller that controls a device in the selectedSectionNode
    const deviceIds = Object.values(selectedSectionNode.data.devices).map(
      (device) => device.id,
    );

    // find all scenes
    filterNodes(nodes, "scene")
      .filter((node) => !node.selected && !node.dragging)
      .forEach((node) => {
        const data = getData(yDoc, node.data.dataId, "scene");
        if (!data) {
          return;
        }
        const { onActivate, onDeactivate } = data;
        const onActivateDeviceIds = onActivate.enabled
          ? Object.values(onActivate.onUpClick)
          : [];
        const onDeactivateDeviceIds = onDeactivate.enabled
          ? Object.values(onDeactivate.onUpClick)
          : [];
        // only keep nodes that are linked to deviceIds
        const allDeviceIds = [...onActivateDeviceIds, ...onDeactivateDeviceIds]
          .map((device) => device.deviceId)
          .filter((deviceId) => deviceIds.includes(deviceId));

        // draw an edge from scene to each device
        for (const deviceId of allDeviceIds) {
          edges.push(
            toEdge({
              source: node.id,
              sourceHandle: "",
              target: selectedSectionNode.id,
              targetHandle: deviceId,
            }),
          );
        }
      });

    // find all canbus controllers
    filterNodes(nodes, "canbusControllerAnchor")
      .filter((node) => !node.selected && !node.dragging)
      .map((node) => {
        // merge in the child nodes
        const childNodes = filterNodes(
          nodes,
          "canbusControllerContainer",
        ).filter((n) => n.parentId === node.id);
        return {
          ...node,
          containerNode: childNodes[0],
        };
      })
      .forEach((node) => {
        const data = getData(
          yDoc,
          node.containerNode.data.dataId,
          "canbusController",
        );
        if (!data) {
          return;
        }
        // only keep nodes that have containerNodes linked to deviceIds
        const allDeviceIds: string[] = [];
        for (const controller of Object.values(data.controllers ?? {})) {
          if (controller.type === "toggle") {
            allDeviceIds.push(
              ...Object.values(controller.onUpClick ?? {}).map(
                (device) => device.deviceId,
              ),
            );
            allDeviceIds.push(
              ...Object.values(controller.onUpHold ?? {}).map(
                (device) => device.deviceId,
              ),
            );
            allDeviceIds.push(
              ...Object.values(controller.onDownClick ?? {}).map(
                (device) => device.deviceId,
              ),
            );
            allDeviceIds.push(
              ...Object.values(controller.onDownHold ?? {}).map(
                (device) => device.deviceId,
              ),
            );
          } else if (controller.type === "momentary") {
            allDeviceIds.push(
              ...Object.values(controller.onUpClick ?? {}).map(
                (device) => device.deviceId,
              ),
            );
            allDeviceIds.push(
              ...Object.values(controller.onUpHold ?? {}).map(
                (device) => device.deviceId,
              ),
            );
          }
        }

        // Add reed switch sensor device IDs
        for (const reedSwitchSensor of Object.values(
          data.reedSwitchSensors ?? {},
        )) {
          allDeviceIds.push(
            ...Object.values(reedSwitchSensor.onOpen ?? {}).map(
              (device) => device.deviceId,
            ),
          );
          allDeviceIds.push(
            ...Object.values(reedSwitchSensor.onClose ?? {}).map(
              (device) => device.deviceId,
            ),
          );
        }

        // filter allDeviceIds to only include deviceIds that are in deviceIds
        const filteredDeviceIds = allDeviceIds.filter((deviceId) =>
          deviceIds.includes(deviceId),
        );

        // draw an edge from node.containerNode to each of the filteredDeviceIds
        for (const deviceId of filteredDeviceIds) {
          edges.push(
            toEdge({
              source: node.id,
              sourceHandle: "",
              target: selectedSectionNode.id,
              targetHandle: deviceId,
            }),
          );
        }
      });

    [
      ...resolveAnchorEdges({
        yDoc,
        nodes,
        deviceIds,
        targetId: selectedSectionNode.id,
        anchorType: "roomSwitchAnchor",
        containerType: "roomSwitchContainer",
        viaIds: roomSwitchViaIds,
        type: "roomSwitch",
      }),
      ...resolveAnchorEdges({
        yDoc,
        nodes,
        deviceIds,
        targetId: selectedSectionNode.id,
        anchorType: "roomDimmerAnchor",
        containerType: "roomDimmerContainer",
        viaIds: roomDimmerViaIds,
        type: "roomDimmer",
      }),
      ...resolveAnchorEdges({
        yDoc,
        nodes,
        deviceIds,
        targetId: selectedSectionNode.id,
        anchorType: "outletDimmerAnchor",
        containerType: "outletDimmerContainer",
        viaIds: outletDimmerViaIds,
        type: "outletDimmer",
      }),
      ...resolveAnchorEdges({
        yDoc,
        nodes,
        deviceIds,
        targetId: selectedSectionNode.id,
        anchorType: "presenceSensorAnchor",
        containerType: "presenceSensorContainer",
        viaIds: presenceSensorViaIds,
        type: "presenceSensor",
      }),
      ...resolveAnchorEdges({
        yDoc,
        nodes,
        deviceIds,
        targetId: selectedSectionNode.id,
        anchorType: "doorSensorAnchor",
        containerType: "doorSensorContainer",
        viaIds: doorSensorViaIds,
        type: "doorSensor",
      }),
      ...resolveAnchorEdges({
        yDoc,
        nodes,
        deviceIds,
        targetId: selectedSectionNode.id,
        anchorType: "somoShadesAnchor",
        containerType: "somoShadesContainer",
        viaIds: somoShadesViaIds,
        type: "somoShades",
      }),
      ...resolveAnchorEdges({
        yDoc,
        nodes,
        deviceIds,
        targetId: selectedSectionNode.id,
        anchorType: "somoFanAnchor",
        containerType: "somoFanContainer",
        viaIds: somoFanViaIds,
        type: "somoFan",
      }),
    ].forEach((edge) => edges.push(edge));
  }

  return uniqueBy(edges, "id");
}

function sceneInputsEdges(yDoc: Y.Doc, nodes: GraphNode[]) {
  const sceneNodes = filterNodes(nodes, "scene");
  const selectedSceneNodes = sceneNodes.filter((n) =>
    isNodeOrParentSelected(nodes, n),
  );

  return sceneNodes
    .filter((scene) => {
      if (isNodeOrParentSelected(nodes, scene)) {
        return true;
      }

      const sceneData = getData(yDoc, scene.data.dataId, "scene");
      return selectedSceneNodes.some((selectedNode) =>
        sceneData?.inputs.some(
          (input) => input.source === selectedNode.data.dataId,
        ),
      );
    })
    .flatMap((scene) => {
      const data = getData(yDoc, scene.data.dataId, "scene");
      if (!data) {
        return [];
      }

      return data.inputs.map((input) => {
        const sourceNode = nodes.find(
          (n) => "dataId" in n.data && n.data.dataId === input.source,
        );

        return {
          source: sourceNode?.id ?? "",
          sourceHandle: input.sourceHandle,
          target: scene.id,
          targetHandle: scene.id,
        };
      });
    });
}

function createDeviceEdges<T extends DataType>(
  yDoc: Y.Doc,
  nodes: GraphNode[],
  containerType: ContainerNodeTypes,
  dataType: T,
  viaIds: readonly (keyof z.output<DataSchemaByType[T]>)[],
) {
  return filterNodes(nodes, containerType)
    .filter((node) => isNodeOrParentSelected(nodes, node))
    .flatMap((node) => {
      const data = getData(yDoc, node.data.dataId, dataType);
      return viaIds
        .filter((viaId) => {
          const via = data?.[viaId as keyof typeof data];
          return (
            via &&
            typeof via === "object" &&
            (!("enabled" in via) || via.enabled)
          );
        })
        .map((viaId) => ({ viaId, node }))
        .flatMap(({ node, viaId }) => {
          const via = data?.[viaId as keyof typeof data];
          const actions =
            via && typeof via === "object" && "onUpClick" in via
              ? Object.values(via.onUpClick as Record<string, any>)
              : [];
          return actions.map((action: any) => ({
            source: node.id,
            sourceHandle: viaId,
            deviceId: action.deviceId,
          }));
        });
    });
}

function canBusControllerEdges(yDoc: Y.Doc, nodes: GraphNode[]) {
  const controllerEdges = filterNodes(nodes, "canbusControllerContainer")
    // Retrieve all nodes' controllers
    .flatMap((node) => {
      const data = getData(yDoc, node.data.dataId, "canbusController");
      if (!data) {
        return [];
      }
      const controllers = Object.values(data.controllers ?? {});
      return controllers.map((controller) => ({
        nodeId: node.id,
        controller,
      }));
    })
    // Retrieve all controllers' devices
    .flatMap(({ nodeId, controller }) => {
      const controllerDevices = match(controller)
        .with({ type: "toggle" }, (controller) => [
          ...Object.values(controller.onUpClick ?? {}),
          ...Object.values(controller.onUpHold ?? {}),
          ...Object.values(controller.onDownClick ?? {}),
          ...Object.values(controller.onDownHold ?? {}),
        ])
        .with({ type: "momentary" }, (controller) => [
          ...Object.values(controller.onUpClick ?? {}),
          ...Object.values(controller.onUpHold ?? {}),
        ])
        .exhaustive();

      return controllerDevices.map((d) => ({
        source: nodeId,
        sourceHandle: controller.id,
        deviceId: d.deviceId,
      }));
    });

  const reedSwitchSensorEdges = filterNodes(nodes, "canbusControllerContainer")
    .filter((node) => isNodeOrParentSelected(nodes, node))
    // Retrieve all nodes' reed switch sensors
    .flatMap((node) => {
      const data = getData(yDoc, node.data.dataId, "canbusController");
      if (!data) {
        return [];
      }
      const reedSwitchSensors = Object.values(data.reedSwitchSensors ?? {});
      return reedSwitchSensors.map((sensor) => ({
        nodeId: node.id,
        sensor,
      }));
    })
    // Retrieve all reed switch sensors' devices (like toggle controllers, combine all actions)
    .flatMap(({ nodeId, sensor }) => {
      const sensorDevices = [
        ...Object.values(sensor.onOpen ?? {}),
        ...Object.values(sensor.onClose ?? {}),
      ];

      return sensorDevices.map((d) => ({
        source: nodeId,
        sourceHandle: sensor.id, // Use just the sensor ID for edge generation (one edge per sensor)
        deviceId: d.deviceId,
      }));
    });

  const pirSensorEdges = filterNodes(nodes, "canbusControllerContainer")
    .filter((node) => isNodeOrParentSelected(nodes, node))
    // Retrieve all nodes' PIR sensors
    .flatMap((node) => {
      const data = getData(yDoc, node.data.dataId, "canbusController");
      if (!data) {
        return [];
      }
      const pirSensors = Object.values(data.pirSensors ?? {});
      return pirSensors.map((sensor) => ({
        nodeId: node.id,
        sensor,
      }));
    })
    // Retrieve all PIR sensors' devices (like toggle controllers, combine all actions)
    .flatMap(({ nodeId, sensor }) => {
      const sensorDevices = [
        ...Object.values(sensor.onActivate ?? {}),
        ...Object.values(sensor.onDeactivate ?? {}),
      ];

      return sensorDevices.map((d) => ({
        source: nodeId,
        sourceHandle: sensor.id, // Use just the sensor ID for edge generation (one edge per sensor)
        deviceId: d.deviceId,
      }));
    });

  return [...controllerEdges, ...reedSwitchSensorEdges, ...pirSensorEdges];
}

type Options<Anchor, Container, ViaIds, Type> = {
  yDoc: Y.Doc;
  nodes: GraphNode[];
  deviceIds: string[];
  targetId: string;
  anchorType: Anchor;
  containerType: Container;
  viaIds: ViaIds;
  type: Type;
};

function resolveAnchorEdges<
  Anchor extends "roomSwitchAnchor",
  Container extends "roomSwitchContainer",
  ViaIds extends typeof roomSwitchViaIds,
  Type extends "roomSwitch",
>(options: Options<Anchor, Container, ViaIds, Type>): Edge[];
function resolveAnchorEdges<
  Anchor extends "roomDimmerAnchor",
  Container extends "roomDimmerContainer",
  ViaIds extends typeof roomDimmerViaIds,
  Type extends "roomDimmer",
>(options: Options<Anchor, Container, ViaIds, Type>): Edge[];
function resolveAnchorEdges<
  Anchor extends "outletDimmerAnchor",
  Container extends "outletDimmerContainer",
  ViaIds extends typeof outletDimmerViaIds,
  Type extends "outletDimmer",
>(options: Options<Anchor, Container, ViaIds, Type>): Edge[];
function resolveAnchorEdges<
  Anchor extends "doorSensorAnchor",
  Container extends "doorSensorContainer",
  ViaIds extends typeof doorSensorViaIds,
  Type extends "doorSensor",
>(options: Options<Anchor, Container, ViaIds, Type>): Edge[];
function resolveAnchorEdges<
  Anchor extends "somoFanAnchor",
  Container extends "somoFanContainer",
  ViaIds extends typeof somoFanViaIds,
  Type extends "somoFan",
>(options: Options<Anchor, Container, ViaIds, Type>): Edge[];
function resolveAnchorEdges<
  Anchor extends "somoShadesAnchor",
  Container extends "somoShadesContainer",
  ViaIds extends typeof somoShadesViaIds,
  Type extends "somoShades",
>(options: Options<Anchor, Container, ViaIds, Type>): Edge[];
function resolveAnchorEdges<
  Anchor extends "presenceSensorAnchor",
  Container extends "presenceSensorContainer",
  ViaIds extends typeof presenceSensorViaIds,
  Type extends "presenceSensor",
>(options: Options<Anchor, Container, ViaIds, Type>): Edge[];
function resolveAnchorEdges<
  Anchor extends keyof GraphNodesByType,
  Container extends ContainerNodeTypes,
  ViaIds extends (keyof z.output<DataSchemaByType[Type]>)[],
  Type extends DataType,
>(options: Options<Anchor, Container, ViaIds, Type>): Edge[] {
  const {
    yDoc,
    nodes,
    deviceIds,
    targetId,
    anchorType,
    containerType,
    viaIds,
    type,
  } = options;
  return filterNodes(nodes, anchorType)
    .filter((node) => !node.selected && !node.dragging)
    .map((node) => {
      // merge in the child nodes
      const [containerNode] = filterNodes(nodes, containerType).filter(
        (n) => n.parentId === node.id,
      );
      return {
        ...node,
        containerNode,
      };
    })
    .flatMap((node) =>
      viaIds
        .flatMap((id) => {
          const data = getData(yDoc, node.containerNode.data.dataId, type);
          const via = data?.[id];
          if (!via || typeof via !== "object") {
            return [];
          }
          if ("enabled" in via && !via.enabled) {
            return [];
          }

          return "onUpClick" in via
            ? Object.values(via.onUpClick ?? {}).map(
                (action) => action.deviceId,
              )
            : [];
        })
        .filter((id) => deviceIds.includes(id))
        .map((deviceId) =>
          toEdge({
            source: node.id,
            sourceHandle: "",
            target: targetId,
            targetHandle: deviceId,
          }),
        ),
    )
    .filter((e) => e !== undefined);
}

function withDeviceTargetOrNull(
  nodes: GraphNode[],
  {
    source,
    sourceHandle,
    deviceId,
  }: Pick<Edge, "source" | "sourceHandle"> & { deviceId: string },
) {
  const targetSectionNode = filterNodes(nodes, "section").find((node) =>
    Object.values(node.data.devices).some((d) => d.id === deviceId),
  );
  return targetSectionNode
    ? {
        source,
        sourceHandle,
        target: targetSectionNode.id,
        targetHandle: deviceId,
      }
    : null;
}

function toEdge({
  source,
  sourceHandle,
  target,
  targetHandle,
}: Pick<Edge, "source" | "sourceHandle" | "target" | "targetHandle">) {
  return {
    id: `${source}-${sourceHandle}/${target}-${targetHandle}`,
    source,
    sourceHandle,
    target,
    targetHandle,
    type: "simplebezier",
    zIndex: 1000,
    animated: true,
    selectable: false,
    style: {
      stroke: "#4D9FC9",
      strokeWidth: 2,
    },
  };
}
