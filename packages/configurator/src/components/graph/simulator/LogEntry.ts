export type LogEntry =
  | ClickLogEntry
  | MouseDownLogEntry
  | MouseUpLogEntry
  | StateLogEntry
  | BooleanStateLogEntry
  | EmitLogEntry
  | CancelLogEntry
  | ServicePadStatusLogEntry
  | DoorbellLogEntry;

type ClickLogEntry = BaseLogEntry & {
  action: "click";
  target: string;
};

type MouseDownLogEntry = BaseLogEntry & {
  action: "mouseDown";
  target: string;
};

type MouseUpLogEntry = BaseLogEntry & {
  action: "mouseUp";
  target: string;
};

type StateLogEntry = BaseLogEntry & {
  action: "set";
  target: string;
  value: string;
};

type BooleanStateLogEntry = BaseLogEntry & {
  action: "turned";
  state: "ON" | "OFF";
};

type EmitLogEntry = BaseLogEntry & {
  action: "emit";
  event: string;
};

type CancelLogEntry = BaseLogEntry & {
  action: "cancel";
  event: string;
};

type ServicePadStatusLogEntry = BaseLogEntry & {
  action: "servicePadStatus";
  status: "doNotDisturb" | "makeUpRoom" | "none";
};
type DoorbellLogEntry = BaseLogEntry & {
  action: "doorbell";
};

type BaseLogEntry = {
  timestamp: number;
  device: {
    // TODO: this requires some rework to make Simulator work again
    // TODO: for actions, should be the list of "Controllers"
    // TODO: for events, should be the list of "Controllables"
    type:
      | "virtualButton"
      | "roomSwitch"
      | "roomDimmer"
      | "outletDimmer"
      | "presenceSensor"
      | "doorSensor"
      | "reedSwitchSensor"
      | "pirSensor"
      | "momentaryCanBusController"
      | "servicePad"
      | "somfyShades"
      | "somoFan"
      | "somoIrController"
      | "somoShades"
      | "somoThermostat"
      | "toggleCanBusController";
    id: string;
  };
};
