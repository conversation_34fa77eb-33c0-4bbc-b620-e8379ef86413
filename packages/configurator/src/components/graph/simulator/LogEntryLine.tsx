import { cn } from "@/lib/classNames";
import { stringToColor } from "@/lib/stringToColor";
import { match } from "ts-pattern";
import { LogEntry } from "./LogEntry";

export function LogEntryLine({
  entry,
  onMouseEnter,
  onMouseLeave,
}: {
  entry: LogEntry;
  onMouseEnter?: () => void;
  onMouseLeave?: () => void;
}) {
  // Format as HH:MM:SS
  const time = new Date(entry.timestamp).toLocaleTimeString("en-US", {
    hour12: false,
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
  });

  return (
    <div
      className={cn(
        "text-gray-500",
        match(entry.action)
          .with("click", "mouseUp", "mouseDown", () => "bg-green-100")
          .with("cancel", () => "bg-red-50")
          .otherwise(() => undefined),
        // Show border around element on hover
        onMouseEnter && "hover:bg-yellow-100",
      )}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
    >
      [{time}]{" "}
      <span
        style={{
          color: stringToColor(`${entry.device.type} ${entry.device.id}`),
        }}
      >
        <span className="font-bold">
          {match(entry.device)
            .with({ type: "virtualButton" }, () => "Virtual Button")
            .with({ type: "momentaryCanBusController" }, () => "Momentary")
            .with({ type: "toggleCanBusController" }, () => "Toggle")
            .with({ type: "roomSwitch" }, () => "Switch")
            .with({ type: "roomDimmer" }, () => "Dimmer")
            .with({ type: "outletDimmer" }, () => "Outlet Dimmer")
            .with({ type: "presenceSensor" }, () => "Presence Sensor")
            .with({ type: "doorSensor" }, () => "Door Sensor")
            .with({ type: "reedSwitchSensor" }, () => "Reed Switch Sensor")
            .with({ type: "pirSensor" }, () => "PIR Sensor")
            .with({ type: "somoThermostat" }, () => "Thermostat")
            .with({ type: "somoIrController" }, () => "IR Controller")
            .with({ type: "somoShades" }, () => "Shades")
            .with({ type: "somfyShades" }, () => "Somfy Shades")
            .with({ type: "somoFan" }, () => "Fan Controller")
            .with({ type: "servicePad" }, () => "Service Pad")
            .exhaustive()}
        </span>{" "}
        ({entry.device.id})
      </span>{" "}
      {match(entry)
        .with({ action: "click" }, (entry) => (
          <>
            was clicked on{" "}
            <span className="font-bold text-amber-500">{entry.target}</span>
          </>
        ))
        .with({ action: "mouseDown" }, (entry) => (
          <>
            was pressed on{" "}
            <span className="font-bold text-amber-500">{entry.target}</span>
          </>
        ))
        .with({ action: "mouseUp" }, (entry) => (
          <>
            was released on{" "}
            <span className="font-bold text-amber-500">{entry.target}</span>
          </>
        ))
        .with({ action: "emit" }, (entry) => (
          <>
            emitted{" "}
            <span className="font-bold text-amber-500">{entry.event}</span>
          </>
        ))
        .with({ action: "cancel" }, (entry) => (
          <>
            event <span className="font-bold text-red-500">{entry.event}</span>{" "}
            got cancelled
          </>
        ))
        .with({ action: "turned" }, ({ state }) => (
          <>
            was turned{" "}
            <span
              className={cn(
                "font-bold",
                state === "ON" ? "text-green-500" : "text-red-500",
              )}
            >
              {state}
            </span>
          </>
        ))
        .with({ action: "set" }, ({ target, value }) => (
          <>
            has <span className="font-bold text-amber-500">{target}</span> set
            to <span className="font-bold">{value}</span>
          </>
        ))
        .with({ action: "servicePadStatus" }, ({ status }) => (
          <>
            changed Room Service Pad Status to{" "}
            <span className="font-bold text-blue-600">{status}</span>
          </>
        ))
        .with({ action: "doorbell" }, () => (
          <>
            rang the <span className="font-bold text-yellow-600">Doorbell</span>
          </>
        ))
        .exhaustive()}
    </div>
  );
}
