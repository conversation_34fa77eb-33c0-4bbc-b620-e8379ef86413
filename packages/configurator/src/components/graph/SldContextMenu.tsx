import { SldNodeData } from "@/components/devices/sld/types";
import { useReactFlowContext } from "@/components/graph/ReactFlowContext";
import { DeviceIcons } from "@/components/icons/DeviceIcons";
import groundIcon from "@/components/icons/SLD/ground.png";
import lineIcon from "@/components/icons/SLD/line.png";
import { SomoButton } from "@/components/icons/SomoButton";
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuSeparator,
  ContextMenuTrigger,
} from "@/components/ui/context-menu";

import { randomId, CommentNodeData } from "@somo/shared";
import { Edge, Node, ReactFlowInstance } from "@xyflow/react";
import { Battery, MessageSquare, Trash2 } from "lucide-react";
import React from "react";
import { useUser } from "@clerk/clerk-react";
import { createDataFromNode } from "../data/createData";
import { SldNodeType, SLD_NODE_TYPES } from "./config/sldComponentConfig";
import { createCommentDataFromNode } from "./helpers/commentHelpers";

// Helper function to create SLD nodes with proper typing, deriving metadata from components
function createSldNode(
  type: SldNodeType,
  position: { x: number; y: number },
  width: number,
  height: number,
  data?: Partial<SldNodeData>,
  additionalProps: Partial<Node<SldNodeData>> = {},
): Node<SldNodeData> {
  return {
    id: randomId(),
    type,
    data: {
      dataId: "replaced at runtime", // This will be replaced by createDataFromNode
      ...data, // Allow overriding with any provided data
    },
    position,
    width,
    height,
    selected: true,
    draggable: true,
    selectable: true,
    ...additionalProps,
  };
}

// Custom icon components that use the actual PNG icons
const GroundIcon = () => (
  <img src={groundIcon} alt="Ground" className="w-4 h-4 object-contain" />
);

const LineIcon = () => (
  <img src={lineIcon} alt="120V Line" className="w-4 h-4 object-contain" />
);

const NeutralIcon = () => (
  <div className="w-4 h-4 bg-gray-600 rounded-full flex items-center justify-center">
    <span className="text-white text-xs font-bold">N</span>
  </div>
);

const PowerSupplyIcon = () => <Battery className="w-4 h-4 text-red-600" />;

const CommentIcon = () => <MessageSquare className="w-4 h-4 text-blue-600" />;

const LampIcon = () => {
  const LampIconComponent = DeviceIcons.ceilingLamp;
  return (
    <div className="relative w-4 h-4 flex items-center justify-center">
      <SomoButton className="w-4 h-4 text-gray-700 opacity-0" />
      <div className="absolute">
        <LampIconComponent className="w-4 h-4 text-gray-700" />
      </div>
    </div>
  );
};

type SLDContextMenuProps = {
  reactFlowInstance: ReactFlowInstance<Node> | null;
  currentContextMenuPosition: { x: number; y: number } | null;
  setCurrentContextMenuPosition: React.Dispatch<
    React.SetStateAction<{ x: number; y: number } | null>
  >;
  selectedNodes: Node[];
  children: React.ReactNode;
};

export const SLDContextMenu: React.FC<SLDContextMenuProps> = ({
  reactFlowInstance,
  currentContextMenuPosition,
  setCurrentContextMenuPosition,
  selectedNodes,
  children,
}) => {
  const { user } = useUser();
  return (
    <ContextMenu modal={true}>
      <ContextMenuTrigger
        className="absolute inset-0"
        disabled={false}
        onContextMenu={(event) => {
          setCurrentContextMenuPosition({
            x: event.clientX,
            y: event.clientY,
          });
        }}
      >
        {children}
      </ContextMenuTrigger>
      <ContextMenuContent>
        <MenuItem
          label={"Ground"}
          Icon={GroundIcon}
          reactFlowInstance={reactFlowInstance}
          currentContextMenuPosition={currentContextMenuPosition}
          addNodes={({ center }) => [
            createSldNode(SLD_NODE_TYPES.GROUND, center, 56, 56),
          ]}
        />
        <MenuItem
          label={"120V Line"}
          Icon={LineIcon}
          reactFlowInstance={reactFlowInstance}
          currentContextMenuPosition={currentContextMenuPosition}
          addNodes={({ center }) => [
            createSldNode(SLD_NODE_TYPES.LINE, center, 56, 56),
          ]}
        />
        <MenuItem
          label={"Neutral"}
          Icon={NeutralIcon}
          reactFlowInstance={reactFlowInstance}
          currentContextMenuPosition={currentContextMenuPosition}
          addNodes={({ center }) => [
            createSldNode(SLD_NODE_TYPES.NEUTRAL, center, 56, 56),
          ]}
        />
        <MenuItem
          label={"Power Supply"}
          Icon={PowerSupplyIcon}
          reactFlowInstance={reactFlowInstance}
          currentContextMenuPosition={currentContextMenuPosition}
          addNodes={({ center }) => [
            createSldNode(SLD_NODE_TYPES.POWER_SUPPLY, center, 80, 100),
          ]}
        />

        <MenuItem
          label={"Lamp"}
          Icon={LampIcon}
          reactFlowInstance={reactFlowInstance}
          currentContextMenuPosition={currentContextMenuPosition}
          addNodes={({ center }) => [
            createSldNode(SLD_NODE_TYPES.LAMP, center, 56, 56),
          ]}
        />
        <ContextMenuSeparator />
        <MenuItem
          label={"Comment"}
          Icon={CommentIcon}
          reactFlowInstance={reactFlowInstance}
          currentContextMenuPosition={currentContextMenuPosition}
          addNodes={({ center }) => {
            if (!user) {
              throw new Error("User not signed in");
            }

            return [
              {
                id: randomId(),
                type: "comment",
                position: center,
                data: {
                  authorId: user.id,
                  author: user.fullName ?? "Anonymous",
                  authorAvatar: user.imageUrl ?? "",
                  replies: [],
                  createdAt: Date.now(),
                  text: { __type__: "XmlFragment" },
                },
                selected: true,
                draggable: true,
                selectable: true,
                zIndex: 100,
              },
            ] as Node<CommentNodeData>[];
          }}
        />
        {selectedNodes.length > 0 && (
          <>
            <ContextMenuSeparator />
            <ContextMenuItem
              onClick={() => {
                if (!reactFlowInstance) {
                  return;
                }

                // Delete the selected nodes
                reactFlowInstance.deleteElements({
                  nodes: selectedNodes.map((node) => ({ id: node.id })),
                });
              }}
            >
              <Trash2 className="size-4 mr-2" />
              <span>Delete</span>
            </ContextMenuItem>
          </>
        )}
      </ContextMenuContent>
    </ContextMenu>
  );
};

function MenuItem({
  reactFlowInstance,
  currentContextMenuPosition,
  addNodes,
  Icon,
  label,
}: {
  reactFlowInstance: ReactFlowInstance<Node> | null;
  currentContextMenuPosition: { x: number; y: number } | null;
  addNodes: (params: {
    reactFlowInstance: ReactFlowInstance<Node>;
    center: { x: number; y: number };
  }) =>
    | Node<SldNodeData>[]
    | Node<CommentNodeData>[]
    | { nodes: Node<SldNodeData>[] | Node<CommentNodeData>[]; edges?: Edge[] };
  Icon: React.ComponentType<React.ComponentProps<"svg">> | React.ComponentType;
  label: string;
}) {
  const { yDoc } = useReactFlowContext();

  return (
    <ContextMenuItem
      onClick={() => {
        if (!reactFlowInstance) {
          throw new Error("reactFlowInstance is not set");
        }
        if (!currentContextMenuPosition) {
          throw new Error("currentContextMenuPosition is not set");
        }

        const center = reactFlowInstance.screenToFlowPosition(
          currentContextMenuPosition,
        ) ?? { x: 0, y: 0 };

        const nodesOrNodesAndEdges = addNodes({ reactFlowInstance, center });
        const nodes = Array.isArray(nodesOrNodesAndEdges)
          ? nodesOrNodesAndEdges
          : nodesOrNodesAndEdges.nodes;
        const edges = Array.isArray(nodesOrNodesAndEdges)
          ? undefined
          : nodesOrNodesAndEdges.edges;

        // Store node data outside of the ReactFlow graph
        nodes.forEach((node) => {
          // Handle comment nodes specially - store them in SLD YDoc with their original data
          if (node.type === "comment") {
            createCommentDataFromNode(yDoc, node);
            return;
          } else {
            const dataId = createDataFromNode(yDoc, node as Node<SldNodeData>);
            if (dataId) {
              node.data = { dataId };
            }
          }
        });

        // Deselect all existing nodes before adding new selected ones
        const allNodes = reactFlowInstance.getNodes();
        const deselectedNodes = allNodes.map((node) => ({
          ...node,
          selected: false,
        }));
        reactFlowInstance.setNodes(deselectedNodes);

        reactFlowInstance.addNodes(nodes);
        if (edges) {
          reactFlowInstance.addEdges(edges);
        }

        // Ensure the new nodes are selected with a small delay to let ReactFlow process
        setTimeout(() => {
          const newNodeIds = nodes.map((node) => node.id);
          const currentNodes = reactFlowInstance.getNodes();
          const updatedNodes = currentNodes.map((node) => ({
            ...node,
            selected: newNodeIds.includes(node.id),
          }));
          reactFlowInstance.setNodes(updatedNodes);
        }, 10);
      }}
    >
      <div className="flex items-center gap-3">
        <Icon className="size-4" />
        <span>{label}</span>
      </div>
    </ContextMenuItem>
  );
}
