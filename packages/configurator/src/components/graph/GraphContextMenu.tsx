import { BaseStationIcon } from "@/components/devices/base-station/BaseStationIcon";
import { DoorSensorIcon } from "@/components/devices/door-sensor/DoorSensorIcon";
import { OutletDimmerIcon } from "@/components/devices/outlet-dimmer/OutletDimmerIcon";
import { PresenceSensorIcon } from "@/components/devices/presence-sensor/PresenceSensorIcon";
import { RoomDimmerIcon } from "@/components/devices/room-dimmer/RoomDimmerIcon";
import { RoomSwitchIcon } from "@/components/devices/room-switch/RoomSwitchIcon";
import { ServicePadIcon } from "@/components/devices/service-pad/ServicePadIcon";
import { SomoFanIcon } from "@/components/devices/somo-fan/SomoFanIcon";
import { SomoIrControllerIcon } from "@/components/devices/somo-ir-controller/SomoIrControllerIcon";
import { SomoShadesIcon } from "@/components/devices/somo-shades/SomoShadesIcon";
import { SomoThermostatIcon } from "@/components/devices/somo-thermostat/SomoThermostatIcon";
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuSeparator,
  ContextMenuTrigger,
} from "@/components/ui/context-menu";
import { useUser } from "@clerk/clerk-react";
import {
  GraphNode,
  GraphNodesByType,
  LayoutContainerNode,
  randomId,
} from "@somo/shared";
import { Edge, ReactFlowInstance } from "@xyflow/react";
import { useFlags } from "launchdarkly-react-client-sdk";
import {
  ArrowDownFromLine,
  ArrowUpFromLine,
  CirclePlayIcon,
  ImageIcon,
  LampIcon,
  MessageSquareIcon,
  PowerIcon,
  Trash2,
} from "lucide-react";
import React from "react";
import { createDataFromNode } from "../data/createData";
import { CanBusControllerIcon } from "../devices/canbus-controller/CanBusControllerIcon";
import { deleteSelectedNodes } from "./deleteNodes";
import { useReactFlowContext } from "./ReactFlowContext";
import { useRoom } from "./useRoom";

type GraphContextMenuProps = {
  orgId: string;
  controllerId: string;
  reactFlowInstance: ReactFlowInstance<GraphNode> | null;
  currentContextMenuPosition: { x: number; y: number } | null;
  setCurrentContextMenuPosition: React.Dispatch<
    React.SetStateAction<{ x: number; y: number } | null>
  >;
  selectedNodes: GraphNode[];
  children: React.ReactNode;
};

export const GraphContextMenu: React.FC<GraphContextMenuProps> = ({
  orgId,
  controllerId,
  reactFlowInstance,
  currentContextMenuPosition,
  setCurrentContextMenuPosition,
  selectedNodes,
  children,
}) => {
  const { user } = useUser();
  const { data: room } = useRoom({ orgId, controllerId });
  const flags = useFlags();
  const {
    scene,
    roomSwitch,
    virtualButton,
    canbo,
    roomDimmer,
    outletDimmer,
    presenceSensor,
    doorSensor,
    thermostat,
    irController,
    shades,
    fan,
    servicePad,
  } = flags;

  return (
    <ContextMenu modal={true}>
      <ContextMenuTrigger
        className="absolute inset-0"
        disabled={false}
        onContextMenu={(event) => {
          setCurrentContextMenuPosition({
            x: event.clientX,
            y: event.clientY,
          });
        }}
      >
        {children}
      </ContextMenuTrigger>
      <ContextMenuContent>
        {!room?.readOnly && (
          <>
            <MenuItem
              label="Background"
              Icon={ImageIcon}
              reactFlowInstance={reactFlowInstance}
              currentContextMenuPosition={currentContextMenuPosition}
              addNodes={({ center }) => [
                {
                  id: randomId(),
                  type: "image",
                  // TODO: omit `data` from type once all have been migrated
                  data: { dataId: "replaced at runtime" },
                  position: center,
                  width: 598,
                  height: 452,
                  selected: true,
                },
              ]}
            />
            {scene && (
              <MenuItem
                label="Scene"
                Icon={CirclePlayIcon}
                reactFlowInstance={reactFlowInstance}
                currentContextMenuPosition={currentContextMenuPosition}
                addNodes={({ center }) => {
                  center.y = center.y - 8;
                  center.x = center.x - 8;

                  const sceneNode: GraphNodesByType["scene"] = {
                    id: randomId(),
                    type: "scene",
                    // TODO: omit `data` from type once all have been migrated
                    data: { dataId: "replaced at runtime" },
                    position: center,
                    width: 16,
                    height: 16,
                    selected: true,
                    selectable: true,
                    draggable: true,
                    deletable: true,
                  };

                  return [sceneNode];
                }}
              />
            )}
            <ContextMenuSeparator />

            <MenuItem
              label="Light"
              Icon={LampIcon}
              reactFlowInstance={reactFlowInstance}
              currentContextMenuPosition={currentContextMenuPosition}
              addNodes={({ center }) => {
                center.y = center.y - 8;
                center.x = center.x - 8;

                const lightNode: LayoutContainerNode<"light"> = {
                  id: randomId(),
                  type: "light",
                  // TODO: omit `data` from type once all have been migrated
                  data: { dataId: "replaced at runtime" },
                  position: center,
                  width: 16,
                  height: 16,
                  selected: true,
                  selectable: true,
                  draggable: true,
                  deletable: true,
                };

                return [lightNode];
              }}
            />
            <MenuItem
              label="Base Station"
              Icon={BaseStationIcon}
              reactFlowInstance={reactFlowInstance}
              currentContextMenuPosition={currentContextMenuPosition}
              addNodes={({ center }) => {
                center.y = center.y - 8;
                center.x = center.x - 8;

                const anchorNode: GraphNodesByType["baseStationAnchor"] = {
                  id: randomId(),
                  type: "baseStationAnchor",
                  data: {},
                  position: center,
                  width: 16,
                  height: 16,
                  selected: true,
                  selectable: true,
                  draggable: true,
                  deletable: true,
                };

                const containerNode: GraphNodesByType["baseStationContainer"] =
                  {
                    id: randomId(),
                    type: "baseStationContainer",
                    data: { dataId: "replaced at runtime" },
                    position: {
                      x: -(80 - (anchorNode.width ?? 0)) / 2,
                      y: -80,
                    },
                    width: 40,
                    height: 40,
                    selected: false,
                    parentId: anchorNode.id,
                    selectable: true,
                    draggable: true,
                    deletable: true,
                  };

                return {
                  nodes: [anchorNode, containerNode],
                  edges: [
                    {
                      id: randomId(),
                      target: containerNode.id,
                      source: anchorNode.id,
                      type: "simplebezier",
                      selectable: false,
                      style: {
                        strokeWidth: 2,
                        stroke: "#6D6D6D",
                        strokeOpacity: 0.6,
                      },
                    },
                  ],
                };
              }}
            />

            {virtualButton && (
              <MenuItem
                label="Virtual Button"
                Icon={PowerIcon}
                reactFlowInstance={reactFlowInstance}
                currentContextMenuPosition={currentContextMenuPosition}
                addNodes={({ center }) => {
                  center.y = center.y - 8;
                  center.x = center.x - 8;

                  const anchorNode: GraphNodesByType["virtualButtonAnchor"] = {
                    id: randomId(),
                    type: "virtualButtonAnchor",
                    data: {},
                    position: center,
                    width: 16,
                    height: 16,
                    selected: true,
                    selectable: true,
                    draggable: true,
                    deletable: true,
                  };

                  const containerNode: GraphNodesByType["virtualButtonContainer"] =
                    {
                      id: randomId(),
                      type: "virtualButtonContainer",
                      data: { dataId: "replaced at runtime" },
                      position: {
                        x: -(40 - (anchorNode.width ?? 0)) / 2,
                        y: -80,
                      },
                      width: 40,
                      height: 40,
                      selected: false,
                      parentId: anchorNode.id,
                      selectable: true,
                      draggable: true,
                      deletable: true,
                    };

                  return {
                    nodes: [anchorNode, containerNode],
                    edges: [
                      {
                        id: randomId(),
                        target: containerNode.id,
                        source: anchorNode.id,
                        type: "simplebezier",
                        selectable: false,
                        style: {
                          strokeWidth: 2,
                          stroke: "#6D6D6D",
                          strokeOpacity: 0.6,
                        },
                      },
                    ],
                  };
                }}
              />
            )}

            {canbo && (
              <MenuItem
                label="CanBus Controller"
                Icon={CanBusControllerIcon}
                reactFlowInstance={reactFlowInstance}
                currentContextMenuPosition={currentContextMenuPosition}
                addNodes={({ center }) => {
                  center.y = center.y - 8;
                  center.x = center.x - 8;

                  const anchorNode: GraphNodesByType["canbusControllerAnchor"] =
                    {
                      id: randomId(),
                      type: "canbusControllerAnchor",
                      data: {},
                      position: center,
                      width: 16,
                      height: 16,
                      selected: true,
                      selectable: true,
                      draggable: true,
                      deletable: true,
                    };

                  const containerNode: GraphNodesByType["canbusControllerContainer"] =
                    {
                      id: randomId(),
                      type: "canbusControllerContainer",
                      data: { dataId: "replaced at runtime" },
                      position: {
                        x: -(80 - (anchorNode.width ?? 0)) / 2,
                        y: -80,
                      },
                      width: 40,
                      height: 40,
                      selected: false,
                      parentId: anchorNode.id,
                      selectable: true,
                      draggable: true,
                      deletable: true,
                    };

                  return {
                    nodes: [anchorNode, containerNode],
                    edges: [
                      {
                        id: randomId(),
                        target: containerNode.id,
                        source: anchorNode.id,
                        type: "simplebezier",
                        selectable: false,
                        style: {
                          strokeWidth: 2,
                          stroke: "#6D6D6D",
                          strokeOpacity: 0.6,
                        },
                      },
                    ],
                  };
                }}
              />
            )}
            {roomSwitch && (
              <MenuItem
                label="Room Switch"
                Icon={RoomSwitchIcon}
                reactFlowInstance={reactFlowInstance}
                currentContextMenuPosition={currentContextMenuPosition}
                addNodes={({ reactFlowInstance, center }) => {
                  center.y = center.y - 8;
                  center.x = center.x - 8;

                  let zIndex =
                    Math.max(
                      ...reactFlowInstance
                        .getNodes()
                        .map((node) => node.zIndex ?? 0),
                    ) + 1;

                  const anchorNode: GraphNodesByType["roomSwitchAnchor"] = {
                    id: randomId(),
                    type: "roomSwitchAnchor",
                    data: {},
                    position: center,
                    width: 16,
                    height: 16,
                    selected: true,
                    selectable: true,
                    draggable: true,
                    deletable: true,
                    zIndex: zIndex++,
                  };

                  const containerNode: GraphNodesByType["roomSwitchContainer"] =
                    {
                      id: randomId(),
                      type: "roomSwitchContainer",
                      // TODO: omit `data` from type once all have been migrated
                      data: { dataId: "replaced at runtime" },
                      position: {
                        x: -(60 - (anchorNode.width ?? 0)) / 2,
                        y: -140,
                      },
                      selected: false,
                      parentId: anchorNode.id,
                      selectable: true,
                      draggable: true,
                      deletable: true,
                      zIndex: zIndex++,
                    };

                  return {
                    nodes: [anchorNode, containerNode],
                    edges: [
                      {
                        id: randomId(),
                        target: containerNode.id,
                        source: anchorNode.id,
                        type: "simplebezier",
                        selectable: false,
                        style: {
                          strokeWidth: 2,
                          stroke: "#6D6D6D",
                          strokeOpacity: 0.6,
                        },
                      },
                    ],
                  };
                }}
              />
            )}
            {roomDimmer && (
              <MenuItem
                label="Room Dimmer"
                Icon={RoomDimmerIcon}
                reactFlowInstance={reactFlowInstance}
                currentContextMenuPosition={currentContextMenuPosition}
                addNodes={({ reactFlowInstance, center }) => {
                  center.y = center.y - 8;
                  center.x = center.x - 8;

                  let zIndex =
                    Math.max(
                      ...reactFlowInstance
                        .getNodes()
                        .map((node) => node.zIndex ?? 0),
                    ) + 1;

                  const anchorNode: GraphNodesByType["roomDimmerAnchor"] = {
                    id: randomId(),
                    type: "roomDimmerAnchor",
                    data: {},
                    position: center,
                    width: 16,
                    height: 16,
                    selected: true,
                    selectable: true,
                    draggable: true,
                    deletable: true,
                    zIndex: zIndex++,
                  };

                  const containerNode: GraphNodesByType["roomDimmerContainer"] =
                    {
                      id: randomId(),
                      type: "roomDimmerContainer",
                      // TODO: omit `data` from type once all have been migrated
                      data: { dataId: "replaced at runtime" },
                      position: {
                        x: -(60 - (anchorNode.width ?? 0)) / 2,
                        y: -140,
                      },
                      selected: false,
                      parentId: anchorNode.id,
                      selectable: true,
                      draggable: true,
                      deletable: true,
                      zIndex: zIndex++,
                    };

                  return {
                    nodes: [anchorNode, containerNode],
                    edges: [
                      {
                        id: randomId(),
                        target: containerNode.id,
                        source: anchorNode.id,
                        type: "simplebezier",
                        selectable: false,
                        style: {
                          strokeWidth: 2,
                          stroke: "#6D6D6D",
                          strokeOpacity: 0.6,
                        },
                      },
                    ],
                  };
                }}
              />
            )}
            {outletDimmer && (
              <MenuItem
                label="Plug-in Dimmer"
                Icon={OutletDimmerIcon}
                reactFlowInstance={reactFlowInstance}
                currentContextMenuPosition={currentContextMenuPosition}
                addNodes={({ reactFlowInstance, center }) => {
                  center.y = center.y - 8;
                  center.x = center.x - 8;

                  let zIndex =
                    Math.max(
                      ...reactFlowInstance
                        .getNodes()
                        .map((node) => node.zIndex ?? 0),
                    ) + 1;

                  const anchorNode: GraphNodesByType["outletDimmerAnchor"] = {
                    id: randomId(),
                    type: "outletDimmerAnchor",
                    data: {},
                    position: center,
                    width: 16,
                    height: 16,
                    selected: true,
                    selectable: true,
                    draggable: true,
                    deletable: true,
                    zIndex: zIndex++,
                  };

                  const containerNode: GraphNodesByType["outletDimmerContainer"] =
                    {
                      id: randomId(),
                      type: "outletDimmerContainer",
                      data: { dataId: "replaced at runtime" },
                      position: {
                        x: -(60 - (anchorNode.width ?? 0)) / 2,
                        y: -140,
                      },
                      selected: false,
                      parentId: anchorNode.id,
                      selectable: true,
                      draggable: true,
                      deletable: true,
                      zIndex: zIndex++,
                    };

                  return {
                    nodes: [anchorNode, containerNode],
                    edges: [
                      {
                        id: randomId(),
                        target: containerNode.id,
                        source: anchorNode.id,
                        type: "simplebezier",
                        selectable: false,
                        style: {
                          strokeWidth: 2,
                          stroke: "#6D6D6D",
                          strokeOpacity: 0.6,
                        },
                      },
                    ],
                  };
                }}
              />
            )}
            {presenceSensor && (
              <MenuItem
                label="Presence Sensor"
                Icon={PresenceSensorIcon}
                reactFlowInstance={reactFlowInstance}
                currentContextMenuPosition={currentContextMenuPosition}
                addNodes={({ reactFlowInstance, center }) => {
                  center.y = center.y - 8;
                  center.x = center.x - 8;

                  let zIndex =
                    Math.max(
                      ...reactFlowInstance
                        .getNodes()
                        .map((node) => node.zIndex ?? 0),
                    ) + 1;

                  const anchorNode: GraphNodesByType["presenceSensorAnchor"] = {
                    id: randomId(),
                    type: "presenceSensorAnchor",
                    data: {},
                    position: center,
                    width: 16,
                    height: 16,
                    selected: true,
                    selectable: true,
                    draggable: true,
                    deletable: true,
                    zIndex: zIndex++,
                  };

                  const containerNode: GraphNodesByType["presenceSensorContainer"] =
                    {
                      id: randomId(),
                      type: "presenceSensorContainer",
                      data: { dataId: "replaced at runtime" },
                      position: {
                        x: -(60 - (anchorNode.width ?? 0)) / 2,
                        y: -90,
                      },
                      selected: false,
                      parentId: anchorNode.id,
                      selectable: true,
                      draggable: true,
                      deletable: true,
                      zIndex: zIndex++,
                    };

                  return {
                    nodes: [anchorNode, containerNode],
                    edges: [
                      {
                        id: randomId(),
                        target: containerNode.id,
                        source: anchorNode.id,
                        type: "simplebezier",
                        selectable: false,
                        style: {
                          strokeWidth: 2,
                          stroke: "#6D6D6D",
                          strokeOpacity: 0.6,
                        },
                      },
                    ],
                  };
                }}
              />
            )}
            {doorSensor && (
              <MenuItem
                label="Door Sensor"
                Icon={DoorSensorIcon}
                reactFlowInstance={reactFlowInstance}
                currentContextMenuPosition={currentContextMenuPosition}
                addNodes={({ reactFlowInstance, center }) => {
                  center.y = center.y - 8;
                  center.x = center.x - 8;

                  let zIndex =
                    Math.max(
                      ...reactFlowInstance
                        .getNodes()
                        .map((node) => node.zIndex ?? 0),
                    ) + 1;

                  const anchorNode: GraphNodesByType["doorSensorAnchor"] = {
                    id: randomId(),
                    type: "doorSensorAnchor",
                    data: {},
                    position: center,
                    width: 16,
                    height: 16,
                    selected: true,
                    selectable: true,
                    draggable: true,
                    deletable: true,
                    zIndex: zIndex++,
                  };

                  const containerNode: GraphNodesByType["doorSensorContainer"] =
                    {
                      id: randomId(),
                      type: "doorSensorContainer",
                      // TODO: omit `data` from type once all have been migrated
                      data: { dataId: "replaced at runtime" },
                      position: {
                        x: -(60 - (anchorNode.width ?? 0)) / 2,
                        y: -90,
                      },
                      selected: false,
                      parentId: anchorNode.id,
                      selectable: true,
                      draggable: true,
                      deletable: true,
                      zIndex: zIndex++,
                    };

                  return {
                    nodes: [anchorNode, containerNode],
                    edges: [
                      {
                        id: randomId(),
                        target: containerNode.id,
                        source: anchorNode.id,
                        type: "simplebezier",
                        selectable: false,
                        style: {
                          strokeWidth: 2,
                          stroke: "#6D6D6D",
                          strokeOpacity: 0.6,
                        },
                      },
                    ],
                  };
                }}
              />
            )}
            {thermostat && (
              <MenuItem
                label="Thermostat"
                Icon={SomoThermostatIcon}
                reactFlowInstance={reactFlowInstance}
                currentContextMenuPosition={currentContextMenuPosition}
                addNodes={({ reactFlowInstance, center }) => {
                  center.y = center.y - 8;
                  center.x = center.x - 8;

                  let zIndex =
                    Math.max(
                      ...reactFlowInstance
                        .getNodes()
                        .map((node) => node.zIndex ?? 0),
                    ) + 1;

                  const anchorNode: GraphNodesByType["somoThermostatAnchor"] = {
                    id: randomId(),
                    type: "somoThermostatAnchor",
                    data: {},
                    position: center,
                    width: 16,
                    height: 16,
                    selected: true,
                    selectable: true,
                    draggable: true,
                    deletable: true,
                    zIndex: zIndex++,
                  };

                  const containerNode: GraphNodesByType["somoThermostatContainer"] =
                    {
                      id: randomId(),
                      type: "somoThermostatContainer",
                      data: { dataId: "replaced at runtime" },
                      position: {
                        x: -(273 - (anchorNode.width ?? 0)) / 2,
                        y: -120,
                      },
                      selected: false,
                      parentId: anchorNode.id,
                      selectable: true,
                      draggable: true,
                      deletable: true,
                      zIndex: zIndex++,
                    };

                  return {
                    nodes: [anchorNode, containerNode],
                    edges: [
                      {
                        id: randomId(),
                        target: containerNode.id,
                        source: anchorNode.id,
                        type: "simplebezier",
                        selectable: false,
                        style: {
                          strokeWidth: 2,
                          stroke: "#6D6D6D",
                          strokeOpacity: 0.6,
                        },
                      },
                    ],
                  };
                }}
              />
            )}
            {irController && (
              <MenuItem
                label="IR HVAC Controller"
                Icon={SomoIrControllerIcon}
                reactFlowInstance={reactFlowInstance}
                currentContextMenuPosition={currentContextMenuPosition}
                addNodes={({ reactFlowInstance, center }) => {
                  center.y = center.y - 8;
                  center.x = center.x - 8;

                  let zIndex =
                    Math.max(
                      ...reactFlowInstance
                        .getNodes()
                        .map((node) => node.zIndex ?? 0),
                    ) + 1;

                  const anchorNode: GraphNodesByType["somoIrControllerAnchor"] =
                    {
                      id: randomId(),
                      type: "somoIrControllerAnchor",
                      data: {},
                      position: center,
                      width: 16,
                      height: 16,
                      selected: true,
                      selectable: true,
                      draggable: true,
                      deletable: true,
                      zIndex: zIndex++,
                    };

                  const containerNode: GraphNodesByType["somoIrControllerContainer"] =
                    {
                      id: randomId(),
                      type: "somoIrControllerContainer",
                      data: { dataId: "replaced at runtime" },
                      position: {
                        x: -(250 - (anchorNode.width ?? 0)) / 2,
                        y: -120,
                      },
                      selected: false,
                      parentId: anchorNode.id,
                      selectable: true,
                      draggable: true,
                      deletable: true,
                      zIndex: zIndex++,
                    };

                  return {
                    nodes: [anchorNode, containerNode],
                    edges: [
                      {
                        id: randomId(),
                        target: containerNode.id,
                        source: anchorNode.id,
                        type: "simplebezier",
                        selectable: false,
                        style: {
                          strokeWidth: 2,
                          stroke: "#6D6D6D",
                          strokeOpacity: 0.6,
                        },
                      },
                    ],
                  };
                }}
              />
            )}
            {shades && (
              <MenuItem
                label="Somo Shades"
                Icon={SomoShadesIcon}
                reactFlowInstance={reactFlowInstance}
                currentContextMenuPosition={currentContextMenuPosition}
                addNodes={({ reactFlowInstance, center }) => {
                  center.y = center.y - 8;
                  center.x = center.x - 8;

                  let zIndex =
                    Math.max(
                      ...reactFlowInstance
                        .getNodes()
                        .map((node) => node.zIndex ?? 0),
                    ) + 1;

                  const anchorNode: GraphNodesByType["somoShadesAnchor"] = {
                    id: randomId(),
                    type: "somoShadesAnchor",
                    data: {},
                    position: center,
                    width: 16,
                    height: 16,
                    selected: true,
                    selectable: true,
                    draggable: true,
                    deletable: true,
                    zIndex: zIndex++,
                  };

                  const containerNode: GraphNodesByType["somoShadesContainer"] =
                    {
                      id: randomId(),
                      type: "somoShadesContainer",
                      data: { dataId: "replaced at runtime" },
                      position: {
                        x: -(68 - (anchorNode.width ?? 0)) / 2,
                        y: -120,
                      },
                      selected: false,
                      parentId: anchorNode.id,
                      selectable: true,
                      draggable: true,
                      deletable: true,
                      zIndex: zIndex++,
                    };

                  return {
                    nodes: [anchorNode, containerNode],
                    edges: [
                      {
                        id: randomId(),
                        target: containerNode.id,
                        source: anchorNode.id,
                        type: "simplebezier",
                        selectable: false,
                        style: {
                          strokeWidth: 2,
                          stroke: "#6D6D6D",
                          strokeOpacity: 0.6,
                        },
                      },
                    ],
                  };
                }}
              />
            )}
            {fan && (
              <MenuItem
                label="Room Fan Controller"
                Icon={SomoFanIcon}
                reactFlowInstance={reactFlowInstance}
                currentContextMenuPosition={currentContextMenuPosition}
                addNodes={({ reactFlowInstance, center }) => {
                  center.y = center.y - 8;
                  center.x = center.x - 8;

                  let zIndex =
                    Math.max(
                      ...reactFlowInstance
                        .getNodes()
                        .map((node) => node.zIndex ?? 0),
                    ) + 1;

                  const anchorNode: GraphNodesByType["somoFanAnchor"] = {
                    id: randomId(),
                    type: "somoFanAnchor",
                    data: {},
                    position: center,
                    width: 16,
                    height: 16,
                    selected: true,
                    selectable: true,
                    draggable: true,
                    deletable: true,
                    zIndex: zIndex++,
                  };

                  const containerNode: GraphNodesByType["somoFanContainer"] = {
                    id: randomId(),
                    type: "somoFanContainer",
                    data: { dataId: "replaced at runtime" },
                    position: {
                      x: -(60 - (anchorNode.width ?? 0)) / 2,
                      y: -90,
                    },
                    selected: false,
                    parentId: anchorNode.id,
                    selectable: true,
                    draggable: true,
                    deletable: true,
                    zIndex: zIndex++,
                  };

                  return {
                    nodes: [anchorNode, containerNode],
                    edges: [
                      {
                        id: randomId(),
                        target: containerNode.id,
                        source: anchorNode.id,
                        type: "simplebezier",
                        selectable: false,
                        style: {
                          strokeWidth: 2,
                          stroke: "#6D6D6D",
                          strokeOpacity: 0.6,
                        },
                      },
                    ],
                  };
                }}
              />
            )}
            {servicePad && (
              <MenuItem
                label="Service Pad"
                Icon={ServicePadIcon}
                reactFlowInstance={reactFlowInstance}
                currentContextMenuPosition={currentContextMenuPosition}
                addNodes={({ reactFlowInstance, center }) => {
                  center.y = center.y - 8;
                  center.x = center.x - 8;

                  let zIndex =
                    Math.max(
                      ...reactFlowInstance
                        .getNodes()
                        .map((node) => node.zIndex ?? 0),
                    ) + 1;

                  const anchorNode: GraphNodesByType["servicePadAnchor"] = {
                    id: randomId(),
                    type: "servicePadAnchor",
                    data: {},
                    position: center,
                    width: 16,
                    height: 16,
                    selected: true,
                    selectable: true,
                    draggable: true,
                    deletable: true,
                    zIndex: zIndex++,
                  };

                  const containerNode: GraphNodesByType["servicePadContainer"] =
                    {
                      id: randomId(),
                      type: "servicePadContainer",
                      data: { dataId: "replaced at runtime" },
                      position: {
                        x: -(76 - (anchorNode.width ?? 0)) / 2,
                        y: -120,
                      },
                      selected: false,
                      parentId: anchorNode.id,
                      selectable: true,
                      draggable: true,
                      deletable: true,
                      zIndex: zIndex++,
                    };

                  return {
                    nodes: [anchorNode, containerNode],
                    edges: [
                      {
                        id: randomId(),
                        target: containerNode.id,
                        source: anchorNode.id,
                        type: "simplebezier",
                        selectable: false,
                        style: {
                          strokeWidth: 2,
                          stroke: "#6D6D6D",
                          strokeOpacity: 0.6,
                        },
                      },
                    ],
                  };
                }}
              />
            )}
            <ContextMenuSeparator />
          </>
        )}
        <MenuItem
          label="Comment"
          Icon={MessageSquareIcon}
          reactFlowInstance={reactFlowInstance}
          currentContextMenuPosition={currentContextMenuPosition}
          addNodes={({ reactFlowInstance, center }) => {
            if (!user) {
              throw new Error("user not signed in");
            }

            center.y = center.y - 8;
            center.x = center.x - 8;

            let zIndex =
              Math.max(
                ...reactFlowInstance.getNodes().map((node) => node.zIndex ?? 0),
              ) + 1;

            const commentNode: GraphNodesByType["comment"] = {
              id: randomId(),
              type: "comment",
              data: {
                authorId: user.id,
                author: user.fullName ?? "",
                authorAvatar: user.imageUrl,
                replies: [],
                createdAt: Date.now(),
                text: {
                  __type__: "XmlFragment",
                },
              },
              position: center,
              width: 16,
              height: 16,
              selected: true,
              selectable: true,
              draggable: true,
              deletable: true,
              zIndex: zIndex++,
            };

            return [commentNode];
          }}
        />
        {selectedNodes.length > 0 && (
          <>
            <ContextMenuSeparator />
            {!room?.readOnly && (
              <>
                <ContextMenuItem
                  onClick={() => {
                    if (!reactFlowInstance) {
                      return;
                    }
                    const nodes = reactFlowInstance.getNodes() ?? [];
                    const maxZIndex = Math.max(
                      ...nodes.map((node) => node.zIndex ?? 0),
                    );
                    let i = 1;
                    for (const node of selectedNodes) {
                      const zIndex = maxZIndex + i++;
                      reactFlowInstance.updateNode(node.id, {
                        zIndex,
                        ...node,
                      });
                    }
                  }}
                >
                  <ArrowUpFromLine className="size-4 mr-2" />
                  <span>Bring to front</span>
                </ContextMenuItem>
                <ContextMenuItem
                  onClick={() => {
                    if (!reactFlowInstance) {
                      return;
                    }
                    const nodes = reactFlowInstance.getNodes() ?? [];
                    const minZIndex = Math.min(
                      ...nodes.map((node) => node.zIndex ?? 0),
                    );
                    let i = 1;
                    for (const node of selectedNodes) {
                      const zIndex = minZIndex - i++;
                      reactFlowInstance.updateNode(node.id, {
                        ...node,
                        zIndex,
                      });
                    }
                  }}
                >
                  <ArrowDownFromLine className="size-4 mr-2" />
                  <span>Send to back</span>
                </ContextMenuItem>

                <ContextMenuSeparator />
              </>
            )}
            <ContextMenuItem
              onClick={() => {
                if (!reactFlowInstance) {
                  return;
                }

                deleteSelectedNodes(
                  reactFlowInstance,
                  selectedNodes,
                  room?.readOnly ?? false,
                );
              }}
            >
              <Trash2 className="size-4 mr-2" />
              <span>Delete</span>
            </ContextMenuItem>
          </>
        )}
      </ContextMenuContent>
    </ContextMenu>
  );
};

type Position = { x: number; y: number };

function MenuItem({
  reactFlowInstance,
  currentContextMenuPosition,
  addNodes,
  Icon,
  label,
}: {
  reactFlowInstance: ReactFlowInstance<GraphNode> | null;
  currentContextMenuPosition: Position | null;
  addNodes: (params: {
    reactFlowInstance: ReactFlowInstance<GraphNode>;
    center: Position;
  }) => GraphNode[] | { nodes: GraphNode[]; edges?: Edge[] };
  Icon: React.ComponentType<React.ComponentProps<"svg">>;
  label: string;
}) {
  const { yDoc } = useReactFlowContext();

  return (
    <ContextMenuItem
      onClick={() => {
        if (!reactFlowInstance) {
          throw new Error("reactFlowInstance is not set");
        }
        if (!currentContextMenuPosition) {
          throw new Error("currentContextMenuPosition is not set");
        }

        const center = reactFlowInstance.screenToFlowPosition(
          currentContextMenuPosition,
        ) ?? { x: 0, y: 0 };

        const nodesOrNodesAndEdges = addNodes({ reactFlowInstance, center });
        const nodes = Array.isArray(nodesOrNodesAndEdges)
          ? nodesOrNodesAndEdges
          : nodesOrNodesAndEdges.nodes;
        const edges = Array.isArray(nodesOrNodesAndEdges)
          ? undefined
          : nodesOrNodesAndEdges.edges;

        // Store node data outside of the ReactFlow graph
        nodes.forEach((node) => {
          const dataId = createDataFromNode(yDoc, node);
          if (dataId) {
            node.data = { dataId };
          }
        });

        reactFlowInstance.addNodes(nodes);
        if (edges) {
          reactFlowInstance.addEdges(edges);
        }
      }}
    >
      <Icon className="size-4 mr-2" />
      <span>{label}</span>
    </ContextMenuItem>
  );
}
