import {
  aDeviceControlSettings,
  aDoorSensorAction,
  aFanControlSettings,
  aMomentaryCanBusController,
  aOutletDimmerButton,
  aOutletDimmerVia,
  aPresenceSensorAction,
  aRoomDimmerButton,
  aRoomDimmerVia,
  aRoomSwitchVia,
  aServicePadAction,
  aShadesControlSettings,
  aSomoFanButton,
  aSomoFanVia,
  aSomoShadesButton,
  aSomoShadesVia,
  GraphNode,
} from "@somo/shared";
import { describe, expect, it } from "vitest";
import * as Y from "yjs";
import { generateVirtualEdges } from "./generateVirtualEdges";
import { aContainerNode, anAnchorNode } from "./layoutGraphFactories";

describe("generateVisibleEdges", () => {
  let yDoc: Y.Doc;

  beforeEach(() => {
    yDoc = new Y.Doc();
  });

  it("returns an empty list if there are no node", () => {
    expect(generateVirtualEdges(yDoc, [])).toEqual([]);
  });

  describe("CanBus Controller", () => {
    it("returns devices that are connected to controller", () => {
      const controller = aMomentaryCanBusController({
        id: "momentaryCanBusControllerId",
        onUpClick: {
          "irrelevant-id": aDeviceControlSettings({
            deviceId: "deviceId",
          }),
        },
      });
      const nodes = [
        aContainerNode(yDoc, "light", { id: "deviceId" }),
        aContainerNode(
          yDoc,
          "canbusControllerContainer",
          { id: "canbusControllerContainerId" },
          { controllers: { "irrelevant-id": controller } },
        ),
      ] satisfies GraphNode[];

      const result = generateVirtualEdges(yDoc, nodes);

      expect(result).toMatchObject([
        {
          id: "canbusControllerContainerId-momentaryCanBusControllerId/deviceId-deviceId",
          source: "canbusControllerContainerId",
          sourceHandle: "momentaryCanBusControllerId",
          target: "deviceId",
          targetHandle: "deviceId",
        },
      ]);
    });

    it("prevents duplicated edges based on IDs for canbus controllers", () => {
      const controller = aMomentaryCanBusController({
        id: "momentaryCanBusControllerId",
        onUpClick: {
          "irrelevant-id": aDeviceControlSettings({
            deviceId: "deviceId",
          }),
        },
      });
      const nodes = [
        aContainerNode(yDoc, "light", { id: "deviceId" }),
        aContainerNode(
          yDoc,
          "canbusControllerContainer",
          { id: "canbusControllerContainerId" },
          {
            controllers: {
              "first-controller": controller,
              "duplicated-controller": controller,
            },
          },
        ),
      ] satisfies GraphNode[];

      const result = generateVirtualEdges(yDoc, nodes);

      expect(result).toMatchObject([
        {
          id: "canbusControllerContainerId-momentaryCanBusControllerId/deviceId-deviceId",
          source: "canbusControllerContainerId",
          sourceHandle: "momentaryCanBusControllerId",
          target: "deviceId",
          targetHandle: "deviceId",
        },
      ]);
    });

    it("returns all canbus controllers anchors that are connected to selected light", () => {
      const controller = aMomentaryCanBusController({
        id: "momentaryCanBusControllerId",
        onUpClick: {
          "irrelevant-id": aDeviceControlSettings({
            deviceId: "deviceId",
          }),
        },
      });
      const nodes = [
        aContainerNode(yDoc, "light", { id: "deviceId", selected: true }),
        anAnchorNode("canbusControllerAnchor", {
          id: "canbusControllerAnchorId",
        }),
        aContainerNode(
          yDoc,
          "canbusControllerContainer",
          {
            id: "canbusControllerContainerId",
            parentId: "canbusControllerAnchorId",
          },
          { controllers: { "irrelevant-id": controller } },
        ),
      ] satisfies GraphNode[];

      const result = generateVirtualEdges(yDoc, nodes);

      expect(result).toMatchObject([
        {
          id: "canbusControllerContainerId-momentaryCanBusControllerId/deviceId-deviceId",
        },
        {
          id: "canbusControllerAnchorId-/deviceId-deviceId",
          source: "canbusControllerAnchorId",
          sourceHandle: "",
          target: "deviceId",
          targetHandle: "deviceId",
        },
      ]);
    });
  });

  describe("BaseStation", () => {
    it("returns empty edges for BaseStation container as it has no actions", () => {
      const nodes = [
        aContainerNode(yDoc, "baseStationContainer", {
          id: "baseStationContainerId",
          selected: true,
        }),
      ] satisfies GraphNode[];

      const result = generateVirtualEdges(yDoc, nodes);

      expect(result).toEqual([]);
    });
  });

  describe("RoomSwitch", () => {
    it("returns devices that are connected if they are enabled and their container is selected", () => {
      const switchVia = aRoomSwitchVia({
        enabled: true,
        onUpClick: {
          "irrelevant-id": aDeviceControlSettings({
            deviceId: "deviceId",
          }),
        },
      });
      const nodes = [
        aContainerNode(yDoc, "light", { id: "deviceId" }),
        aContainerNode(
          yDoc,
          "roomSwitchContainer",
          { id: "roomSwitchContainerId", selected: true },
          { via1: switchVia, via2: switchVia },
        ),
      ] satisfies GraphNode[];

      const result = generateVirtualEdges(yDoc, nodes);

      expect(result).toMatchObject([
        {
          id: "roomSwitchContainerId-via1/deviceId-deviceId",
          source: "roomSwitchContainerId",
          sourceHandle: "via1",
          target: "deviceId",
          targetHandle: "deviceId",
        },
        {
          id: "roomSwitchContainerId-via2/deviceId-deviceId",
          source: "roomSwitchContainerId",
          sourceHandle: "via2",
          target: "deviceId",
          targetHandle: "deviceId",
        },
      ]);
    });

    it("omits RoomSwitch via if they are disabled", () => {
      const switchVia = aRoomSwitchVia({
        enabled: false,
        onUpClick: {
          "irrelevant-id": aDeviceControlSettings({
            deviceId: "deviceId",
          }),
        },
      });
      const nodes = [
        aContainerNode(yDoc, "light", { id: "deviceId" }),
        aContainerNode(
          yDoc,
          "roomSwitchContainer",
          { id: "roomSwitchContainerId", selected: true },
          { via1: switchVia },
        ),
      ] satisfies GraphNode[];

      const result = generateVirtualEdges(yDoc, nodes);

      expect(result).toEqual([]);
    });

    it("omits RoomSwitch via if their container is not selected", () => {
      const switchVia = aRoomSwitchVia({
        enabled: true,
        onUpClick: {
          "irrelevant-id": aDeviceControlSettings({
            deviceId: "deviceId",
          }),
        },
      });
      const nodes = [
        aContainerNode(yDoc, "light", { id: "deviceId" }),
        aContainerNode(
          yDoc,
          "roomSwitchContainer",
          { id: "roomSwitchContainerId", selected: false },
          { via1: switchVia },
        ),
      ] satisfies GraphNode[];

      const result = generateVirtualEdges(yDoc, nodes);

      expect(result).toEqual([]);
    });

    it("returns all RoomSwitch anchors that are connected to selected light", () => {
      const switchVia = aRoomSwitchVia({
        enabled: true,
        onUpClick: {
          "irrelevant-id": aDeviceControlSettings({
            deviceId: "deviceId",
          }),
        },
      });
      const nodes = [
        aContainerNode(yDoc, "light", { id: "deviceId", selected: true }),
        anAnchorNode("roomSwitchAnchor", { id: "roomSwitchAnchorId" }),
        aContainerNode(
          yDoc,
          "roomSwitchContainer",
          { id: "roomSwitchContainerId", parentId: "roomSwitchAnchorId" },
          { via1: switchVia },
        ),
      ] satisfies GraphNode[];

      const result = generateVirtualEdges(yDoc, nodes);

      expect(result).toMatchObject([
        {
          id: "roomSwitchAnchorId-/deviceId-deviceId",
          source: "roomSwitchAnchorId",
          sourceHandle: "",
          target: "deviceId",
          targetHandle: "deviceId",
        },
      ]);
    });
  });

  describe("RoomDimmer", () => {
    it("returns devices that are connected if they are not buttons or enabled, and their container is selected", () => {
      const dimmerVia = aRoomDimmerVia({
        onUpClick: {
          "irrelevant-id": aDeviceControlSettings({
            deviceId: "deviceId",
          }),
        },
      });
      const dimmerButton = aRoomDimmerButton({
        enabled: true,
        onUpClick: {
          "irrelevant-id": aDeviceControlSettings({
            deviceId: "deviceId",
          }),
        },
      });
      const nodes = [
        aContainerNode(yDoc, "light", { id: "deviceId" }),
        aContainerNode(
          yDoc,
          "roomDimmerContainer",
          { id: "roomDimmerContainerId", selected: true },
          { via: dimmerVia, viaUp: dimmerButton },
        ),
      ] satisfies GraphNode[];

      const result = generateVirtualEdges(yDoc, nodes);

      expect(result).toMatchObject([
        {
          id: "roomDimmerContainerId-via/deviceId-deviceId",
          source: "roomDimmerContainerId",
          sourceHandle: "via",
          target: "deviceId",
          targetHandle: "deviceId",
        },
        {
          id: "roomDimmerContainerId-viaUp/deviceId-deviceId",
          source: "roomDimmerContainerId",
          sourceHandle: "viaUp",
          target: "deviceId",
          targetHandle: "deviceId",
        },
      ]);
    });

    it("omits RoomDimmer via devices buttons that are disabled", () => {
      const dimmerVia = aRoomDimmerVia({
        onUpClick: {
          "irrelevant-id": aDeviceControlSettings({
            deviceId: "deviceId",
          }),
        },
      });
      const dimmerButton = aRoomDimmerButton({
        enabled: false,
        onUpClick: {
          "irrelevant-id": aDeviceControlSettings({
            deviceId: "deviceId",
          }),
        },
      });
      const nodes = [
        aContainerNode(yDoc, "light", { id: "deviceId" }),
        aContainerNode(
          yDoc,
          "roomDimmerContainer",
          { id: "roomDimmerContainerId", selected: true },
          { via: dimmerVia, viaUp: dimmerButton },
        ),
      ] satisfies GraphNode[];

      const result = generateVirtualEdges(yDoc, nodes);

      expect(result).toMatchObject([
        {
          id: "roomDimmerContainerId-via/deviceId-deviceId",
          source: "roomDimmerContainerId",
          sourceHandle: "via",
          target: "deviceId",
          targetHandle: "deviceId",
        },
      ]);
    });

    it("omits RoomDimmer via if their container is not selected", () => {
      const dimmerVia = aRoomDimmerVia({
        onUpClick: {
          "irrelevant-id": aDeviceControlSettings({
            deviceId: "deviceId",
          }),
        },
      });
      const nodes = [
        aContainerNode(yDoc, "light", { id: "deviceId" }),
        aContainerNode(
          yDoc,
          "roomDimmerContainer",
          { id: "roomDimmerContainerId", selected: false },
          { via: dimmerVia },
        ),
      ] satisfies GraphNode[];

      const result = generateVirtualEdges(yDoc, nodes);

      expect(result).toEqual([]);
    });

    it("returns all RoomDimmer anchors that are connected to selected light", () => {
      const dimmerVia = aRoomDimmerVia({
        onUpClick: {
          "irrelevant-id": aDeviceControlSettings({
            deviceId: "deviceId",
          }),
        },
      });
      const nodes = [
        aContainerNode(yDoc, "light", { id: "deviceId", selected: true }),
        anAnchorNode("roomDimmerAnchor", { id: "roomDimmerAnchorId" }),
        aContainerNode(
          yDoc,
          "roomDimmerContainer",
          { id: "roomDimmerContainerId", parentId: "roomDimmerAnchorId" },
          { via: dimmerVia },
        ),
      ] satisfies GraphNode[];

      const result = generateVirtualEdges(yDoc, nodes);

      expect(result).toMatchObject([
        {
          id: "roomDimmerAnchorId-/deviceId-deviceId",
          source: "roomDimmerAnchorId",
          sourceHandle: "",
          target: "deviceId",
          targetHandle: "deviceId",
        },
      ]);
    });
  });

  describe("OutletDimmer", () => {
    it("returns devices that are connected if they are not buttons or enabled, and their container is selected", () => {
      const dimmerVia = aOutletDimmerVia({
        onUpClick: {
          "irrelevant-id": aDeviceControlSettings({
            deviceId: "deviceId",
          }),
        },
      });
      const dimmerButton = aOutletDimmerButton({
        enabled: true,
        onUpClick: {
          "irrelevant-id": aDeviceControlSettings({
            deviceId: "deviceId",
          }),
        },
      });
      const nodes = [
        aContainerNode(yDoc, "light", { id: "deviceId" }),
        aContainerNode(
          yDoc,
          "outletDimmerContainer",
          { id: "outletDimmerContainerId", selected: true },
          { via: dimmerVia, viaUp: dimmerButton },
        ),
      ] satisfies GraphNode[];

      const result = generateVirtualEdges(yDoc, nodes);

      expect(result).toMatchObject([
        {
          id: "outletDimmerContainerId-via/deviceId-deviceId",
          source: "outletDimmerContainerId",
          sourceHandle: "via",
          target: "deviceId",
          targetHandle: "deviceId",
        },
        {
          id: "outletDimmerContainerId-viaUp/deviceId-deviceId",
          source: "outletDimmerContainerId",
          sourceHandle: "viaUp",
          target: "deviceId",
          targetHandle: "deviceId",
        },
      ]);
    });

    it("omits OutletDimmer via devices buttons that are disabled", () => {
      const dimmerVia = aOutletDimmerVia({
        onUpClick: {
          "irrelevant-id": aDeviceControlSettings({
            deviceId: "deviceId",
          }),
        },
      });
      const dimmerButton = aOutletDimmerButton({
        enabled: false,
        onUpClick: {
          "irrelevant-id": aDeviceControlSettings({
            deviceId: "deviceId",
          }),
        },
      });
      const nodes = [
        aContainerNode(yDoc, "light", { id: "deviceId" }),
        aContainerNode(
          yDoc,
          "outletDimmerContainer",
          { id: "outletDimmerContainerId", selected: true },
          { via: dimmerVia, viaUp: dimmerButton },
        ),
      ] satisfies GraphNode[];

      const result = generateVirtualEdges(yDoc, nodes);

      expect(result).toMatchObject([
        {
          id: "outletDimmerContainerId-via/deviceId-deviceId",
          source: "outletDimmerContainerId",
          sourceHandle: "via",
          target: "deviceId",
          targetHandle: "deviceId",
        },
      ]);
    });

    it("omits OutletDimmer via if their container is not selected", () => {
      const dimmerVia = aOutletDimmerVia({
        onUpClick: {
          "irrelevant-id": aDeviceControlSettings({
            deviceId: "deviceId",
          }),
        },
      });
      const nodes = [
        aContainerNode(yDoc, "light", { id: "deviceId" }),
        aContainerNode(
          yDoc,
          "outletDimmerContainer",
          { id: "outletDimmerContainerId", selected: false },
          { via: dimmerVia },
        ),
      ] satisfies GraphNode[];

      const result = generateVirtualEdges(yDoc, nodes);

      expect(result).toEqual([]);
    });

    it("returns all OutletDimmer anchors that are connected to selected light", () => {
      const dimmerVia = aOutletDimmerVia({
        onUpClick: {
          "irrelevant-id": aDeviceControlSettings({
            deviceId: "deviceId",
          }),
        },
      });
      const nodes = [
        aContainerNode(yDoc, "light", { id: "deviceId", selected: true }),
        anAnchorNode("outletDimmerAnchor", { id: "outletDimmerAnchorId" }),
        aContainerNode(
          yDoc,
          "outletDimmerContainer",
          { id: "outletDimmerContainerId", parentId: "outletDimmerAnchorId" },
          { via: dimmerVia },
        ),
      ] satisfies GraphNode[];

      const result = generateVirtualEdges(yDoc, nodes);

      expect(result).toMatchObject([
        {
          id: "outletDimmerAnchorId-/deviceId-deviceId",
          source: "outletDimmerAnchorId",
          sourceHandle: "",
          target: "deviceId",
          targetHandle: "deviceId",
        },
      ]);
    });
  });

  describe("SomoShades", () => {
    it("returns devices that are connected if they are not buttons or enabled, and their container is selected", () => {
      const shadesVia = aSomoShadesVia({
        onUpClick: {
          "irrelevant-id": aShadesControlSettings({
            deviceId: "deviceId",
          }),
        },
      });
      const shadesButton = aSomoShadesButton({
        enabled: true,
        onUpClick: {
          "irrelevant-id": aShadesControlSettings({
            deviceId: "deviceId",
          }),
        },
      });
      const nodes = [
        aContainerNode(yDoc, "light", { id: "deviceId" }),
        aContainerNode(
          yDoc,
          "somoShadesContainer",
          { id: "somoShadesContainerId", selected: true },
          { via: shadesVia, viaUp: shadesButton },
        ),
      ] satisfies GraphNode[];

      const result = generateVirtualEdges(yDoc, nodes);

      expect(result).toMatchObject([
        {
          id: "somoShadesContainerId-via/deviceId-deviceId",
          source: "somoShadesContainerId",
          sourceHandle: "via",
          target: "deviceId",
          targetHandle: "deviceId",
        },
        {
          id: "somoShadesContainerId-viaUp/deviceId-deviceId",
          source: "somoShadesContainerId",
          sourceHandle: "viaUp",
          target: "deviceId",
          targetHandle: "deviceId",
        },
      ]);
    });

    it("omits SomoShades via devices buttons that are disabled", () => {
      const shadesVia = aSomoShadesVia({
        onUpClick: {
          "irrelevant-id": aShadesControlSettings({
            deviceId: "deviceId",
          }),
        },
      });
      const shadesButton = aSomoShadesButton({
        enabled: false,
        onUpClick: {
          "irrelevant-id": aShadesControlSettings({
            deviceId: "deviceId",
          }),
        },
      });
      const nodes = [
        aContainerNode(yDoc, "light", { id: "deviceId" }),
        aContainerNode(
          yDoc,
          "somoShadesContainer",
          { id: "somoShadesContainerId", selected: true },
          { via: shadesVia, viaUp: shadesButton },
        ),
      ] satisfies GraphNode[];

      const result = generateVirtualEdges(yDoc, nodes);

      expect(result).toMatchObject([
        {
          id: "somoShadesContainerId-via/deviceId-deviceId",
          source: "somoShadesContainerId",
          sourceHandle: "via",
          target: "deviceId",
          targetHandle: "deviceId",
        },
      ]);
    });

    it("omits SomoShades via if their container is not selected", () => {
      const shadesVia = aSomoShadesVia({
        onUpClick: {
          "irrelevant-id": aShadesControlSettings({
            deviceId: "deviceId",
          }),
        },
      });
      const nodes = [
        aContainerNode(yDoc, "light", { id: "deviceId" }),
        aContainerNode(
          yDoc,
          "somoShadesContainer",
          { id: "somoShadesContainerId", selected: false },
          { via: shadesVia },
        ),
      ] satisfies GraphNode[];

      const result = generateVirtualEdges(yDoc, nodes);

      expect(result).toEqual([]);
    });

    it("returns all SomoShades anchors that are connected to selected light", () => {
      const shadesVia = aSomoShadesVia({
        onUpClick: {
          "irrelevant-id": aShadesControlSettings({
            deviceId: "deviceId",
          }),
        },
      });
      const nodes = [
        aContainerNode(yDoc, "light", { id: "deviceId", selected: true }),
        anAnchorNode("somoShadesAnchor", { id: "somoShadesAnchorId" }),
        aContainerNode(
          yDoc,
          "somoShadesContainer",
          { id: "somoShadesContainerId", parentId: "somoShadesAnchorId" },
          { via: shadesVia },
        ),
      ] satisfies GraphNode[];

      const result = generateVirtualEdges(yDoc, nodes);

      expect(result).toMatchObject([
        {
          id: "somoShadesAnchorId-/deviceId-deviceId",
          source: "somoShadesAnchorId",
          sourceHandle: "",
          target: "deviceId",
          targetHandle: "deviceId",
        },
      ]);
    });
  });

  describe("SomoFan", () => {
    it("returns devices that are connected if they are not buttons or enabled, and their container is selected", () => {
      const fanVia = aSomoFanVia({
        onUpClick: {
          "irrelevant-id": aFanControlSettings({
            deviceId: "deviceId",
          }),
        },
      });
      const fanButton = aSomoFanButton({
        enabled: true,
        onUpClick: {
          "irrelevant-id": aFanControlSettings({
            deviceId: "deviceId",
          }),
        },
      });
      const nodes = [
        aContainerNode(yDoc, "light", { id: "deviceId" }),
        aContainerNode(
          yDoc,
          "somoFanContainer",
          { id: "somoFanContainerId", selected: true },
          {
            via: fanVia,
            viaHigh: fanButton,
            viaMed: fanButton,
            viaLow: fanButton,
          },
        ),
      ] satisfies GraphNode[];

      const result = generateVirtualEdges(yDoc, nodes);

      expect(result).toMatchObject([
        {
          id: "somoFanContainerId-via/deviceId-deviceId",
          source: "somoFanContainerId",
          sourceHandle: "via",
          target: "deviceId",
          targetHandle: "deviceId",
        },
        {
          id: "somoFanContainerId-viaLow/deviceId-deviceId",
          source: "somoFanContainerId",
          sourceHandle: "viaLow",
          target: "deviceId",
          targetHandle: "deviceId",
        },
        {
          id: "somoFanContainerId-viaMed/deviceId-deviceId",
          source: "somoFanContainerId",
          sourceHandle: "viaMed",
          target: "deviceId",
          targetHandle: "deviceId",
        },
        {
          id: "somoFanContainerId-viaHigh/deviceId-deviceId",
          source: "somoFanContainerId",
          sourceHandle: "viaHigh",
          target: "deviceId",
          targetHandle: "deviceId",
        },
      ]);
    });

    it("omits SomoFan via devices buttons that are disabled", () => {
      const fanVia = aSomoFanVia({
        onUpClick: {
          "irrelevant-id": aFanControlSettings({
            deviceId: "deviceId",
          }),
        },
      });
      const fanButton = aSomoFanButton({
        enabled: false,
        onUpClick: {
          "irrelevant-id": aFanControlSettings({
            deviceId: "deviceId",
          }),
        },
      });
      const nodes = [
        aContainerNode(yDoc, "light", { id: "deviceId" }),
        aContainerNode(
          yDoc,
          "somoFanContainer",
          { id: "somoFanContainerId", selected: true },
          {
            via: fanVia,
            viaHigh: fanButton,
            viaMed: fanButton,
            viaLow: fanButton,
          },
        ),
      ] satisfies GraphNode[];

      const result = generateVirtualEdges(yDoc, nodes);

      expect(result).toMatchObject([
        {
          id: "somoFanContainerId-via/deviceId-deviceId",
          source: "somoFanContainerId",
          sourceHandle: "via",
          target: "deviceId",
          targetHandle: "deviceId",
        },
      ]);
    });

    it("omits SomoFan via if their container is not selected", () => {
      const fanVia = aSomoFanVia({
        onUpClick: {
          "irrelevant-id": aFanControlSettings({
            deviceId: "deviceId",
          }),
        },
      });
      const nodes = [
        aContainerNode(yDoc, "light", { id: "deviceId" }),
        aContainerNode(
          yDoc,
          "somoFanContainer",
          { id: "somoFanContainerId", selected: false },
          { via: fanVia },
        ),
      ] satisfies GraphNode[];

      const result = generateVirtualEdges(yDoc, nodes);

      expect(result).toEqual([]);
    });

    it("returns all SomoFan anchors that are connected to selected light", () => {
      const fanVia = aSomoFanVia({
        onUpClick: {
          "irrelevant-id": aFanControlSettings({
            deviceId: "deviceId",
          }),
        },
      });
      const nodes = [
        aContainerNode(yDoc, "light", { id: "deviceId", selected: true }),
        anAnchorNode("somoFanAnchor", { id: "somoFanAnchorId" }),
        aContainerNode(
          yDoc,
          "somoFanContainer",
          { id: "somoFanContainerId", parentId: "somoFanAnchorId" },
          { via: fanVia },
        ),
      ] satisfies GraphNode[];

      const result = generateVirtualEdges(yDoc, nodes);

      expect(result).toMatchObject([
        {
          id: "somoFanAnchorId-/deviceId-deviceId",
          source: "somoFanAnchorId",
          sourceHandle: "",
          target: "deviceId",
          targetHandle: "deviceId",
        },
      ]);
    });
  });

  describe("PresenceSensor", () => {
    it("returns devices that are connected if they are enabled and their container is selected", () => {
      const presenceSensorAction = aPresenceSensorAction({
        enabled: true,
        onUpClick: {
          "irrelevant-id": aDeviceControlSettings({
            deviceId: "deviceId",
          }),
        },
      });
      const nodes = [
        aContainerNode(yDoc, "light", { id: "deviceId" }),
        aContainerNode(
          yDoc,
          "presenceSensorContainer",
          { id: "presenceSensorContainerId", selected: true },
          {
            onActivate: presenceSensorAction,
            onDeactivate: presenceSensorAction,
          },
        ),
      ] satisfies GraphNode[];

      const result = generateVirtualEdges(yDoc, nodes);

      expect(result).toMatchObject([
        {
          id: "presenceSensorContainerId-onActivate/deviceId-deviceId",
          source: "presenceSensorContainerId",
          sourceHandle: "onActivate",
          target: "deviceId",
          targetHandle: "deviceId",
        },
        {
          id: "presenceSensorContainerId-onDeactivate/deviceId-deviceId",
          source: "presenceSensorContainerId",
          sourceHandle: "onDeactivate",
          target: "deviceId",
          targetHandle: "deviceId",
        },
      ]);
    });

    it("omits PresenceSensor action devices that are disabled", () => {
      const presenceSensorAction = aPresenceSensorAction({
        enabled: false,
        onUpClick: {
          "irrelevant-id": aDeviceControlSettings({
            deviceId: "deviceId",
          }),
        },
      });
      const nodes = [
        aContainerNode(yDoc, "light", { id: "deviceId" }),
        aContainerNode(
          yDoc,
          "presenceSensorContainer",
          { id: "presenceSensorContainerId", selected: true },
          { onActivate: presenceSensorAction },
        ),
      ] satisfies GraphNode[];

      const result = generateVirtualEdges(yDoc, nodes);

      expect(result).toEqual([]);
    });

    it("omits PresenceSensor via if their container is not selected", () => {
      const presenceSensorAction = aPresenceSensorAction({
        enabled: true,
        onUpClick: {
          "irrelevant-id": aDeviceControlSettings({
            deviceId: "deviceId",
          }),
        },
      });
      const nodes = [
        aContainerNode(yDoc, "light", { id: "deviceId" }),
        aContainerNode(
          yDoc,
          "presenceSensorContainer",
          { id: "presenceSensorContainerId", selected: false },
          { onActivate: presenceSensorAction },
        ),
      ] satisfies GraphNode[];

      const result = generateVirtualEdges(yDoc, nodes);

      expect(result).toEqual([]);
    });

    it("returns all PresenceSensor anchors that are connected to selected light", () => {
      const presenceSensorAction = aPresenceSensorAction({
        enabled: true,
        onUpClick: {
          "irrelevant-id": aDeviceControlSettings({
            deviceId: "deviceId",
          }),
        },
      });
      const nodes = [
        aContainerNode(yDoc, "light", { id: "deviceId", selected: true }),
        anAnchorNode("presenceSensorAnchor", { id: "presenceSensorAnchorId" }),
        aContainerNode(
          yDoc,
          "presenceSensorContainer",
          {
            id: "presenceSensorContainerId",
            parentId: "presenceSensorAnchorId",
          },
          { onActivate: presenceSensorAction },
        ),
      ] satisfies GraphNode[];

      const result = generateVirtualEdges(yDoc, nodes);

      expect(result).toMatchObject([
        {
          id: "presenceSensorAnchorId-/deviceId-deviceId",
          source: "presenceSensorAnchorId",
          sourceHandle: "",
          target: "deviceId",
          targetHandle: "deviceId",
        },
      ]);
    });
  });

  describe("DoorSensor", () => {
    it("returns devices that are connected if they are enabled and their container is selected", () => {
      const doorSensorAction = aDoorSensorAction({
        enabled: true,
        onUpClick: {
          "irrelevant-id": aDeviceControlSettings({
            deviceId: "deviceId",
          }),
        },
      });
      const nodes = [
        aContainerNode(yDoc, "light", { id: "deviceId" }),
        aContainerNode(
          yDoc,
          "doorSensorContainer",
          { id: "doorSensorContainerId", selected: true },
          {
            onOpen: doorSensorAction,
            onClose: doorSensorAction,
          },
        ),
      ] satisfies GraphNode[];

      const result = generateVirtualEdges(yDoc, nodes);

      expect(result).toMatchObject([
        {
          id: "doorSensorContainerId-onOpen/deviceId-deviceId",
          source: "doorSensorContainerId",
          sourceHandle: "onOpen",
          target: "deviceId",
          targetHandle: "deviceId",
        },
        {
          id: "doorSensorContainerId-onClose/deviceId-deviceId",
          source: "doorSensorContainerId",
          sourceHandle: "onClose",
          target: "deviceId",
          targetHandle: "deviceId",
        },
      ]);
    });

    it("omits DoorSensor action devices that are disabled", () => {
      const doorSensorAction = aDoorSensorAction({
        enabled: false,
        onUpClick: {
          "irrelevant-id": aDeviceControlSettings({
            deviceId: "deviceId",
          }),
        },
      });
      const nodes = [
        aContainerNode(yDoc, "light", { id: "deviceId" }),
        aContainerNode(
          yDoc,
          "doorSensorContainer",
          { id: "doorSensorContainerId", selected: true },
          { onOpen: doorSensorAction },
        ),
      ] satisfies GraphNode[];

      const result = generateVirtualEdges(yDoc, nodes);

      expect(result).toEqual([]);
    });

    it("omits DoorSensor via if their container is not selected", () => {
      const doorSensorAction = aDoorSensorAction({
        enabled: true,
        onUpClick: {
          "irrelevant-id": aDeviceControlSettings({
            deviceId: "deviceId",
          }),
        },
      });
      const nodes = [
        aContainerNode(yDoc, "light", { id: "deviceId" }),
        aContainerNode(
          yDoc,
          "doorSensorContainer",
          { id: "doorSensorContainerId", selected: false },
          { onOpen: doorSensorAction },
        ),
      ] satisfies GraphNode[];

      const result = generateVirtualEdges(yDoc, nodes);

      expect(result).toEqual([]);
    });

    it("returns all DoorSensor anchors that are connected to selected light", () => {
      const doorSensorAction = aDoorSensorAction({
        enabled: true,
        onUpClick: {
          "irrelevant-id": aDeviceControlSettings({
            deviceId: "deviceId",
          }),
        },
      });
      const nodes = [
        aContainerNode(yDoc, "light", { id: "deviceId", selected: true }),
        anAnchorNode("doorSensorAnchor", { id: "doorSensorAnchorId" }),
        aContainerNode(
          yDoc,
          "doorSensorContainer",
          {
            id: "doorSensorContainerId",
            parentId: "doorSensorAnchorId",
          },
          { onOpen: doorSensorAction },
        ),
      ] satisfies GraphNode[];

      const result = generateVirtualEdges(yDoc, nodes);

      expect(result).toMatchObject([
        {
          id: "doorSensorAnchorId-/deviceId-deviceId",
          source: "doorSensorAnchorId",
          sourceHandle: "",
          target: "deviceId",
          targetHandle: "deviceId",
        },
      ]);
    });
  });

  describe("ServicePad", () => {
    it("omits ServicePad action devices that are disabled", () => {
      const servicePadAction = aServicePadAction({
        enabled: false,
      });
      const nodes = [
        aContainerNode(yDoc, "light", { id: "deviceId" }),
        aContainerNode(
          yDoc,
          "servicePadContainer",
          { id: "servicePadContainerId", selected: true },
          { makeUpRoomButton: servicePadAction },
        ),
      ] satisfies GraphNode[];

      const result = generateVirtualEdges(yDoc, nodes);

      expect(result).toEqual([]);
    });

    it("omits ServicePad via if their container is not selected", () => {
      const servicePadAction = aServicePadAction({
        enabled: true,
      });
      const nodes = [
        aContainerNode(yDoc, "light", { id: "deviceId" }),
        aContainerNode(
          yDoc,
          "servicePadContainer",
          { id: "servicePadContainerId", selected: false },
          { makeUpRoomButton: servicePadAction },
        ),
      ] satisfies GraphNode[];

      const result = generateVirtualEdges(yDoc, nodes);

      expect(result).toEqual([]);
    });
  });
});
