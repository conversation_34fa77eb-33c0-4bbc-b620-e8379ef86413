import { useGetData } from "@/components/data/useData";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { DialogHeader, DialogTitle } from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import {
  RfController,
  RfTables,
  RfTestCommands,
  RoomSwitchTable,
} from "@/lib/RfController";
import { cn } from "@/lib/classNames";
import { zodResolver } from "@hookform/resolvers/zod";
import { LayoutContainerNode, filterNodes } from "@somo/shared";
import { Allotment } from "allotment";
import { useEffect, useRef, useState } from "react";
import { ControllerRenderProps, useForm } from "react-hook-form";
import { toast } from "sonner";
import "web-serial-polyfill";
import { SerialPort } from "web-serial-polyfill";
import { z } from "zod";
import { Graph } from "../Graph";
import { DimmerQRCodeMapping } from "./SerialMonitorQRCodeMapping";

const RfNetwork = z
  .object({
    channel: z.number(),
    network: z.number(),
  })
  .prefault({
    channel: 15,
    network: 15,
  });
type RfNetwork = z.infer<typeof RfNetwork>;

const RoomSwitches = z
  .object({
    roomSwitchIdToQRCodeMapping: z
      .array(
        z.object({
          id: z.string(),
          qrCode: z.string(),
          nodeId: z.number().prefault(0),
          via1On: z.boolean().prefault(false),
          via2On: z.boolean().prefault(false),
          via3On: z.boolean().prefault(false),
        }),
      )
      .prefault([]),
    roomDimmerIdToQRCodeMapping: z
      .array(
        z.object({
          id: z.string(),
          qrCode: z.string(),
          nodeId: z.number().prefault(0),
        }),
      )
      .prefault([]),
    outletDimmerIdToQRCodeMapping: z
      .array(
        z.object({
          id: z.string(),
          qrCode: z.string(),
          nodeId: z.number().prefault(0),
        }),
      )
      .prefault([]),
    canBusControllerIdToQRCodeMapping: z
      .array(
        z.object({
          id: z.string(),
          qrCode: z.string(),
          nodeId: z.number().prefault(0),
        }),
      )
      .prefault([]),
  })
  .prefault({
    roomSwitchIdToQRCodeMapping: [],
    roomDimmerIdToQRCodeMapping: [],
    outletDimmerIdToQRCodeMapping: [],
    canBusControllerIdToQRCodeMapping: [],
  });
export type RoomSwitches = z.infer<typeof RoomSwitches>;

export function SerialMonitor({
  port,
  graph,
  roomId,
}: {
  port: SerialPort;
  graph: Graph;
  roomId: string;
}) {
  const [rfController, setRfController] = useState<RfController | null>(null);
  const [controllerConnected, setControllerConnected] = useState(false);
  const [dataLog, setDataLog] = useState<string[]>([]);
  const [initialized, setInitialized] = useState(false);

  const roomSwitchNodes = filterNodes(graph.nodes, "roomSwitchContainer");
  const roomDimmerNodes = filterNodes(graph.nodes, "roomDimmerContainer");
  const outletDimmerNodes = filterNodes(graph.nodes, "outletDimmerContainer");
  const canBusControllerNodes = filterNodes(
    graph.nodes,
    "canbusControllerContainer",
  );

  const networkForm = useForm<RfNetwork, unknown, RfNetwork>({
    // @ts-expect-error - https://github.com/react-hook-form/resolvers/issues/792
    resolver: zodResolver(RfNetwork),
    defaultValues: localStorage.getItem(`${roomId}|rfNetwork`)
      ? JSON.parse(localStorage.getItem(`${roomId}|rfNetwork`) ?? "{}")
      : {
          channel: 15,
          network: 15,
        },
  });

  const switchesForm = useForm<RoomSwitches, unknown, RoomSwitches>({
    // @ts-expect-error - https://github.com/react-hook-form/resolvers/issues/792
    resolver: zodResolver(RoomSwitches),
    defaultValues: localStorage.getItem(`${roomId}|roomSwitches`)
      ? JSON.parse(localStorage.getItem(`${roomId}|roomSwitches`) ?? "{}")
      : {
          roomSwitchIdToQRCodeMapping: roomSwitchNodes.map((node) => ({
            id: node.id,
            qrCode: "",
            nodeId: 0,
            via1On: false,
            via2On: false,
            via3On: false,
          })),
          roomDimmerIdToQRCodeMapping: roomDimmerNodes.map((node) => ({
            id: node.id,
            qrCode: "",
            nodeId: 0,
          })),
          outletDimmerIdToQRCodeMapping: outletDimmerNodes.map((node) => ({
            id: node.id,
            qrCode: "",
            nodeId: 0,
          })),
          canBusControllerIdToQRCodeMapping: canBusControllerNodes.map(
            (node) => ({
              id: node.id,
              qrCode: "",
              nodeId: 0,
            }),
          ),
        },
  });

  const [networkInfo, setNetworkInfo] = useState<{
    channel: number;
    network: number;
  }>(networkForm.getValues());

  const [, setSwitchInfo] = useState<RoomSwitches>(switchesForm.getValues());

  useEffect(() => {
    if (!initialized) {
      return;
    }

    if (!rfController) {
      return;
    }

    rfController.changeChannel(networkInfo.channel, networkInfo.network);
  }, [networkInfo, initialized]);

  useEffect(() => {
    const rfController = new RfController(
      port,
      false,
      networkInfo.channel,
      networkInfo.network,
    );
    setRfController(rfController);

    rfController.on("connected", async () => {
      setControllerConnected(true);
    });

    rfController.on("serialData", (data: string) => {
      setDataLog((prev) => [...prev, data]);
    });

    rfController.on("error", (error: Error) => {
      setDataLog((prev) => [...prev, error.message]);
    });

    rfController.on("initialized", async () => {
      setInitialized(true);
    });

    rfController.on(
      "rfIncoming",
      (rfIncoming: { data: number[]; rssi: number }) => {
        console.log(rfIncoming);
        const destinationNode = rfIncoming.data[1];
        const sourcenNode = rfIncoming.data[2];
        const table = rfIncoming.data[3];
        const command = rfIncoming.data[4];

        if (destinationNode !== 1) {
          return;
        }

        if (table === RfTables.TABLE_ROOM_SWITCH) {
          const currentMapping = switchesForm.getValues(
            "roomSwitchIdToQRCodeMapping",
          );
          const foundNode = currentMapping.find(
            (item) => item.nodeId === sourcenNode,
          );

          if (foundNode) {
            switch (command) {
              case RoomSwitchTable.VIA1_ON:
                foundNode.via1On = true;
                break;
              case RoomSwitchTable.VIA1_OFF:
                foundNode.via1On = false;
                break;
              case RoomSwitchTable.VIA2_ON:
                foundNode.via2On = true;
                break;
              case RoomSwitchTable.VIA2_OFF:
                foundNode.via2On = false;
                break;
              case RoomSwitchTable.VIA3_ON:
                foundNode.via3On = true;
                break;
              case RoomSwitchTable.VIA3_OFF:
                foundNode.via3On = false;
                break;
            }

            switchesForm.setValue(
              "roomSwitchIdToQRCodeMapping",
              currentMapping,
            );
            setSwitchInfo(switchesForm.getValues());
            localStorage.setItem(
              `${roomId}|roomSwitches`,
              JSON.stringify(switchesForm.getValues()),
            );
          }
        }
      },
    );

    return () => {
      rfController.close();
      setRfController(null);
      setControllerConnected(false);
      setInitialized(false);
    };
  }, [port]);

  const scollRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (scollRef.current) {
      scollRef.current.scrollTop = scollRef.current.scrollHeight;
    }
  }, [dataLog]);

  const onSwitchesChange = async (values: RoomSwitches) => {
    if (!rfController) {
      return;
    }
    setSwitchInfo(values);
    localStorage.setItem(`${roomId}|roomSwitches`, JSON.stringify(values));
    toast("Switches saved");
  };

  const onNetworkChange = async (values: RfNetwork) => {
    if (!rfController) {
      return;
    }

    setNetworkInfo(values);
    localStorage.setItem(`${roomId}|rfNetwork`, JSON.stringify(values));

    toast("Network saved");
  };

  return (
    <>
      <DialogHeader>
        <DialogTitle>
          Serial Monitor - {controllerConnected ? "Connected" : "Connecting..."}
        </DialogTitle>
      </DialogHeader>
      <div className="flex flex-grow flex-col gap-4 border-t border-gray-200 -mx-6 -mb-6 rounded-b-md relative">
        <div className={cn("absolute top-0 left-0 w-full h-full bg-gray-100")}>
          <Allotment>
            <Allotment.Pane
              maxSize={1000}
              minSize={200}
              className="absolute inset-0 flex flex-row py-2 pr-1 pl-2"
              preferredSize={"50%"}
            >
              <div
                className="overflow-y-auto h-full p-2 select-text w-full"
                ref={scollRef}
              >
                {dataLog.map((data, index) => (
                  <div key={index} className="text-xs font-mono select-text">
                    {data}
                  </div>
                ))}
              </div>
            </Allotment.Pane>
            <Allotment.Pane
              minSize={200}
              className="absolute inset-0 flex flex-row py-2 pr-1 pl-2"
            >
              <div className="overflow-y-auto h-full p-2 w-full flex flex-col gap-10">
                <Form {...networkForm}>
                  <form onSubmit={networkForm.handleSubmit(onNetworkChange)}>
                    <div className="flex flex-col gap-4">
                      <Card className="rounded-md">
                        <CardHeader className="px-3 pt-2 pb-0 font-medium text-sm text-gray-500">
                          RF Network
                        </CardHeader>
                        <CardContent className="space-y-2 p-3">
                          <div className="flex flex-row items-center gap-8">
                            <FormField
                              control={networkForm.control}
                              name="channel"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Channel</FormLabel>
                                  <FormControl>
                                    <Input
                                      placeholder="Channel"
                                      {...field}
                                      type="number"
                                      min={1}
                                      max={30}
                                      onChange={(e) => {
                                        field.onChange(
                                          Number.parseInt(e.target.value),
                                        );
                                      }}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={networkForm.control}
                              name="network"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Network</FormLabel>
                                  <FormControl>
                                    <Input
                                      placeholder="Network"
                                      {...field}
                                      type="number"
                                      min={1}
                                      onChange={(e) => {
                                        field.onChange(
                                          Number.parseInt(e.target.value),
                                        );
                                      }}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                    <div className="flex justify-center mt-6">
                      <Button type="submit">Change Network</Button>
                    </div>
                  </form>
                </Form>

                <Form {...switchesForm}>
                  <form onSubmit={switchesForm.handleSubmit(onSwitchesChange)}>
                    <div className="flex flex-col gap-4">
                      {roomSwitchNodes.length > 0 && (
                        <Card className="rounded-md">
                          <CardHeader className="px-3 pt-2 pb-0 font-medium text-sm text-gray-500">
                            Room Switches
                          </CardHeader>
                          <CardContent className="space-y-2 p-3">
                            <FormField
                              control={switchesForm.control}
                              name={"roomSwitchIdToQRCodeMapping"}
                              render={({ field }) => {
                                return (
                                  <div className="flex flex-col gap-2">
                                    {roomSwitchNodes.map((node) => (
                                      <RoomSwitchFormItem
                                        key={node.id}
                                        node={node}
                                        field={field}
                                        rfController={rfController}
                                        networkInfo={networkInfo}
                                        setDataLog={setDataLog}
                                      />
                                    ))}
                                  </div>
                                );
                              }}
                            />
                          </CardContent>
                        </Card>
                      )}

                      {roomDimmerNodes.length > 0 && (
                        <DimmerQRCodeMapping
                          title="Room Dimmers"
                          nodes={roomDimmerNodes}
                          control={switchesForm.control}
                          fieldName="roomDimmerIdToQRCodeMapping"
                        />
                      )}

                      {outletDimmerNodes.length > 0 && (
                        <DimmerQRCodeMapping
                          title="Outlet Dimmers"
                          nodes={outletDimmerNodes}
                          control={switchesForm.control}
                          fieldName="outletDimmerIdToQRCodeMapping"
                        />
                      )}
                    </div>
                    <div className="flex justify-center mt-6">
                      <Button type="submit">Save Devices</Button>
                    </div>
                  </form>
                </Form>
              </div>
            </Allotment.Pane>
          </Allotment>
        </div>
      </div>
    </>
  );
}

function RoomSwitchFormItem({
  node,
  field,
  rfController,
  networkInfo,
  setDataLog,
}: {
  node: LayoutContainerNode;
  field: ControllerRenderProps<RoomSwitches, "roomSwitchIdToQRCodeMapping">;
  rfController: RfController | null;
  networkInfo: { channel: number; network: number };
  setDataLog: React.Dispatch<React.SetStateAction<string[]>>;
}) {
  const data = useGetData(node.data.dataId, "roomSwitch");
  if (!data) {
    return <></>;
  }

  return (
    <FormItem>
      <FormLabel className="flex flex-row items-center">
        {data.title}{" "}
        <span className="font-mono text-xs text-gray-500 ml-2">
          ({node.id})
        </span>
      </FormLabel>
      <FormControl>
        <div className="flex flex-col gap-4">
          <div className="flex flex-row items-center gap-2">
            <Input
              placeholder="QR Code"
              className="flex-grow"
              value={
                field.value.find((item) => item.id === node.id)?.qrCode ?? ""
              }
              onChange={(e) => {
                // new values
                const newValues = field.value.map((item) => {
                  if (item.id === node.id) {
                    return {
                      ...item,
                      qrCode: e.target.value,
                    };
                  }
                  return item;
                });
                field.onChange(newValues);
              }}
              onBlur={() => {
                field.onBlur();
              }}
            />

            <Input
              placeholder="NodeId"
              className="flex-1 flex-shrink-0 min-w-14"
              value={
                field.value.find((item) => item.id === node.id)?.nodeId ?? "0"
              }
              onChange={(e) => {
                let nodeId = Number.parseInt(e.target.value);
                if (isNaN(nodeId)) {
                  nodeId = 0;
                }
                // new values
                const newValues = field.value.map((item) => {
                  if (item.id === node.id) {
                    return {
                      ...item,
                      nodeId,
                    };
                  }
                  return item;
                });
                field.onChange(newValues);
              }}
              onBlur={() => {
                field.onBlur();
              }}
            />
            <Button
              variant="outline"
              size="default"
              className="flex-shrink-0"
              onClick={async (e) => {
                e.preventDefault();
                if (!rfController) {
                  return;
                }
                const qrCode =
                  field.value.find((item) => item.id === node.id)?.qrCode ?? "";
                const nodeId =
                  field.value.find((item) => item.id === node.id)?.nodeId ?? -1;

                await rfController.adoptDevice(
                  qrCode,
                  nodeId,
                  networkInfo.channel,
                  networkInfo.network,
                );
              }}
            >
              Provision
            </Button>
            <Button
              variant="outline"
              size="default"
              className="flex-shrink-0"
              onClick={async (e) => {
                e.preventDefault();
                if (!rfController) {
                  return;
                }
                const qrCode =
                  field.value.find((item) => item.id === node.id)?.qrCode ?? "";
                const nodeId =
                  field.value.find((item) => item.id === node.id)?.nodeId ?? -1;
                await rfController.removeDevice(
                  qrCode,
                  nodeId,
                  networkInfo.channel,
                  networkInfo.network,
                );
              }}
            >
              Remove
            </Button>
            <Button
              variant="outline"
              size="default"
              className="flex-shrink-0"
              onClick={(e) => {
                e.preventDefault();
                const nodeId =
                  field.value.find((item) => item.id === node.id)?.nodeId ?? -1;

                rfController
                  ?.sendRfCommand(
                    nodeId,
                    1,
                    RfTables.TABLE_RF_TEST,
                    RfTestCommands.RF_TEST_STATUS,
                  )
                  .then((response) => {
                    const data = response?.data;
                    if (!data) {
                      return;
                    }
                    const lastFourBytes = data.slice(-4);
                    const via1On = lastFourBytes[0] === 1;
                    const via2On = lastFourBytes[1] === 1;
                    const via3On = lastFourBytes[2] === 1;

                    setDataLog((dataLog) => [
                      ...dataLog,
                      `[Sync Duration] ${response?.duration ?? 0}`,
                    ]);

                    field.onChange(
                      field.value.map((item) => {
                        if (item.nodeId === nodeId) {
                          return {
                            ...item,
                            via1On,
                            via2On,
                            via3On,
                          };
                        }
                        return item;
                      }),
                    );
                  });
              }}
            >
              Sync Vias
            </Button>
          </div>
          <div className="flex flex-row items-center gap-2">
            <div className="text-sm">Via 1</div>
            <Switch
              checked={field.value.find((item) => item.id === node.id)?.via1On}
              onCheckedChange={(checked) => {
                // new values
                const newValues = field.value.map((item) => {
                  if (item.id === node.id) {
                    return {
                      ...item,
                      via1On: checked,
                    };
                  }
                  return item;
                });
                field.onChange(newValues);

                const nodeId =
                  field.value.find((item) => item.id === node.id)?.nodeId ?? -1;

                rfController
                  ?.sendRfCommand(
                    nodeId,
                    1,
                    RfTables.TABLE_ROOM_SWITCH,
                    checked
                      ? RoomSwitchTable.VIA1_ON
                      : RoomSwitchTable.VIA1_OFF,
                  )
                  .then((response) => {
                    setDataLog((data) => [
                      ...data,
                      `[Last Command duration]: ${response?.duration ?? 0}`,
                    ]);
                  });
              }}
            />
            <div className="text-sm pl-4">Via 2</div>
            <Switch
              checked={field.value.find((item) => item.id === node.id)?.via2On}
              onCheckedChange={(checked) => {
                // new values
                const newValues = field.value.map((item) => {
                  if (item.id === node.id) {
                    return {
                      ...item,
                      via2On: checked,
                    };
                  }
                  return item;
                });
                field.onChange(newValues);

                const nodeId =
                  field.value.find((item) => item.id === node.id)?.nodeId ?? -1;

                rfController
                  ?.sendRfCommand(
                    nodeId,
                    1,
                    RfTables.TABLE_ROOM_SWITCH,
                    checked
                      ? RoomSwitchTable.VIA2_ON
                      : RoomSwitchTable.VIA2_OFF,
                  )
                  .then((response) => {
                    setDataLog((data) => [
                      ...data,
                      `[Last Command duration]: ${response?.duration ?? 0}`,
                    ]);
                  });
              }}
            />
            <div className="text-sm pl-4">Via 3</div>
            <Switch
              checked={field.value.find((item) => item.id === node.id)?.via3On}
              onCheckedChange={(checked) => {
                // new values
                const newValues = field.value.map((item) => {
                  if (item.id === node.id) {
                    return {
                      ...item,
                      via3On: checked,
                    };
                  }
                  return item;
                });
                field.onChange(newValues);

                const nodeId =
                  field.value.find((item) => item.id === node.id)?.nodeId ?? -1;

                rfController
                  ?.sendRfCommand(
                    nodeId,
                    1,
                    RfTables.TABLE_ROOM_SWITCH,
                    checked
                      ? RoomSwitchTable.VIA3_ON
                      : RoomSwitchTable.VIA3_OFF,
                  )
                  .then((response) => {
                    setDataLog((data) => [
                      ...data,
                      `[Last Command duration]: ${response?.duration ?? 0}`,
                    ]);
                  });
              }}
            />
          </div>
        </div>
      </FormControl>
    </FormItem>
  );
}
