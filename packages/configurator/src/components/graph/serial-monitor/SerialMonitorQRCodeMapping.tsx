import { useGetData } from "@/components/data/useData";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { GraphNodesByType, LayoutContainerNode } from "@somo/shared";
import React from "react";
import { Control, ControllerRenderProps } from "react-hook-form";
import { RoomSwitches } from "./SerialMonitor";

interface DimmerQRCodeMappingProps {
  title: string;
  nodes: (LayoutContainerNode | GraphNodesByType["outletDimmerContainer"])[];
  control: Control<RoomSwitches>;
  fieldName: "outletDimmerIdToQRCodeMapping" | "roomDimmerIdToQRCodeMapping";
}

export const DimmerQRCodeMapping: React.FC<DimmerQRCodeMappingProps> = ({
  title,
  nodes,
  control,
  fieldName,
}) => {
  if (nodes.length === 0) {
    return <></>;
  }

  return (
    <Card className="rounded-md">
      <CardHeader className="px-3 pt-2 pb-0 font-medium text-sm text-gray-500">
        {title}
      </CardHeader>
      <CardContent className="space-y-2 p-3">
        <FormField
          control={control}
          name={fieldName}
          render={({ field }) => {
            const items = Array.isArray(field.value) ? field.value : [];
            return (
              <div className="flex flex-col gap-2">
                {nodes.map((node) => (
                  <NodeFormItem
                    key={node.id}
                    node={node}
                    field={field}
                    items={items}
                  />
                ))}
              </div>
            );
          }}
        />
      </CardContent>
    </Card>
  );
};

function NodeFormItem({
  node,
  field,
  items,
}: {
  node: LayoutContainerNode | GraphNodesByType["outletDimmerContainer"];
  field: ControllerRenderProps<
    RoomSwitches,
    "roomDimmerIdToQRCodeMapping" | "outletDimmerIdToQRCodeMapping"
  >;
  items: { id: string; qrCode: string; nodeId: number }[];
}) {
  // TODO: get rid of the ternary once outletDimmerContainer has dataId
  const data =
    "dataId" in node.data
      ? useGetData(node.data.dataId, "roomDimmer")
      : node.data;
  if (!data) {
    return <></>;
  }

  return (
    <FormItem>
      <FormLabel className="flex flex-row items-center">
        {data.title}{" "}
        <span className="font-mono text-xs text-gray-500 ml-2">
          ({node.id})
        </span>
      </FormLabel>
      <FormControl>
        <Input
          placeholder="QR Code"
          value={items.find((item) => item.id === node.id)?.qrCode ?? ""}
          onChange={(e) => {
            const idx = items.findIndex((item) => item.id === node.id);
            const newValues =
              idx >= 0
                ? items.map((item) =>
                    item.id === node.id
                      ? { ...item, qrCode: e.target.value }
                      : item,
                  )
                : [
                    ...items,
                    {
                      id: node.id,
                      qrCode: e.target.value,
                      nodeId: 0,
                    },
                  ];
            field.onChange(newValues);
          }}
          onBlur={field.onBlur}
        />
      </FormControl>
    </FormItem>
  );
}
