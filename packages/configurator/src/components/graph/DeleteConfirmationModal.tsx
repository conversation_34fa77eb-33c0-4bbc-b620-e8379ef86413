import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { AlertTriangle } from "lucide-react";
import { useEffect, useRef } from "react";

interface DeleteConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  mirroredNodeIds: string[];
  sldNodeIds: string[];
}

export function DeleteConfirmationModal({
  isOpen,
  onClose,
  onConfirm,
  mirroredNodeIds,
  sldNodeIds,
}: DeleteConfirmationModalProps) {
  const deleteButtonRef = useRef<HTMLButtonElement>(null);

  // Focus the delete button when modal opens
  useEffect(() => {
    if (isOpen && deleteButtonRef.current) {
      // Small delay to ensure the modal is fully rendered
      const timeoutId = setTimeout(() => {
        deleteButtonRef.current?.focus();
      }, 100);

      // Cleanup function to clear timeout if component unmounts
      return () => clearTimeout(timeoutId);
    }
    return undefined;
  }, [isOpen]);

  // Handle keyboard events
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === "Enter") {
      event.preventDefault();
      onConfirm();
      onClose();
    } else if (event.key === "Escape") {
      event.preventDefault();
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md" onKeyDown={handleKeyDown}>
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-yellow-500" />
            Delete Mirrored Node
          </DialogTitle>
          <DialogDescription>
            You are trying to delete a node that is mirrored from the main
            graph. This will also remove the corresponding node from the main
            graph.
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          <p className="text-sm text-gray-600 mb-2">
            The following main graph node(s) will be deleted:
          </p>
          <div className="bg-gray-50 p-3 rounded-md">
            {mirroredNodeIds.map((nodeId) => (
              <div key={nodeId} className="text-sm font-mono text-gray-800">
                {nodeId}
              </div>
            ))}
          </div>
        </div>

        <DialogFooter className="flex gap-2">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            ref={deleteButtonRef}
            variant="destructive"
            onClick={() => {
              onConfirm();
              onClose();
            }}
          >
            Delete
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
