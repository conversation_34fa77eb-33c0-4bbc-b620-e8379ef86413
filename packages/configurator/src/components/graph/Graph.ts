import { GraphNode } from "@somo/shared";
import { Edge } from "@xyflow/react";
import * as Y from "yjs";
import { z } from "zod";

export type Graph = {
  nodes: GraphNode[];
  edges: Edge[];
};

const YMap = z.instanceof(Y.Map);
const YMapChild = YMap.or(z.undefined());

function getGraph(yDoc: Y.Doc) {
  return YMap.parse(yDoc.getMap("graph"));
}

export function getNodes(yDoc: Y.Doc) {
  return YMapChild.parse(getGraph(yDoc).get("nodes"));
}

export function getNode(yDoc: Y.Doc, nodeId: string) {
  return YMapChild.parse(getNodes(yDoc)?.get(nodeId));
}

export function getMainGraphNodeData(yDoc: Y.Doc, nodeId: string) {
  return YMapChild.parse(getNode(yDoc, nodeId)?.get("data"));
}

// SLD-specific functions

function getSldGraph(yDoc: Y.Doc) {
  return YMap.parse(yDoc.getMap("SldGraph"));
}

export function getSldNodes(yDoc: Y.Doc) {
  return YMapChild.parse(getSldGraph(yDoc).get("nodes"));
}

export function getSldNode(yDoc: Y.Doc, nodeId: string) {
  return YMapChild.parse(getSldNodes(yDoc)?.get(nodeId));
}

export function getSldNodeData(yDoc: Y.Doc, nodeId: string) {
  return YMapChild.parse(getSldNode(yDoc, nodeId)?.get("data"));
}

export function getSldEdges(yDoc: Y.Doc) {
  return YMapChild.parse(getSldGraph(yDoc).get("edges"));
}

export function getSldEdge(yDoc: Y.Doc, edgeId: string) {
  return YMapChild.parse(getSldEdges(yDoc)?.get(edgeId));
}

// SLD YDoc Helper Functions - For eliminating duplication in SldEditor

/**
 * Get SLD-specific nodes map from YDoc (live Y.Map for modifications)
 * @param yDoc YDoc instance
 * @returns SLD-specific nodes Y.Map or null if not found
 */
export function getSldSpecificNodes(yDoc: Y.Doc): Y.Map<any> | null {
  return yDoc.getMap("SldGraph") as Y.Map<any>;
}

/**
 * Get SLD nodes map from YDoc (live Y.Map for modifications)
 * @param yDoc YDoc instance
 * @returns SLD nodes Y.Map or null if not found
 */
export function getSldNodesMap(yDoc: Y.Doc): Y.Map<any> | null {
  const sldSpecificNodes = getSldSpecificNodes(yDoc);
  if (!sldSpecificNodes) {
    return null;
  }
  return (sldSpecificNodes.get("nodes") as Y.Map<any>) || null;
}

/**
 * Get SLD edges map from YDoc (live Y.Map for modifications)
 * @param yDoc YDoc instance
 * @returns SLD edges Y.Map or null if not found
 */
export function getSldEdgesMap(yDoc: Y.Doc): Y.Map<any> | null {
  const sldSpecificNodes = getSldSpecificNodes(yDoc);
  if (!sldSpecificNodes) {
    return null;
  }
  return (sldSpecificNodes.get("edges") as Y.Map<any>) || null;
}

/**
 * Validate SLD YDoc structure exists and return validation result
 * @param yDoc YDoc instance
 * @returns Object with validation result and maps if valid
 */
export function validateSldYDocStructure(yDoc: Y.Doc): {
  isValid: boolean;
  sldSpecificNodes?: Y.Map<any>;
  sldNodesMap?: Y.Map<any>;
  sldEdgesMap?: Y.Map<any>;
} {
  const sldSpecificNodes = getSldSpecificNodes(yDoc);
  if (!sldSpecificNodes) {
    return { isValid: false };
  }

  const sldNodesMap = getSldNodesMap(yDoc);
  const sldEdgesMap = getSldEdgesMap(yDoc);

  return {
    isValid: true,
    sldSpecificNodes,
    sldNodesMap: sldNodesMap || undefined,
    sldEdgesMap: sldEdgesMap || undefined,
  };
}

/**
 * Ensure SLD structure exists in YDoc and return maps
 * Creates missing structure if needed
 * @param yDoc YDoc instance
 * @returns Object with created/existing maps
 */
export function ensureSldYDocStructure(yDoc: Y.Doc): {
  sldSpecificNodes: Y.Map<any>;
  sldNodesMap: Y.Map<any>;
  sldEdgesMap: Y.Map<any>;
} {
  const sldSpecificNodes = yDoc.getMap("SldGraph") as Y.Map<any>;

  let sldNodesMap = sldSpecificNodes.get("nodes") as Y.Map<any>;
  if (!sldNodesMap) {
    sldNodesMap = new Y.Map();
    sldSpecificNodes.set("nodes", sldNodesMap);
  }

  let sldEdgesMap = sldSpecificNodes.get("edges") as Y.Map<any>;
  if (!sldEdgesMap) {
    sldEdgesMap = new Y.Map();
    sldSpecificNodes.set("edges", sldEdgesMap);
  }

  return {
    sldSpecificNodes,
    sldNodesMap,
    sldEdgesMap,
  };
}

/**
 * Generate unique edge ID with timestamp and random suffix
 * @param source Source node ID
 * @param sourceHandle Source handle ID
 * @param target Target node ID
 * @param targetHandle Target handle ID
 * @returns Unique edge ID
 */
export function generateUniqueEdgeId(
  source: string,
  sourceHandle: string,
  target: string,
  targetHandle: string,
): string {
  const timestamp = Date.now();
  const randomSuffix = Math.random().toString(36).substring(2, 9);
  return `sld-edge-${source}-${sourceHandle}-${target}-${targetHandle}-${timestamp}-${randomSuffix}`;
}

/**
 * Check if edge ID collision exists and regenerate if needed
 * @param baseId Base edge ID
 * @param sldEdgesMap SLD edges map to check against
 * @returns Collision-free edge ID
 */
export function ensureUniqueEdgeId(
  baseId: string,
  sldEdgesMap: Y.Map<any>,
): string {
  if (!sldEdgesMap.has(baseId)) {
    return baseId;
  }

  // Generate new ID with fresh timestamp if collision
  const timestamp = Date.now();
  const randomSuffix = Math.random().toString(36).substring(2, 9);
  return ensureUniqueEdgeId(
    `${baseId}-collision-${timestamp}-${randomSuffix}`,
    sldEdgesMap,
  );
}

// Unified function that detects context and uses appropriate accessor
export function getNodeData(
  yDoc: Y.Doc,
  nodeId: string,
  context: "main" | "sld" | "auto" = "auto",
) {
  if (context === "main") {
    return getMainGraphNodeData(yDoc, nodeId);
  } else if (context === "sld") {
    return getSldNodeData(yDoc, nodeId);
  } else {
    // Auto-detect context
    if (!yDoc) {
      return undefined;
    }

    try {
      const sldNodes = getSldNodes(yDoc);
      if (sldNodes && sldNodes.has(nodeId)) {
        return getSldNodeData(yDoc, nodeId);
      }
      // If not found in SLD, try main graph
      return getMainGraphNodeData(yDoc, nodeId);
    } catch {
      // Fallback to main graph
      return getMainGraphNodeData(yDoc, nodeId);
    }
  }
}
