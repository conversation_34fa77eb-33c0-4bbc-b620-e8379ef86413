import { addAction } from "@/components/devices/addAction";
import { pick } from "@/lib/object";
import { objectToYMap } from "@/lib/yjsUtils";
import {
  CommentNodeData,
  DoorSensorViaId,
  GraphNode,
  LayoutAnchorNodeData,
  LayoutContainerNodeData,
  MomentaryCanBusControllerViaId,
  PirSensorViaId,
  PresenceSensorViaId,
  randomId,
  reactFlowKeysToPersist,
  ReedSwitchSensorViaId,
  RoomDimmerAction,
  RoomDimmerViaId,
  RoomSwitchViaId,
  SceneViaIds,
  SectionNodeData,
  SomoFanViaId,
  SomoShadesViaId,
  SomoThermostatViaId,
  ToggleCanBusControllerViaId,
  VirtualButtonViaId,
} from "@somo/shared";
import {
  applyEdgeChanges,
  applyNodeChanges,
  Connection,
  Edge,
  EdgeChange,
  NodeChange,
} from "@xyflow/react";
import deepEqual from "fast-deep-equal/react";
import { match } from "ts-pattern";
import * as Y from "yjs";
import { z } from "zod";
import { deleteData, getData, updateNestedData } from "../data/useData";
import {
  actionAlreadyExistsForDevice,
  getDeviceDimmingSpeed,
  getHoldDimmingSpeed,
  isDeviceConnectionAllowed,
} from "../devices/deviceUtils";
import { generateVirtualEdges } from "./generateVirtualEdges";
import { getNode, getNodeData, getNodes, Graph } from "./Graph";
import { addInput } from "./scene/sceneOperations";

export function handleConnect(
  connection: Connection,
  { nodes, edges }: Graph,
  yDoc: Y.Doc,
) {
  const { sourceHandle, targetHandle } = connection;
  if (!sourceHandle) {
    return;
  }
  if (!targetHandle) {
    return;
  }

  // dont add an edge if there's already one
  const existingEdge = edges.find(
    (edge) =>
      edge.source === connection.source &&
      edge.sourceHandle === sourceHandle &&
      edge.target === connection.target &&
      edge.targetHandle === targetHandle,
  );
  if (existingEdge) {
    return;
  }

  const sourceNode = nodes.find((node) => node.id === connection.source);
  if (!sourceNode) {
    return;
  }

  const targetNode = nodes.find((node) => node.id === connection.target);

  if (targetNode?.type === "scene") {
    addInput({
      yDoc,
      sceneId: targetNode.id,
      input: { source: sourceNode.id, sourceHandle },
    });
    return;
  }

  if (targetNode?.type !== "section") {
    return;
  }

  const targetSectionDevice = targetNode.data.devices[targetHandle];
  if (!targetSectionDevice) {
    console.warn("targetSectionDevice not found");
    return;
  }

  if (!isDeviceConnectionAllowed(sourceNode.type, targetSectionDevice.type)) {
    console.warn(
      `Connection not allowed between ${sourceNode.type} and ${targetSectionDevice.type}`,
    );
    return;
  }

  const sectionDevices = Object.values(targetNode.data.devices);
  const defaultDimSpeedOnClick = getDeviceDimmingSpeed(
    targetSectionDevice.id,
    sectionDevices,
    nodes,
    0.2,
  );
  const defaultDimSpeedOnHold = getHoldDimmingSpeed();

  match(sourceNode)
    .with({ type: "virtualButtonContainer" }, (sourceNode) => {
      const viaIdResult = VirtualButtonViaId.safeParse(sourceHandle);
      if (!viaIdResult.success) {
        return;
      }
      const viaId = viaIdResult.data;

      // Get the Y.Map for the via
      const dataMap = getNodeData(yDoc, sourceNode.id);
      const viaMap = dataMap?.get(viaId) as Y.Map<any> | undefined;
      if (!viaMap) {
        console.warn(`Via map not found for ${viaId}`);
        return;
      }

      if (actionAlreadyExistsForDevice(viaMap, targetSectionDevice.id)) {
        console.warn(`Action for ${targetSectionDevice.id} already exists`);
        return;
      }

      addAction(viaMap, {
        device: "virtualButton",
        key: "onUpClick",
        params: {
          deviceId: targetSectionDevice.id,
          dimSpeed: defaultDimSpeedOnClick,
          targetValue: 100,
          onValue: 100,
          offValue: 0,
          delay: 0,
          type: "lighting",
        },
      });
    })
    .with({ type: "canbusControllerContainer" }, (sourceNode) => {
      const data = getData(yDoc, sourceNode.data.dataId, "canbusController");
      if (!data) {
        return;
      }

      // Check if this is a reed switch sensor handle
      const reedSwitchSensor = Object.values(data.reedSwitchSensors || {}).find(
        (sensor) => sensor.id === sourceHandle,
      );
      if (reedSwitchSensor) {
        if (
          ReedSwitchSensorViaId.options.some((actionType) =>
            Object.values(reedSwitchSensor[actionType] || {}).some(
              (action) => action.deviceId === targetSectionDevice.id,
            ),
          )
        ) {
          console.warn(`Action for ${targetSectionDevice.id} already exists`);
          return;
        }

        ReedSwitchSensorViaId.options.forEach((actionType) => {
          const { targetValue, onValue, offValue } = match(actionType)
            .with("onOpen", () => ({
              targetValue: 100,
              onValue: 100,
              offValue: 0,
            }))
            .with("onClose", () => ({
              targetValue: 0,
              onValue: 100,
              offValue: 0,
            }))
            .exhaustive();

          const newActionId = randomId();
          updateNestedData(
            yDoc,
            sourceNode.data.dataId,
            "canbusController",
            `reedSwitchSensors.${sourceHandle}.${actionType}.${newActionId}`,
            {
              id: newActionId,
              sortIndex: Object.values(
                data?.reedSwitchSensors?.[sourceHandle]?.[actionType] ?? {},
              ).length,
              deviceId: targetSectionDevice.id,
              targetValue,
              onValue,
              offValue,
              delay: 0,
              type: "lighting",
              dimSpeed: match(actionType)
                .with("onOpen", () => defaultDimSpeedOnClick)
                .with("onClose", () => defaultDimSpeedOnHold)
                .exhaustive(),
            },
          );
        });
        return;
      }

      // Check if this is a PIR sensor handle
      const pirSensor = Object.values(data.pirSensors || {}).find(
        (sensor) => sensor.id === sourceHandle,
      );
      if (pirSensor) {
        if (
          PirSensorViaId.options.some((actionType) =>
            Object.values(pirSensor[actionType] || {}).some(
              (action) => action.deviceId === targetSectionDevice.id,
            ),
          )
        ) {
          console.warn(`Action for ${targetSectionDevice.id} already exists`);
          return;
        }

        PirSensorViaId.options.forEach((actionType) => {
          const { targetValue, onValue, offValue } = match(actionType)
            .with("onActivate", () => ({
              targetValue: 100,
              onValue: 100,
              offValue: 0,
            }))
            .with("onDeactivate", () => ({
              targetValue: 0,
              onValue: 100,
              offValue: 0,
            }))
            .exhaustive();

          const newActionId = randomId();
          updateNestedData(
            yDoc,
            sourceNode.data.dataId,
            "canbusController",
            `pirSensors.${sourceHandle}.${actionType}.${newActionId}`,
            {
              id: newActionId,
              sortIndex: Object.values(
                data?.pirSensors?.[sourceHandle]?.[actionType] ?? {},
              ).length,
              deviceId: targetSectionDevice.id,
              targetValue,
              onValue,
              offValue,
              delay: 0,
              type: "lighting",
              dimSpeed: match(actionType)
                .with("onActivate", () => defaultDimSpeedOnClick)
                .with("onDeactivate", () => defaultDimSpeedOnHold)
                .exhaustive(),
            },
          );
        });
        return;
      }

      const sourceController = data.controllers[sourceHandle];
      if (!sourceController) {
        console.warn(`Controller not found for handle ${sourceHandle}`);
        return;
      }

      if (sourceController.type === "toggle") {
        if (
          ToggleCanBusControllerViaId.options.some((actionType) =>
            Object.values(sourceController[actionType] ?? {}).some(
              (action) => action.deviceId === targetSectionDevice.id,
            ),
          )
        ) {
          console.warn(`Action for ${targetSectionDevice.id} already exists`);
          return;
        }

        const toggleActionTypes = z.enum([
          "onUpClick",
          "onUpHold",
          "onDownClick",
          "onDownHold",
        ]);

        toggleActionTypes.options.forEach((actionType) => {
          const { targetValue, onValue, offValue } = match(actionType)
            .with("onUpClick", () => ({
              targetValue: 100,
              onValue: 100,
              offValue: 0,
            }))
            .with("onUpHold", () => ({
              targetValue: 100,
              onValue: 100,
              offValue: 0,
            }))
            .with("onDownClick", () => ({
              targetValue: 0,
              onValue: 100,
              offValue: 0,
            }))
            .with("onDownHold", () => ({
              targetValue: 0,
              onValue: 100,
              offValue: 0,
            }))
            .exhaustive();

          const newActionId = randomId();
          updateNestedData(
            yDoc,
            sourceNode.data.dataId,
            "canbusController",
            `controllers.${sourceController.id}.${actionType}.${newActionId}`,
            {
              id: newActionId,
              sortIndex: Object.values(sourceController.onUpClick ?? {}).length,
              deviceId: targetSectionDevice.id,
              dimSpeed: defaultDimSpeedOnClick,
              targetValue,
              onValue,
              offValue,
              delay: 0,
              type: "lighting",
            },
          );
        });
      } else if (sourceController.type === "momentary") {
        if (
          MomentaryCanBusControllerViaId.options.some((actionType) =>
            Object.values(sourceController[actionType] ?? {}).some(
              (action) => action.deviceId === targetSectionDevice.id,
            ),
          )
        ) {
          console.warn(`Action for ${targetSectionDevice.id} already exists`);
          return;
        }

        const toggleActionTypes = z.enum(["onUpClick", "onUpHold"]);

        toggleActionTypes.options.forEach((actionType) => {
          const newActionId = randomId();
          updateNestedData(
            yDoc,
            sourceNode.data.dataId,
            "canbusController",
            `controllers.${sourceController.id}.${actionType}.${newActionId}`,
            {
              id: newActionId,
              sortIndex: Object.values(sourceController.onUpClick ?? {}).length,
              deviceId: targetSectionDevice.id,
              dimSpeed: defaultDimSpeedOnClick,
              targetValue: 100,
              onValue: 100,
              offValue: 0,
              delay: 0,
              type: "lighting",
            },
          );
        });
      }
    })
    .with({ type: "roomSwitchContainer" }, (sourceNode) => {
      const viaIdResult = RoomSwitchViaId.safeParse(sourceHandle);
      if (!viaIdResult.success) {
        return;
      }

      const viaId = viaIdResult.data;
      const data = getData(yDoc, sourceNode.data.dataId, "roomSwitch");
      const actions = Object.values(data?.[viaId].onUpClick ?? {});

      if (actions.some((a) => a.deviceId === targetSectionDevice.id)) {
        console.warn(`Action for ${targetSectionDevice.id} already exists`);
        return;
      }

      const newActionId = randomId();
      updateNestedData(
        yDoc,
        sourceNode.data.dataId,
        "roomSwitch",
        `${viaId}.onUpClick.${newActionId}`,
        {
          id: newActionId,
          sortIndex: actions.length,
          type: "lighting",
          deviceId: targetSectionDevice.id,
          dimSpeed: defaultDimSpeedOnClick,
          targetValue: 100,
          onValue: 100,
          offValue: 0,
          delay: 0,
        },
      );
    })
    .with({ type: "roomDimmerContainer" }, (sourceNode) => {
      const viaIdResult = RoomDimmerViaId.safeParse(sourceHandle);
      if (!viaIdResult.success) {
        return;
      }

      const viaId = viaIdResult.data;
      const data = getData(yDoc, sourceNode.data.dataId, "roomDimmer");

      createActionFor("onUpClick");
      createActionFor("onUpHold");

      function createActionFor(actionType: RoomDimmerAction): void {
        const actions = Object.values(data?.[viaId].onUpClick ?? {});
        if (actions.some((a) => a.deviceId === targetSectionDevice.id)) {
          console.warn(
            `Action ${actionType} for ${targetSectionDevice.id} already exists`,
          );
          return;
        }

        const { targetValue, onValue, offValue } = match(viaId)
          .with("viaUp", () => ({
            targetValue: 100,
            onValue: 100,
            offValue: 100,
          }))
          .with("via", () => ({ targetValue: 100, onValue: 100, offValue: 0 }))
          .with("viaDown", () => ({ targetValue: 0, onValue: 0, offValue: 0 }))
          .exhaustive();

        const newActionId = randomId();
        updateNestedData(
          yDoc,
          sourceNode.data.dataId,
          "roomDimmer",
          `${viaId}.${actionType}.${newActionId}`,
          {
            id: newActionId,
            sortIndex: actions.length,
            deviceId: targetSectionDevice.id,
            targetValue,
            onValue,
            offValue,
            delay: 0,
            type: "lighting",
            dimSpeed: match(actionType)
              .with("onUpClick", () => defaultDimSpeedOnClick)
              .with("onUpHold", () => defaultDimSpeedOnHold)
              .exhaustive(),
          },
        );
      }
    })
    .with({ type: "presenceSensorContainer" }, (sourceNode) => {
      const viaIdResult = PresenceSensorViaId.safeParse(sourceHandle);
      if (!viaIdResult.success) {
        return;
      }

      const viaId = viaIdResult.data;
      const data = getData(yDoc, sourceNode.data.dataId, "presenceSensor");
      const actions = Object.values(data?.[viaId].onUpClick ?? {});

      if (actions.some((a) => a.deviceId === targetSectionDevice.id)) {
        console.warn(`Action for ${targetSectionDevice.id} already exists`);
        return;
      }

      const newActionId = randomId();
      updateNestedData(
        yDoc,
        sourceNode.data.dataId,
        "presenceSensor",
        `${viaId}.onUpClick.${newActionId}`,
        {
          id: newActionId,
          sortIndex: actions.length,
          type: "lighting",
          deviceId: targetSectionDevice.id,
          dimSpeed: defaultDimSpeedOnClick,
          targetValue: 100,
          onValue: 100,
          offValue: 0,
          delay: 0,
        },
      );
    })
    .with({ type: "doorSensorContainer" }, (sourceNode) => {
      const viaIdResult = DoorSensorViaId.safeParse(sourceHandle);
      if (!viaIdResult.success) {
        return;
      }

      const viaId = viaIdResult.data;
      const data = getData(yDoc, sourceNode.data.dataId, "doorSensor");
      const actions = Object.values(data?.[viaId].onUpClick ?? {});

      if (actions.some((a) => a.deviceId === targetSectionDevice.id)) {
        console.warn(`Action for ${targetSectionDevice.id} already exists`);
        return;
      }

      const newActionId = randomId();

      if (
        targetSectionDevice.type === "somoThermostat" ||
        targetSectionDevice.type === "somoIrController"
      ) {
        updateNestedData(
          yDoc,
          sourceNode.data.dataId,
          "doorSensor",
          `${viaId}.onUpClick.${newActionId}`,
          {
            id: newActionId,
            sortIndex: actions.length,
            deviceId: targetSectionDevice.id,
            setpoint: 22,
            mode: "auto",
            fanSpeed: "auto",
            type: "thermostat",
          },
        );
      } else {
        updateNestedData(
          yDoc,
          sourceNode.data.dataId,
          "doorSensor",
          `${viaId}.onUpClick.${newActionId}`,
          {
            id: newActionId,
            sortIndex: actions.length,
            deviceId: targetSectionDevice.id,
            dimSpeed: defaultDimSpeedOnClick,
            targetValue: 100,
            onValue: 100,
            offValue: 0,
            delay: 0,
            type: "lighting",
          },
        );
      }
    })
    .with({ type: "somoShadesContainer" }, (sourceNode) => {
      const viaIdResult = SomoShadesViaId.safeParse(sourceHandle);
      if (!viaIdResult.success) {
        return;
      }

      const viaId = viaIdResult.data;
      const data = getData(yDoc, sourceNode.data.dataId, "somoShades");
      const actions = Object.values(data?.[viaId].onUpClick ?? {});

      if (actions.some((a) => a.deviceId === targetSectionDevice.id)) {
        console.warn(`Action for ${targetSectionDevice.id} already exists`);
        return;
      }

      const newActionId = randomId();
      updateNestedData(
        yDoc,
        sourceNode.data.dataId,
        "somoShades",
        `${viaId}.onUpClick.${newActionId}`,
        {
          id: newActionId,
          sortIndex: actions.length,
          deviceId: targetSectionDevice.id,
          raceTime: data?.raceTime ?? 30,
          targetValue: 100,
          onValue: 100,
          offValue: 0,
        },
      );
    })
    .with({ type: "somoFanContainer" }, (sourceNode) => {
      const viaIdResult = SomoFanViaId.safeParse(sourceHandle);
      if (!viaIdResult.success) {
        return;
      }

      const viaId = viaIdResult.data;
      const data = getData(yDoc, sourceNode.data.dataId, "somoFan");
      const actions = Object.values(data?.[viaId].onUpClick ?? {});

      if (actions.some((a) => a.deviceId === targetSectionDevice.id)) {
        console.warn(`Action for ${targetSectionDevice.id} already exists`);
        return;
      }

      const newActionId = randomId();
      updateNestedData(
        yDoc,
        sourceNode.data.dataId,
        "somoFan",
        `${viaId}.onUpClick.${newActionId}`,
        {
          id: newActionId,
          sortIndex: actions.length,
          deviceId: targetSectionDevice.id,
          targetValue: 100,
          onValue: 100,
          offValue: 0,
        },
      );
    })
    .with({ type: "somoThermostatContainer" }, (sourceNode) => {
      const viaIdResult = SomoThermostatViaId.safeParse(sourceHandle);
      if (!viaIdResult.success) {
        return;
      }
      const viaId = viaIdResult.data;

      // Get the Y.Map for the via
      const dataMap = getNodeData(yDoc, sourceNode.id);
      const viaMap = dataMap?.get(viaId) as Y.Map<any> | undefined;
      if (!viaMap) {
        console.warn(`Via map not found for ${viaId}`);
        return;
      }

      if (actionAlreadyExistsForDevice(viaMap, targetSectionDevice.id)) {
        console.warn(`Action for ${targetSectionDevice.id} already exists`);
        return;
      }

      addAction(viaMap, {
        device: "somoThermostat",
        key: "onUpClick",
        params: {
          deviceId: targetSectionDevice.id,
          setpoint: 22,
          mode: "auto",
          fanSpeed: "auto",
          type: "thermostat",
        },
      });
    })
    .with({ type: "scene" }, (sourceNode) => {
      createActionFor("onActivate");
      createActionFor("onDeactivate");

      function createActionFor(viaId: SceneViaIds) {
        const data = getData(yDoc, sourceNode.data.dataId, "scene");
        if (!data?.[viaId].enabled) {
          return;
        }

        const actions = Object.values(data?.[viaId].onUpClick ?? {});

        if (actions.some((a) => a.deviceId === targetSectionDevice.id)) {
          console.warn(`Action for ${targetSectionDevice.id} already exists`);
          return;
        }

        const newActionId = randomId();
        updateNestedData(
          yDoc,
          sourceNode.data.dataId,
          "scene",
          `${viaId}.onUpClick.${newActionId}`,
          {
            id: newActionId,
            sortIndex: actions.length,
            type: "lighting",
            deviceId: targetSectionDevice.id,
            dimSpeed: defaultDimSpeedOnClick,
            targetValue: 100,
            onValue: 100,
            offValue: 0,
            delay: 0,
          },
        );
      }
    })
    // Trying to perform an exhaustive check results in a type error:
    // `Type instantiation is excessively deep and possibly infinite`
    // => Use `otherwise` instead.
    .otherwise(() => null);
}

export function handleEdgesChange(
  changes: EdgeChange<Edge>[],
  { nodes, edges }: Graph,
  setEdges: (edges: Edge[]) => void,
  yDoc: Y.Doc,
) {
  const virtualEdges = generateVirtualEdges(yDoc, nodes);
  const newEdges = applyEdgeChanges(changes, edges);
  setEdges(newEdges);

  // update the yDoc
  yDoc.transact(() => {
    const graph = yDoc.getMap("graph");
    const yEdges = graph.get("edges") as Y.Map<any>;
    for (const change of changes) {
      // skip virtual edges
      const changedEdgeId = change.type === "add" ? change.item.id : change.id;
      if (virtualEdges.some((edge) => edge.id === changedEdgeId)) {
        continue;
      }

      match(change)
        .with({ type: "add" }, (change) => {
          const newEdge = objectToYMap({
            id: change.item.id,
            source: change.item.source,
            sourceHandle: change.item.sourceHandle,
            target: change.item.target,
            targetHandle: change.item.targetHandle,
            label: change.item.label,
            type: change.item.type,
            data: change.item.data,
          });
          yEdges.set(change.item.id, newEdge);
        })
        .with({ type: "remove" }, (change) => {
          yEdges.delete(change.id);
        })
        .with({ type: "replace" }, (change) => {
          const edge = yEdges.get(change.id) as Y.Map<any> | undefined;
          if (!edge) {
            return;
          }

          edge.set("id", change.item.id);
          edge.set("source", change.item.source);
          edge.set("sourceHandle", change.item.sourceHandle);
          edge.set("target", change.item.target);
          edge.set("targetHandle", change.item.targetHandle);
          edge.set("label", change.item.label);
          edge.set("type", change.item.type);

          if (change.item.data) {
            let data = edge.get("data") as Y.Map<any>;
            if (!data) {
              data = new Y.Map();
              edge.set("data", data);
            }
            for (const [key, value] of Object.entries(change.item.data)) {
              if (
                data.get(key) instanceof Y.XmlFragment ||
                data.get(key) instanceof Y.Array ||
                data.get(key) instanceof Y.Map
              ) {
                continue;
              }
              data.set(key, value);
            }
          }
        })
        .with({ type: "select" }, () => {
          // Do nothing
        })
        .exhaustive();
    }
  });
}

export function handleNodesChange(
  changes: NodeChange<GraphNode>[],
  { nodes }: Graph,
  setNodes: (nodes: GraphNode[]) => void,
  yDoc: Y.Doc,
) {
  let newNodes = applyNodeChanges(changes, nodes);
  const hasAddChange = changes.some((change) => change.type === "add");
  if (hasAddChange) {
    newNodes = newNodes.map((node) => {
      const includedInAddChange = changes.some(
        (change) => change.type === "add" && change.item.id === node.id,
      );
      if (!includedInAddChange) {
        return { ...node, selected: false };
      }
      return node;
    });
  }

  let hightestZIndex = newNodes.reduce((max, node) => {
    return Math.max(max, node.zIndex ?? 0);
  }, 0);

  // sort nodes by zIndex
  newNodes.sort((a, b) => (a.zIndex ?? 0) - (b.zIndex ?? 0));

  // Remove nodes with invalid data
  const invalidNodes = newNodes.filter((node) => {
    const DataSchema = match(node.type)
      .with(
        "baseStationContainer",
        "canbusControllerContainer",
        "doorSensorContainer",
        "image",
        "light",
        "outletDimmerContainer",
        "outletDimmerLight",
        "roomDimmerContainer",
        "somoFanContainer",
        "presenceSensorContainer",
        "roomSwitchContainer",
        "servicePadContainer",
        "scene",
        "somoIrControllerContainer",
        "somoShadesContainer",
        "somoThermostatContainer",
        "virtualButtonContainer",
        () => LayoutContainerNodeData,
      )
      .with(
        "baseStationAnchor",
        "canbusControllerAnchor",
        "doorSensorAnchor",
        "roomDimmerAnchor",
        "somoFanAnchor",
        "roomSwitchAnchor",
        "outletDimmerAnchor",
        "presenceSensorAnchor",
        "servicePadAnchor",
        "servicePadAnchor",
        "somoIrControllerAnchor",
        "somoShadesAnchor",
        "somoThermostatAnchor",
        "virtualButtonAnchor",
        () => LayoutAnchorNodeData,
      )
      // 👇 What's left to refactor
      .with("section", () => SectionNodeData)
      .with("comment", () => CommentNodeData)
      .with(undefined, () => z.record(z.string(), z.unknown()))
      .exhaustive();

    return !DataSchema.safeParse(node.data).success;
  });
  invalidNodes.forEach((node) => {
    console.warn("Node with invalid data found. Removing it.", { node });
    changes.push({ type: "remove", id: node.id });
  });

  setNodes(
    newNodes.filter((n) => {
      // delete children of removed nodes
      for (const change of changes) {
        if (change.type === "remove") {
          if (change.id === n.parentId) {
            return false;
          }
        }
      }
      return true;
    }),
  );

  // TODO: write into yDoc.getMap("layoutData")

  // update the yDoc
  yDoc.transact(() => {
    const graph = yDoc.getMap("graph");
    const yNodes = graph.get("nodes") as Y.Map<any>;
    for (const change of changes) {
      match(change)
        .with({ type: "dimensions" }, (change) => {
          const node = yNodes.get(change.id) as Y.Map<number> | undefined;
          if (!node) {
            console.warn(`Can't find node ${change.id} for ${change.type}`);
            return;
          }
          if (change.dimensions) {
            node.set("width", change.dimensions.width);
            node.set("height", change.dimensions.height);
          }
        })
        .with({ type: "position" }, (change) => {
          const node = yNodes.get(change.id) as Y.Map<any> | undefined;
          if (!node) {
            console.warn(`Can't find node ${change.id} for ${change.type}`);
            return;
          }
          if (change.position) {
            const position = node.get("position") as Y.Map<number> | undefined;
            if (!position) {
              return;
            }
            position.set("x", change.position.x);
            position.set("y", change.position.y);
          }
        })
        .with({ type: "add" }, ({ item }) => {
          const newNode = objectToYMap({
            ...pick(item, reactFlowKeysToPersist),
            zIndex: hightestZIndex++,
          });

          yNodes.set(item.id, newNode);
        })
        .with({ type: "remove" }, (change) => {
          deleteGraphNode(yDoc, change.id);
        })
        .with({ type: "replace" }, (change) => {
          const foundNode = nodes.find((n) => n.id === change.item.id);
          if (!foundNode) {
            console.log("node not found", change.item.id);
            return;
          }

          const foundNoteChangeSet = pick(foundNode, reactFlowKeysToPersist);
          const changeSet = pick(change.item, reactFlowKeysToPersist);
          if (deepEqual(foundNoteChangeSet, changeSet)) {
            return;
          }

          // TODO: replace with a single update to the nodes Map that merges with current values
          const nodeMap = yNodes.get(change.id) as Y.Map<any> | undefined;
          if (!nodeMap) {
            console.warn(`Can't find node ${change.id} for ${change.type}`);
            return;
          }
          nodeMap.set("id", change.item.id);
          nodeMap.set("type", change.item.type);
          nodeMap.set("width", change.item.width);
          nodeMap.set("height", change.item.height);
          nodeMap.set("draggable", change.item.draggable);
          nodeMap.set("selectable", change.item.selectable);
          nodeMap.set("connectable", change.item.connectable);
          nodeMap.set("deletable", change.item.deletable);
          nodeMap.set("zIndex", change.item.zIndex);
          nodeMap.set("parentId", change.item.parentId);
          nodeMap.set("expandParent", change.item.expandParent);
          nodeMap.set("extent", change.item.extent);
          let data = nodeMap.get("data") as Y.Map<any>;
          if (!data) {
            data = new Y.Map();
            nodeMap.set("data", data);
          }
          for (const [key, value] of Object.entries(change.item.data)) {
            // check if it's a yjs type (we handle those somewhere else)
            if (
              data.get(key) instanceof Y.XmlFragment ||
              data.get(key) instanceof Y.Map ||
              data.get(key) instanceof Y.Array
            ) {
              continue;
            }
            data.set(key, value);
          }
          const position = nodeMap.get("position") as Y.Map<number>;
          if (!position) {
            return;
          }
          position.set("x", change.item.position.x);
          position.set("y", change.item.position.y);
        })
        .with({ type: "select" }, () => {
          // do nothing
        })
        .exhaustive();
    }
  });
}

function deleteGraphNode(
  yDoc: Y.Doc,
  nodeId: string,
  options: { preserveParent?: boolean } = {},
) {
  const allNodes = getNodes(yDoc);
  const nodeToDelete = getNode(yDoc, nodeId);
  if (!allNodes || !nodeToDelete) {
    return;
  }

  // Delete children nodes
  Object.values(allNodes.toJSON())
    .filter((node) => node.parentId === nodeId)
    .forEach((childNode) => {
      deleteGraphNode(yDoc, childNode.id, {
        // Don't delete the parent (= this node) to avoid infinite recursion
        preserveParent: true,
      });
    });

  const { parentId, data } = nodeToDelete.toJSON();

  // Delete the node
  allNodes.delete(nodeId);

  // Delete the node associated Data
  if (data.dataId) {
    deleteData(yDoc, data.dataId);
  }

  // Delete the node parent
  // Container nodes have 1 parent node (Anchor). They must be deleted together.
  if (parentId && !options.preserveParent) {
    deleteGraphNode(yDoc, parentId);
  }
}
