import { useCallback } from "react";
import { useReactFlow } from "@xyflow/react";
import { toPng } from "html-to-image";
import { Button } from "@/components/ui/button";
import { Download } from "lucide-react";

interface DownloadButtonProps {
  roomName?: string;
}

export function DownloadButton({ roomName }: DownloadButtonProps) {
  const { getNodes, fitView } = useReactFlow();

  const downloadImage = useCallback(() => {
    // Find the specific ReactFlow container that this DownloadButton belongs to
    // We need to find the ReactFlow container that's closest to this button
    const downloadButton = document.querySelector(
      '[data-testid="download-button"]',
    ) as HTMLElement;
    let reactFlowContainer: HTMLElement | null = null;

    if (downloadButton) {
      // Find the ReactFlow container that contains this button
      const panel = downloadButton.closest(".react-flow__panel");
      if (panel) {
        const reactFlow = panel.closest(".react-flow");
        if (reactFlow) {
          reactFlowContainer = reactFlow as HTMLElement;
        }
      }
    }

    // Fallback to the first ReactFlow container if we can't find the specific one
    if (!reactFlowContainer) {
      reactFlowContainer = document.querySelector(".react-flow") as HTMLElement;
    }

    if (!reactFlowContainer) {
      console.error("ReactFlow container not found");
      return;
    }

    // Get the nodes to determine the bounds
    const nodes = getNodes();

    if (nodes.length === 0) {
      console.error("No nodes to download");
      return;
    }

    // Filter out the background rect node
    const actualNodes = nodes.filter((node) => node.id !== "bounds-background");

    if (actualNodes.length === 0) {
      console.error("No actual nodes to download");
      return;
    }

    // First, fit the view to show all nodes
    fitView({ padding: 0.1, includeHiddenNodes: false });

    // Wait a bit for the view to update, then capture
    setTimeout(() => {
      // Temporarily hide the grid by removing the bg-grid class
      const originalClassName = reactFlowContainer.className;
      const hasGrid = originalClassName.includes("bg-grid");

      if (hasGrid) {
        reactFlowContainer.className = originalClassName.replace(
          "bg-grid",
          "bg-gray-50",
        );
      }

      // Temporarily hide comments and cursors
      const commentElements = reactFlowContainer.querySelectorAll(
        '[data-type="comment"], .react-flow__node-comment',
      );
      const cursorElements = reactFlowContainer.querySelectorAll(
        '.cursor, .collaboration-cursor, .live-user-cursor, .CollabPointer, svg[viewBox="0 0 60 60"]',
      );

      // Also hide collaborative cursor elements by their specific class combinations
      const cursorContainerElements = reactFlowContainer.querySelectorAll(
        "div.absolute.max-w-md.flex-shrink-0.pointer-events-none",
      );
      const collabPointerElements = reactFlowContainer.querySelectorAll(
        "svg.text-blue-200.drop-shadow-sm.shadow-black",
      );

      const hiddenElements: HTMLElement[] = [];

      // Hide comment elements
      commentElements.forEach((element) => {
        const htmlElement = element as HTMLElement;
        if (htmlElement.style.display !== "none") {
          htmlElement.style.display = "none";
          hiddenElements.push(htmlElement);
        }
      });

      // Hide cursor elements
      cursorElements.forEach((element) => {
        const htmlElement = element as HTMLElement;
        if (htmlElement.style.display !== "none") {
          htmlElement.style.display = "none";
          hiddenElements.push(htmlElement);
        }
      });

      // Hide collaborative cursor container elements
      cursorContainerElements.forEach((element) => {
        const htmlElement = element as HTMLElement;
        if (htmlElement.style.display !== "none") {
          htmlElement.style.display = "none";
          hiddenElements.push(htmlElement);
        }
      });

      // Hide CollabPointer SVG elements
      collabPointerElements.forEach((element) => {
        const htmlElement = element as HTMLElement;
        if (htmlElement.style.display !== "none") {
          htmlElement.style.display = "none";
          hiddenElements.push(htmlElement);
        }
      });

      // Add room name label if provided
      let roomLabelElement: HTMLElement | null = null;
      if (roomName) {
        roomLabelElement = document.createElement("div");
        roomLabelElement.style.position = "absolute";
        roomLabelElement.style.top = "20px";
        roomLabelElement.style.left = "20px";
        roomLabelElement.style.backgroundColor = "rgba(255, 255, 255, 0.9)";
        roomLabelElement.style.padding = "8px 12px";
        roomLabelElement.style.borderRadius = "6px";
        roomLabelElement.style.fontFamily = "Arial, sans-serif";
        roomLabelElement.style.fontSize = "11px";
        roomLabelElement.style.fontWeight = "normal";
        roomLabelElement.style.color = "#888";
        roomLabelElement.style.border = "1px solid #f0f0f0";
        roomLabelElement.style.zIndex = "1000";
        roomLabelElement.textContent = `Room: ${roomName}`;
        reactFlowContainer.appendChild(roomLabelElement);
      }

      // Convert to image with proper scaling
      toPng(reactFlowContainer, {
        width: reactFlowContainer.offsetWidth,
        height: reactFlowContainer.offsetHeight,
        style: {
          transform: "scale(1)",
          transformOrigin: "top left",
        },
        // Add high-resolution scaling
        pixelRatio: 2, // 2x resolution for crisp images
        filter: (node) => {
          // Safely get className and id, ensuring they are strings
          const className = (node.className || "").toString();
          const id = (node.id || "").toString();
          const dataType = (node as HTMLElement).getAttribute?.("data-type");

          // Check if this is the background rect node
          if (id === "bounds-background") {
            return false; // Exclude the background rect
          }

          // Check if this element has the background rect styling
          const style = (node as HTMLElement).style;
          if (style) {
            const backgroundColor = style.backgroundColor;
            const border = style.border;
            // Background rect has light grey background and black border
            if (
              backgroundColor === "rgb(245, 245, 245)" &&
              border.includes("3px solid rgb(0, 0, 0)")
            ) {
              return false; // Exclude the background rect
            }
          }

          // Filter out comments - check for comment nodes and their content
          if (
            dataType === "comment" ||
            // Only check for comment-node class if it's a direct match
            className === "comment-node" ||
            className.includes("comment-node ") ||
            // Check for React Flow comment node class
            className.includes("react-flow__node-comment")
          ) {
            return false; // Exclude comment nodes
          }

          // Filter out cursors - check for cursor-related elements
          if (
            className.includes("collaboration-cursor") ||
            className.includes("live-user-cursor") ||
            className.includes("CollabPointer") ||
            id.includes("cursor") ||
            (node as HTMLElement).getAttribute?.("data-cursor") ||
            // Check for cursor SVG elements
            ((node as HTMLElement).tagName === "svg" &&
              (node as HTMLElement).getAttribute?.("viewBox") === "0 0 60 60")
          ) {
            return false; // Exclude cursor elements
          }

          // Filter out collaborative cursor elements by their specific class combinations
          if (
            className.includes("absolute") &&
            className.includes("max-w-md") &&
            className.includes("flex-shrink-0") &&
            className.includes("pointer-events-none")
          ) {
            return false; // Exclude cursor container divs
          }

          // Filter out CollabPointer SVG elements
          if (
            (node as HTMLElement).tagName === "svg" &&
            className.includes("text-blue-200") &&
            className.includes("drop-shadow-sm") &&
            className.includes("shadow-black")
          ) {
            return false; // Exclude CollabPointer SVGs
          }

          // Filter out controls and other UI elements
          const shouldInclude =
            !className.includes("react-flow__controls") &&
            !className.includes("react-flow__panel") &&
            !className.includes("react-flow__attribution") &&
            !className.includes("react-flow__minimap") &&
            !id.includes("bounds-background") &&
            !className.includes("download-image");

          return shouldInclude;
        },
        backgroundColor: "#ffffff",
      })
        .then((dataUrl) => {
          const link = document.createElement("a");
          link.download = `sld-diagram-${new Date().toISOString().split("T")[0]}.png`;
          link.href = dataUrl;
          link.click();
        })
        .catch((error) => {
          console.error("Error generating image:", error);
          throw error; // Re-throw the error after logging
        })
        .finally(() => {
          // Clean up room label element
          if (roomLabelElement && roomLabelElement.parentNode) {
            roomLabelElement.parentNode.removeChild(roomLabelElement);
          }

          // Restore hidden elements
          hiddenElements.forEach((element) => {
            element.style.display = "";
          });

          // Restore the original className (including grid if it was there)
          reactFlowContainer.className = originalClassName;
        });
    }, 100);
  }, [getNodes, fitView, roomName]);

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={downloadImage}
      className="h-8 px-2"
      data-testid="download-button"
    >
      <Download className="w-4 h-4 mr-1" />
      Download
    </Button>
  );
}
