import { match } from "ts-pattern";
import { Node } from "@xyflow/react";
import { SldNodeData } from "@/components/devices/sld/types";
import { CommentSettings } from "./comment/CommentSettings";

interface SLDSettingsEditorProps {
  selectedNodes: Node<SldNodeData>[];
}

export function SLDSettingsEditor({ selectedNodes }: SLDSettingsEditorProps) {
  return (
    <div className="flex flex-col flex-shrink-0 divide-y-8 divide-gray-300 dark:divide-gray-800">
      {selectedNodes.map((node) => {
        return match(node)
          .with({ type: "comment" }, (node) => (
            <CommentSettings key={node.id} node={node as any} />
          ))
          .otherwise(() => null);
      })}
      {selectedNodes.length === 0 && (
        <div className="p-4 text-center text-gray-500 dark:text-gray-400">
          Select a node to view its settings
        </div>
      )}
    </div>
  );
}
