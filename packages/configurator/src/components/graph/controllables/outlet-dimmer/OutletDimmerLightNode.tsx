import { useGetData } from "@/components/data/useData";
import { DeviceIcons } from "@/components/icons/DeviceIcons";
import { useExecutionContext } from "@/contexts/ExecutionContext";
import { cn } from "@/lib/classNames";
import { LayoutContainerNode, OutletDimmerLight } from "@somo/shared";
import { Handle, NodeProps, Position } from "@xyflow/react";
import equal from "fast-deep-equal/react";
import React from "react";
import { useReactFlowContext } from "../../ReactFlowContext";

// TODO: update logic for computing the virtual edges
// TODO: refactor common logic to render a generic Controllable node

export const OutletDimmerLightNode = React.memo(RegularNode, equal);
export const OutletDimmerLightNodeSimulator = React.memo(SimulatorNode, equal);

function RegularNode(props: NodeProps<LayoutContainerNode>) {
  const { readOnly } = useReactFlowContext();
  const data = useGetData(props.data.dataId, "outletDimmerLight");
  if (!data) {
    return <></>;
  }

  const propsWithData = { ...props, data: { ...props.data, ...data } };
  return (
    <BaseNode
      {...propsWithData}
      isConnectable={!readOnly}
      className="text-white"
    >
      <div className="text-xs flex-shrink-0">{data.name}</div>
    </BaseNode>
  );
}

function SimulatorNode(props: NodeProps<LayoutContainerNode>) {
  const { isLightOn, getBrightness } = useExecutionContext();
  const data = useGetData(props.data.dataId, "outletDimmerLight");
  if (!data) {
    return <></>;
  }

  const propsWithData = { ...props, data: { ...props.data, ...data } };
  return (
    <BaseNode
      {...propsWithData}
      isConnectable={false}
      className={cn(
        "border cursor-pointer hover:outline hover:outline-blue-500 outline-2 outline-offset-0",
        isLightOn(data.id)
          ? "bg-yellow-100 text-black shadow-md border-yellow-300 "
          : "bg-gray-600 text-white border-gray-700",
      )}
    >
      <div className="flex flex-col items-start flex-1">
        <div className="text-xs flex-shrink-0">{data.name}</div>
        <div className="flex-shrink-0 mr-2 text-[9px]">
          {getBrightness(data.id)}%
        </div>
      </div>
    </BaseNode>
  );
}

type BaseNodeProps = NodeProps<LayoutContainerNode> & {
  data: OutletDimmerLight;
  isConnectable: boolean;
  children: React.ReactNode;
  onClick?: () => void;
  className?: string;
};

function BaseNode(props: BaseNodeProps) {
  const { data, isConnectable, children, onClick, className, selected } = props;
  const IconComponent = data.icon ? DeviceIcons[data.icon] : null;

  return (
    <div
      onClick={onClick}
      className={cn(
        "bg-gray-600 rounded-md shadow-md flex items-center justify-center gap-2 p-2 w-max min-w-[40px] relative",
        className,
        selected && "outline outline-2 outline-offset-1 outline-blue-500",
      )}
    >
      {IconComponent && <IconComponent className="size-6 flex-shrink-0" />}
      {data.showLabel && children}
      <Handle
        type="target"
        id={data.id}
        isConnectable={isConnectable}
        // TODO: use `useHandlePosition` to dynamically compute this
        position={Position.Left}
        style={{
          background: "white",
          border: "1px solid #4b5563",
          color: "transparent",
          width: 6,
          height: 6,
          borderRadius: "50%",
          boxShadow: "0 1px 2px rgba(0, 0, 0, 0.1)",
        }}
      />
    </div>
  );
}
