import { useData } from "@/components/data/useData";
import { useReactFlowContext } from "@/components/graph/ReactFlowContext";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import {
  LayoutContainerNode
} from "@somo/shared";
import { DimmingCurve } from "../../settings/DimmingCurve";
import { LampPopover } from "../../settings/LampPopover";
import {
  SettingsGroup,
  SettingsGroupContent,
  SettingsRow
} from "../../settings/SettingsGroup";
import { SettingsInput } from "../../settings/SettingsInput";

export function OutletDimmerLightSettings({
  node,
}: {
  node: LayoutContainerNode<"outletDimmerLight">;
}) {
  const { readOnly } = useReactFlowContext();
  const { data, updateNestedData } = useData(node.data.dataId, "outletDimmerLight");
  if (!data) {
    return <div>Loading...</div>;
  }

  return (
    <div>
      <SettingsGroup>
        <SettingsGroupContent>
          <SettingsRow>
            <div className="font-mono text-xs text-gray-500">ID: {node.id}</div>
          </SettingsRow>
          <SettingsRow>
            <div className="flex flex-row gap-0.5 text-xs font-bold text-gray-800">
              <span className="flex-grow">{data.name}</span>
            </div>
            <div className="flex flex-col gap-2">
              <div className="flex flex-row gap-2 text-xs font-bold text-gray-800 mt-2 items-center">
                <LampPopover
                  activeIconKey={data.icon}
                  onIconClick={(iconKey) => updateNestedData("icon", iconKey)}
                  disabled={readOnly}
                />

                <SettingsInput
                  className="text-sm font-normal h-10"
                  value={data.name}
                  disabled={readOnly}
                  onEndEdit={(value) => updateNestedData("name", value)}
                />
                <div className="flex flex-row items-center gap-1 ml-3">
                  <Checkbox
                    id="showLabel"
                    checked={data.showLabel}
                    disabled={readOnly}
                    onCheckedChange={(checked) =>
                      updateNestedData("showLabel", Boolean(checked))
                    }
                  />
                  <Label
                    htmlFor="showLabel"
                    className="text-gray-500 text-xs font-semibold flex-shrink-0 truncate"
                  >
                    Show label
                  </Label>
                </div>
              </div>

              <DimmingCurve
                points={data.defaultDimmingCurve.points}
                curveType={data.defaultDimmingCurve.type}
                disabled={readOnly}
                onChange={(points) =>
                  updateNestedData("defaultDimmingCurve.points", points)
                }
                onCurveTypeChange={(type) =>
                  updateNestedData("defaultDimmingCurve.type", type)
                }
                className="mt-2"
                height={180}
              />
            </div>
          </SettingsRow>
        </SettingsGroupContent>
      </SettingsGroup>
    </div>
  );
}
