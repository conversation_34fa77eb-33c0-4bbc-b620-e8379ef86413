import { useData } from "@/components/data/useData";
import { useReactFlowContext } from "@/components/graph/ReactFlowContext";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  LayoutContainerNode,
  LightFixture,
  LightFixtureType,
  lightFixtureTypes,
  randomId,
} from "@somo/shared";
import { Trash2 } from "lucide-react";
import { DimmingCurve } from "../../settings/DimmingCurve";
import { LampPopover } from "../../settings/LampPopover";
import {
  SettingsGroup,
  SettingsGroupContent,
  SettingsLabel,
  SettingsRow,
} from "../../settings/SettingsGroup";
import { SettingsInput } from "../../settings/SettingsInput";

export function LightSettings({
  node,
}: {
  node: LayoutContainerNode<"light">;
}) {
  const { readOnly } = useReactFlowContext();
  const { data, updateNestedData } = useData(node.data.dataId, "light");
  if (!data) {
    return <div>Loading...</div>;
  }

  const fixtures = Object.values(data.fixtures);

  return (
    <div>
      <SettingsGroup>
        <SettingsGroupContent>
          <SettingsRow>
            <div className="font-mono text-xs text-gray-500">ID: {node.id}</div>
          </SettingsRow>
          <SettingsRow>
            <div className="flex flex-row gap-0.5 text-xs font-bold text-gray-800">
              <span className="flex-grow">{data.name}</span>
            </div>
            <div className="flex flex-col gap-2">
              <div className="flex flex-row gap-2 text-xs font-bold text-gray-800 mt-2 items-center">
                <LampPopover
                  activeIconKey={data.icon}
                  onIconClick={(iconKey) => updateNestedData("icon", iconKey)}
                  disabled={readOnly}
                />

                <SettingsInput
                  className="text-sm font-normal h-10"
                  value={data.name}
                  disabled={readOnly}
                  onEndEdit={(value) => updateNestedData("name", value)}
                />
                <div className="flex flex-row items-center gap-1 ml-3">
                  <Checkbox
                    id="showLabel"
                    checked={data.showLabel}
                    disabled={readOnly}
                    onCheckedChange={(checked) =>
                      updateNestedData("showLabel", Boolean(checked))
                    }
                  />
                  <Label
                    htmlFor="showLabel"
                    className="text-gray-500 text-xs font-semibold flex-shrink-0 truncate"
                  >
                    Show label
                  </Label>
                </div>
              </div>

              <div className="flex flex-row items-center justify-between gap-2 flex-wrap">
                <div className="flex flex-row items-center gap-1 h-10">
                  <Checkbox
                    id={`isDimmable-${data.id}`}
                    checked={data.isDimmable}
                    disabled={readOnly}
                    onCheckedChange={(checked) =>
                      updateNestedData("isDimmable", Boolean(checked))
                    }
                  />
                  <Label
                    htmlFor={`isDimmable-${data.id}`}
                    className="text-gray-500 text-xs font-semibold"
                  >
                    Dimmable
                  </Label>
                </div>

                {data.isDimmable && (
                  <SettingsInput
                    className="w-auto"
                    inputClassName="w-[60px] text-center"
                    label="Default dim speed (sec)"
                    value={String(data.defaultDimmingSpeed)}
                    disabled={readOnly}
                    onEndEdit={(value) =>
                      updateNestedData("defaultDimmingSpeed", parseFloat(value))
                    }
                  />
                )}
              </div>

              {data.isDimmable && (
                <DimmingCurve
                  points={data.defaultDimmingCurve.points}
                  curveType={data.defaultDimmingCurve.type}
                  disabled={readOnly}
                  onChange={(points) =>
                    updateNestedData("defaultDimmingCurve.points", points)
                  }
                  onCurveTypeChange={(type) =>
                    updateNestedData("defaultDimmingCurve.type", type)
                  }
                  className="mt-2"
                  height={180}
                />
              )}

              <div className="flex flex-row items-center gap-1 mt-2 mr-1">
                <SettingsLabel className="flex-grow">Fixtures</SettingsLabel>
                {!readOnly && (
                  <Button
                    variant="ghost"
                    className="h-6 flex-shrink-0 text-[10px] px-1.5 text-gray-500"
                    onClick={() => {
                      const newFixtureId = randomId();
                      updateNestedData(`fixtures.${newFixtureId}`, {
                        id: newFixtureId,
                        type: "Analog",
                        channel: 1,
                        minBrightness: 0,
                        maxBrightness: 100,
                      });
                    }}
                  >
                    + Add Fixture
                  </Button>
                )}
              </div>

              {fixtures.length > 0 && (
                <div className="flex flex-col gap-6">
                  {fixtures.map((fixture) => (
                    <FixtureSettings
                      key={fixture.id}
                      fixture={fixture}
                      dataId={node.data.dataId}
                    />
                  ))}
                </div>
              )}
            </div>
          </SettingsRow>
        </SettingsGroupContent>
      </SettingsGroup>
    </div>
  );
}

function FixtureSettings({
  fixture,
  dataId,
}: {
  fixture: LightFixture;
  dataId: string;
}) {
  const { readOnly } = useReactFlowContext();
  const { updateNestedData, deleteNestedData } = useData(dataId, "light");
  const dataKey = `fixtures.${fixture.id}` as const;

  return (
    <div className="flex flex-col gap-2">
      <div className="flex flex-row items-center gap-2">
        <Select
          value={fixture.type}
          disabled={readOnly}
          onValueChange={(value) => {
            const type = LightFixtureType.parse(value);
            updateNestedData(`${dataKey}.type`, type);
          }}
        >
          <SelectTrigger className="h-10">
            <SelectValue placeholder="Select fixture" />
          </SelectTrigger>
          <SelectContent>
            {lightFixtureTypes.map((type) => (
              <SelectItem key={type} value={type}>
                {type}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <SettingsInput
          className="w-auto flex-shrink-0"
          inputClassName="w-[40px] text-center flex-shrink-0"
          label="Channel"
          value={String(fixture.channel)}
          disabled={readOnly}
          onEndEdit={(value) => {
            const channel = parseInt(value);
            if (isNaN(channel)) {
              return;
            }
            updateNestedData(`${dataKey}.channel`, channel);
          }}
        />

        {!readOnly && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="flex-shrink-0 h-10"
                onClick={() => deleteNestedData(dataKey)}
              >
                <Trash2 className="size-4 flex-shrink-0" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Delete Fixture</TooltipContent>
          </Tooltip>
        )}
      </div>
      <div className="flex flex-row items-center gap-2 flex-wrap">
        <SettingsInput
          className="w-auto flex-shrink-0"
          inputClassName="w-[60px] text-center flex-shrink-0"
          label="Min. brightness"
          value={String(fixture.minBrightness)}
          disabled={readOnly}
          onEndEdit={(value) => {
            const minBrightness = parseInt(value);
            if (isNaN(minBrightness)) {
              return;
            }
            updateNestedData(`${dataKey}.minBrightness`, minBrightness);
          }}
        />
        <SettingsInput
          className="w-auto flex-shrink-0"
          inputClassName="w-[60px] text-center flex-shrink-0"
          label="Max. brightness"
          value={String(fixture.maxBrightness)}
          disabled={readOnly}
          onEndEdit={(value) => {
            const maxBrightness = parseInt(value);
            if (isNaN(maxBrightness)) {
              return;
            }
            updateNestedData(`${dataKey}.maxBrightness`, maxBrightness);
          }}
        />
      </div>
    </div>
  );
}
