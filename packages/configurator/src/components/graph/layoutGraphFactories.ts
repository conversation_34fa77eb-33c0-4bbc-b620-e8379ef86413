import { createDataFromNode, DataDefaults } from "@/components/data/createData";
import {
  AnchorNodeTypes,
  ContainerNodeTypes,
  DataByContainerNodeType,
  LayoutAnchorNode,
  LayoutAnchorNodeData,
  LayoutContainerNode,
  randomId,
} from "@somo/shared";
import * as Y from "yjs";

export function anAnchorNode(
  type: AnchorNodeTypes,
  params: Partial<Omit<LayoutAnchorNode, "data" | "type">>,
  data: Partial<LayoutAnchorNodeData> = {},
): LayoutAnchorNode {
  return {
    id: `anchor-node-${randomId()}`,
    type,
    position: { x: 0, y: 0 },
    data,
    ...params,
  };
}

export function aContainerNode<Type extends ContainerNodeTypes>(
  yDoc: Y.Doc,
  type: Type,
  params: Partial<Omit<LayoutContainerNode, "data" | "type">>,
  data?: Partial<Omit<DataByContainerNodeType[Type], "id">>,
): LayoutContainerNode {
  const node = {
    id: `container-node-${randomId()}`,
    type,
    position: { x: 0, y: 0 },
    data: {
      dataId: "replaced at runtime",
    },
    ...params,
  };

  // There is no static guarantee that `DataByContainerNodeType` have the same
  // values that `DataDefaults`, but if they don't match `assert()` will throw.
  const dataID = createDataFromNode(yDoc, node, data as Partial<DataDefaults>);
  assert(dataID);
  node.data.dataId = dataID;

  return node;
}
