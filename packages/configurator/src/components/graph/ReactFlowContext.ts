import { GraphNode } from "@somo/shared";
import { Edge, ReactFlowInstance } from "@xyflow/react";
import { createContext, useContext } from "react";
import * as Y from "yjs";
import { EditorType } from "./utils/editorConstants";

export type FocusedEditor = EditorType | null;

interface ReactFlowContextType {
  reactFlowInstance: ReactFlowInstance<GraphNode> | null;
  sldReactFlowInstance: ReactFlowInstance | null;
  setSldReactFlowInstance: (instance: ReactFlowInstance | null) => void;
  nodes: GraphNode[];
  edges: Edge<any>[];
  yDoc: Y.Doc;
  orgId: string;
  controllerId: string;
  readOnly: boolean;
  focusedEditor: FocusedEditor;
  setFocusedEditor: (editor: FocusedEditor) => void;
  // Helper method to get the appropriate ReactFlow instance based on context
  getReactFlowInstance: (
    context?: FocusedEditor,
  ) => ReactFlowInstance | ReactFlowInstance<GraphNode> | null;
}

// Create the context
export const ReactFlowContext = createContext<ReactFlowContextType | null>(
  null,
);

// Custom hook to use the ReactFlowContext
export const useReactFlowContext = () => {
  const context = useContext(ReactFlowContext);
  if (context === null) {
    throw new Error(
      "useReactFlowContext must be used within a ReactFlowContext.Provider",
    );
  }
  return context;
};
