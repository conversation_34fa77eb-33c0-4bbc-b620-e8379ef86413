import { EditorComponent } from "@/components/graph/comment/EditorComponent";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/classNames";
import { jsonToXmlFragment } from "@/lib/jsonToXmlFragment";
import { useUser } from "@clerk/clerk-react";
import {
  CommentNodeData,
  CommentNodeReply,
  GraphNodesByType,
  randomId,
} from "@somo/shared";
import Document from "@tiptap/extension-document";
import Paragraph from "@tiptap/extension-paragraph";
import Placeholder from "@tiptap/extension-placeholder";
import Text from "@tiptap/extension-text";
import { EditorContent, useEditor } from "@tiptap/react";
import { formatDistanceToNow } from "date-fns";
import { LocateFixed, Trash } from "lucide-react";
import React, { useEffect, useMemo, useState } from "react";
import * as Y from "yjs";
import { getNodeData, getNodes, getSldNodes } from "../Graph";
import { useReactFlowContext } from "../ReactFlowContext";
import { getInitials } from "./getInitials";

export function CommentSettings({
  node,
}: {
  node: GraphNodesByType["comment"];
}) {
  const {
    yDoc,
    focusedEditor: _focusedEditor,
    getReactFlowInstance,
  } = useReactFlowContext();
  const { user } = useUser();

  const editor = useEditor(
    {
      extensions: [
        Document,
        Paragraph,
        Text,
        Placeholder.configure({
          placeholder: "Write a reply...",
        }),
      ],
    },
    [],
  );

  const [isAddingReply, setIsAddingReply] = useState(false);
  const [repliesUpdateTrigger, setRepliesUpdateTrigger] = useState(0);

  // Set up YDoc observer for replies changes
  useEffect(() => {
    const nodeData = getNodeData(yDoc, node.id);
    const yReplies = nodeData?.get("replies") as Y.Array<Y.Map<any>>;

    if (!yReplies) {
      return;
    }

    const repliesObserver = () => {
      setRepliesUpdateTrigger((prev) => prev + 1);
    };

    yReplies.observeDeep(repliesObserver);

    return () => {
      yReplies.unobserveDeep(repliesObserver);
    };
  }, [yDoc, node.id]);

  // Get fresh replies data directly from YDoc instead of relying on potentially stale node prop
  const liveRepliesData = useMemo(() => {
    const nodeData = getNodeData(yDoc, node.id);
    const yReplies = nodeData?.get("replies") as Y.Array<Y.Map<any>>;
    if (!yReplies) {
      return [];
    }

    const repliesData = yReplies.toArray().map((reply: Y.Map<any>) => ({
      id: reply.get("id"),
      text: reply.get("text"),
      author: reply.get("author"),
      authorId: reply.get("authorId"),
      authorAvatar: reply.get("authorAvatar"),
      createdAt: reply.get("createdAt"),
    }));

    return repliesData;
  }, [yDoc, node.id, repliesUpdateTrigger]);

  return (
    <div className="absolute inset-0 flex flex-col overflow-y-auto">
      <CommentDetailsTitle>
        <div className="flex flex-col gap-4 mt-2 flex-grow font-normal">
          <CommentDetailsComment id={node.id} data={node.data} />
        </div>
        <div className="flex flex-row items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => {
              yDoc.transact(() => {
                // Try to delete from SLD nodes first (since SLD comments are more likely)
                const sldNodes = getSldNodes(yDoc);
                if (sldNodes?.has(node.id)) {
                  sldNodes.delete(node.id);
                } else {
                  // If not in SLD, try main graph
                  const mainNodes = getNodes(yDoc);
                  if (mainNodes?.has(node.id)) {
                    mainNodes.delete(node.id);
                  }
                }
              });
            }}
          >
            <Trash className="size-4" />
          </Button>

          <Button
            variant="ghost"
            size="icon"
            onClick={() => {
              // Use the helper method to get the appropriate ReactFlow instance
              const instanceToUse = getReactFlowInstance();
              instanceToUse?.fitView({
                padding: 0.5,
                nodes: [node],
              });
            }}
          >
            <LocateFixed className="size-4" />
          </Button>
        </div>
      </CommentDetailsTitle>

      {liveRepliesData && liveRepliesData.length > 0 && (
        <>
          <div className="flex flex-col gap-4 px-4 ml-8 mt-4">
            {liveRepliesData?.map((reply) => (
              <CommentDetailsCommentReply
                key={reply.id}
                id={node.id}
                data={reply}
              />
            ))}
          </div>
        </>
      )}
      {isAddingReply && (
        <div className="flex flex-col gap-2 mt-4 px-4 ml-8">
          <div className="flex flex-row">
            <Avatar className="size-6">
              <AvatarFallback className="text-[10px]">
                {getInitials(user?.fullName ?? "Anonymous User")}
              </AvatarFallback>
              <AvatarImage src={user?.imageUrl} className="rounded-full" />
            </Avatar>
            <div className={cn("flex flex-col ml-2 gap-1 w-full")}>
              <div className="flex flex-row">
                <div className="text-xs font-semibold truncate">
                  {user?.fullName ?? "Anonymous User"}
                </div>
              </div>

              <EditorContent editor={editor} className="text-xs mb-2 w-full" />
            </div>
          </div>
          <div className="flex flex-row w-full gap-2">
            <Button
              variant="outline"
              size="sm"
              className="w-full flex-grow"
              onClick={() => {
                const content = editor?.getJSON();
                if (!content) {
                  return;
                }
                yDoc.transact(() => {
                  const nodeData = getNodeData(yDoc, node.id);
                  const yReplies = nodeData?.get("replies") as Y.Array<
                    Y.Map<any>
                  >;

                  if (!yReplies) {
                    return;
                  }

                  const reply = new Y.Map();
                  reply.set("id", randomId());
                  reply.set("text", jsonToXmlFragment(content));
                  reply.set("author", user?.fullName ?? "Anonymous User");
                  reply.set("authorId", user?.id ?? "Anonymous User");
                  reply.set("authorAvatar", user?.imageUrl ?? "");
                  reply.set("createdAt", Date.now());
                  yReplies.push([reply]);
                  setIsAddingReply(false);
                });
                editor?.commands.setContent("");
              }}
            >
              Save Reply
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setIsAddingReply(false);
              }}
            >
              Discard
            </Button>
          </div>
        </div>
      )}
      {!isAddingReply && (
        <div className="flex flex-col gap-2 mt-4 px-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              setIsAddingReply(true);
            }}
          >
            Add reply
          </Button>
        </div>
      )}
    </div>
  );
}

function CommentDetailsTitle({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) {
  return (
    <div
      className={cn(
        "px-4 font-semibold text-xs mt-4 flex flex-row items-center",
        className,
      )}
    >
      {children}
    </div>
  );
}

function CommentDetailsComment({
  id,
  data,
}: {
  id: string;
  data: CommentNodeData;
}) {
  const { yDoc } = useReactFlowContext();
  const { user } = useUser();

  const commentTextFragment = useMemo(() => {
    const text = getNodeData(yDoc, id)?.get("text") as Y.XmlFragment;
    return text;
  }, [yDoc, id]);

  return (
    <div className="flex flex-row group">
      <Avatar className="size-6">
        <AvatarFallback className="text-[10px]">
          {getInitials(data.author)}
        </AvatarFallback>
        <AvatarImage src={data.authorAvatar} className="rounded-full" />
      </Avatar>
      <div className={cn("flex flex-col ml-2 gap-1 flex-grow")}>
        <div className="flex flex-row">
          <div className="text-xs font-semibold truncate">{data.author}</div>
          <div className="text-xs text-gray-500 ml-2 flex-shrink-0">
            {formatDistanceToNow(data.createdAt, {
              addSuffix: true,
            })}
          </div>
        </div>

        <EditorComponent
          fragment={commentTextFragment}
          className="text-xs nodrag cursor-text"
          readOnly={data.authorId !== user?.id}
          placeholder="Write a comment..."
        />
      </div>
    </div>
  );
}

function CommentDetailsCommentReply({
  id,
  data,
}: {
  id: string;
  data: CommentNodeReply;
}) {
  const { yDoc } = useReactFlowContext();
  const { user } = useUser();

  const commentTextFragment = useMemo(() => {
    const replies = getNodeData(yDoc, id)?.get("replies") as Y.Array<
      Y.Map<any>
    >;
    const text = replies
      ?.toArray()
      .find((r: Y.Map<any>) => r.get("id").toString() === data.id)
      ?.get("text") as Y.XmlFragment;
    return text;
  }, [yDoc, id, data.id]);

  return (
    <div className="flex flex-row group">
      <Avatar className="size-6">
        <AvatarFallback className="text-[10px]">
          {getInitials(data.author)}
        </AvatarFallback>
        <AvatarImage src={data.authorAvatar} className="rounded-full" />
      </Avatar>
      <div className={cn("flex flex-col ml-2 gap-1 flex-grow")}>
        <div className="flex flex-row">
          <div className="text-xs font-semibold truncate">{data.author}</div>
          <div className="text-xs text-gray-500 ml-2 flex-shrink-0">
            {formatDistanceToNow(data.createdAt, {
              addSuffix: true,
            })}
          </div>
        </div>

        <EditorComponent
          fragment={commentTextFragment}
          className="text-xs nodrag cursor-text"
          readOnly={data.authorId !== user?.id}
        />
      </div>
      {data.authorId === user?.id && (
        <div className="ml-2 flex-shrink-0 opacity-0 group-hover:opacity-100 duration-300">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => {
              const replies = getNodeData(yDoc, id)?.get("replies") as Y.Array<
                Y.Map<any>
              >;

              if (!replies) {
                return;
              }
              const index = replies
                .toArray()
                .findIndex(
                  (r: Y.Map<any>) => r.get("id").toString() === data.id,
                );
              if (index === -1) {
                return;
              }
              yDoc.transact(() => {
                replies.delete(index);
              });
            }}
          >
            <Trash className="size-4" />
          </Button>
        </div>
      )}
    </div>
  );
}
