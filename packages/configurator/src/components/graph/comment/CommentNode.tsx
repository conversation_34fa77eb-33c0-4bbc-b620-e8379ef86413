import {
  useGetSldNodeData,
  useGetSldYXmlFragment,
} from "@/components/sld-graph/useSldGraph";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { cn } from "@/lib/classNames";
import { useUser } from "@clerk/clerk-react";
import { CommentNodeData } from "@somo/shared";
import {
  Node,
  NodeProps,
  useNodeId,
  useUpdateNodeInternals,
} from "@xyflow/react";
import { formatDistanceToNow } from "date-fns";
import equal from "fast-deep-equal/react";
import React, { useEffect } from "react";
import { match } from "ts-pattern";
import { EditorComponent } from "./EditorComponent";
import { getInitials } from "./getInitials";

export const CommentNode = React.memo(InternalCommentNode, equal);

function InternalCommentNode({
  selected,
  dragging,
}: NodeProps<Node<CommentNodeData>>) {
  const { user } = useUser();

  const nodeId = useNodeId();
  // TODO: this is not good, it will get data from SLD only
  // We should either:
  // 1. have a CommentNode specific to sld-graph/ and to layout-graph/
  //    (we can certainly share a common CommentNode that takes data as props)
  // 2. use the same component, but define a Context with a different Provider
  //    for sld vs layout graphs (IMO, overkill for a single shared component)
  const data = useGetSldNodeData<CommentNodeData>(nodeId ?? "");
  const textFragment = useGetSldYXmlFragment(nodeId ?? "");

  const updateNodeInternals = useUpdateNodeInternals();
  useEffect(() => {
    if (nodeId) {
      updateNodeInternals(nodeId);
    }
  }, [nodeId, updateNodeInternals, data]);

  if (!data) {
    return (
      <div className="w-20 h-20 bg-gray-200 border border-gray-300 rounded flex items-center justify-center text-xs text-gray-500">
        Loading...
      </div>
    );
  }

  return (
    <div className="flex size-10 items-end justify-start relative">
      <div
        className={cn(
          "absolute size-10 bg-gray-100 rounded-tl-[20px] rounded-br-[20px] rounded-tr-[20px] shadow-md transition-all duration-100 p-[2px] border border-white",
          "hover:w-[280px] hover:h-auto hover:p-[6px] group",
          (dragging || selected) && "w-[280px] h-auto p-[6px] group",
          selected &&
            "outline outline-blue-500 outline-offset-[2px] outline-1 ",
        )}
      >
        <div className="flex flex-row">
          <Avatar className="size-9">
            <AvatarFallback className="text-sm">
              {getInitials(data.author)}
            </AvatarFallback>
            <AvatarImage src={data.authorAvatar} className="rounded-full" />
          </Avatar>
          <div
            className={cn(
              "flex-col ml-2 hidden group-hover:flex",
              (dragging || selected) && "flex",
            )}
          >
            <div className="flex flex-row">
              <div className="text-sm font-semibold truncate">
                {data.author}
              </div>
              <div className="text-sm text-gray-500 ml-2 flex-shrink-0">
                {relativeTime(data.createdAt)}
              </div>
            </div>

            <EditorComponent
              fragment={textFragment}
              className="text-sm nodrag cursor-text"
              readOnly={data.authorId !== user?.id || dragging || !selected}
              placeholder="Write a comment..."
            />

            <div className="text-sm text-gray-500">
              {match(data.replies.length)
                .with(0, () => "No replies")
                .with(1, () => "1 reply")
                .otherwise((count) => `${count} replies`)}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

function relativeTime(rawDate: number | string | Date) {
  try {
    const date = new Date(rawDate);
    if (isNaN(date.getTime())) {
      return "just now";
    }
    return formatDistanceToNow(date, {
      addSuffix: true,
    });
  } catch {
    return "just now";
  }
}
