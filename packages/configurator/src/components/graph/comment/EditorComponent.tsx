import { cn } from "@/lib/classNames";
import Collaboration from "@tiptap/extension-collaboration";
import Document from "@tiptap/extension-document";
import Paragraph from "@tiptap/extension-paragraph";
import Placeholder from "@tiptap/extension-placeholder";
import Text from "@tiptap/extension-text";
import { AnyExtension, EditorContent, useEditor } from "@tiptap/react";
import * as Y from "yjs";

export function EditorComponent({
  className,
  placeholder,
  readOnly,
  fragment,
  extensions,
}: {
  readOnly?: boolean;
  className?: string;
  placeholder?: string;
  fragment: Y.XmlFragment;
  extensions?: AnyExtension[];
}) {
  const editor = useEditor(
    {
      extensions: [
        ...(extensions ?? [Document, Paragraph, Text]),
        Collaboration.configure({
          fragment,
        }),
        Placeholder.configure({
          placeholder: placeholder ?? "...",
        }),
      ].filter(Boolean) as AnyExtension[],
      editable: !readOnly,
    },
    [fragment, placeholder, readOnly],
  );

  return (
    <EditorContent
      editor={editor}
      className={cn(readOnly && "select-none", className)}
    />
  );
}
