import { DataType } from "../../data/Data";
import { CanBoSLD } from "../../devices/canbus-controller/CanBoSLD";
import { RoomDimmerSLD } from "../../devices/room-dimmer/RoomDimmerSLD";
import { RoomSwitchSLD } from "../../devices/room-switch/RoomSwitchSLD";
import { RfServicePadSLD } from "../../devices/service-pad/RfServicePadSLD";
import { GhostNodeSLD } from "../../devices/sld/GhostNodeSLD";
import { GroundSLD } from "../../devices/sld/GroundSLD";
import { LampSLD } from "../../devices/sld/LampSLD";
import { LineSLD } from "../../devices/sld/LineSLD";
import { NeutralSLD } from "../../devices/sld/NeutralSLD";
import { PowerSupplySLD } from "../../devices/sld/PowerSupplySLD";
import { SldComponent } from "../../devices/sld/types";
import { RfFanControllerSLD } from "../../devices/somo-fan/RfFanControllerSLD";
import { RFShadesSLD } from "../../devices/somo-shades/RFShadesSLD";

// SLD Node Type Constants - compile-time names instead of magic strings
export const SLD_NODE_TYPES = {
  GROUND: "groundSLD",
  LINE: "lineSLD",
  NEUTRAL: "neutralSLD",
  POWER_SUPPLY: "powerSupplySLD",
  LAMP: "lampSLD",
  ROOM_SWITCH: "roomSwitchSLD",
  ROOM_DIMMER: "roomDimmerSLD",
  RF_FAN_CONTROLLER: "rfFanControllerSLD",
  RF_SERVICE_PAD: "rfServicePadSLD",
  CANBUS_CONTROLLER: "canBoSLD",
  GHOST: "ghostSLD",
  RF_SHADES: "rfShadesSLD",
} as const as Record<string, string>;

// SLD-specific node types (special nodes that are not also in the main graph)
const SLD_SPECIFIC_NODE_TYPES = new Set<SldNodeType>([
  SLD_NODE_TYPES.GROUND,
  SLD_NODE_TYPES.LINE,
  SLD_NODE_TYPES.NEUTRAL,
  SLD_NODE_TYPES.POWER_SUPPLY,
  SLD_NODE_TYPES.LAMP,
  SLD_NODE_TYPES.GHOST,
] as const);

type SldDataType = DataType | "powerSupply" | "lamp" | "ghost";

// SLD-specific DataTypes (subset of DATA_TO_SLD_MAPPING keys)
const SLD_SPECIFIC_DATATYPES = new Set<SldDataType>([
  "ground",
  "line",
  "neutral",
  "powerSupply",
  "lamp",
  "ghost",
]);

const MIRRORED_DATATYPES = new Set<DataType>([
  "roomSwitch",
  "roomDimmer",
  "servicePad",
  "canbusController",
  "somoShades",
]);

// Component mapping for easy access - uses ReactFlow type names consistently
export const SLD_COMPONENTS: Record<string, SldComponent> = {
  [SLD_NODE_TYPES.GROUND]: GroundSLD,
  [SLD_NODE_TYPES.LINE]: LineSLD,
  [SLD_NODE_TYPES.NEUTRAL]: NeutralSLD,
  [SLD_NODE_TYPES.POWER_SUPPLY]: PowerSupplySLD,
  [SLD_NODE_TYPES.LAMP]: LampSLD,
  [SLD_NODE_TYPES.ROOM_SWITCH]: RoomSwitchSLD,
  [SLD_NODE_TYPES.ROOM_DIMMER]: RoomDimmerSLD,
  [SLD_NODE_TYPES.RF_FAN_CONTROLLER]: RfFanControllerSLD,
  [SLD_NODE_TYPES.RF_SERVICE_PAD]: RfServicePadSLD,
  [SLD_NODE_TYPES.CANBUS_CONTROLLER]: CanBoSLD,
  [SLD_NODE_TYPES.GHOST]: GhostNodeSLD,
  [SLD_NODE_TYPES.RF_SHADES]: RFShadesSLD,
};

// Type for SLD node types based on existing config
export type SldNodeType = keyof typeof SLD_COMPONENTS;

// Mapping from main graph types to their corresponding SLD components
export const MAIN_GRAPH_TO_SLD_MAPPING: Record<string, SldComponent> = {
  roomSwitchContainer: SLD_COMPONENTS[SLD_NODE_TYPES.ROOM_SWITCH],
  roomDimmerContainer: SLD_COMPONENTS[SLD_NODE_TYPES.ROOM_DIMMER],
  somoFanContainer: SLD_COMPONENTS[SLD_NODE_TYPES.RF_FAN_CONTROLLER],
  servicePadContainer: SLD_COMPONENTS[SLD_NODE_TYPES.RF_SERVICE_PAD],
  canbusControllerContainer: SLD_COMPONENTS[SLD_NODE_TYPES.CANBUS_CONTROLLER],
  somoShadesContainer: SLD_COMPONENTS[SLD_NODE_TYPES.RF_SHADES],
};

// Mapping from main graph types to their corresponding SLD components
export const DATA_TO_SLD_MAPPING: Partial<Record<DataType, SldComponent>> = {
  ground: SLD_COMPONENTS[SLD_NODE_TYPES.GROUND],
  line: SLD_COMPONENTS[SLD_NODE_TYPES.LINE],
  neutral: SLD_COMPONENTS[SLD_NODE_TYPES.NEUTRAL],
  roomSwitch: SLD_COMPONENTS[SLD_NODE_TYPES.ROOM_SWITCH],
  roomDimmer: SLD_COMPONENTS[SLD_NODE_TYPES.ROOM_DIMMER],
  somoFan: SLD_COMPONENTS[SLD_NODE_TYPES.RF_FAN_CONTROLLER],
  servicePad: SLD_COMPONENTS[SLD_NODE_TYPES.RF_SERVICE_PAD],
  canbusController: SLD_COMPONENTS[SLD_NODE_TYPES.CANBUS_CONTROLLER],
  somoShades: SLD_COMPONENTS[SLD_NODE_TYPES.RF_SHADES],
};

// Helper function to get component from either mapping
export function getComponent(type: string): SldComponent | undefined {
  return (
    SLD_COMPONENTS[type as keyof typeof SLD_COMPONENTS] ||
    MAIN_GRAPH_TO_SLD_MAPPING[type as keyof typeof MAIN_GRAPH_TO_SLD_MAPPING]
  );
}

// Helper function to get ReactFlow type from component
export function getNodeTypeFromDataType(dataType: string): string | undefined {
  const component = getComponent(dataType);
  if (!component) {
    return undefined;
  }

  // Find the ReactFlow type by looking up the component in SLD_COMPONENTS
  for (const [type, comp] of Object.entries(SLD_COMPONENTS)) {
    if (comp === component) {
      return type;
    }
  }

  return undefined;
}

export function isMirroredDataType(dataType: DataType): boolean {
  return MIRRORED_DATATYPES.has(dataType);
}

// Helper function to get ReactFlow type from main graph type
export function getReactFlowTypeFromMainGraphType(
  mainGraphType: string,
): string | undefined {
  const component = MAIN_GRAPH_TO_SLD_MAPPING[mainGraphType];
  if (!component) {
    return undefined;
  }

  // Find the ReactFlow type by looking up the component in SLD_COMPONENTS
  for (const [type, comp] of Object.entries(SLD_COMPONENTS)) {
    if (comp === component) {
      return type;
    }
  }

  return undefined;
}

// Helper function to get SLD node type from main graph node type (alias for consistency)
export function getSldNodeTypeFromMainGraphType(
  mainGraphType: string,
): string | null {
  // Get the ReactFlow type from the main graph type
  const reactFlowType = getReactFlowTypeFromMainGraphType(mainGraphType);

  if (reactFlowType) {
    return reactFlowType;
  }

  return null;
}

export function getSldNodeTypeFromDataType(dataType: string): string | null {
  const component = DATA_TO_SLD_MAPPING[dataType as DataType];
  if (!component) {
    return null;
  }

  // Find the SLD type that corresponds to this component
  for (const [sldType, comp] of Object.entries(SLD_COMPONENTS)) {
    if (comp === component) {
      return sldType;
    }
  }
  return null;
}

// Helper function to check if a node type is SLD-specific (not mirrored)
export function isSldSpecificNode(nodeType: string): nodeType is SldNodeType {
  return SLD_SPECIFIC_NODE_TYPES.has(nodeType as SldNodeType);
}

// Helper function to determine edge style based on handle ID
export function determineEdgeStyle(
  sourceNode: { type?: string } | undefined,
  targetNode: { type?: string } | undefined,
  handleId?: string | null,
): {
  stroke: string;
  strokeWidth: number;
  strokeOpacity: number;
  strokeDasharray?: string;
} {
  // Check for light handles first (highest priority) - this overrides everything
  if (
    handleId &&
    (handleId === "via1" ||
      handleId === "via2" ||
      handleId === "via3" ||
      handleId === "via" ||
      handleId === "viaLow" ||
      handleId === "viaMed" ||
      handleId === "viaHigh" ||
      handleId === "makeUpRoomButton" ||
      handleId === "doorbellButton" ||
      handleId === "doNotDisturbButton")
  ) {
    return {
      stroke: "#eab308", // Yellow
      strokeWidth: 2,
      strokeOpacity: 0.8,
    };
  }

  // Check source node for edge style override
  if (sourceNode?.type) {
    const sourceComponent = getComponent(sourceNode.type);
    if (sourceComponent?.overrideEdgeColor && sourceComponent?.getEdgeStyle) {
      return sourceComponent.getEdgeStyle(handleId || "");
    }
  }

  // Check target node for edge style override
  if (targetNode?.type) {
    const targetComponent = getComponent(targetNode.type);
    if (targetComponent?.overrideEdgeColor && targetComponent?.getEdgeStyle) {
      return targetComponent.getEdgeStyle(handleId || "");
    }
  }

  // Default edge style
  return {
    stroke: "#4D9FC9",
    strokeWidth: 2,
    strokeOpacity: 0.9,
  };
}
