import { <PERSON><PERSON>h<PERSON><PERSON>, isAnchor, isIdle, isManipulated } from "@somo/shared";
import { Edge } from "@xyflow/react";
import { useFlags } from "launchdarkly-react-client-sdk";
import { useMemo } from "react";
import * as Y from "yjs";
import { generateVirtualEdges } from "./generateVirtualEdges";

export function useVisibleNodesAndEdges({
  yDoc,
  nodes,
  edges,
}: {
  yDoc: Y.Doc;
  nodes: GraphNode[];
  edges: Edge[];
}) {
  const flags = useFlags();

  return useMemo(() => {
    const idleAnchors = nodes.filter(isAnchor).filter(isIdle);
    const visibleNodes = nodes.filter((n) => {
      const isChildOfIdleAnchor = idleAnchors.some((a) => a.id === n.parentId);

      // feature flag for RF switches
      if (
        !flags.roomSwitch &&
        n.type &&
        ["roomSwitchAnchor", "roomSwitchContainer"].includes(n.type)
      ) {
        return false;
      }

      // feature flag for virtual buttons
      if (
        !flags.virtualButton &&
        n.type &&
        ["virtualButtonAnchor", "virtualButtonContainer"].includes(n.type)
      ) {
        return false;
      }

      // feature flag for canbus controllers
      if (
        !flags.canbo &&
        n.type &&
        ["canbusControllerAnchor", "canbusControllerContainer"].includes(n.type)
      ) {
        return false;
      }

      // feature flag for RF dimmers
      if (
        !flags.roomDimmer &&
        n.type &&
        ["roomDimmerAnchor", "roomDimmerContainer"].includes(n.type)
      ) {
        return false;
      }

      // feature flag for outlet dimmers
      if (
        !flags.outletDimmer &&
        n.type &&
        ["outletDimmerAnchor", "outletDimmerContainer"].includes(n.type)
      ) {
        return false;
      }

      // feature flag for presence sensors
      if (
        !flags.presenceSensor &&
        n.type &&
        ["presenceSensorAnchor", "presenceSensorContainer"].includes(n.type)
      ) {
        return false;
      }

      // feature flag for door sensors
      if (
        !flags.doorSensor &&
        n.type &&
        ["doorSensorAnchor", "doorSensorContainer"].includes(n.type)
      ) {
        return false;
      }

      // feature flag for thermostats
      if (
        !flags.thermostat &&
        n.type &&
        ["somoThermostatAnchor", "somoThermostatContainer"].includes(n.type)
      ) {
        return false;
      }

      // feature flag for IR controllers
      if (
        !flags.irController &&
        n.type &&
        ["somoIrControllerAnchor", "somoIrControllerContainer"].includes(n.type)
      ) {
        return false;
      }

      // feature flag for shades
      if (
        !flags.shades &&
        n.type &&
        ["somoShadesAnchor", "somoShadesContainer"].includes(n.type)
      ) {
        return false;
      }

      // feature flag for fans
      if (
        !flags.fan &&
        n.type &&
        ["somoFanAnchor", "somoFanContainer"].includes(n.type)
      ) {
        return false;
      }

      // feature flag for service pads
      if (
        !flags.servicePad &&
        n.type &&
        ["servicePadAnchor", "servicePadContainer"].includes(n.type)
      ) {
        return false;
      }

      return isManipulated(n) || !isChildOfIdleAnchor;
    });

    const virtualEdges = generateVirtualEdges(yDoc, nodes);

    return {
      visibleNodes,
      visibleEdges: edges.concat(virtualEdges),
    };
  }, [nodes, edges, flags]);
}
