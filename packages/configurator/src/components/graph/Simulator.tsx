import { BaseStationAnchorNode } from "@/components/devices/base-station/BaseStationAnchorNode";
import { BaseStationContainerNodeSimulator } from "@/components/devices/base-station/BaseStationContainerNode";
import { CanBusControllerAnchorNode } from "@/components/devices/canbus-controller/CanBusControllerAnchorNode";
import { CanBusControllerContainerNodeSimulator } from "@/components/devices/canbus-controller/CanBusControllerContainerNode";
import { DoorSensorAnchorNode } from "@/components/devices/door-sensor/DoorSensorAnchorNode";
import { DoorSensorContainerNodeSimulator } from "@/components/devices/door-sensor/DoorSensorContainerNode";
import { OutletDimmerAnchorNode } from "@/components/devices/outlet-dimmer/OutletDimmerAnchorNode";
import { OutletDimmerContainerNodeSimulator } from "@/components/devices/outlet-dimmer/OutletDimmerContainerNode";
import { PresenceSensorAnchorNode } from "@/components/devices/presence-sensor/PresenceSensorAnchorNode";
import { PresenceSensorContainerNodeSimulator } from "@/components/devices/presence-sensor/PresenceSensorContainerNode";
import { RoomDimmerAnchorNode } from "@/components/devices/room-dimmer/RoomDimmerAnchorNode";
import { RoomDimmerContainerNodeSimulator } from "@/components/devices/room-dimmer/RoomDimmerContainerNode";
import { RoomSwitchAnchorNode } from "@/components/devices/room-switch/RoomSwitchAnchorNode";
import { RoomSwitchContainerNodeSimulator } from "@/components/devices/room-switch/RoomSwitchContainerNode";
import { ServicePadAnchorNode } from "@/components/devices/service-pad/ServicePadAnchorNode";
import { ServicePadContainerNodeSimulator } from "@/components/devices/service-pad/ServicePadContainerNode";
import { SomoFanAnchorNode } from "@/components/devices/somo-fan/SomoFanAnchorNode";
import { SomoFanContainerNodeSimulator } from "@/components/devices/somo-fan/SomoFanContainerNode";
import { SomoIrControllerAnchorNode } from "@/components/devices/somo-ir-controller/SomoIrControllerAnchorNode";
import { SomoIrControllerContainerNodeSimulator } from "@/components/devices/somo-ir-controller/SomoIrControllerContainerNode";
import { SomoShadesAnchorNode } from "@/components/devices/somo-shades/SomoShadesAnchorNode";
import { SomoShadesContainerNodeSimulator } from "@/components/devices/somo-shades/SomoShadesContainerNode";
import { SomoThermostatAnchorNode } from "@/components/devices/somo-thermostat/SomoThermostatAnchorNode";
import { SomoThermostatContainerNodeSimulator } from "@/components/devices/somo-thermostat/SomoThermostatContainerNode";
import { VirtualButtonAnchorNode } from "@/components/devices/virtual-button/VirtualButtonAnchorNode";
import { VirtualButtonContainerNodeSimulator } from "@/components/devices/virtual-button/VirtualButtonContainerNode";
import { ExecutionContextProvider } from "@/contexts/ExecutionContext";
import { useSimulationExecutionContextProvider } from "@/contexts/ExecutionContextSimulationProvider";
import { MainModule } from "@/types/basestation";
import { GraphNode } from "@somo/shared";
import {
  applyNodeChanges,
  NodeChange,
  ReactFlow,
  ReactFlowInstance,
} from "@xyflow/react";
import { Allotment } from "allotment";
import { useEffect, useRef, useState } from "react";
import * as Y from "yjs";
import { PaneContainer, PaneTitle } from "../ui/Layout";
import { LightNodeSimulator } from "./controllables/light/LightNode";
import { Graph } from "./Graph";
import { ImageNode } from "./image/ImageNode";
import { SceneNodeSimulator } from "./scene/SceneNode";
import { SectionNodeSimulator } from "./section/SectionNode";
import { LogEntry } from "./simulator/LogEntry";
import { LogEntryLine } from "./simulator/LogEntryLine";
import { Room } from "./useRoom";
import { useVisibleNodesAndEdges } from "./useVisibleNodesAndEdges";

const nodeTypes = {
  image: ImageNode,
  section: SectionNodeSimulator,
  scene: SceneNodeSimulator,
  light: LightNodeSimulator,
  virtualButtonAnchor: VirtualButtonAnchorNode,
  virtualButtonContainer: VirtualButtonContainerNodeSimulator,
  baseStationAnchor: BaseStationAnchorNode,
  baseStationContainer: BaseStationContainerNodeSimulator,
  canbusControllerAnchor: CanBusControllerAnchorNode,
  canbusControllerContainer: CanBusControllerContainerNodeSimulator,
  roomSwitchAnchor: RoomSwitchAnchorNode,
  roomSwitchContainer: RoomSwitchContainerNodeSimulator,
  roomDimmerAnchor: RoomDimmerAnchorNode,
  roomDimmerContainer: RoomDimmerContainerNodeSimulator,
  outletDimmerAnchor: OutletDimmerAnchorNode,
  outletDimmerContainer: OutletDimmerContainerNodeSimulator,
  presenceSensorAnchor: PresenceSensorAnchorNode,
  presenceSensorContainer: PresenceSensorContainerNodeSimulator,
  doorSensorAnchor: DoorSensorAnchorNode,
  doorSensorContainer: DoorSensorContainerNodeSimulator,
  somoShadesAnchor: SomoShadesAnchorNode,
  somoShadesContainer: SomoShadesContainerNodeSimulator,
  somoFanAnchor: SomoFanAnchorNode,
  somoFanContainer: SomoFanContainerNodeSimulator,
  servicePadAnchor: ServicePadAnchorNode,
  servicePadContainer: ServicePadContainerNodeSimulator,
  somoThermostatAnchor: SomoThermostatAnchorNode,
  somoThermostatContainer: SomoThermostatContainerNodeSimulator,
  somoIrControllerAnchor: SomoIrControllerAnchorNode,
  somoIrControllerContainer: SomoIrControllerContainerNodeSimulator,
};

export function Simulator({
  basestation,
  room,
  graph,
  yDoc,
}: {
  basestation: MainModule;
  room: Room;
  graph: Graph;
  yDoc: Y.Doc;
}) {
  const { nodes, edges } = graph;
  const [renderedNodes, setRenderedNodes] = useState<GraphNode[]>([]);
  const [logEntries, setLogEntries] = useState<LogEntry[]>([]);
  const [highlightedNodeId, setHighlightedNodeId] = useState<string | null>(
    null,
  );

  useEffect(() => {
    setRenderedNodes(
      nodes
        .filter((n) => Object.keys(nodeTypes).includes(n.type ?? ""))
        .map((node) => {
          return {
            ...node,
            deletable: false,
            draggable: false,
            style:
              node.id === highlightedNodeId
                ? {
                    ...node.style,
                    borderRadius: "2px",
                    boxShadow: "0 0 3px 3px oklch(82.8% 0.189 84.429)",
                    zIndex: 1000,
                  }
                : node.style,
          } as GraphNode;
        }),
    );
  }, [nodes, highlightedNodeId]);

  const { visibleNodes, visibleEdges } = useVisibleNodesAndEdges({
    yDoc,
    nodes: renderedNodes,
    edges,
  });

  const reactFlowInstance = useRef<ReactFlowInstance<GraphNode> | null>(null);
  const provider = useSimulationExecutionContextProvider({
    yDoc,
    basestation,
    graph,
    room,
    captureLog(entry) {
      setLogEntries((prev) => [...prev, entry]);
    },
  });

  const [panesSizes, setPanesSizes] = useState([undefined, 400]);

  return (
    <Allotment separator={false} onDragEnd={setPanesSizes}>
      <Allotment.Pane
        className="absolute inset-0 flex flex-row py-2 pr-1 pl-2"
        preferredSize={panesSizes[0]}
      >
        <ExecutionContextProvider value={provider}>
          <PaneContainer className="h-full">
            {/* Allow users to click through the visible edges */}
            <style>{`.simulator .react-flow__edge { pointer-events: none; }`}</style>
            <ReactFlow
              className="simulator"
              onInit={(instance) => {
                reactFlowInstance.current = instance;
                instance.fitView();
              }}
              nodes={visibleNodes}
              edges={visibleEdges}
              elevateEdgesOnSelect={false}
              elevateNodesOnSelect={false}
              nodeTypes={nodeTypes}
              proOptions={{ hideAttribution: true }}
              panOnDrag={[1]}
              selectionOnDrag
              panOnScroll
              zoomOnDoubleClick={false}
              edgesFocusable={false}
              multiSelectionKeyCode="Shift"
              onNodesChange={(changes: NodeChange<GraphNode>[]) => {
                setRenderedNodes(applyNodeChanges(changes, renderedNodes));
              }}
            />
          </PaneContainer>
        </ExecutionContextProvider>
      </Allotment.Pane>
      <Allotment.Pane
        maxSize={600}
        minSize={300}
        className="absolute inset-0 flex flex-row py-2 pr-2 pl-1"
        preferredSize={panesSizes[1]}
      >
        <PaneContainer className="flex flex-col flex-shrink-0">
          <PaneTitle>Simulation logs</PaneTitle>
          <div className="flex flex-col gap-0 p-3 font-mono text-xs overflow-y-auto h-full">
            {logEntries
              .sort((a, b) => b.timestamp - a.timestamp)
              .map((entry, index) => (
                <LogEntryLine
                  key={index}
                  entry={entry}
                  onMouseEnter={() => {
                    const node = renderedNodes.find(
                      (n) => n.id === entry.device.id,
                    );
                    if (node) {
                      setHighlightedNodeId(node.parentId ?? node.id);
                    }
                  }}
                  onMouseLeave={() => setHighlightedNodeId(null)}
                />
              ))}
          </div>
        </PaneContainer>
      </Allotment.Pane>
    </Allotment>
  );
}
