import {
  RelayOutputButton,
  RelayOutputButtonSimulator,
} from "@/components/devices/canbus-controller/RelayOutputButton";
import {
  ZeroToTenVoltDimmerButton,
  ZeroToTenVoltDimmerButtonSimulator,
} from "@/components/devices/canbus-controller/ZeroToTenVoltDimmerButton";
import {
  Room<PERSON><PERSON>Button,
  RoomDimmerButtonSimulator,
} from "@/components/devices/room-dimmer/RoomDimmerButton";
import {
  RoomSwitchButton,
  RoomSwitchButtonSimulator,
} from "@/components/devices/room-switch/RoomSwitchButton";
import {
  SomfyShadesButton,
  SomfyShadesButtonSimulator,
} from "@/components/devices/somfy-shades/SomfyShadesButton";
import {
  SomoFanButton,
  SomoFanButtonSimulator,
} from "@/components/devices/somo-fan/SomoFanButton";
import {
  SomoIrControllerButton,
  SomoIrControllerButtonSimulator,
} from "@/components/devices/somo-ir-controller/SomoIrControllerButton";
import {
  Somo<PERSON><PERSON><PERSON><PERSON><PERSON>on,
  SomoShadesButtonSimulator,
} from "@/components/devices/somo-shades/SomoShadesButton";
import {
  SomoThermostatButton,
  SomoThermostatButtonSimulator,
} from "@/components/devices/somo-thermostat/SomoThermostatButton";
import { cn } from "@/lib/classNames";
import { Device, SectionNodeData } from "@somo/shared";
import { NodeProps, NodeResizer, useUpdateNodeInternals } from "@xyflow/react";
import equal from "fast-deep-equal/react";
import React, { useEffect } from "react";
import { match } from "ts-pattern";

export const SectionNode = React.memo(RegularNode, equal);
export const SectionNodeSimulator = React.memo(SimulatorNode, equal);

interface Props extends NodeProps {
  data: SectionNodeData;
  type: "section";
}

function RegularNode(props: Props) {
  return (
    <BaseNode
      {...props}
      renderDevice={(device, direction) =>
        match(device)
          .with({ type: "roomSwitch" }, (device) => (
            <RoomSwitchButton
              key={device.id}
              device={device}
              direction={direction}
            />
          ))
          .with({ type: "roomDimmer" }, (device) => (
            <RoomDimmerButton
              key={device.id}
              device={device}
              direction={direction}
            />
          ))
          .with({ type: "somoShades" }, (device) => (
            <SomoShadesButton
              key={device.id}
              device={device}
              direction={direction}
            />
          ))
          .with({ type: "somfyShades" }, (device) => (
            <SomfyShadesButton
              key={device.id}
              device={device}
              direction={direction}
            />
          ))
          .with({ type: "somoFan" }, (device) => (
            <SomoFanButton
              key={device.id}
              device={device}
              direction={direction}
            />
          ))
          .with({ type: "somoThermostat" }, (device) => (
            <SomoThermostatButton
              key={device.id}
              device={device}
              direction={direction}
            />
          ))
          .with({ type: "somoIrController" }, (device) => (
            <SomoIrControllerButton
              key={device.id}
              device={device}
              direction={direction}
            />
          ))
          .with({ type: "zeroToTenVoltDimmer" }, (device) => (
            <ZeroToTenVoltDimmerButton
              key={device.id}
              device={device}
              direction={direction}
            />
          ))
          .with({ type: "relayOutput" }, (device) => (
            <RelayOutputButton
              key={device.id}
              device={device}
              direction={direction}
            />
          ))
          // Trying to perform an exhaustive check results in a type error:
          // `Type instantiation is excessively deep and possibly infinite`
          // => Use `otherwise` instead.
          .otherwise(() => null)
      }
    />
  );
}

function SimulatorNode(props: Props) {
  return (
    <BaseNode
      {...props}
      className="nodrag"
      renderDevice={(device, direction) =>
        match(device)
          .with({ type: "roomSwitch" }, (device) => (
            <RoomSwitchButtonSimulator
              key={device.id}
              device={device}
              direction={direction}
            />
          ))
          .with({ type: "roomDimmer" }, (device) => (
            <RoomDimmerButtonSimulator
              key={device.id}
              device={device}
              direction={direction}
            />
          ))
          .with({ type: "somoShades" }, (device) => (
            <SomoShadesButtonSimulator
              key={device.id}
              device={device}
              direction={direction}
            />
          ))
          .with({ type: "somfyShades" }, (device) => (
            <SomfyShadesButtonSimulator
              key={device.id}
              device={device}
              direction={direction}
            />
          ))
          .with({ type: "somoFan" }, (device) => (
            <SomoFanButtonSimulator
              key={device.id}
              device={device}
              direction={direction}
            />
          ))
          .with({ type: "somoThermostat" }, (device) => (
            <SomoThermostatButtonSimulator
              key={device.id}
              device={device}
              direction={direction}
            />
          ))
          .with({ type: "somoIrController" }, (device) => (
            <SomoIrControllerButtonSimulator
              key={device.id}
              device={device}
              direction={direction}
            />
          ))
          .with({ type: "zeroToTenVoltDimmer" }, (device) => (
            <ZeroToTenVoltDimmerButtonSimulator
              key={device.id}
              device={device}
              direction={direction}
            />
          ))
          .with({ type: "relayOutput" }, (device) => (
            <RelayOutputButtonSimulator
              key={device.id}
              device={device}
              direction={direction}
            />
          ))
          // Trying to perform an exhaustive check results in a type error:
          // `Type instantiation is excessively deep and possibly infinite`
          // => Use `otherwise` instead.
          .otherwise(() => null)
      }
    />
  );
}

function BaseNode({
  selected,
  draggable,
  id,
  data,
  dragging,
  renderDevice,
  className = "",
}: Props & {
  renderDevice: (
    device: Device,
    direction: "left" | "right",
  ) => React.ReactNode;
  className?: string;
}) {
  const direction = data.direction ?? "right";

  const updateNodeInternals = useUpdateNodeInternals();

  useEffect(() => {
    const timeout = setTimeout(() => {
      updateNodeInternals(id);
    }, 1);
    return () => clearTimeout(timeout);
  }, [updateNodeInternals, id]);

  const devices = Object.values(data.devices ?? {}).sort(
    (a, b) => a.sortIndex - b.sortIndex,
  );

  useEffect(() => {
    updateNodeInternals(id);
  }, [devices, updateNodeInternals, id, selected, dragging, direction]);

  return (
    <>
      {selected && draggable && <NodeResizer minWidth={60} minHeight={200} />}
      <div className={cn("absolute inset-[4px]", className)}>
        <div
          className={cn(
            "overflow-visible z-0",
            "absolute inset-0 pointer-events-none",
            direction === "right" && "border-r border-gray-400 items-end",
            direction === "left" && "border-l border-gray-400",
          )}
        >
          <div
            className={cn(
              "px-2 text-sm font-medium text-[#919191]",
              direction === "right" && "text-right w-full",
            )}
          >
            {data.title}
          </div>
          <div
            className={cn(
              "w-full flex flex-row flex-wrap px-2 mt-2 gap-2",
              direction === "right" && "justify-end ",
            )}
          >
            {devices.map((device) => renderDevice(device, direction))}
          </div>
        </div>
      </div>
    </>
  );
}
