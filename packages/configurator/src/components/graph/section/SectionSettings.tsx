import { getData } from "@/components/data/useData";
import { cleanupActionsForDevice } from "@/components/devices/deviceUtils";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { TooltipDropdownMenu } from "@/components/ui/tooltip-dropdown-menu";
import { cn } from "@/lib/classNames";
import { objectToYMap } from "@/lib/yjsUtils";
import {
  defaultDmxDevice,
  filterNodes,
  GraphNodesByType,
  randomId,
  RelayOutputDevice,
  RoomDimmerDevice,
  RoomSwitchDevice,
  roomSwitchViaIds,
  SomfyShadesBaseDevice,
  SomoFanDevice,
  SomoIrControllerDevice,
  SomoShadesDevice,
  SomoThermostatDevice,
  ZeroToTenVoltDimmerDevice,
} from "@somo/shared";
import { Position } from "@xyflow/react";
import {
  ArrowRightSquareIcon,
  ChevronDown,
  PlusIcon,
  Trash2,
  Trash2Icon,
} from "lucide-react";
import React, { useMemo } from "react";
import { match } from "ts-pattern";
import * as Y from "yjs";
import { getNodeData } from "../Graph";
import { useReactFlowContext } from "../ReactFlowContext";
import {
  SettingsGroup,
  SettingsGroupContent,
  SettingsGroupHeader,
  SettingsLabel,
  SettingsRow,
} from "../settings/SettingsGroup";
import { SettingsInput } from "../settings/SettingsInput";

function SomoDeviceSettings({
  device,
  nodeId,
  collapsed,
  onCollapseChange,
  readOnly,
}: {
  device: RoomSwitchDevice;
  nodeId: string;
  collapsed: boolean;
  onCollapseChange: (isCollapsed: boolean) => void;
  readOnly?: boolean;
}) {
  const { nodes, yDoc, reactFlowInstance } = useReactFlowContext();

  const yDevicesMap = useMemo(() => {
    const dataMap = getNodeData(yDoc, nodeId);
    const devicesMap = dataMap?.get("devices") as Y.Map<any> | undefined;
    return devicesMap;
  }, [yDoc, nodeId]);

  const yDevice = useMemo(() => {
    try {
      const dataMap = getNodeData(yDoc, nodeId);
      const devicesMap = dataMap?.get("devices") as Y.Map<any> | undefined;
      const deviceMap = devicesMap?.get(device.id) as Y.Map<any> | null;
      return deviceMap;
    } catch (e) {
      console.error(e);
      return null;
    }
  }, [yDoc, nodeId, device.id]);

  const light = useMemo(() => {
    const switchNodes = filterNodes(nodes, "roomSwitchContainer");
    const lights = [];
    for (const switchNode of switchNodes) {
      const data = getData(yDoc, switchNode?.data.dataId, "roomSwitch");
      if (!data) {
        console.error("Could not retrieve data for node: ", switchNode.id);
        return null;
      }
      const via1 = data.via1;
      const via2 = data.via2;
      const via3 = data.via3;
      if (via1.hasLoad) {
        lights.push({ nodeId: switchNode.id, id: "via1", ...via1 });
      }
      if (via2.hasLoad) {
        lights.push({ nodeId: switchNode.id, id: "via2", ...via2 });
      }
      if (via3.hasLoad) {
        lights.push({ nodeId: switchNode.id, id: "via3", ...via3 });
      }
    }

    return lights.find(
      (light) => light.id === device.viaId && light.nodeId === device.nodeId,
    );
  }, [nodes, yDoc, device.viaId, device.nodeId]);

  return (
    <>
      <SettingsRow>
        <div
          className={cn(
            "flex flex-row gap-0.5 text-xs font-bold text-gray-500 cursor-pointer items-center",
            !collapsed && "text-gray-800",
            !light && "text-red-500",
          )}
          onClick={() => {
            onCollapseChange(!collapsed);
          }}
        >
          <ChevronDown
            className={cn(
              "size-4 transition-transform duration-300 -ml-1",
              collapsed && "-rotate-90",
            )}
            onClick={() => {
              onCollapseChange(!collapsed);
            }}
          />
          <span
            className="hover:text-gray-400"
            onClick={() => {
              onCollapseChange(!collapsed);
            }}
          >
            {light ? light.lightName : "Light missing"}
          </span>
          <span className="flex-grow text-gray-500 text-xs font-normal">
            (from RF Switch)
          </span>

          <ArrowRightSquareIcon
            className="size-4 flex-shrink-0 mr-1 opacity-100 hover:opacity-40 cursor-pointer"
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();

              if (!light) {
                return;
              }
              const linkedNode = nodes.find((n) => n.id === light.nodeId);
              if (!linkedNode) {
                return;
              }
              const parentNode = nodes.find(
                (n) => n.id === linkedNode.parentId,
              );
              if (!parentNode) {
                return;
              }

              reactFlowInstance?.setNodes((nodes) =>
                nodes.map((n) => ({
                  ...n,
                  selected:
                    n.id === linkedNode.parentId || n.id === linkedNode.id,
                })),
              );
              reactFlowInstance?.setCenter(
                parentNode.position.x,
                parentNode.position.y,
                {
                  duration: 800,
                  zoom: reactFlowInstance.getViewport().zoom ?? 1,
                },
              );
            }}
          />
        </div>
      </SettingsRow>
      {!collapsed && (
        <>
          <SettingsRow className="flex flex-row items-center gap-2 -mt-2 pl-3.5">
            <Checkbox
              id={`${device.id}-showLabel`}
              checked={device.showLabel}
              disabled={readOnly}
              onCheckedChange={(checked) => {
                if (!yDevice) {
                  return;
                }
                yDevice.set("showLabel", checked);
              }}
            />
            <Label
              htmlFor={`${device.id}-showLabel`}
              className="text-gray-500 text-xs font-semibold flex-shrink-0 truncate flex-grow"
            >
              Show label
            </Label>
            {!readOnly && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="flex-shrink-0 -mr-1.5"
                    onClick={() => {
                      if (!yDevicesMap) {
                        return;
                      }
                      // Clean up orphaned actions before deleting the device
                      if (yDoc) {
                        cleanupActionsForDevice(yDoc, device.id);
                      }
                      yDevicesMap.delete(device.id);
                    }}
                  >
                    <Trash2 className="size-4 flex-shrink-0" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Delete Device</TooltipContent>
              </Tooltip>
            )}
          </SettingsRow>
          <SettingsRow className="ml-3 -mt-2">
            <SettingsLabel htmlFor="anchorPosition">
              Anchor Position
            </SettingsLabel>
            <Select
              value={device.anchorPosition ?? Position.Left}
              disabled={readOnly}
              onValueChange={(value) => {
                if (!yDevice) {
                  return;
                }
                yDevice.set("anchorPosition", value);
              }}
            >
              <SelectTrigger className="h-10">
                <SelectValue placeholder="Select position" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={Position.Left}>Left</SelectItem>
                <SelectItem value={Position.Right}>Right</SelectItem>
                <SelectItem value={Position.Top}>Top</SelectItem>
                <SelectItem value={Position.Bottom}>Bottom</SelectItem>
              </SelectContent>
            </Select>
          </SettingsRow>
        </>
      )}
    </>
  );
}

function RoomDimmerDeviceSettings({
  device,
  nodeId,
  collapsed,
  onCollapseChange,
  readOnly,
}: {
  device: RoomDimmerDevice;
  nodeId: string;
  collapsed: boolean;
  onCollapseChange: (isCollapsed: boolean) => void;
  readOnly?: boolean;
}) {
  const { nodes, yDoc, reactFlowInstance } = useReactFlowContext();

  const yDevicesMap = useMemo(() => {
    const dataMap = getNodeData(yDoc, nodeId);
    const devicesMap = dataMap?.get("devices") as Y.Map<any> | undefined;
    return devicesMap;
  }, [yDoc, nodeId]);

  const yDevice = useMemo(() => {
    try {
      const dataMap = getNodeData(yDoc, nodeId);
      const devicesMap = dataMap?.get("devices") as Y.Map<any> | undefined;
      const deviceMap = devicesMap?.get(device.id) as Y.Map<any> | null;
      return deviceMap;
    } catch (e) {
      console.error(e);
      return null;
    }
  }, [yDoc, nodeId, device.id]);

  const light = useMemo(() => {
    const dimmerNodes = filterNodes(nodes, "roomDimmerContainer");
    const lights = [];
    for (const dimmerNode of dimmerNodes) {
      const data = getData(yDoc, dimmerNode?.data.dataId, "roomDimmer");
      if (!data) {
        console.error("Could not retrieve data for node: ", dimmerNode.id);
        return null;
      }

      const via = data.via;
      lights.push({ nodeId: dimmerNode.id, id: "via", ...via });
    }

    return lights.find(
      (light) => light.id === device.viaId && light.nodeId === device.nodeId,
    );
  }, [nodes, yDoc, device.viaId, device.nodeId]);

  return (
    <>
      <SettingsRow>
        <div
          className={cn(
            "flex flex-row gap-0.5 text-xs font-bold text-gray-500 cursor-pointer items-center",
            !collapsed && "text-gray-800",
            !light && "text-red-500",
          )}
          onClick={() => {
            onCollapseChange(!collapsed);
          }}
        >
          <ChevronDown
            className={cn(
              "size-4 transition-transform duration-300 -ml-1",
              collapsed && "-rotate-90",
            )}
            onClick={() => {
              onCollapseChange(!collapsed);
            }}
          />
          <span
            className="hover:text-gray-400"
            onClick={() => {
              onCollapseChange(!collapsed);
            }}
          >
            {light ? light.lightName : "Light missing"}
          </span>
          <span className="flex-grow text-gray-500 text-xs font-normal">
            (from Room Dimmer)
          </span>

          <ArrowRightSquareIcon
            className="size-4 flex-shrink-0 mr-1 opacity-100 hover:opacity-40 cursor-pointer"
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();

              if (!light) {
                return;
              }
              const linkedNode = nodes.find((n) => n.id === light.nodeId);
              if (!linkedNode) {
                return;
              }
              const parentNode = nodes.find(
                (n) => n.id === linkedNode.parentId,
              );
              if (!parentNode) {
                return;
              }

              reactFlowInstance?.setNodes((nodes) =>
                nodes.map((n) => ({
                  ...n,
                  selected:
                    n.id === linkedNode.parentId || n.id === linkedNode.id,
                })),
              );
              reactFlowInstance?.setCenter(
                parentNode.position.x,
                parentNode.position.y,
                {
                  duration: 800,
                  zoom: reactFlowInstance.getViewport().zoom ?? 1,
                },
              );
            }}
          />
        </div>
      </SettingsRow>
      {!collapsed && (
        <>
          <SettingsRow className="flex flex-row items-center gap-2 -mt-2 pl-3.5">
            <Checkbox
              id={`${device.id}-showLabel`}
              checked={device.showLabel}
              disabled={readOnly}
              onCheckedChange={(checked) => {
                if (!yDevice) {
                  return;
                }
                yDevice.set("showLabel", checked);
              }}
            />
            <Label
              htmlFor={`${device.id}-showLabel`}
              className="text-gray-500 text-xs font-semibold flex-shrink-0 truncate flex-grow"
            >
              Show label
            </Label>
            {!readOnly && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="flex-shrink-0 -mr-1.5"
                    onClick={() => {
                      if (!yDevicesMap) {
                        return;
                      }
                      yDevicesMap.delete(device.id);
                    }}
                  >
                    <Trash2 className="size-4 flex-shrink-0" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Delete Device</TooltipContent>
              </Tooltip>
            )}
          </SettingsRow>
          <SettingsRow className="ml-3 -mt-2">
            <SettingsLabel htmlFor="anchorPosition">
              Anchor Position
            </SettingsLabel>
            <Select
              value={device.anchorPosition ?? Position.Left}
              disabled={readOnly}
              onValueChange={(value) => {
                if (!yDevice) {
                  return;
                }
                yDevice.set("anchorPosition", value);
              }}
            >
              <SelectTrigger className="h-10">
                <SelectValue placeholder="Select position" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={Position.Left}>Left</SelectItem>
                <SelectItem value={Position.Right}>Right</SelectItem>
                <SelectItem value={Position.Top}>Top</SelectItem>
                <SelectItem value={Position.Bottom}>Bottom</SelectItem>
              </SelectContent>
            </Select>
          </SettingsRow>
        </>
      )}
    </>
  );
}
function SomfyShadesDeviceSettings({
  device,
  nodeId,
  collapsed,
  onCollapseChange,
  readOnly,
}: {
  device: SomfyShadesBaseDevice;
  nodeId: string;
  collapsed: boolean;
  onCollapseChange: (isCollapsed: boolean) => void;
  readOnly?: boolean;
}) {
  const { nodes, yDoc, reactFlowInstance } = useReactFlowContext();

  const yDevicesMap = useMemo(() => {
    const dataMap = getNodeData(yDoc, nodeId);
    const devicesMap = dataMap?.get("devices") as Y.Map<any> | undefined;
    return devicesMap;
  }, [yDoc, nodeId]);

  const yDevice = useMemo(() => {
    try {
      const dataMap = getNodeData(yDoc, nodeId);
      const devicesMap = dataMap?.get("devices") as Y.Map<any> | undefined;
      const deviceMap = devicesMap?.get(device.id) as Y.Map<any> | null;
      return deviceMap;
    } catch (e) {
      console.error(e);
      return null;
    }
  }, [yDoc, nodeId, device.id]);

  const shade = useMemo(() => {
    const baseStationNodes = filterNodes(nodes, "baseStationContainer");
    const shades = [];
    for (const baseStationNode of baseStationNodes) {
      const data = getData(yDoc, baseStationNode.data.dataId, "baseStation");
      const somfyShades = data?.somfyShades || {};
      for (const shade of Object.values(somfyShades)) {
        shades.push({ nodeId: baseStationNode.id, ...shade });
      }
    }

    return shades.find(
      (shade) => shade.id === device.viaId && shade.nodeId === device.nodeId,
    );
  }, [nodes, device.viaId, device.nodeId]);

  return (
    <>
      <SettingsRow>
        <div
          className={cn(
            "flex flex-row gap-0.5 text-xs font-bold text-gray-500 cursor-pointer items-center",
            !collapsed && "text-gray-800",
            !shade && "text-red-500",
          )}
          onClick={() => {
            onCollapseChange(!collapsed);
          }}
        >
          <ChevronDown
            className={cn(
              "size-4 transition-transform duration-300 -ml-1",
              collapsed && "-rotate-90",
            )}
            onClick={() => {
              onCollapseChange(!collapsed);
            }}
          />
          <span
            className="hover:text-gray-400"
            onClick={() => {
              onCollapseChange(!collapsed);
            }}
          >
            {shade ? shade.name : "Somfy Shades missing"}
          </span>
          <span className="flex-grow text-gray-500 text-xs font-normal">
            (from Somfy Shades)
          </span>

          <ArrowRightSquareIcon
            className="size-4 flex-shrink-0 mr-1 opacity-100 hover:opacity-40 cursor-pointer"
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();

              if (!shade) {
                return;
              }
              const linkedNode = nodes.find((n) => n.id === shade.nodeId);
              if (!linkedNode) {
                return;
              }

              reactFlowInstance?.setNodes((nodes) =>
                nodes.map((n) => ({
                  ...n,
                  selected: n.id === linkedNode.id,
                })),
              );
              reactFlowInstance?.setCenter(
                linkedNode.position.x,
                linkedNode.position.y,
                {
                  duration: 800,
                  zoom: reactFlowInstance.getViewport().zoom ?? 1,
                },
              );
            }}
          />
        </div>
      </SettingsRow>
      {!collapsed && (
        <>
          <SettingsRow className="flex flex-row items-center gap-2 -mt-2 pl-3.5">
            <Checkbox
              id={`${device.id}-showLabel`}
              checked={device.showLabel}
              disabled={readOnly}
              onCheckedChange={(checked) => {
                if (!yDevice) {
                  return;
                }
                yDevice.set("showLabel", checked);
              }}
            />
            <Label
              htmlFor={`${device.id}-showLabel`}
              className="text-gray-500 text-xs font-semibold flex-shrink-0 truncate flex-grow"
            >
              Show label
            </Label>
            {!readOnly && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="flex-shrink-0 -mr-1.5"
                    onClick={() => {
                      if (!yDevicesMap) {
                        return;
                      }
                      // Clean up orphaned actions before deleting the device
                      if (yDoc) {
                        cleanupActionsForDevice(yDoc, device.id);
                      }
                      yDevicesMap.delete(device.id);
                    }}
                  >
                    <Trash2 className="size-4 flex-shrink-0" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Delete Device</TooltipContent>
              </Tooltip>
            )}
          </SettingsRow>
          <SettingsRow className="ml-3 -mt-2">
            <SettingsLabel htmlFor="anchorPosition">
              Anchor Position
            </SettingsLabel>
            <Select
              value={device.anchorPosition ?? Position.Left}
              disabled={readOnly}
              onValueChange={(value) => {
                if (!yDevice) {
                  return;
                }
                yDevice.set("anchorPosition", value);
              }}
            >
              <SelectTrigger className="h-10">
                <SelectValue placeholder="Select position" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={Position.Left}>Left</SelectItem>
                <SelectItem value={Position.Right}>Right</SelectItem>
                <SelectItem value={Position.Top}>Top</SelectItem>
                <SelectItem value={Position.Bottom}>Bottom</SelectItem>
              </SelectContent>
            </Select>
          </SettingsRow>
        </>
      )}
    </>
  );
}

function SomoShadesDeviceSettings({
  device,
  nodeId,
  collapsed,
  onCollapseChange,
  readOnly,
}: {
  device: SomoShadesDevice;
  nodeId: string;
  collapsed: boolean;
  onCollapseChange: (isCollapsed: boolean) => void;
  readOnly?: boolean;
}) {
  const { nodes, yDoc, reactFlowInstance } = useReactFlowContext();

  const yDevicesMap = useMemo(() => {
    const dataMap = getNodeData(yDoc, nodeId);
    const devicesMap = dataMap?.get("devices") as Y.Map<any> | undefined;
    return devicesMap;
  }, [yDoc, nodeId]);

  const yDevice = useMemo(() => {
    try {
      const dataMap = getNodeData(yDoc, nodeId);
      const devicesMap = dataMap?.get("devices") as Y.Map<any> | undefined;
      const deviceMap = devicesMap?.get(device.id) as Y.Map<any> | null;
      return deviceMap;
    } catch (e) {
      console.error(e);
      return null;
    }
  }, [yDoc, nodeId, device.id]);

  const load = useMemo(() => {
    const shadesNodes = filterNodes(nodes, "somoShadesContainer");
    const lights = [];
    for (const shadesNode of shadesNodes) {
      const data = getData(yDoc, shadesNode?.data.dataId, "somoShades");
      if (!data) {
        console.error(
          "Could not retrieve data for Shades node: ",
          shadesNode.id,
        );
        return null;
      }

      const via = data.via;
      lights.push({ nodeId: shadesNode.id, id: "via", ...via });
    }

    return lights.find(
      (load) => load.id === device.viaId && load.nodeId === device.nodeId,
    );
  }, [nodes, device.viaId, device.nodeId]);

  return (
    <>
      <SettingsRow>
        <div
          className={cn(
            "flex flex-row gap-0.5 text-xs font-bold text-gray-500 cursor-pointer items-center",
            !collapsed && "text-gray-800",
            !load && "text-red-500",
          )}
          onClick={() => {
            onCollapseChange(!collapsed);
          }}
        >
          <ChevronDown
            className={cn(
              "size-4 transition-transform duration-300 -ml-1",
              collapsed && "-rotate-90",
            )}
            onClick={() => {
              onCollapseChange(!collapsed);
            }}
          />
          <span
            className="hover:text-gray-400"
            onClick={() => {
              onCollapseChange(!collapsed);
            }}
          >
            {load ? load.loadName : "Shades/Curtains missing"}
          </span>
          <span className="flex-grow text-gray-500 text-xs font-normal">
            (from Somo Shades)
          </span>

          <ArrowRightSquareIcon
            className="size-4 flex-shrink-0 mr-1 opacity-100 hover:opacity-40 cursor-pointer"
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();

              if (!load) {
                return;
              }
              const linkedNode = nodes.find((n) => n.id === load.nodeId);
              if (!linkedNode) {
                return;
              }
              const parentNode = nodes.find(
                (n) => n.id === linkedNode.parentId,
              );
              if (!parentNode) {
                return;
              }

              reactFlowInstance?.setNodes((nodes) =>
                nodes.map((n) => ({
                  ...n,
                  selected:
                    n.id === linkedNode.parentId || n.id === linkedNode.id,
                })),
              );
              reactFlowInstance?.setCenter(
                parentNode.position.x,
                parentNode.position.y,
                {
                  duration: 800,
                  zoom: reactFlowInstance.getViewport().zoom ?? 1,
                },
              );
            }}
          />
        </div>
      </SettingsRow>
      {!collapsed && (
        <>
          <SettingsRow className="flex flex-row items-center gap-2 -mt-2 pl-3.5">
            <Checkbox
              id={`${device.id}-showLabel`}
              checked={device.showLabel}
              disabled={readOnly}
              onCheckedChange={(checked) => {
                if (!yDevice) {
                  return;
                }
                yDevice.set("showLabel", checked);
              }}
            />
            <Label
              htmlFor={`${device.id}-showLabel`}
              className="text-gray-500 text-xs font-semibold flex-shrink-0 truncate flex-grow"
            >
              Show label
            </Label>
            {!readOnly && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="flex-shrink-0 -mr-1.5"
                    onClick={() => {
                      if (!yDevicesMap) {
                        return;
                      }
                      // Clean up orphaned actions before deleting the device
                      if (yDoc) {
                        cleanupActionsForDevice(yDoc, device.id);
                      }
                      yDevicesMap.delete(device.id);
                    }}
                  >
                    <Trash2 className="size-4 flex-shrink-0" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Delete Device</TooltipContent>
              </Tooltip>
            )}
          </SettingsRow>
          <SettingsRow className="ml-3 -mt-2">
            <SettingsLabel htmlFor="anchorPosition">
              Anchor Position
            </SettingsLabel>
            <Select
              value={device.anchorPosition ?? Position.Left}
              disabled={readOnly}
              onValueChange={(value) => {
                if (!yDevice) {
                  return;
                }
                yDevice.set("anchorPosition", value);
              }}
            >
              <SelectTrigger className="h-10">
                <SelectValue placeholder="Select position" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={Position.Left}>Left</SelectItem>
                <SelectItem value={Position.Right}>Right</SelectItem>
                <SelectItem value={Position.Top}>Top</SelectItem>
                <SelectItem value={Position.Bottom}>Bottom</SelectItem>
              </SelectContent>
            </Select>
          </SettingsRow>
        </>
      )}
    </>
  );
}

function SomoFanDeviceSettings({
  device,
  nodeId,
  collapsed,
  onCollapseChange,
  readOnly,
}: {
  device: SomoFanDevice;
  nodeId: string;
  collapsed: boolean;
  onCollapseChange: (isCollapsed: boolean) => void;
  readOnly?: boolean;
}) {
  const { nodes, yDoc, reactFlowInstance } = useReactFlowContext();

  const yDevicesMap = useMemo(() => {
    const dataMap = getNodeData(yDoc, nodeId);
    const devicesMap = dataMap?.get("devices") as Y.Map<any> | undefined;
    return devicesMap;
  }, [yDoc, nodeId]);

  const yDevice = useMemo(() => {
    try {
      const dataMap = getNodeData(yDoc, nodeId);
      const devicesMap = dataMap?.get("devices") as Y.Map<any> | undefined;
      const deviceMap = devicesMap?.get(device.id) as Y.Map<any> | null;
      return deviceMap;
    } catch (e) {
      console.error(e);
      return null;
    }
  }, [yDoc, nodeId, device.id]);

  const load = useMemo(() => {
    const fanNodes = filterNodes(nodes, "somoFanContainer");
    const lights = [];
    for (const fanNode of fanNodes) {
      const data = getData(yDoc, fanNode.data.dataId, "somoFan");
      if (!data) {
        return null;
      }
      const via = data.via;
      lights.push({ nodeId: fanNode.id, id: "via", ...via });
    }

    return lights.find(
      (load) => load.id === device.viaId && load.nodeId === device.nodeId,
    );
  }, [nodes, device.viaId, device.nodeId]);

  return (
    <>
      <SettingsRow>
        <div
          className={cn(
            "flex flex-row gap-0.5 text-xs font-bold text-gray-500 cursor-pointer items-center",
            !collapsed && "text-gray-800",
            !load && "text-red-500",
          )}
          onClick={() => {
            onCollapseChange(!collapsed);
          }}
        >
          <ChevronDown
            className={cn(
              "size-4 transition-transform duration-300 -ml-1",
              collapsed && "-rotate-90",
            )}
            onClick={() => {
              onCollapseChange(!collapsed);
            }}
          />
          <span
            className="hover:text-gray-400"
            onClick={() => {
              onCollapseChange(!collapsed);
            }}
          >
            {load ? load.loadName : "Fan/Curtains missing"}
          </span>
          <span className="flex-grow text-gray-500 text-xs font-normal">
            (from Somo Fan)
          </span>

          <ArrowRightSquareIcon
            className="size-4 flex-shrink-0 mr-1 opacity-100 hover:opacity-40 cursor-pointer"
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();

              if (!load) {
                return;
              }
              const linkedNode = nodes.find((n) => n.id === load.nodeId);
              if (!linkedNode) {
                return;
              }
              const parentNode = nodes.find(
                (n) => n.id === linkedNode.parentId,
              );
              if (!parentNode) {
                return;
              }

              reactFlowInstance?.setNodes((nodes) =>
                nodes.map((n) => ({
                  ...n,
                  selected:
                    n.id === linkedNode.parentId || n.id === linkedNode.id,
                })),
              );
              reactFlowInstance?.setCenter(
                parentNode.position.x,
                parentNode.position.y,
                {
                  duration: 800,
                  zoom: reactFlowInstance.getViewport().zoom ?? 1,
                },
              );
            }}
          />
        </div>
      </SettingsRow>
      {!collapsed && (
        <>
          <SettingsRow className="flex flex-row items-center gap-2 -mt-2 pl-3.5">
            <Checkbox
              id={`${device.id}-showLabel`}
              checked={device.showLabel}
              disabled={readOnly}
              onCheckedChange={(checked) => {
                if (!yDevice) {
                  return;
                }
                yDevice.set("showLabel", checked);
              }}
            />
            <Label
              htmlFor={`${device.id}-showLabel`}
              className="text-gray-500 text-xs font-semibold flex-shrink-0 truncate flex-grow"
            >
              Show label
            </Label>
            {!readOnly && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="flex-shrink-0 -mr-1.5"
                    onClick={() => {
                      if (!yDevicesMap) {
                        return;
                      }
                      // Clean up orphaned actions before deleting the device
                      if (yDoc) {
                        cleanupActionsForDevice(yDoc, device.id);
                      }
                      yDevicesMap.delete(device.id);
                    }}
                  >
                    <Trash2 className="size-4 flex-shrink-0" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Delete Device</TooltipContent>
              </Tooltip>
            )}
          </SettingsRow>
          <SettingsRow className="ml-3 -mt-2">
            <SettingsLabel htmlFor="anchorPosition">
              Anchor Position
            </SettingsLabel>
            <Select
              value={device.anchorPosition ?? Position.Left}
              disabled={readOnly}
              onValueChange={(value) => {
                if (!yDevice) {
                  return;
                }
                yDevice.set("anchorPosition", value);
              }}
            >
              <SelectTrigger className="h-10">
                <SelectValue placeholder="Select position" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={Position.Left}>Left</SelectItem>
                <SelectItem value={Position.Right}>Right</SelectItem>
                <SelectItem value={Position.Top}>Top</SelectItem>
                <SelectItem value={Position.Bottom}>Bottom</SelectItem>
              </SelectContent>
            </Select>
          </SettingsRow>
        </>
      )}
    </>
  );
}

function SomoThermostatDeviceSettings({
  device,
  nodeId,
  collapsed,
  onCollapseChange,
  readOnly,
}: {
  device: SomoThermostatDevice;
  nodeId: string;
  collapsed: boolean;
  onCollapseChange: (isCollapsed: boolean) => void;
  readOnly?: boolean;
}) {
  const { nodes, yDoc, reactFlowInstance } = useReactFlowContext();

  const yDevicesMap = useMemo(() => {
    const dataMap = getNodeData(yDoc, nodeId);
    const devicesMap = dataMap?.get("devices") as Y.Map<any> | undefined;
    return devicesMap;
  }, [yDoc, nodeId]);

  const yDevice = useMemo(() => {
    try {
      const dataMap = getNodeData(yDoc, nodeId);
      const devicesMap = dataMap?.get("devices") as Y.Map<any> | undefined;
      const deviceMap = devicesMap?.get(device.id) as Y.Map<any> | null;
      return deviceMap;
    } catch (e) {
      console.error(e);
      return null;
    }
  }, [yDoc, nodeId, device.id]);

  const load = useMemo(() => {
    const thermostatNodes = filterNodes(nodes, "somoThermostatContainer");
    const lights = [];
    for (const thermostatNode of thermostatNodes) {
      const data = getData(yDoc, thermostatNode.data.dataId, "somoThermostat");
      if (!data) {
        return null;
      }
      lights.push({ nodeId: thermostatNode.id, id: "via", ...data.via });
    }

    return lights.find(
      (load) => load.id === device.viaId && load.nodeId === device.nodeId,
    );
  }, [nodes, device.viaId, device.nodeId]);

  return (
    <>
      <SettingsRow>
        <div
          className={cn(
            "flex flex-row gap-0.5 text-xs font-bold text-gray-500 cursor-pointer items-center",
            !collapsed && "text-gray-800",
            !load && "text-red-500",
          )}
          onClick={() => {
            onCollapseChange(!collapsed);
          }}
        >
          <ChevronDown
            className={cn(
              "size-4 transition-transform duration-300 -ml-1",
              collapsed && "-rotate-90",
            )}
            onClick={() => {
              onCollapseChange(!collapsed);
            }}
          />
          <span
            className="hover:text-gray-400"
            onClick={() => {
              onCollapseChange(!collapsed);
            }}
          >
            {load ? load.hvacName : "Thermostat missing"}
          </span>
          <span className="flex-grow text-gray-500 text-xs font-normal">
            (from Thermostat)
          </span>

          <ArrowRightSquareIcon
            className="size-4 flex-shrink-0 mr-1 opacity-100 hover:opacity-40 cursor-pointer"
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();

              if (!load) {
                return;
              }
              const linkedNode = nodes.find((n) => n.id === load.nodeId);
              if (!linkedNode) {
                return;
              }
              const parentNode = nodes.find(
                (n) => n.id === linkedNode.parentId,
              );
              if (!parentNode) {
                return;
              }

              reactFlowInstance?.setNodes((nodes) =>
                nodes.map((n) => ({
                  ...n,
                  selected:
                    n.id === linkedNode.parentId || n.id === linkedNode.id,
                })),
              );
              reactFlowInstance?.setCenter(
                parentNode.position.x,
                parentNode.position.y,
                {
                  duration: 800,
                  zoom: reactFlowInstance.getViewport().zoom ?? 1,
                },
              );
            }}
          />
        </div>
      </SettingsRow>
      {!collapsed && (
        <>
          <SettingsRow className="flex flex-row items-center gap-2 -mt-2 pl-3.5">
            <Checkbox
              id={`${device.id}-showLabel`}
              checked={device.showLabel}
              disabled={readOnly}
              onCheckedChange={(checked) => {
                if (!yDevice) {
                  return;
                }
                yDevice.set("showLabel", checked);
              }}
            />
            <Label
              htmlFor={`${device.id}-showLabel`}
              className="text-gray-500 text-xs font-semibold flex-shrink-0 truncate flex-grow"
            >
              Show label
            </Label>
            {!readOnly && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="flex-shrink-0 -mr-1.5"
                    onClick={() => {
                      if (!yDevicesMap) {
                        return;
                      }
                      // Clean up orphaned actions before deleting the device
                      if (yDoc) {
                        cleanupActionsForDevice(yDoc, device.id);
                      }
                      yDevicesMap.delete(device.id);
                    }}
                  >
                    <Trash2 className="size-4 flex-shrink-0" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Delete Device</TooltipContent>
              </Tooltip>
            )}
          </SettingsRow>
          <SettingsRow className="ml-3 -mt-2">
            <SettingsLabel htmlFor="anchorPosition">
              Anchor Position
            </SettingsLabel>
            <Select
              value={device.anchorPosition ?? Position.Left}
              disabled={readOnly}
              onValueChange={(value) => {
                if (!yDevice) {
                  return;
                }
                yDevice.set("anchorPosition", value);
              }}
            >
              <SelectTrigger className="h-10">
                <SelectValue placeholder="Select position" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={Position.Left}>Left</SelectItem>
                <SelectItem value={Position.Right}>Right</SelectItem>
                <SelectItem value={Position.Top}>Top</SelectItem>
                <SelectItem value={Position.Bottom}>Bottom</SelectItem>
              </SelectContent>
            </Select>
          </SettingsRow>
        </>
      )}
    </>
  );
}

function SomoIrControllerDeviceSettings({
  device,
  nodeId,
  collapsed,
  onCollapseChange,
  readOnly,
}: {
  device: SomoIrControllerDevice;
  nodeId: string;
  collapsed: boolean;
  onCollapseChange: (isCollapsed: boolean) => void;
  readOnly?: boolean;
}) {
  const { nodes, yDoc, reactFlowInstance } = useReactFlowContext();

  const yDevicesMap = useMemo(() => {
    const dataMap = getNodeData(yDoc, nodeId);
    const devicesMap = dataMap?.get("devices") as Y.Map<any> | undefined;
    return devicesMap;
  }, [yDoc, nodeId]);

  const yDevice = useMemo(() => {
    try {
      const dataMap = getNodeData(yDoc, nodeId);
      const devicesMap = dataMap?.get("devices") as Y.Map<any> | undefined;
      const deviceMap = devicesMap?.get(device.id) as Y.Map<any> | null;
      return deviceMap;
    } catch (e) {
      console.error(e);
      return null;
    }
  }, [yDoc, nodeId, device.id]);

  const load = useMemo(() => {
    const irControllerNodes = filterNodes(nodes, "somoIrControllerContainer");
    const loads = [];
    for (const irControllerNode of irControllerNodes) {
      const data = getData(
        yDoc,
        irControllerNode.data.dataId,
        "somoIrController",
      );
      if (!data) {
        return null;
      }
      loads.push({ nodeId: irControllerNode.id, id: "via", ...data.via });
    }

    return loads.find(
      (load) => load.id === device.viaId && load.nodeId === device.nodeId,
    );
  }, [nodes, device.viaId, device.nodeId]);

  return (
    <>
      <SettingsRow>
        <div
          className={cn(
            "flex flex-row gap-0.5 text-xs font-bold text-gray-500 cursor-pointer items-center",
            !collapsed && "text-gray-800",
            !load && "text-red-500",
          )}
          onClick={() => {
            onCollapseChange(!collapsed);
          }}
        >
          <ChevronDown
            className={cn(
              "size-4 transition-transform duration-300 -ml-1",
              collapsed && "-rotate-90",
            )}
            onClick={() => {
              onCollapseChange(!collapsed);
            }}
          />
          <span
            className="hover:text-gray-400"
            onClick={() => {
              onCollapseChange(!collapsed);
            }}
          >
            {load ? load.hvacName : "IR Controller missing"}
          </span>
          <span className="flex-grow text-gray-500 text-xs font-normal">
            (from IR HVAC Controller)
          </span>

          <ArrowRightSquareIcon
            className="size-4 flex-shrink-0 mr-1 opacity-100 hover:opacity-40 cursor-pointer"
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();

              if (!load) {
                return;
              }
              const linkedNode = nodes.find((n) => n.id === load.nodeId);
              if (!linkedNode) {
                return;
              }
              const parentNode = nodes.find(
                (n) => n.id === linkedNode.parentId,
              );
              if (!parentNode) {
                return;
              }

              reactFlowInstance?.setNodes((nodes) =>
                nodes.map((n) => ({
                  ...n,
                  selected:
                    n.id === linkedNode.parentId || n.id === linkedNode.id,
                })),
              );
              reactFlowInstance?.setCenter(
                parentNode.position.x,
                parentNode.position.y,
                {
                  duration: 800,
                  zoom: reactFlowInstance.getViewport().zoom ?? 1,
                },
              );
            }}
          />
        </div>
      </SettingsRow>
      {!collapsed && (
        <>
          <SettingsRow className="flex flex-row items-center gap-2 -mt-2 pl-3.5">
            <Checkbox
              id={`${device.id}-showLabel`}
              checked={device.showLabel}
              disabled={readOnly}
              onCheckedChange={(checked) => {
                if (!yDevice) {
                  return;
                }
                yDevice.set("showLabel", checked);
              }}
            />
            <Label
              htmlFor={`${device.id}-showLabel`}
              className="text-gray-500 text-xs font-semibold flex-shrink-0 truncate flex-grow"
            >
              Show label
            </Label>
            {!readOnly && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="flex-shrink-0 -mr-1.5"
                    onClick={() => {
                      if (!yDevicesMap) {
                        return;
                      }
                      // Clean up orphaned actions before deleting the device
                      if (yDoc) {
                        cleanupActionsForDevice(yDoc, device.id);
                      }
                      yDevicesMap.delete(device.id);
                    }}
                  >
                    <Trash2 className="size-4 flex-shrink-0" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Delete Device</TooltipContent>
              </Tooltip>
            )}
          </SettingsRow>
          <SettingsRow className="ml-3 -mt-2">
            <SettingsLabel htmlFor="anchorPosition">
              Anchor Position
            </SettingsLabel>
            <Select
              value={device.anchorPosition ?? Position.Left}
              disabled={readOnly}
              onValueChange={(value) => {
                if (!yDevice) {
                  return;
                }
                yDevice.set("anchorPosition", value);
              }}
            >
              <SelectTrigger className="h-10">
                <SelectValue placeholder="Select position" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={Position.Left}>Left</SelectItem>
                <SelectItem value={Position.Right}>Right</SelectItem>
                <SelectItem value={Position.Top}>Top</SelectItem>
                <SelectItem value={Position.Bottom}>Bottom</SelectItem>
              </SelectContent>
            </Select>
          </SettingsRow>
        </>
      )}
    </>
  );
}

/**
 * Generic function to add control settings or hvac actions to parent vias.
 *
 * @param options - Configuration options
 * @param options.yDoc - The Yjs document
 * @param options.parentNodeId - The ID of the parent node
 * @param options.viaKeys - The keys of the vias to add actions to
 * @param options.onUpClickKey - The event key to use (default: "onUpClick")
 * @param options.actionType - The type of action to add ("control" or "hvac")
 * @param options.makeControlSettings - Function to create control settings (for "control" actionType)
 * @param options.deviceId - The ID of the device (for "hvac" actionType)
 * @param options.hvacDefaults - Default values for hvac actions (for "hvac" actionType)
 */
function addActionsToParentVias({
  yDoc,
  parentNodeId,
  viaKeys,
  onUpClickKey = "onUpClick",
  actionType = "control",
  makeControlSettings,
  deviceId,
  thermostatDefaults = {
    setpoint: 22,
    mode: "auto" as const,
    fanSpeed: "auto" as const,
  },
}: {
  yDoc: Y.Doc;
  parentNodeId: string;
  viaKeys: string[];
  onUpClickKey?: string;
  actionType?: "control" | "hvac";
  makeControlSettings?: (
    viaKey: string,
    sortIndex: number,
  ) => { id: string; [key: string]: any };
  deviceId?: string;
  thermostatDefaults?: {
    setpoint: number;
    mode: "heat" | "cool" | "fan" | "auto";
    fanSpeed: "low" | "medium" | "high" | "auto";
  };
}) {
  const parentDataMap = getNodeData(yDoc, parentNodeId);
  if (!parentDataMap) {
    return;
  }

  viaKeys.forEach((viaKey, sortIndex) => {
    const viaMap = parentDataMap.get(viaKey) as Y.Map<any> | undefined;
    if (!viaMap) {
      return;
    }

    let onUpClickMap = viaMap.get(onUpClickKey) as Y.Map<any> | undefined;
    if (!onUpClickMap) {
      onUpClickMap = new Y.Map();
      viaMap.set(onUpClickKey, onUpClickMap);
    }

    if (actionType === "hvac") {
      if (!deviceId) {
        console.error("deviceId is required for hvac actions");
        return;
      }

      const thermostatAction = {
        id: randomId(),
        deviceId,
        sortIndex,
        setpoint: thermostatDefaults.setpoint,
        mode: thermostatDefaults.mode,
        fanSpeed: thermostatDefaults.fanSpeed,
      };

      onUpClickMap.set(thermostatAction.id, objectToYMap(thermostatAction));
    } else {
      if (!makeControlSettings) {
        console.error("makeControlSettings is required for control actions");
        return;
      }

      const controlSettings = makeControlSettings(viaKey, sortIndex);
      onUpClickMap.set(controlSettings.id, objectToYMap(controlSettings));
    }
  });
}

function ZeroToTenVoltDimmerDeviceSettings({
  device,
  nodeId,
  collapsed,
  onCollapseChange,
  readOnly,
}: {
  device: ZeroToTenVoltDimmerDevice;
  nodeId: string;
  collapsed: boolean;
  onCollapseChange: (isCollapsed: boolean) => void;
  readOnly?: boolean;
}) {
  const { nodes, yDoc, reactFlowInstance } = useReactFlowContext();

  const yDevicesMap = useMemo(() => {
    const dataMap = getNodeData(yDoc, nodeId);
    const devicesMap = dataMap?.get("devices") as Y.Map<any> | undefined;
    return devicesMap;
  }, [yDoc, nodeId]);

  const yDevice = useMemo(() => {
    try {
      const dataMap = getNodeData(yDoc, nodeId);
      const devicesMap = dataMap?.get("devices") as Y.Map<any> | undefined;
      const deviceMap = devicesMap?.get(device.id) as Y.Map<any> | null;
      return deviceMap;
    } catch (e) {
      console.error(e);
      return null;
    }
  }, [yDoc, nodeId, device.id]);

  const dimmer = useMemo(() => {
    const canBusControllerNodes = filterNodes(
      nodes,
      "canbusControllerContainer",
    );
    const dimmers = [];
    for (const canBusControllerNode of canBusControllerNodes) {
      const data = getData(
        yDoc,
        canBusControllerNode.data.dataId,
        "canbusController",
      );
      if (!data) {
        return null;
      }
      const zeroToTenVoltDimmers = data.zeroToTenVoltDimmers || {};
      for (const dimmer of Object.values(zeroToTenVoltDimmers)) {
        dimmers.push({ nodeId: canBusControllerNode.id, ...dimmer });
      }
    }

    return dimmers.find(
      (dimmer) => dimmer.id === device.viaId && dimmer.nodeId === device.nodeId,
    );
  }, [nodes, device.viaId, device.nodeId]);

  return (
    <>
      <SettingsRow>
        <div
          className={cn(
            "flex flex-row gap-0.5 text-xs font-bold text-gray-500 cursor-pointer items-center",
            !collapsed && "text-gray-800",
            !dimmer && "text-red-500",
          )}
          onClick={() => {
            onCollapseChange(!collapsed);
          }}
        >
          <ChevronDown
            className={cn(
              "size-4 transition-transform duration-300 -ml-1",
              collapsed && "-rotate-90",
            )}
            onClick={() => {
              onCollapseChange(!collapsed);
            }}
          />
          <span
            className="hover:text-gray-400"
            onClick={() => {
              onCollapseChange(!collapsed);
            }}
          >
            {dimmer ? dimmer.name : "0-10V Dimmer missing"}
          </span>
          <span className="flex-grow text-gray-500 text-xs font-normal">
            (from 0-10V Dimmer)
          </span>

          <ArrowRightSquareIcon
            className="size-4 flex-shrink-0 mr-1 opacity-100 hover:opacity-40 cursor-pointer"
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();

              if (!dimmer) {
                return;
              }
              const linkedNode = nodes.find((n) => n.id === dimmer.nodeId);
              if (!linkedNode) {
                return;
              }
              const parentNode = nodes.find(
                (n) => n.id === linkedNode.parentId,
              );
              if (!parentNode) {
                return;
              }

              reactFlowInstance?.setNodes((nodes) =>
                nodes.map((n) => ({
                  ...n,
                  selected:
                    n.id === linkedNode.parentId || n.id === linkedNode.id,
                })),
              );
              reactFlowInstance?.setCenter(
                parentNode.position.x,
                parentNode.position.y,
                {
                  duration: 800,
                  zoom: reactFlowInstance.getViewport().zoom ?? 1,
                },
              );
            }}
          />
        </div>
      </SettingsRow>
      {!collapsed && (
        <>
          <SettingsRow className="flex flex-row items-center gap-2 -mt-2 pl-3.5">
            <Checkbox
              id={`${device.id}-showLabel`}
              checked={device.showLabel}
              disabled={readOnly}
              onCheckedChange={(checked) => {
                if (!yDevice) {
                  return;
                }
                yDevice.set("showLabel", checked);
              }}
            />
            <Label
              htmlFor={`${device.id}-showLabel`}
              className="text-gray-500 text-xs font-semibold flex-shrink-0 truncate flex-grow"
            >
              Show label
            </Label>
            {!readOnly && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="flex-shrink-0 -mr-1.5"
                    onClick={() => {
                      if (!yDevicesMap) {
                        return;
                      }
                      yDevicesMap.delete(device.id);
                    }}
                  >
                    <Trash2 className="size-4 flex-shrink-0" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Delete Device</TooltipContent>
              </Tooltip>
            )}
          </SettingsRow>
          <SettingsRow className="ml-3 -mt-2">
            <SettingsLabel htmlFor="anchorPosition">
              Anchor Position
            </SettingsLabel>
            <Select
              value={device.anchorPosition ?? Position.Left}
              disabled={readOnly}
              onValueChange={(value) => {
                if (!yDevice) {
                  return;
                }
                yDevice.set("anchorPosition", value);
              }}
            >
              <SelectTrigger className="h-10">
                <SelectValue placeholder="Select position" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={Position.Left}>Left</SelectItem>
                <SelectItem value={Position.Right}>Right</SelectItem>
                <SelectItem value={Position.Top}>Top</SelectItem>
                <SelectItem value={Position.Bottom}>Bottom</SelectItem>
              </SelectContent>
            </Select>
          </SettingsRow>
        </>
      )}
    </>
  );
}

function RelayOutputDeviceSettings({
  device,
  nodeId,
  collapsed,
  onCollapseChange,
  readOnly,
}: {
  device: RelayOutputDevice;
  nodeId: string;
  collapsed: boolean;
  onCollapseChange: (isCollapsed: boolean) => void;
  readOnly?: boolean;
}) {
  const { nodes, yDoc } = useReactFlowContext();

  const yDevicesMap = useMemo(() => {
    const dataMap = getNodeData(yDoc, nodeId);
    const devicesMap = dataMap?.get("devices") as Y.Map<any> | undefined;
    if (!devicesMap && dataMap) {
      const newDevicesMap = new Y.Map();
      dataMap?.set("devices", newDevicesMap);
      return newDevicesMap as Y.Map<any>;
    }
    return devicesMap as Y.Map<any>;
  }, [yDoc, nodeId]);

  const yDeviceMap = useMemo(() => {
    if (!yDevicesMap) {
      return null;
    }
    return yDevicesMap.get(device.id) as Y.Map<any> | null;
  }, [yDevicesMap, device.id]);

  const relayOutputNode = filterNodes(nodes, "canbusControllerContainer").find(
    (node) => node.id === device.nodeId,
  );
  if (!relayOutputNode) {
    return (
      <div className="text-xs font-mono h-[40px] flex items-center justify-center px-2">
        Relay output missing
      </div>
    );
  }
  const data = getData(yDoc, relayOutputNode.data.dataId, "canbusController");
  const relayOutput = data?.relayOutputs?.[device.viaId];

  if (!relayOutput) {
    return (
      <div className="text-xs font-mono h-[40px] flex items-center justify-center px-2">
        Relay output missing
      </div>
    );
  }

  return (
    <div className="mb-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <button
            className={cn(
              "flex items-center gap-2 text-sm font-medium text-gray-700 hover:text-gray-900",
              collapsed && "text-gray-500",
            )}
            onClick={() => {
              onCollapseChange(!collapsed);
            }}
          >
            <ChevronDown
              className={cn(
                "size-4 transition-transform duration-300",
                collapsed && "-rotate-90",
              )}
            />
            {relayOutput.name}
          </button>
        </div>
        <div className="flex items-center gap-2">
          {!readOnly && (
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="size-5"
                  onClick={() => {
                    yDevicesMap?.delete(device.id);
                  }}
                >
                  <Trash2Icon className="size-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Remove Device</TooltipContent>
            </Tooltip>
          )}
        </div>
      </div>
      {!collapsed && (
        <div className="ml-6 mt-2 space-y-2">
          <SettingsRow>
            <div className="font-mono text-xs text-gray-500">
              ID: {device.id}
            </div>
          </SettingsRow>
          <SettingsRow>
            <SettingsLabel>Show Label</SettingsLabel>
            <Checkbox
              checked={device.showLabel}
              disabled={readOnly}
              onCheckedChange={(checked) => {
                yDeviceMap?.set("showLabel", checked);
              }}
            />
          </SettingsRow>
          <SettingsRow>
            <SettingsLabel>Anchor Position</SettingsLabel>
            <Select
              value={device.anchorPosition || "left"}
              disabled={readOnly}
              onValueChange={(value) => {
                yDeviceMap?.set("anchorPosition", value);
              }}
            >
              <SelectTrigger className="h-10">
                <SelectValue placeholder="Select position" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="left">Left</SelectItem>
                <SelectItem value="right">Right</SelectItem>
                <SelectItem value="top">Top</SelectItem>
                <SelectItem value="bottom">Bottom</SelectItem>
              </SelectContent>
            </Select>
          </SettingsRow>
        </div>
      )}
    </div>
  );
}

export function SectionSettings({
  node,
}: {
  node: GraphNodesByType["section"];
}) {
  const { reactFlowInstance, yDoc, nodes, readOnly } = useReactFlowContext();

  const [deviceCollapsedState, setDeviceCollapsedState] = React.useState<{
    [deviceId: string]: boolean;
  }>({});

  const yDevicesMap = useMemo(() => {
    const dataMap = getNodeData(yDoc, node.id);
    return dataMap?.get("devices") as Y.Map<any> | undefined;
  }, [yDoc, node.id]);

  const nodeDevices = Object.values(node.data.devices ?? {}).sort(
    (a, b) => a.sortIndex - b.sortIndex,
  );

  const allDevices = filterNodes(nodes, "section").flatMap((node) =>
    Object.values(node.data.devices ?? {}),
  );

  const switchNodes = filterNodes(nodes, "roomSwitchContainer");
  const switchDevices = allDevices.filter((d) => d.type === "roomSwitch");
  const availableSomoLights = switchNodes.flatMap((node) => {
    const data = getData(yDoc, node.data.dataId, "roomSwitch");
    if (!data) {
      return [];
    }
    return roomSwitchViaIds
      .filter((viaId) => data[viaId].hasLoad)
      .filter(
        (viaId) =>
          !switchDevices.find((d) => d.nodeId === node.id && d.viaId === viaId),
      )
      .map((viaId) => ({
        id: viaId,
        nodeId: node.id,
        name: data[viaId].lightName,
      }));
  });

  const dimmerNodes = filterNodes(nodes, "roomDimmerContainer");
  const dimmerDevices = allDevices.filter((d) => d.type === "roomDimmer");
  const availableRoomDimmerLights = dimmerNodes.flatMap((node) => {
    const data = getData(yDoc, node.data.dataId, "roomDimmer");
    if (!data) {
      return [];
    }

    // viaUp and viaDown should not be bound to a specific light
    return (["via"] as const)
      .filter(
        (viaId) =>
          !dimmerDevices.find((d) => d.nodeId === node.id && d.viaId === viaId),
      )
      .map((viaId) => ({
        id: viaId,
        nodeId: node.id,
        name: data[viaId].lightName,
      }));
  });

  const shadesNodes = filterNodes(nodes, "somoShadesContainer");
  const shadesDevices = allDevices.filter((d) => d.type === "somoShades");
  const availableSomoShadesLights = shadesNodes.flatMap((node) => {
    // viaUp and viaDown should not be bound to a specific load
    const data = getData(yDoc, node.data.dataId, "somoFan");
    if (!data) {
      return [];
    }

    return (["via"] as const)
      .filter(
        (viaId) =>
          !shadesDevices.find((d) => d.nodeId === node.id && d.viaId === viaId),
      )
      .map((viaId) => ({
        id: viaId,
        nodeId: node.id,
        name: data[viaId].loadName,
      }));
  });

  const fanNodes = filterNodes(nodes, "somoFanContainer");
  const fanDevices = allDevices.filter((d) => d.type === "somoFan");
  const availableSomoFanLights = fanNodes.flatMap((node) => {
    const data = getData(yDoc, node.data.dataId, "somoFan");
    if (!data) {
      return [];
    }
    return (["via"] as const)
      .filter(
        (viaId) =>
          !fanDevices.find((d) => d.nodeId === node.id && d.viaId === viaId),
      )
      .map((viaId) => ({
        id: viaId,
        nodeId: node.id,
        name: data[viaId].loadName,
      }));
  });

  const thermostatNodes = filterNodes(nodes, "somoThermostatContainer");
  const thermostatDevices = allDevices.filter(
    (d) => d.type === "somoThermostat",
  );
  const availableSomoThermostats = thermostatNodes.flatMap((node) => {
    const data = getData(yDoc, node.data.dataId, "somoThermostat");
    if (!data) {
      return [];
    }
    return (["via"] as const)
      .filter(
        (viaId) =>
          !thermostatDevices.find(
            (d) => d.nodeId === node.id && d.viaId === viaId,
          ),
      )
      .map((viaId) => ({
        id: viaId,
        nodeId: node.id,
        name: data[viaId].hvacName,
      }));
  });

  const irControllerNodes = filterNodes(nodes, "somoIrControllerContainer");
  const irControllerDevices = allDevices.filter(
    (d) => d.type === "somoIrController",
  );
  const availableSomoIrControllers = irControllerNodes.flatMap((node) => {
    const data = getData(yDoc, node.data.dataId, "somoIrController");
    if (!data) {
      return [];
    }

    return (["via"] as const)
      .filter(
        (viaId) =>
          !irControllerDevices.find(
            (d) => d.nodeId === node.id && d.viaId === viaId,
          ),
      )
      .map((viaId) => ({
        id: viaId,
        nodeId: node.id,
        name: data[viaId].hvacName,
      }));
  });

  const canBusControllerNodes = filterNodes(nodes, "canbusControllerContainer");
  const zeroToTenVoltDimmerDevices = allDevices.filter(
    (d) => d.type === "zeroToTenVoltDimmer",
  );
  const availableZeroToTenVoltDimmers = canBusControllerNodes.flatMap(
    (node) => {
      const data = getData(yDoc, node.data.dataId, "canbusController");
      if (!data) {
        return [];
      }
      return Object.values(data.zeroToTenVoltDimmers || {})
        .filter(
          (dimmer) =>
            !zeroToTenVoltDimmerDevices.find(
              (d) => d.nodeId === node.id && d.viaId === dimmer.id,
            ),
        )
        .map((dimmer) => ({
          id: dimmer.id,
          nodeId: node.id,
          name: dimmer.name,
          dimmingType: dimmer.dimmingType,
          useRelay: dimmer.useRelay,
          minBrightness: dimmer.minBrightness,
          maxBrightness: dimmer.maxBrightness,
          defaultDimmingSpeed: dimmer.defaultDimmingSpeed,
        }));
    },
  );

  const relayOutputDevices = allDevices.filter((d) => d.type === "relayOutput");
  const availableRelayOutputs = canBusControllerNodes.flatMap((node) => {
    const data = getData(yDoc, node.data.dataId, "canbusController");
    if (!data) {
      return [];
    }
    return Object.values(data.relayOutputs || {})
      .filter(
        (relayOutput) =>
          !relayOutputDevices.find(
            (d) => d.nodeId === node.id && d.viaId === relayOutput.id,
          ),
      )
      .map((relayOutput) => ({
        label: `${data.title} - ${relayOutput.name}`,
        value: `${node.id}:${relayOutput.id}`,
      }));
  });

  const baseStationNodes = filterNodes(nodes, "baseStationContainer");
  const somfyShadesDevices = allDevices.filter((d) => d.type === "somfyShades");
  const availableSomfyShades = baseStationNodes.flatMap((node) => {
    const data = getData(yDoc, node.data.dataId, "baseStation");
    if (!data) {
      return [];
    }
    return Object.values(data.somfyShades || {})
      .filter(
        (shade) =>
          !somfyShadesDevices.find(
            (d) => d.nodeId === node.id && d.viaId === shade.id,
          ),
      )
      .map((shade) => ({
        id: shade.id,
        nodeId: node.id,
        name: shade.name,
      }));
  });

  return (
    <div>
      <SettingsGroup>
        <SettingsGroupContent>
          <SettingsRow>
            <div className="font-mono text-xs text-gray-500">ID: {node.id}</div>
          </SettingsRow>
          <SettingsRow>
            <SettingsLabel htmlFor="name">Name</SettingsLabel>
            <SettingsInput
              id="name"
              value={node.data.title}
              disabled={readOnly}
              onEndEdit={(value) => {
                reactFlowInstance?.updateNodeData(node.id, {
                  title: value,
                });
              }}
            />
          </SettingsRow>
          <SettingsRow>
            <SettingsLabel htmlFor="direction">Direction</SettingsLabel>
            <Select
              value={node.data.direction ?? "left"}
              disabled={readOnly}
              onValueChange={(value) => {
                reactFlowInstance?.updateNodeData(node.id, {
                  direction: value,
                });
              }}
            >
              <SelectTrigger className="h-10">
                <SelectValue placeholder="Select direction" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="left">Left</SelectItem>
                <SelectItem value="right">Right</SelectItem>
              </SelectContent>
            </Select>
          </SettingsRow>
        </SettingsGroupContent>
      </SettingsGroup>
      <SettingsGroup>
        <SettingsGroupHeader className="flex flex-row justify-between items-center -mb-2">
          <span>Devices</span>

          {!readOnly && (
            <Tooltip>
              <TooltipTrigger asChild>
                <TooltipDropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="size-6 data-[state=open]:bg-gray-100"
                    >
                      <PlusIcon className="size-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="mr-5">
                    <DropdownMenuLabel>Add Device</DropdownMenuLabel>
                    <DropdownMenuItem
                      onClick={() => {
                        if (!yDevicesMap) {
                          return;
                        }

                        // Create default dimming curve
                        const dimmingCurveArray = new Y.Array<any>();
                        dimmingCurveArray.insert(0, [
                          { x: 0, y: 0 },
                          { x: 0.5, y: 0.5 },
                          { x: 1, y: 1 },
                        ]);

                        const device = defaultDmxDevice({
                          sortIndex: Object.keys(yDevicesMap.entries).length,
                        });

                        const deviceMap = objectToYMap(device);
                        if (deviceMap) {
                          // @ts-expect-error - not sure why type isn't happy, may be fixed with Y.js v14
                          deviceMap.set("dimmingCurve", dimmingCurveArray);
                          yDevicesMap.set(device.id, deviceMap);
                        }
                      }}
                    >
                      DMX Device
                    </DropdownMenuItem>
                    {availableSomoLights.length > 0 && (
                      <>
                        <DropdownMenuSeparator />
                        <DropdownMenuLabel>RF Switch Lights</DropdownMenuLabel>
                        {availableSomoLights.map((light) => {
                          return (
                            <DropdownMenuItem
                              onClick={() => {
                                if (!yDevicesMap) {
                                  return;
                                }
                                const index = Object.keys(
                                  yDevicesMap.entries,
                                ).length;
                                const device: RoomSwitchDevice = {
                                  id: randomId(),
                                  type: "roomSwitch",
                                  viaId: light.id,
                                  nodeId: light.nodeId,
                                  sortIndex: index,
                                  showLabel: true,
                                };
                                yDevicesMap.set(
                                  device.id,
                                  objectToYMap(device),
                                );
                              }}
                              key={`${light.nodeId}-${light.id}`}
                            >
                              {light.name}
                            </DropdownMenuItem>
                          );
                        })}
                      </>
                    )}
                    {availableRoomDimmerLights.length > 0 && (
                      <>
                        <DropdownMenuSeparator />
                        <DropdownMenuLabel>
                          Room Dimmer Lights
                        </DropdownMenuLabel>
                        {availableRoomDimmerLights.map((light) => {
                          return (
                            <DropdownMenuItem
                              onClick={() => {
                                if (!yDevicesMap) {
                                  return;
                                }
                                const index = Object.keys(
                                  yDevicesMap.entries,
                                ).length;
                                const device: RoomDimmerDevice = {
                                  id: randomId(),
                                  type: "roomDimmer",
                                  viaId: light.id as RoomDimmerDevice["viaId"],
                                  nodeId: light.nodeId,
                                  sortIndex: index,
                                  showLabel: true,
                                };
                                yDevicesMap.set(
                                  device.id,
                                  objectToYMap(device),
                                );
                              }}
                              key={`${light.nodeId}-${light.id}`}
                            >
                              {light.name}
                            </DropdownMenuItem>
                          );
                        })}
                      </>
                    )}
                    {availableSomoShadesLights.length > 0 && (
                      <>
                        <DropdownMenuSeparator />
                        <DropdownMenuLabel>Somo Shades</DropdownMenuLabel>
                        {availableSomoShadesLights.map((light) => {
                          return (
                            <DropdownMenuItem
                              onClick={() => {
                                if (!yDevicesMap) {
                                  return;
                                }
                                const index = Object.keys(
                                  yDevicesMap.entries,
                                ).length;
                                const device: SomoShadesDevice = {
                                  id: randomId(),
                                  type: "somoShades",
                                  viaId: light.id as SomoShadesDevice["viaId"],
                                  nodeId: light.nodeId,
                                  sortIndex: index,
                                  showLabel: true,
                                };
                                yDevicesMap.set(
                                  device.id,
                                  objectToYMap(device),
                                );
                              }}
                              key={`${light.nodeId}-${light.id}`}
                            >
                              {light.name}
                            </DropdownMenuItem>
                          );
                        })}
                      </>
                    )}
                    {availableSomoFanLights.length > 0 && (
                      <>
                        <DropdownMenuSeparator />
                        <DropdownMenuLabel>Somo Fan</DropdownMenuLabel>
                        {availableSomoFanLights.map((light) => {
                          return (
                            <DropdownMenuItem
                              onClick={() => {
                                if (!yDevicesMap) {
                                  return;
                                }
                                const index = Object.keys(
                                  yDevicesMap.entries,
                                ).length;
                                const device: SomoFanDevice = {
                                  id: randomId(),
                                  type: "somoFan",
                                  viaId: light.id as SomoFanDevice["viaId"],
                                  nodeId: light.nodeId,
                                  sortIndex: index,
                                  showLabel: true,
                                  defaultFanType: "onoff",
                                };
                                yDevicesMap.set(
                                  device.id,
                                  objectToYMap(device),
                                );
                              }}
                              key={`${light.nodeId}-${light.id}`}
                            >
                              {light.name}
                            </DropdownMenuItem>
                          );
                        })}
                      </>
                    )}
                    {availableSomoThermostats.length > 0 && (
                      <>
                        <DropdownMenuSeparator />
                        <DropdownMenuLabel>Thermostat</DropdownMenuLabel>
                        {availableSomoThermostats.map((thermostat) => {
                          return (
                            <DropdownMenuItem
                              onClick={() => {
                                if (!yDevicesMap) {
                                  return;
                                }
                                const index = Object.keys(
                                  yDevicesMap.entries,
                                ).length;
                                const device: SomoThermostatDevice = {
                                  id: randomId(),
                                  type: "somoThermostat",
                                  viaId:
                                    thermostat.id as SomoThermostatDevice["viaId"],
                                  nodeId: thermostat.nodeId,
                                  sortIndex: index,
                                  showLabel: true,
                                };
                                yDevicesMap.set(
                                  device.id,
                                  objectToYMap(device),
                                );

                                // Add thermostat actions to the parent vias
                                addActionsToParentVias({
                                  yDoc,
                                  parentNodeId: thermostat.nodeId,
                                  viaKeys: [
                                    "viaUp",
                                    "viaDown",
                                    "modeCool",
                                    "modeHeat",
                                    "modeAuto",
                                    "modeFan",
                                    "fanAuto",
                                    "fanLow",
                                    "fanMedium",
                                    "fanHigh",
                                  ],
                                  actionType: "hvac",
                                  deviceId: device.id,
                                });
                              }}
                              key={`${thermostat.nodeId}-${thermostat.id}`}
                            >
                              {thermostat.name}
                            </DropdownMenuItem>
                          );
                        })}
                      </>
                    )}
                    {availableSomoIrControllers.length > 0 && (
                      <>
                        <DropdownMenuSeparator />
                        <DropdownMenuLabel>
                          IR HVAC Controller
                        </DropdownMenuLabel>
                        {availableSomoIrControllers.map((irController) => {
                          return (
                            <DropdownMenuItem
                              onClick={() => {
                                if (!yDevicesMap) {
                                  return;
                                }
                                const index = Object.keys(
                                  yDevicesMap.entries,
                                ).length;
                                const device: SomoIrControllerDevice = {
                                  id: randomId(),
                                  type: "somoIrController",
                                  viaId:
                                    irController.id as SomoIrControllerDevice["viaId"],
                                  nodeId: irController.nodeId,
                                  sortIndex: index,
                                  showLabel: true,
                                };
                                yDevicesMap.set(
                                  device.id,
                                  objectToYMap(device),
                                );

                                // Add thermostat actions to the parent vias
                                addActionsToParentVias({
                                  yDoc,
                                  parentNodeId: irController.nodeId,
                                  viaKeys: [
                                    "viaUp",
                                    "viaDown",
                                    "modeCool",
                                    "modeHeat",
                                    "modeAuto",
                                    "modeFan",
                                    "fanAuto",
                                    "fanLow",
                                    "fanMedium",
                                    "fanHigh",
                                  ],
                                  actionType: "hvac",
                                  deviceId: device.id,
                                });
                              }}
                              key={`${irController.nodeId}-${irController.id}`}
                            >
                              {irController.name}
                            </DropdownMenuItem>
                          );
                        })}
                      </>
                    )}
                    {availableZeroToTenVoltDimmers.length > 0 && (
                      <>
                        <DropdownMenuSeparator />
                        <DropdownMenuLabel>0-10V Dimmers</DropdownMenuLabel>
                        {availableZeroToTenVoltDimmers.map((dimmer) => {
                          return (
                            <DropdownMenuItem
                              onClick={() => {
                                if (!yDevicesMap) {
                                  return;
                                }
                                const index = Object.keys(
                                  yDevicesMap.entries,
                                ).length;
                                const device: ZeroToTenVoltDimmerDevice = {
                                  id: randomId(),
                                  type: "zeroToTenVoltDimmer",
                                  viaId: dimmer.id,
                                  nodeId: dimmer.nodeId,
                                  sortIndex: index,
                                  showLabel: true,
                                  name: dimmer.name,
                                  dimmingType: dimmer.dimmingType,
                                  useRelay: dimmer.useRelay || false,
                                  minBrightness: dimmer.minBrightness || 0,
                                  maxBrightness: dimmer.maxBrightness || 100,
                                  defaultDimmingSpeed:
                                    dimmer.defaultDimmingSpeed || 0.2,
                                };
                                yDevicesMap.set(
                                  device.id,
                                  objectToYMap(device),
                                );
                              }}
                              key={`${dimmer.nodeId}-${dimmer.id}`}
                            >
                              {dimmer.name}
                            </DropdownMenuItem>
                          );
                        })}
                      </>
                    )}
                    {availableRelayOutputs.length > 0 && (
                      <>
                        <DropdownMenuSeparator />
                        <DropdownMenuLabel>Relay Outputs</DropdownMenuLabel>
                        {availableRelayOutputs.map((relayOutput) => {
                          return (
                            <DropdownMenuItem
                              onClick={() => {
                                if (!yDevicesMap) {
                                  return;
                                }
                                // Parse the value to get nodeId and relayOutputId
                                const [nodeId, relayOutputId] =
                                  relayOutput.value.split(":");

                                // Find the actual relay output from the node data
                                const canBusNode = canBusControllerNodes.find(
                                  (n) => n.id === nodeId,
                                );
                                if (!canBusNode) {
                                  return;
                                }
                                const data = getData(
                                  yDoc,
                                  canBusNode.data.dataId,
                                  "canbusController",
                                );
                                const actualRelayOutput =
                                  data?.relayOutputs?.[relayOutputId];

                                if (!actualRelayOutput) {
                                  return;
                                }

                                const newDevice: RelayOutputDevice = {
                                  id: randomId(),
                                  type: "relayOutput",
                                  name: actualRelayOutput.name,
                                  icon: actualRelayOutput.icon,
                                  nodeId: nodeId,
                                  viaId: actualRelayOutput.id,
                                  anchorPosition: "left",
                                  showLabel: true,
                                  sortIndex: Array.from(yDevicesMap.entries())
                                    .length,
                                };
                                yDevicesMap.set(
                                  newDevice.id,
                                  objectToYMap(newDevice),
                                );
                              }}
                              key={relayOutput.value}
                            >
                              {relayOutput.label}
                            </DropdownMenuItem>
                          );
                        })}
                      </>
                    )}
                    {availableSomfyShades.length > 0 && (
                      <>
                        <DropdownMenuSeparator />
                        <DropdownMenuLabel>Somfy Shades</DropdownMenuLabel>
                        {availableSomfyShades.map((shade) => {
                          return (
                            <DropdownMenuItem
                              onClick={() => {
                                if (!yDevicesMap) {
                                  return;
                                }
                                const index = Object.keys(
                                  yDevicesMap.entries,
                                ).length;
                                const device: SomfyShadesBaseDevice = {
                                  id: randomId(),
                                  type: "somfyShades",
                                  viaId: shade.id,
                                  nodeId: shade.nodeId,
                                  sortIndex: index,
                                  showLabel: true,
                                  name: shade.name,
                                  icon: "shade",
                                };
                                yDevicesMap.set(
                                  device.id,
                                  objectToYMap(device),
                                );
                              }}
                              key={`${shade.nodeId}-${shade.id}`}
                            >
                              {shade.name}
                            </DropdownMenuItem>
                          );
                        })}
                      </>
                    )}
                  </DropdownMenuContent>
                </TooltipDropdownMenu>
              </TooltipTrigger>
              <TooltipContent>Add Device</TooltipContent>
            </Tooltip>
          )}
        </SettingsGroupHeader>
        <SettingsGroupContent>
          {nodeDevices.map((device) =>
            match(device)
              .with({ type: "roomSwitch" }, (device) => (
                <SomoDeviceSettings
                  key={device.id}
                  device={device}
                  nodeId={node.id}
                  collapsed={deviceCollapsedState[device.id] ?? true}
                  onCollapseChange={(isCollapsed) => {
                    setDeviceCollapsedState((prev) => ({
                      ...prev,
                      [device.id]: isCollapsed,
                    }));
                  }}
                  readOnly={readOnly}
                />
              ))
              .with({ type: "roomDimmer" }, (device) => (
                <RoomDimmerDeviceSettings
                  key={device.id}
                  device={device}
                  nodeId={node.id}
                  collapsed={deviceCollapsedState[device.id] ?? true}
                  onCollapseChange={(isCollapsed) => {
                    setDeviceCollapsedState((prev) => ({
                      ...prev,
                      [device.id]: isCollapsed,
                    }));
                  }}
                  readOnly={readOnly}
                />
              ))
              .with({ type: "somoShades" }, (device) => (
                <SomoShadesDeviceSettings
                  key={device.id}
                  device={device}
                  nodeId={node.id}
                  collapsed={deviceCollapsedState[device.id] ?? true}
                  onCollapseChange={(isCollapsed) => {
                    setDeviceCollapsedState((prev) => ({
                      ...prev,
                      [device.id]: isCollapsed,
                    }));
                  }}
                  readOnly={readOnly}
                />
              ))
              .with({ type: "somfyShades" }, (device) => (
                <SomfyShadesDeviceSettings
                  key={device.id}
                  device={device}
                  nodeId={node.id}
                  collapsed={deviceCollapsedState[device.id] ?? true}
                  onCollapseChange={(isCollapsed) => {
                    setDeviceCollapsedState((prev) => ({
                      ...prev,
                      [device.id]: isCollapsed,
                    }));
                  }}
                  readOnly={readOnly}
                />
              ))
              .with({ type: "somoFan" }, (device) => (
                <SomoFanDeviceSettings
                  key={device.id}
                  device={device}
                  nodeId={node.id}
                  collapsed={deviceCollapsedState[device.id] ?? true}
                  onCollapseChange={(isCollapsed) => {
                    setDeviceCollapsedState((prev) => ({
                      ...prev,
                      [device.id]: isCollapsed,
                    }));
                  }}
                  readOnly={readOnly}
                />
              ))
              .with({ type: "somoThermostat" }, (device) => (
                <SomoThermostatDeviceSettings
                  key={device.id}
                  device={device}
                  nodeId={node.id}
                  collapsed={deviceCollapsedState[device.id] ?? true}
                  onCollapseChange={(isCollapsed) => {
                    setDeviceCollapsedState((prev) => ({
                      ...prev,
                      [device.id]: isCollapsed,
                    }));
                  }}
                  readOnly={readOnly}
                />
              ))
              .with({ type: "somoIrController" }, (device) => (
                <SomoIrControllerDeviceSettings
                  key={device.id}
                  device={device}
                  nodeId={node.id}
                  collapsed={deviceCollapsedState[device.id] ?? true}
                  onCollapseChange={(isCollapsed) => {
                    setDeviceCollapsedState((prev) => ({
                      ...prev,
                      [device.id]: isCollapsed,
                    }));
                  }}
                  readOnly={readOnly}
                />
              ))
              .with({ type: "zeroToTenVoltDimmer" }, (device) => (
                <ZeroToTenVoltDimmerDeviceSettings
                  key={device.id}
                  device={device}
                  nodeId={node.id}
                  collapsed={deviceCollapsedState[device.id] ?? true}
                  onCollapseChange={(isCollapsed) => {
                    setDeviceCollapsedState((prev) => ({
                      ...prev,
                      [device.id]: isCollapsed,
                    }));
                  }}
                  readOnly={readOnly}
                />
              ))
              .with({ type: "relayOutput" }, (device) => (
                <RelayOutputDeviceSettings
                  key={device.id}
                  device={device}
                  nodeId={node.id}
                  collapsed={deviceCollapsedState[device.id] ?? true}
                  onCollapseChange={(isCollapsed) => {
                    setDeviceCollapsedState((prev) => ({
                      ...prev,
                      [device.id]: isCollapsed,
                    }));
                  }}
                  readOnly={readOnly}
                />
              ))
              // Trying to perform an exhaustive check results in a type error:
              // `Type instantiation is excessively deep and possibly infinite`
              // => Use `otherwise` instead.
              .otherwise(() => null),
          )}
        </SettingsGroupContent>
      </SettingsGroup>
    </div>
  );
}
