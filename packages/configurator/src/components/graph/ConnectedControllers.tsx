import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { Controller, useControllers } from "@/contexts/ControllerContext";
import { useBasestationSerial } from "@/hooks/useBasestationSerial";
import { useOnlineStatus } from "@/hooks/useOnlineStatus";
import { parseSystemStats } from "@/lib/parseSystemStats";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  filterNodes,
  GraphNodesByType,
  LayoutContainerNode,
  SomfyShadesDevice,
} from "@somo/shared";
import CryptoJS from "crypto-js";
import TimeAgo from "javascript-time-ago";
import en from "javascript-time-ago/locale/en";
import {
  CirclePower,
  EllipsisVertical,
  EthernetPortIcon,
  FileWarning,
  Usb,
  Wifi,
} from "lucide-react";
import { useEffect, useMemo, useState } from "react";
import { ControllerRenderProps, useForm, UseFormReturn } from "react-hook-form";
import { match } from "ts-pattern";
import "web-serial-polyfill";
import { SerialPort } from "web-serial-polyfill";
import { z } from "zod";
import { getData, useGetData } from "../data/useData";
import { FirmwareUpdateModal } from "./FirmwareUpdateModal";
import { Graph } from "./Graph";
import { useReactFlowContext } from "./ReactFlowContext";
import { SerialMonitorButton } from "./serial-monitor/SerialMonitorButton";
import { SerialMonitorDialog } from "./serial-monitor/SerialMonitorDialog";
import { SystemStats, SystemStatsModal } from "./SystemStatsModal";
import { UpdateQRModal } from "./UpdateQRModal";

TimeAgo.addDefaultLocale(en);
const timeAgo = new TimeAgo("en-US");

const idQrCode = z.object({ id: z.string(), qrCode: z.string() });

const FormValues = z.object({
  configuration: z
    .object({
      rf: z.object({
        channel: z.number().prefault(15),
        network: z.number().prefault(15),
      }),
      networkType: z.enum(["wifi", "ethernet"]).prefault("wifi"),
      wifi: z.object({
        ssid: z.string().max(32).prefault(""),
        password: z.string().max(32).prefault(""),
      }),
      serverAddress: z
        .string()
        .max(128)
        .prefault("wss://somo-server.fly.dev/ws/service"),
      mac: z.object({
        useMacAddress: z.boolean().prefault(false),
        macAddress: z
          .string()
          .regex(
            /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/,
            "Invalid MAC address format",
          )
          .or(z.literal(""))
          .prefault(""),
      }),
      dhcp: z.object({
        staticIp: z.boolean().prefault(false),
        ipAddress: z
          .string()
          .max(15)
          .regex(
            /^((25[0-5]|(2[0-4]|1\d|[1-9]|)\d)\.?\b){4}$/,
            "Invalid IP address",
          )
          .or(z.literal(""))
          .prefault(""),
        subnetMask: z
          .string()
          .max(15)
          .regex(
            /^((25[0-5]|(2[0-4]|1\d|[1-9]|)\d)\.?\b){4}$/,
            "Invalid subnet mask",
          )
          .or(z.literal(""))
          .prefault(""),
        gateway: z
          .string()
          .max(15)
          .regex(
            /^((25[0-5]|(2[0-4]|1\d|[1-9]|)\d)\.?\b){4}$/,
            "Invalid gateway address",
          )
          .or(z.literal(""))
          .prefault(""),
        dnsServer: z
          .string()
          .max(15)
          .regex(
            /^((25[0-5]|(2[0-4]|1\d|[1-9]|)\d)\.?\b){4}$/,
            "Invalid DNS address",
          )
          .or(z.literal(""))
          .prefault(""),
      }),
      roomSwitchIdToQRCodeMapping: z.array(idQrCode).prefault([]),
      roomDimmerIdToQRCodeMapping: z.array(idQrCode).prefault([]),
      outletDimmerIdToQRCodeMapping: z.array(idQrCode).prefault([]),
      canBusControllerIdToQRCodeMapping: z.array(idQrCode).prefault([]),
      doorSensorIdToQRCodeMapping: z.array(idQrCode).prefault([]),
      presenceSensorIdToQRCodeMapping: z.array(idQrCode).prefault([]),
      somfyShadesDeviceIdMapping: z
        .array(
          z.object({
            baseStationId: z.string(),
            shadeKey: z.string(),
            deviceId: z.number(),
          }),
        )
        .prefault([]),
    })
    .prefault({
      rf: {
        channel: 15,
        network: 15,
      },
      networkType: "wifi",
      wifi: {
        ssid: "",
        password: "",
      },
      serverAddress: "wss://somo-server.fly.dev/ws/service",
      mac: {
        useMacAddress: false,
        macAddress: "",
      },
      dhcp: {
        staticIp: false,
        ipAddress: "",
        subnetMask: "",
        gateway: "",
        dnsServer: "",
      },
      roomSwitchIdToQRCodeMapping: [],
      roomDimmerIdToQRCodeMapping: [],
      outletDimmerIdToQRCodeMapping: [],
      canBusControllerIdToQRCodeMapping: [],
      doorSensorIdToQRCodeMapping: [],
      presenceSensorIdToQRCodeMapping: [],
      somfyShadesDeviceIdMapping: [],
    }),
});
type FormValues = z.infer<typeof FormValues>;

type Form = UseFormReturn<FormValues, unknown, FormValues>;

type QrCodeMapping = {
  id: string;
  qrCode: string;
};

export function ConnectedControllers({
  orgId,
  roomId,
  graph,
}: {
  orgId: string;
  roomId: string;
  graph: Graph;
}) {
  const { nodes, edges } = graph;
  const { controllers, sendMessage, updateController } = useControllers();
  const [serialMonitorDialogOpen, setSerialMonitorDialogOpen] = useState(false);
  const [serialMonitorPort, setSerialMonitorPort] = useState<
    SerialPort | undefined
  >(undefined);
  const [serialConfigDialogOpen, setSerialConfigDialogOpen] = useState(false);
  const [updateQRModalOpen, setUpdateQRModalOpen] = useState(false);
  const [systemStatsModalOpen, setSystemStatsModalOpen] = useState(false);
  const [systemStats, setSystemStats] = useState<SystemStats | null>(null);
  const [statsLoading, setStatsLoading] = useState(false);
  const [statsError, setStatsError] = useState<string | null>(null);

  // Connection mode and serial handling
  const isOnline = useOnlineStatus();
  const basestationSerial = useBasestationSerial();

  // Initialize serial mode from localStorage or default to offline behavior
  const [useSerialMode, setUseSerialMode] = useState(() => {
    const saved = localStorage.getItem("connectionMode");
    return saved ? saved === "serial" : !isOnline;
  });

  // Save to localStorage when mode changes
  const handleSerialModeChange = (checked: boolean) => {
    setUseSerialMode(checked);
    localStorage.setItem("connectionMode", checked ? "serial" : "backend");
  };

  // Force serial mode when offline
  useEffect(() => {
    if (!isOnline) {
      setUseSerialMode(true);
    }
  }, [isOnline]);

  // Auto-refresh stats every 5 seconds when modal is open and connected
  useEffect(() => {
    if (!systemStatsModalOpen) {
      return;
    }

    // Set up interval for auto-refresh
    const interval = setInterval(() => {
      const isSerial = useSerialMode && basestationSerial.isConnected;
      const qrCode = basestationSerial.qrCode;

      if (isSerial || (!isSerial && qrCode)) {
        // Refresh stats silently (without changing loading state since we have cached data)
        const currentIsSerial = useSerialMode && basestationSerial.isConnected;
        const currentQrCode = basestationSerial.qrCode;

        if (currentIsSerial && basestationSerial.controller) {
          // For serial connection, silently refresh
          let capturedOutput = "";
          let timeout: NodeJS.Timeout;

          const handleSerialData = (data: string) => {
            capturedOutput += data;

            clearTimeout(timeout);
            timeout = setTimeout(() => {
              const parsedStats = parseSystemStats(capturedOutput);
              if (parsedStats) {
                setSystemStats(parsedStats);
                setStatsError(null);
              }
              basestationSerial.controller?.off("serialData", handleSerialData);
            }, 1000);
          };

          basestationSerial.controller.on("serialData", handleSerialData);
          basestationSerial.controller.sendCommand("STATS_JSON").catch(() => {
            basestationSerial.controller?.off("serialData", handleSerialData);
          });
        } else if (!currentIsSerial && currentQrCode) {
          // For WebSocket, would implement here when ready
          sendMessage({
            action: "sendCommand",
            qrCode: currentQrCode,
            command: "STATS_JSON",
          });
        }
      }
    }, 5000); // Refresh every 5 seconds

    return () => clearInterval(interval);
  }, [
    systemStatsModalOpen,
    useSerialMode,
    basestationSerial.isConnected,
    basestationSerial.controller,
    basestationSerial.qrCode,
    sendMessage,
  ]);

  const roomSwitchNodes = filterNodes(nodes, "roomSwitchContainer");
  const roomDimmerNodes = filterNodes(nodes, "roomDimmerContainer");
  const outletDimmerNodes = filterNodes(nodes, "outletDimmerContainer");
  const canBusControllerNodes = filterNodes(nodes, "canbusControllerContainer");
  const doorSensorNodes = filterNodes(nodes, "doorSensorContainer");
  const presenceSensorNodes = filterNodes(nodes, "presenceSensorContainer");
  const baseStationNodes = filterNodes(nodes, "baseStationContainer");

  // create a hash of the nodes and edges
  const currentVersion = useMemo(() => {
    const hash = CryptoJS.SHA256(
      JSON.stringify(nodes.map((n) => n.data)) +
        JSON.stringify(edges.map((e) => e.data ?? {})),
    );
    return hash.toString(CryptoJS.enc.Hex);
  }, [nodes, edges]);

  const handleStatsRequest = async (isSerial = false, qrCode?: string) => {
    // Only show loading spinner if we don't have cached stats yet
    if (!systemStats) {
      setStatsLoading(true);
    }
    setStatsError(null);
    setSystemStatsModalOpen(true);

    try {
      if (
        isSerial &&
        basestationSerial.isConnected &&
        basestationSerial.controller
      ) {
        // For serial connection, listen for the serialData event
        let capturedOutput = "";
        let timeout: NodeJS.Timeout;

        const handleSerialData = (data: string) => {
          capturedOutput += data;

          // Clear existing timeout and set a new one
          clearTimeout(timeout);
          timeout = setTimeout(() => {
            // Parse the captured output after no new data for 500ms
            const parsedStats = parseSystemStats(capturedOutput);
            if (parsedStats) {
              setSystemStats(parsedStats);
            } else {
              setStatsError("Failed to parse system statistics");
            }
            setStatsLoading(false);
            basestationSerial.controller?.off("serialData", handleSerialData);
          }, 1000); // Increased timeout to 1 second
        };

        // Listen for serial data
        basestationSerial.controller.on("serialData", handleSerialData);

        // Send the STATS_JSON command for more reliable parsing
        await basestationSerial.controller.sendCommand("STATS_JSON");

        // Set a maximum timeout of 10 seconds
        setTimeout(() => {
          if (statsLoading) {
            clearTimeout(timeout);
            setStatsError("Timeout waiting for system statistics");
            setStatsLoading(false);
            basestationSerial.controller?.off("serialData", handleSerialData);
          }
        }, 10000);
      } else if (!isSerial && qrCode) {
        // For WebSocket connection
        sendMessage({
          action: "sendCommand",
          qrCode: qrCode,
          command: "STATS_JSON",
        });
        // Note: For WebSocket, we'd need to set up a listener for the response
        // For now, just show an info message
        setStatsError(
          "WebSocket stats collection not fully implemented yet. Check browser console for output.",
        );
        setStatsLoading(false);
      }
    } catch (error) {
      setStatsError(`Failed to collect system stats: ${error}`);
      setStatsLoading(false);
    }
  };

  return (
    <div className="flex flex-col p-3">
      <SerialMonitorDialog
        port={serialMonitorPort}
        dialogOpen={serialMonitorDialogOpen}
        setDialogOpen={setSerialMonitorDialogOpen}
        graph={graph}
        roomId={roomId}
      />
      <UpdateQRModal
        open={updateQRModalOpen}
        onOpenChange={setUpdateQRModalOpen}
        controller={basestationSerial.controller}
        isConnected={basestationSerial.isConnected}
        currentQRCode={basestationSerial.qrCode}
        onUpdated={(qrCode) => {
          basestationSerial.setQrCode(qrCode);
        }}
      />
      <FirmwareUpdateModal
        open={basestationSerial.isBootloader}
        onOpenChange={(open) => {
          if (!open) {
            // If user closes the modal, disconnect from bootloader
            basestationSerial.disconnect();
          }
        }}
        basestationSerial={basestationSerial}
      />
      <SystemStatsModal
        open={systemStatsModalOpen}
        onOpenChange={setSystemStatsModalOpen}
        stats={systemStats}
        loading={statsLoading}
        error={statsError}
      />
      <div className="flex flex-row items-center">
        <div className="text-sm font-medium flex-grow">
          Connected Controllers
        </div>
        <SerialMonitorButton
          onClick={(port) => {
            setSerialMonitorPort(port);
            setSerialMonitorDialogOpen(true);
          }}
        />
      </div>

      {/* Connection Mode Switch */}
      <div className="flex flex-row items-center justify-between py-2 px-3 bg-gray-50 rounded-md mb-3">
        <div className="flex items-center gap-2">
          <Wifi className="size-4" />
          <span className="text-sm">Backend</span>
        </div>
        <Switch
          checked={useSerialMode}
          onCheckedChange={handleSerialModeChange}
          disabled={!isOnline}
        />
        <div className="flex items-center gap-2">
          <Usb className="size-4" />
          <span className="text-sm">Serial</span>
        </div>
      </div>

      {/* Serial Connection Status */}
      {useSerialMode && (
        <>
          <div className="flex flex-row items-center justify-between py-2 px-3 bg-blue-50 rounded-md mb-3">
            <div className="flex items-center gap-2">
              <div
                className={`size-2 rounded-full ${basestationSerial.isConnected ? "bg-green-500" : "bg-gray-400"}`}
              />
              <span className="text-sm">
                {basestationSerial.isConnected
                  ? "Serial Connected"
                  : "Serial Disconnected"}
              </span>
            </div>
            {!basestationSerial.isConnected &&
              basestationSerial.isSupported && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={basestationSerial.connect}
                  disabled={basestationSerial.isConnecting}
                >
                  {basestationSerial.isConnecting ? "Connecting..." : "Connect"}
                </Button>
              )}
            {basestationSerial.isConnected && (
              <Button
                size="sm"
                variant="outline"
                onClick={basestationSerial.disconnect}
              >
                Disconnect
              </Button>
            )}
          </div>
          {basestationSerial.lastError && !basestationSerial.isConnected && (
            <div className="py-2 px-3 bg-red-50 rounded-md mb-3">
              <div className="text-sm text-red-800">
                {basestationSerial.lastError}
              </div>
            </div>
          )}
        </>
      )}

      {useSerialMode && !basestationSerial.isSupported && (
        <div className="py-2 px-3 bg-yellow-50 rounded-md mb-3">
          <div className="text-sm text-yellow-800">
            Web Serial API not supported in this browser
          </div>
        </div>
      )}

      {!isOnline && (
        <div className="py-2 px-3 bg-orange-50 rounded-md mb-3">
          <div className="text-sm text-orange-800">
            Offline mode - using serial connection only
          </div>
        </div>
      )}
      <div className="text-xs text-gray-500 mb-3">
        Current version #{currentVersion.slice(-8)}
      </div>
      {controllers.length === 0 && !useSerialMode && (
        <div className="text-xs text-gray-500">No controllers connected</div>
      )}
      {useSerialMode && (
        <div className="space-y-3 mb-4">
          {controllers.length === 0 && (
            <div className="text-xs text-gray-500">
              No WebSocket controllers connected. Use serial connection to
              configure a basestation directly.
            </div>
          )}
          {basestationSerial.isConnected && (
            <div className="border border-gray-200 rounded-lg p-3">
              <div className="flex items-center gap-2 mb-2">
                <div className="size-2 rounded-full bg-green-500" />
                <span className="text-sm font-medium">Serial Basestation</span>
              </div>
              <div className="text-xs text-gray-500 mb-3 space-y-1">
                <div>Connected via USB Serial</div>
                {basestationSerial.qrCode && (
                  <div>
                    QR Code:{" "}
                    <span className="font-mono">
                      {basestationSerial.qrCode}
                    </span>
                  </div>
                )}
                {basestationSerial.firmwareVersion && (
                  <div>
                    Firmware:{" "}
                    <span className="font-mono">
                      {basestationSerial.firmwareVersion}
                    </span>
                  </div>
                )}
              </div>
              <Dialog
                open={serialConfigDialogOpen}
                onOpenChange={setSerialConfigDialogOpen}
              >
                <div className="flex gap-2 justify-between items-center">
                  <DialogTrigger asChild>
                    <Button variant="outline" size="sm">
                      Configure
                    </Button>
                  </DialogTrigger>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <EllipsisVertical className="size-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      <DropdownMenuItem
                        onClick={() => setUpdateQRModalOpen(true)}
                      >
                        Update QR Code
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={async () => {
                          if (
                            basestationSerial.isConnected &&
                            basestationSerial.controller
                          ) {
                            try {
                              await basestationSerial.controller.sendCommand(
                                "PRINT_ACTIVE_CONFIGURATION",
                              );
                              console.log(
                                "Print active configuration command sent",
                              );
                            } catch (error) {
                              console.error(
                                "Failed to send print command:",
                                error,
                              );
                            }
                          }
                        }}
                      >
                        Print Active Configuration
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => handleStatsRequest(true)}
                      >
                        Print System Stats
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={async () => {
                          if (
                            basestationSerial.isConnected &&
                            basestationSerial.controller
                          ) {
                            try {
                              await basestationSerial.controller.sendCommand(
                                "ENTER_BOOTLOADER",
                              );
                              console.log("Enter bootloader command sent");
                            } catch (error) {
                              console.error(
                                "Failed to send enter bootloader command:",
                                error,
                              );
                            }
                          }
                        }}
                      >
                        Enter Bootloader
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={async () => {
                          if (
                            basestationSerial.isConnected &&
                            basestationSerial.controller
                          ) {
                            try {
                              await basestationSerial.controller.sendCommand(
                                "REBOOT",
                              );
                              console.log("Reboot command sent");
                            } catch (error) {
                              console.error(
                                "Failed to send reboot command:",
                                error,
                              );
                            }
                          }
                        }}
                      >
                        <CirclePower className="size-4 mr-2" />
                        <span>Reboot</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                <DialogContent className="py-0">
                  <DialogHeader className="pt-6 -mx-6 px-6 bg-gray-100 pb-6 rounded-t-md">
                    <DialogTitle>Configure Basestation</DialogTitle>
                    <DialogDescription>
                      Configure the basestation connected via serial.
                    </DialogDescription>
                  </DialogHeader>
                  <div className="max-h-[600px] overflow-y-auto -mx-6 px-6 pb-8 mb-0 p-4">
                    <ControllerForm
                      cacheQRCode={basestationSerial.qrCode}
                      initialConfiguration={null}
                      roomSwitchNodes={roomSwitchNodes}
                      roomDimmerNodes={roomDimmerNodes}
                      outletDimmerNodes={outletDimmerNodes}
                      canBusControllerNodes={canBusControllerNodes}
                      doorSensorNodes={doorSensorNodes}
                      presenceSensorNodes={presenceSensorNodes}
                      baseStationNodes={baseStationNodes}
                      onSubmit={async (values) => {
                        try {
                          // Filter out wifi config if using ethernet
                          const configuration = {
                            ...values.configuration,
                            wifi:
                              values.configuration.networkType === "wifi"
                                ? values.configuration.wifi
                                : undefined,
                          };
                          // Remove networkType as it's not part of the protobuf
                          const {
                            networkType: _,
                            ...configWithoutNetworkType
                          } = configuration;

                          await basestationSerial.sendConfigurationUpdate({
                            roomId,
                            version: currentVersion.slice(-6),
                            nodes: graph.nodes,
                            edges: graph.edges,
                            configuration: configWithoutNetworkType,
                            qrCode: basestationSerial.qrCode || "SERIAL", // Use retrieved QR code or fallback
                          });
                          console.log(
                            "Configuration sent to serial basestation",
                          );
                          setSerialConfigDialogOpen(false);
                        } catch (error) {
                          console.error(
                            "Failed to send configuration to serial basestation:",
                            error,
                          );
                          throw error;
                        }
                      }}
                      submitTitle="Configure Basestation"
                    />
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          )}
        </div>
      )}
      <div className="flex flex-col gap-2">
        {controllers.map((controller) => (
          <div
            key={controller.qrCode}
            className="flex flex-row items-center gap-2 border border-gray-200 rounded-lg p-2"
          >
            <div className="flex items-center justify-center size-6">
              <EthernetPortIcon className="size-4" />
            </div>

            <div className="flex flex-col flex-grow">
              <div className="text-xs font-medium">
                {controller.qrCode} - Version #
                {controller.currentVersion
                  ? controller.currentVersion.slice(-8)
                  : "unknown"}
              </div>
              <div className="flex flex-row gap-1">
                <div className="text-xs text-gray-500">{controller.ip}</div>
                <div className="text-xs text-gray-500">
                  - {timeAgo.format(controller.lastSeen)}
                </div>
              </div>
            </div>
            {(controller.orgId !== orgId || controller.roomId !== roomId) && (
              <Button
                variant="outline"
                onClick={() => {
                  sendMessage({
                    action: "adopt",
                    qrCode: controller.qrCode,
                    orgId,
                    roomId,
                  });
                }}
              >
                Adopt
              </Button>
            )}
            {controller.orgId === orgId && controller.roomId === roomId && (
              <>
                <ControllerFormDialog
                  currentVersion={currentVersion}
                  controller={controller}
                  roomSwitchNodes={roomSwitchNodes}
                  roomDimmerNodes={roomDimmerNodes}
                  outletDimmerNodes={outletDimmerNodes}
                  canBusControllerNodes={canBusControllerNodes}
                  doorSensorNodes={doorSensorNodes}
                  presenceSensorNodes={presenceSensorNodes}
                  baseStationNodes={baseStationNodes}
                  onUpdate={async (values) => {
                    // Filter out wifi config if using ethernet
                    const configuration = {
                      ...values.configuration,
                      wifi:
                        values.configuration.networkType === "wifi"
                          ? values.configuration.wifi
                          : undefined,
                    };
                    // Remove networkType as it's not part of the protobuf
                    const { networkType: _, ...configWithoutNetworkType } =
                      configuration;

                    updateController(
                      controller.qrCode,
                      currentVersion,
                      graph,
                      configWithoutNetworkType as Controller["configuration"],
                      roomId,
                    );
                    if (useSerialMode && basestationSerial.isConnected) {
                      // Use direct serial connection
                      try {
                        // Filter out wifi config if using ethernet
                        const configuration = {
                          ...values.configuration,
                          wifi:
                            values.configuration.networkType === "wifi"
                              ? values.configuration.wifi
                              : undefined,
                        };
                        // Remove networkType as it's not part of the protobuf
                        const { networkType: _, ...configWithoutNetworkType } =
                          configuration;

                        await basestationSerial.sendConfigurationUpdate({
                          roomId,
                          version: currentVersion,
                          nodes: graph.nodes,
                          edges: graph.edges,
                          configuration: configWithoutNetworkType,
                          qrCode: controller.qrCode,
                        });
                        console.log("Configuration sent via serial");
                      } catch (error) {
                        console.error(
                          "Failed to send configuration via serial:",
                          error,
                        );
                      }
                    }
                  }}
                />

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="flex-shrink-0 data-[state=open]:bg-gray-100"
                    >
                      <EllipsisVertical className="size-4 flex-shrink-0" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="mr-6">
                    <DropdownMenuItem
                      onClick={() => {
                        sendMessage({
                          action: "sendCommand",
                          qrCode: controller.qrCode,
                          command: "PRINT_ACTIVE_CONFIGURATION",
                        });
                        console.log(
                          "Print active configuration command sent to",
                          controller.qrCode,
                        );
                      }}
                    >
                      Print Active Configuration
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() =>
                        handleStatsRequest(false, controller.qrCode)
                      }
                    >
                      Print System Stats
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => {
                        sendMessage({
                          action: "reboot",
                          qrCode: controller.qrCode,
                        });
                      }}
                    >
                      <CirclePower className="size-4 flex-shrink-0" />
                      <span>Reboot</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}

function ControllerFormDialog({
  controller,
  roomSwitchNodes,
  roomDimmerNodes,
  outletDimmerNodes,
  canBusControllerNodes,
  doorSensorNodes,
  presenceSensorNodes,
  baseStationNodes,
  onUpdate,
  currentVersion,
}: {
  controller: Controller;
  roomSwitchNodes: LayoutContainerNode<"roomSwitchContainer">[];
  roomDimmerNodes: LayoutContainerNode<"roomDimmerContainer">[];
  outletDimmerNodes: GraphNodesByType["outletDimmerContainer"][];
  canBusControllerNodes: GraphNodesByType["canbusControllerContainer"][];
  onUpdate: (values: FormValues) => void | Promise<void>;
  doorSensorNodes: GraphNodesByType["doorSensorContainer"][];
  presenceSensorNodes: GraphNodesByType["presenceSensorContainer"][];
  baseStationNodes: GraphNodesByType["baseStationContainer"][];
  currentVersion: string;
}) {
  const [dialogOpen, setDialogOpen] = useState(false);

  async function onSubmit(values: FormValues) {
    try {
      await onUpdate(values);
      setDialogOpen(false);
    } catch (error) {
      console.error("Failed to update controller:", error);
      // Keep dialog open on error
    }
  }

  return (
    <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
      <DialogTrigger asChild>
        <Button variant="outline">
          {controller.currentVersion !== currentVersion && (
            <FileWarning className="size-4 flex-shrink-0" />
          )}
          <span>Update</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="py-0">
        <DialogHeader className="pt-6 -mx-6 px-6 bg-gray-100 pb-6 rounded-t-md">
          <DialogTitle>Update Controller</DialogTitle>
          <DialogDescription>
            Update the controller to the latest version.
          </DialogDescription>
        </DialogHeader>

        <div className="max-h-[600px] overflow-y-auto -mx-6 px-6 pb-8 mb-0 p-4">
          <ControllerForm
            cacheQRCode={controller.qrCode}
            initialConfiguration={controller.configuration}
            roomSwitchNodes={roomSwitchNodes}
            roomDimmerNodes={roomDimmerNodes}
            outletDimmerNodes={outletDimmerNodes}
            canBusControllerNodes={canBusControllerNodes}
            doorSensorNodes={doorSensorNodes}
            presenceSensorNodes={presenceSensorNodes}
            baseStationNodes={baseStationNodes}
            onSubmit={onSubmit}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
}

function ControllerForm({
  cacheQRCode,
  initialConfiguration,
  roomSwitchNodes,
  roomDimmerNodes,
  outletDimmerNodes,
  canBusControllerNodes,
  doorSensorNodes,
  presenceSensorNodes,
  baseStationNodes,
  onSubmit,
  hideWifi = false,
  submitTitle = "Update Controller",
}: {
  cacheQRCode: string | null;
  initialConfiguration: Controller["configuration"];
  roomSwitchNodes: LayoutContainerNode<"roomSwitchContainer">[];
  roomDimmerNodes: LayoutContainerNode<"roomDimmerContainer">[];
  doorSensorNodes: LayoutContainerNode<"doorSensorContainer">[];
  outletDimmerNodes: GraphNodesByType["outletDimmerContainer"][];
  canBusControllerNodes: GraphNodesByType["canbusControllerContainer"][];
  presenceSensorNodes: GraphNodesByType["presenceSensorContainer"][];
  baseStationNodes: GraphNodesByType["baseStationContainer"][];
  onSubmit: (values: FormValues) => void | Promise<void>;
  hideWifi?: boolean;
  submitTitle?: string;
}) {
  const { yDoc } = useReactFlowContext();
  const saveFormDataToCache = (qrCode: string | null, data: FormValues) => {
    if (!qrCode) {
      return;
    }
    try {
      localStorage.setItem(`form_cache_${qrCode}`, JSON.stringify(data));
    } catch (error) {
      console.warn("Failed to save form data to cache:", error);
    }
  };

  const form = useForm<FormValues, unknown, FormValues>({
    // @ts-expect-error - https://github.com/react-hook-form/resolvers/issues/792
    resolver: zodResolver(FormValues),
    defaultValues: getDefaultValues({
      cacheQRCode,
      initialConfiguration,
      roomSwitchNodes,
      roomDimmerNodes,
      outletDimmerNodes,
      canBusControllerNodes,
      doorSensorNodes,
      presenceSensorNodes,
      baseStationNodes,
    }),
  });

  // State to track form submission
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Clear cache on successful submission
  const handleSubmit = async (values: FormValues) => {
    setIsSubmitting(true);
    try {
      saveFormDataToCache(cacheQRCode, values);
      await onSubmit(values);
    } finally {
      setIsSubmitting(false);
    }
  };

  const [emptyFieldsCount, setEmptyFieldCount] = useState(0);
  const hasEmptyFields = emptyFieldsCount > 0;

  useEffect(() => {
    setEmptyFieldCount(countEmptyFields(form));
  }, [form]);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)}>
        <div className="flex flex-col gap-4">
          <Card className="rounded-md">
            <CardHeader className="px-3 pt-2 pb-0 font-medium text-sm text-gray-500">
              RF Network
            </CardHeader>
            <CardContent className="space-y-2 p-3">
              <FormField
                control={form.control}
                name="configuration.rf.channel"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Channel</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Channel"
                        {...field}
                        type="number"
                        min={1}
                        max={30}
                        disabled={isSubmitting}
                        onChange={(e) => {
                          field.onChange(Number.parseInt(e.target.value));
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="configuration.rf.network"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Network</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Network"
                        {...field}
                        type="number"
                        min={1}
                        disabled={isSubmitting}
                        onChange={(e) => {
                          field.onChange(Number.parseInt(e.target.value));
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {!hideWifi && <WiFiForm form={form} disabled={isSubmitting} />}

          {roomSwitchNodes.length > 0 && (
            <Card className="rounded-md">
              <CardHeader className="px-3 pt-2 pb-0 font-medium text-sm text-gray-500">
                Room Switches
              </CardHeader>
              <CardContent className="space-y-2 p-3">
                <FormField
                  control={form.control}
                  name={"configuration.roomSwitchIdToQRCodeMapping"}
                  render={({ field }) => {
                    return (
                      <div className="flex flex-col gap-2">
                        {roomSwitchNodes.map((node) => (
                          <SwitchControllerForm
                            key={node.id}
                            node={node}
                            form={form}
                            disabled={isSubmitting}
                            field={field}
                            onChange={({ emptyFieldsCount }) =>
                              setEmptyFieldCount(emptyFieldsCount)
                            }
                          />
                        ))}
                      </div>
                    );
                  }}
                />
              </CardContent>
            </Card>
          )}

          {roomDimmerNodes.length > 0 && (
            <Card className="rounded-md">
              <CardHeader className="px-3 pt-2 pb-0 font-medium text-sm text-gray-500">
                Room Dimmers
              </CardHeader>
              <CardContent className="space-y-2 p-3">
                <FormField
                  control={form.control}
                  name={"configuration.roomDimmerIdToQRCodeMapping"}
                  render={({ field }) => {
                    return (
                      <div className="flex flex-col gap-2">
                        {roomDimmerNodes.map((node) => (
                          <DimmerControllerForm
                            key={node.id}
                            node={node}
                            form={form}
                            disabled={isSubmitting}
                            field={field}
                            onChange={({ emptyFieldsCount }) =>
                              setEmptyFieldCount(emptyFieldsCount)
                            }
                          />
                        ))}
                      </div>
                    );
                  }}
                />
              </CardContent>
            </Card>
          )}

          {outletDimmerNodes.length > 0 && (
            <Card className="rounded-md">
              <CardHeader className="px-3 pt-2 pb-0 font-medium text-sm text-gray-500">
                Outlet Dimmers
              </CardHeader>
              <CardContent className="space-y-2 p-3">
                <FormField
                  control={form.control}
                  name={"configuration.outletDimmerIdToQRCodeMapping"}
                  render={({ field }) => {
                    return (
                      <div className="flex flex-col gap-2">
                        {outletDimmerNodes.map((node) => {
                          return (
                            <OutletDimmerControllerForm
                              key={node.id}
                              node={node}
                              form={form}
                              disabled={isSubmitting}
                              field={field}
                              onChange={({ emptyFieldsCount }) =>
                                setEmptyFieldCount(emptyFieldsCount)
                              }
                            />
                          );
                        })}
                      </div>
                    );
                  }}
                />
              </CardContent>
            </Card>
          )}

          {canBusControllerNodes.length > 0 && (
            <Card className="rounded-md">
              <CardHeader className="px-3 pt-2 pb-0 font-medium text-sm text-gray-500">
                CanBus Controllers
              </CardHeader>
              <CardContent className="space-y-2 p-3">
                <FormField
                  control={form.control}
                  name={"configuration.canBusControllerIdToQRCodeMapping"}
                  render={({ field }) => {
                    return (
                      <div className="flex flex-col gap-2">
                        {canBusControllerNodes.map((node) => (
                          <CanBusControllerForm
                            key={node.id}
                            node={node}
                            form={form}
                            disabled={isSubmitting}
                            field={field}
                            onChange={({ emptyFieldsCount }) =>
                              setEmptyFieldCount(emptyFieldsCount)
                            }
                          />
                        ))}
                      </div>
                    );
                  }}
                />
              </CardContent>
            </Card>
          )}

          {doorSensorNodes.length > 0 && (
            <Card className="rounded-md">
              <CardHeader className="px-3 pt-2 pb-0 font-medium text-sm text-gray-500">
                Door Sensors
              </CardHeader>
              <CardContent className="space-y-2 p-3">
                <FormField
                  control={form.control}
                  name={"configuration.doorSensorIdToQRCodeMapping"}
                  render={({ field }) => {
                    return (
                      <div className="flex flex-col gap-2">
                        {doorSensorNodes.map((node) => (
                          <DoorSensorControllerForm
                            key={node.id}
                            node={node}
                            form={form}
                            disabled={isSubmitting}
                            field={field}
                            onChange={({ emptyFieldsCount }) =>
                              setEmptyFieldCount(emptyFieldsCount)
                            }
                          />
                        ))}
                      </div>
                    );
                  }}
                />
              </CardContent>
            </Card>
          )}

          {presenceSensorNodes.length > 0 && (
            <Card className="rounded-md">
              <CardHeader className="px-3 pt-2 pb-0 font-medium text-sm text-gray-500">
                Presence Sensors
              </CardHeader>
              <CardContent className="space-y-2 p-3">
                <FormField
                  control={form.control}
                  name={"configuration.presenceSensorIdToQRCodeMapping"}
                  render={({ field }) => {
                    return (
                      <div className="flex flex-col gap-2">
                        {presenceSensorNodes.map((node) => (
                          <PresenceSensorControllerForm
                            key={node.id}
                            node={node}
                            form={form}
                            disabled={isSubmitting}
                            field={field}
                            onChange={({ emptyFieldsCount }) =>
                              setEmptyFieldCount(emptyFieldsCount)
                            }
                          />
                        ))}
                      </div>
                    );
                  }}
                />
              </CardContent>
            </Card>
          )}

          {baseStationNodes.some((node) => {
            const data = getData(yDoc, node.data.dataId, "baseStation");
            return Object.keys(data?.somfyShades || {}).length > 0;
          }) && (
            <Card className="rounded-md">
              <CardHeader className="px-3 pt-2 pb-0 font-medium text-sm text-gray-500">
                Somfy Shades Device IDs
              </CardHeader>
              <CardContent className="space-y-2 p-3">
                <FormField
                  control={form.control}
                  name={"configuration.somfyShadesDeviceIdMapping"}
                  render={({ field }) => {
                    return (
                      <div className="flex flex-col gap-2">
                        {baseStationNodes.map((baseStation) => {
                          const basestationData = getData(
                            yDoc,
                            baseStation.data.dataId,
                            "baseStation",
                          );
                          const somfyShades =
                            basestationData?.somfyShades || {};
                          return Object.entries(somfyShades).map(
                            ([shadeKey, shade]) => {
                              return (
                                <SomfyShadesControllerForm
                                  key={`${baseStation.id}-${shadeKey}`}
                                  shadeKey={shadeKey}
                                  shade={shade}
                                  node={baseStation}
                                  form={form}
                                  disabled={isSubmitting}
                                  field={field}
                                  onChange={({ emptyFieldsCount }) =>
                                    setEmptyFieldCount(emptyFieldsCount)
                                  }
                                />
                              );
                            },
                          );
                        })}
                      </div>
                    );
                  }}
                />
              </CardContent>
            </Card>
          )}
        </div>

        {hasEmptyFields && (
          <div className="flex justify-center mt-4">
            <div className="text-sm text-red-600 bg-red-50 border border-red-200 rounded-md px-3 py-2 flex items-center gap-2">
              <span className="text-red-500">⚠</span>
              Please fill in all required fields ({emptyFieldsCount} missing)
              before updating the controller
            </div>
          </div>
        )}

        <div className="flex justify-center mt-6 gap-2">
          <Button type="submit" disabled={hasEmptyFields || isSubmitting}>
            {isSubmitting ? "Updating..." : submitTitle}
          </Button>
          {cacheQRCode && (
            <Button
              type="button"
              variant="outline"
              disabled={isSubmitting}
              onClick={() => {
                const cached = getCachedFormData(cacheQRCode);
                console.log(
                  "Current cached data for",
                  cacheQRCode,
                  ":",
                  cached,
                );
                const currentValues = form.getValues();
                console.log("Current form values:", currentValues);
                saveFormDataToCache(cacheQRCode, form.getValues());
              }}
            >
              Debug Cache
            </Button>
          )}
        </div>
      </form>
    </Form>
  );
}

function WiFiForm({ form, disabled }: { form: Form; disabled: boolean }) {
  return (
    <>
      <Card className="rounded-md">
        <CardHeader className="px-3 pt-2 pb-0 font-medium text-sm text-gray-500">
          Network Configuration
        </CardHeader>
        <CardContent className="space-y-4 p-3">
          <FormField
            control={form.control}
            name="configuration.networkType"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Connection Type</FormLabel>
                <FormControl>
                  <ToggleGroup
                    type="single"
                    value={field.value}
                    onValueChange={(value) => {
                      if (value) {
                        field.onChange(value);
                      }
                    }}
                    className="justify-start"
                  >
                    <ToggleGroupItem value="wifi" aria-label="WiFi">
                      <Wifi className="mr-2 h-4 w-4" />
                      WiFi
                    </ToggleGroupItem>
                    <ToggleGroupItem value="ethernet" aria-label="Ethernet">
                      <EthernetPortIcon className="mr-2 h-4 w-4" />
                      Ethernet
                    </ToggleGroupItem>
                  </ToggleGroup>
                </FormControl>
              </FormItem>
            )}
          />

          {form.watch("configuration.networkType") === "wifi" && (
            <>
              <FormField
                control={form.control}
                name="configuration.wifi.ssid"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>SSID</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="SSID"
                        {...field}
                        type="text"
                        disabled={disabled}
                        maxLength={32}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="configuration.wifi.password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Password</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Password"
                        {...field}
                        type="password"
                        disabled={disabled}
                        maxLength={32}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </>
          )}

          <FormField
            control={form.control}
            name="configuration.serverAddress"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Server Address</FormLabel>
                <FormControl>
                  <Input
                    placeholder="wss://somo-server.fly.dev/ws/service"
                    {...field}
                    type="text"
                    disabled={disabled}
                    maxLength={128}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {form.watch("configuration.networkType") === "ethernet" && (
            <>
              <FormField
                control={form.control}
                name="configuration.dhcp.staticIp"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        disabled={disabled}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Use Static IP</FormLabel>
                      <p className="text-xs text-muted-foreground">
                        Configure static IP address instead of DHCP
                      </p>
                    </div>
                  </FormItem>
                )}
              />

              {form.watch("configuration.dhcp.staticIp") && (
                <div className="space-y-2 pl-6">
                  <FormField
                    control={form.control}
                    name="configuration.dhcp.ipAddress"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>IP Address</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="*************"
                            {...field}
                            type="text"
                            disabled={disabled}
                            maxLength={15}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="configuration.dhcp.subnetMask"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Subnet Mask</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="*************"
                            {...field}
                            type="text"
                            disabled={disabled}
                            maxLength={15}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="configuration.dhcp.gateway"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Gateway</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="192.168.1.1"
                            {...field}
                            type="text"
                            disabled={disabled}
                            maxLength={15}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="configuration.dhcp.dnsServer"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>DNS Server</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="8.8.8.8"
                            {...field}
                            type="text"
                            disabled={disabled}
                            maxLength={15}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      <Card className="rounded-md">
        <CardHeader className="px-3 pt-2 pb-0 font-medium text-sm text-gray-500">
          MAC Address Configuration
        </CardHeader>
        <CardContent className="space-y-4 p-3">
          <FormField
            control={form.control}
            name="configuration.mac.useMacAddress"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                    disabled={disabled}
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel>Use Custom MAC Address</FormLabel>
                  <p className="text-xs text-muted-foreground">
                    Override the default MAC address
                  </p>
                </div>
              </FormItem>
            )}
          />

          {form.watch("configuration.mac.useMacAddress") && (
            <FormField
              control={form.control}
              name="configuration.mac.macAddress"
              render={({ field }) => (
                <FormItem className="pl-6">
                  <FormLabel>MAC Address</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="AA:BB:CC:DD:EE:FF"
                      {...field}
                      type="text"
                      disabled={disabled}
                      maxLength={17}
                      onChange={(e) => {
                        // Auto-format MAC address as user types
                        const value = e.target.value
                          .replace(/[^0-9A-Fa-f]/g, "")
                          .slice(0, 12);
                        const formatted =
                          value.match(/.{1,2}/g)?.join(":") || value;
                        field.onChange(formatted);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}
        </CardContent>
      </Card>
    </>
  );
}

function SwitchControllerForm(props: {
  disabled: boolean;
  node: LayoutContainerNode<"roomSwitchContainer">;
  form: Form;
  field: ControllerRenderProps<
    FormValues,
    "configuration.roomSwitchIdToQRCodeMapping"
  >;
  onChange: (params: { emptyFieldsCount: number }) => void;
}) {
  const { disabled, node, form, field, onChange } = props;
  const data = useGetData(node.data.dataId, "roomSwitch");
  if (!data) {
    return <></>;
  }

  return (
    <FormItem>
      <FormLabel className="flex flex-row items-center">
        {data.title}{" "}
        <span className="font-mono text-xs text-gray-500 ml-2">
          ({node.id})
        </span>
      </FormLabel>
      <FormControl>
        <Input
          placeholder="QR Code"
          className={
            isQRCodeEmpty(form, node.id, "roomSwitch")
              ? "border-red-500 focus:border-red-500"
              : ""
          }
          disabled={disabled}
          value={field.value.find((item) => item.id === node.id)?.qrCode ?? ""}
          onChange={(e) => {
            const newValues = field.value.map((item) => {
              if (item.id === node.id) {
                return {
                  ...item,
                  qrCode: e.target.value,
                };
              }
              return item;
            });
            field.onChange(newValues);
            setTimeout(() => {
              onChange({ emptyFieldsCount: countEmptyFields(form) });
            }, 0);
          }}
          onBlur={() => field.onBlur()}
        />
      </FormControl>
    </FormItem>
  );
}

function DimmerControllerForm(props: {
  disabled: boolean;
  node: LayoutContainerNode<"roomDimmerContainer">;
  form: Form;
  field: ControllerRenderProps<
    FormValues,
    "configuration.roomDimmerIdToQRCodeMapping"
  >;
  onChange: (params: { emptyFieldsCount: number }) => void;
}) {
  const { disabled, node, form, field, onChange } = props;
  const data = useGetData(node.data.dataId, "roomDimmer");
  if (!data) {
    return <></>;
  }

  return (
    <FormItem>
      <FormLabel className="flex flex-row items-center">
        {data.title}{" "}
        <span className="font-mono text-xs text-gray-500 ml-2">
          ({node.id})
        </span>
      </FormLabel>
      <FormControl>
        <Input
          placeholder="QR Code"
          className={
            isQRCodeEmpty(form, node.id, "roomDimmer")
              ? "border-red-500 focus:border-red-500"
              : ""
          }
          disabled={disabled}
          value={field.value.find((item) => item.id === node.id)?.qrCode ?? ""}
          onChange={(e) => {
            const newValues = field.value.map((item) => {
              if (item.id === node.id) {
                return {
                  ...item,
                  qrCode: e.target.value,
                };
              }
              return item;
            });
            field.onChange(newValues);
            setTimeout(() => {
              onChange({ emptyFieldsCount: countEmptyFields(form) });
            }, 0);
          }}
          onBlur={() => field.onBlur()}
        />
      </FormControl>
    </FormItem>
  );
}

function OutletDimmerControllerForm(props: {
  disabled: boolean;
  node: GraphNodesByType["outletDimmerContainer"];
  form: Form;
  field: ControllerRenderProps<
    FormValues,
    "configuration.outletDimmerIdToQRCodeMapping"
  >;
  onChange: (params: { emptyFieldsCount: number }) => void;
}) {
  const { disabled, node, form, field, onChange } = props;
  const data = useGetData(node.data.dataId, "roomDimmer");
  if (!data) {
    return <></>;
  }

  return (
    <FormItem>
      <FormLabel className="flex flex-row items-center">
        {data.title}{" "}
        <span className="font-mono text-xs text-gray-500 ml-2">
          ({node.id})
        </span>
      </FormLabel>
      <FormControl>
        <Input
          placeholder="QR Code"
          className={
            isQRCodeEmpty(form, node.id, "outletDimmer")
              ? "border-red-500 focus:border-red-500"
              : ""
          }
          disabled={disabled}
          value={field.value.find((item) => item.id === node.id)?.qrCode ?? ""}
          onChange={(e) => {
            const newValues = field.value.map((item) => {
              if (item.id === node.id) {
                return {
                  ...item,
                  qrCode: e.target.value,
                };
              }
              return item;
            });
            field.onChange(newValues);
            setTimeout(() => {
              onChange({ emptyFieldsCount: countEmptyFields(form) });
            }, 0);
          }}
          onBlur={() => field.onBlur()}
        />
      </FormControl>
    </FormItem>
  );
}

function CanBusControllerForm(props: {
  disabled: boolean;
  node: GraphNodesByType["canbusControllerContainer"];
  form: Form;
  field: ControllerRenderProps<
    FormValues,
    "configuration.canBusControllerIdToQRCodeMapping"
  >;
  onChange: (params: { emptyFieldsCount: number }) => void;
}) {
  const { disabled, node, form, field, onChange } = props;
  const data = useGetData(node.data.dataId, "canbusController");
  if (!data) {
    return <></>;
  }

  return (
    <FormItem>
      <FormLabel className="flex flex-row items-center">
        {data.title}{" "}
        <span className="font-mono text-xs text-gray-500 ml-2">
          ({node.id})
        </span>
      </FormLabel>
      <FormControl>
        <Input
          placeholder="QR Code"
          className={
            isQRCodeEmpty(form, node.id, "canBusController")
              ? "border-red-500 focus:border-red-500"
              : ""
          }
          disabled={disabled}
          value={field.value.find((item) => item.id === node.id)?.qrCode ?? ""}
          onChange={(e) => {
            const newValues = field.value.map((item) => {
              if (item.id === node.id) {
                return {
                  ...item,
                  qrCode: e.target.value,
                };
              }
              return item;
            });
            field.onChange(newValues);
            setTimeout(() => {
              onChange({ emptyFieldsCount: countEmptyFields(form) });
            }, 0);
          }}
          onBlur={() => field.onBlur()}
        />
      </FormControl>
    </FormItem>
  );
}

function DoorSensorControllerForm(props: {
  disabled: boolean;
  node: LayoutContainerNode<"doorSensorContainer">;
  form: Form;
  field: ControllerRenderProps<
    FormValues,
    "configuration.doorSensorIdToQRCodeMapping"
  >;
  onChange: (params: { emptyFieldsCount: number }) => void;
}) {
  const { disabled, node, form, field, onChange } = props;
  const data = useGetData(node.data.dataId, "doorSensor");
  if (!data) {
    return <></>;
  }

  return (
    <FormItem>
      <FormLabel className="flex flex-row items-center">
        {data.title}{" "}
        <span className="font-mono text-xs text-gray-500 ml-2">
          ({node.id})
        </span>
      </FormLabel>
      <FormControl>
        <Input
          placeholder="QR Code"
          className={
            isQRCodeEmpty(form, node.id, "doorSensor")
              ? "border-red-500 focus:border-red-500"
              : ""
          }
          disabled={disabled}
          value={field.value.find((item) => item.id === node.id)?.qrCode ?? ""}
          onChange={(e) => {
            const newValues = field.value.map((item) => {
              if (item.id === node.id) {
                return {
                  ...item,
                  qrCode: e.target.value,
                };
              }
              return item;
            });
            field.onChange(newValues);
            setTimeout(() => {
              onChange({ emptyFieldsCount: countEmptyFields(form) });
            }, 0);
          }}
          onBlur={() => field.onBlur()}
        />
      </FormControl>
    </FormItem>
  );
}

function PresenceSensorControllerForm(props: {
  disabled: boolean;
  node: GraphNodesByType["presenceSensorContainer"];
  form: Form;
  field: ControllerRenderProps<
    FormValues,
    "configuration.presenceSensorIdToQRCodeMapping"
  >;
  onChange: (params: { emptyFieldsCount: number }) => void;
}) {
  const { disabled, node, form, field, onChange } = props;
  const data = useGetData(node.data.dataId, "doorSensor");
  if (!data) {
    return <></>;
  }

  return (
    <FormItem>
      <FormLabel className="flex flex-row items-center">
        {data.title}{" "}
        <span className="font-mono text-xs text-gray-500 ml-2">
          ({node.id})
        </span>
      </FormLabel>
      <FormControl>
        <Input
          placeholder="QR Code"
          className={
            isQRCodeEmpty(form, node.id, "presenceSensor")
              ? "border-red-500 focus:border-red-500"
              : ""
          }
          disabled={disabled}
          value={field.value.find((item) => item.id === node.id)?.qrCode ?? ""}
          onChange={(e) => {
            const newValues = field.value.map((item) => {
              if (item.id === node.id) {
                return {
                  ...item,
                  qrCode: e.target.value,
                };
              }
              return item;
            });
            field.onChange(newValues);
            setTimeout(() => {
              onChange({ emptyFieldsCount: countEmptyFields(form) });
            }, 0);
          }}
          onBlur={() => field.onBlur()}
        />
      </FormControl>
    </FormItem>
  );
}

function SomfyShadesControllerForm(props: {
  shadeKey: string;
  shade: SomfyShadesDevice;
  disabled: boolean;
  node: GraphNodesByType["baseStationContainer"];
  form: Form;
  field: ControllerRenderProps<
    FormValues,
    "configuration.somfyShadesDeviceIdMapping"
  >;
  onChange: (params: { emptyFieldsCount: number }) => void;
}) {
  const {
    shadeKey,
    shade,
    disabled,
    node: baseStation,
    form,
    field,
    onChange,
  } = props;
  const baseStationData = useGetData(baseStation.data.dataId, "baseStation");
  if (!baseStationData) {
    return <></>;
  }

  return (
    <FormItem>
      <FormLabel className="flex flex-row items-center">
        {baseStationData.title} - {shade.name}
        <span className="font-mono text-xs text-gray-500 ml-2">
          ({baseStation.id} - {shadeKey})
        </span>
      </FormLabel>
      <FormControl>
        <Input
          placeholder="Device ID"
          type="number"
          min={0}
          disabled={disabled}
          className={
            field.value.find(
              (item) =>
                item.baseStationId === baseStation.id &&
                item.shadeKey === shadeKey,
            )?.deviceId === undefined
              ? "border-red-500 focus:border-red-500"
              : ""
          }
          value={
            field.value.find(
              (item) =>
                item.baseStationId === baseStation.id &&
                item.shadeKey === shadeKey,
            )?.deviceId ?? ""
          }
          onChange={(e) => {
            const deviceId = parseInt(e.target.value, 10);

            const newValues = field.value.filter(
              (item) =>
                !(
                  item.baseStationId === baseStation.id &&
                  item.shadeKey === shadeKey
                ),
            );
            newValues.push({
              baseStationId: baseStation.id,
              shadeKey,
              deviceId,
            });
            field.onChange(newValues);
            setTimeout(() => {
              onChange({ emptyFieldsCount: countEmptyFields(form) });
            }, 0);
          }}
          onBlur={() => field.onBlur()}
        />
      </FormControl>
    </FormItem>
  );
}

function getCachedFormData(qrCode: string | null): FormValues | null {
  if (!qrCode) {
    return null;
  }
  try {
    const cached = localStorage.getItem(`form_cache_${qrCode}`);
    return cached ? JSON.parse(cached) : null;
  } catch (error) {
    console.warn("Failed to parse cached form data:", error);
    return null;
  }
}

/**
 * Priority: initialConfiguration > cachedData > defaults
 */
function getDefaultValues({
  cacheQRCode,
  initialConfiguration,
  roomSwitchNodes,
  roomDimmerNodes,
  outletDimmerNodes,
  canBusControllerNodes,
  doorSensorNodes,
  presenceSensorNodes,
  baseStationNodes,
}: {
  cacheQRCode: string | null;
  initialConfiguration: Controller["configuration"];
  roomSwitchNodes: LayoutContainerNode<"roomSwitchContainer">[];
  roomDimmerNodes: LayoutContainerNode<"roomDimmerContainer">[];
  doorSensorNodes: LayoutContainerNode<"doorSensorContainer">[];
  outletDimmerNodes: GraphNodesByType["outletDimmerContainer"][];
  canBusControllerNodes: GraphNodesByType["canbusControllerContainer"][];
  presenceSensorNodes: GraphNodesByType["presenceSensorContainer"][];
  baseStationNodes: GraphNodesByType["baseStationContainer"][];
}): FormValues {
  // Get cached data fresh each time
  const cachedData = getCachedFormData(cacheQRCode);

  return {
    configuration: {
      rf: {
        channel:
          initialConfiguration?.rf?.channel ??
          cachedData?.configuration?.rf?.channel ??
          15,
        network:
          initialConfiguration?.rf?.network ??
          cachedData?.configuration?.rf?.network ??
          15,
      },
      networkType: cachedData?.configuration?.networkType ?? "wifi",
      wifi: {
        ssid:
          initialConfiguration?.wifi?.ssid ??
          cachedData?.configuration?.wifi?.ssid ??
          "",
        password:
          initialConfiguration?.wifi?.password ??
          cachedData?.configuration?.wifi?.password ??
          "",
      },
      serverAddress:
        initialConfiguration?.serverAddress ??
        cachedData?.configuration?.serverAddress ??
        "wss://somo-server.fly.dev/ws/service",
      mac: {
        useMacAddress:
          initialConfiguration?.mac?.useMacAddress ??
          cachedData?.configuration?.mac?.useMacAddress ??
          false,
        macAddress:
          initialConfiguration?.mac?.macAddress ??
          cachedData?.configuration?.mac?.macAddress ??
          "",
      },
      dhcp: {
        staticIp:
          initialConfiguration?.dhcp?.staticIp ??
          cachedData?.configuration?.dhcp?.staticIp ??
          false,
        ipAddress:
          initialConfiguration?.dhcp?.ipAddress ??
          cachedData?.configuration?.dhcp?.ipAddress ??
          "",
        subnetMask:
          initialConfiguration?.dhcp?.subnetMask ??
          cachedData?.configuration?.dhcp?.subnetMask ??
          "",
        gateway:
          initialConfiguration?.dhcp?.gateway ??
          cachedData?.configuration?.dhcp?.gateway ??
          "",
        dnsServer:
          initialConfiguration?.dhcp?.dnsServer ??
          cachedData?.configuration?.dhcp?.dnsServer ??
          "",
      },
      roomSwitchIdToQRCodeMapping: mapNodeQRCodes(
        roomSwitchNodes,
        initialConfiguration?.roomSwitchIdToQRCodeMapping,
        cachedData?.configuration?.roomSwitchIdToQRCodeMapping,
      ),
      roomDimmerIdToQRCodeMapping: mapNodeQRCodes(
        roomDimmerNodes,
        initialConfiguration?.roomDimmerIdToQRCodeMapping,
        cachedData?.configuration?.roomDimmerIdToQRCodeMapping,
      ),
      outletDimmerIdToQRCodeMapping: mapNodeQRCodes(
        outletDimmerNodes,
        initialConfiguration?.outletDimmerIdToQRCodeMapping,
        cachedData?.configuration?.outletDimmerIdToQRCodeMapping,
      ),
      canBusControllerIdToQRCodeMapping: mapNodeQRCodes(
        canBusControllerNodes,
        initialConfiguration?.canBusControllerIdToQRCodeMapping,
        cachedData?.configuration?.canBusControllerIdToQRCodeMapping,
      ),
      doorSensorIdToQRCodeMapping: mapNodeQRCodes(
        doorSensorNodes,
        initialConfiguration?.doorSensorIdToQRCodeMapping,
        cachedData?.configuration?.doorSensorIdToQRCodeMapping,
      ),
      presenceSensorIdToQRCodeMapping: mapNodeQRCodes(
        presenceSensorNodes,
        initialConfiguration?.presenceSensorIdToQRCodeMapping,
        cachedData?.configuration?.presenceSensorIdToQRCodeMapping,
      ),
      somfyShadesDeviceIdMapping: mapSomfyShadesDeviceIds(
        baseStationNodes,
        (
          initialConfiguration as Controller["configuration"] & {
            somfyShadesDeviceIdMapping?: {
              baseStationId: string;
              shadeKey: string;
              deviceId: number;
            }[];
          }
        )?.somfyShadesDeviceIdMapping,
        cachedData?.configuration?.somfyShadesDeviceIdMapping,
      ),
    },
  };
}

function mapNodeQRCodes<T extends { id: string }>(
  nodes: T[],
  initial?: QrCodeMapping[],
  cached?: QrCodeMapping[],
) {
  return nodes.map(({ id }) => ({
    id,
    qrCode:
      initial?.find((x) => x.id === id)?.qrCode ??
      cached?.find((x) => x.id === id)?.qrCode ??
      "",
  }));
}

function mapSomfyShadesDeviceIds(
  baseStationNodes: GraphNodesByType["baseStationContainer"][],
  initial?: { baseStationId: string; shadeKey: string; deviceId: number }[],
  cached?: { baseStationId: string; shadeKey: string; deviceId: number }[],
) {
  const mapping: {
    baseStationId: string;
    shadeKey: string;
    deviceId: number;
  }[] = [];

  const { yDoc } = useReactFlowContext();

  baseStationNodes.forEach((baseStation) => {
    const baseStationData = getData(
      yDoc,
      baseStation.data.dataId,
      "baseStation",
    );
    if (!baseStationData) {
      return;
    }
    const somfyShades = baseStationData.somfyShades || {};
    Object.keys(somfyShades).forEach((shadeKey) => {
      const deviceId =
        initial?.find(
          (x) => x.baseStationId === baseStation.id && x.shadeKey === shadeKey,
        )?.deviceId ??
        cached?.find(
          (x) => x.baseStationId === baseStation.id && x.shadeKey === shadeKey,
        )?.deviceId ??
        0;

      mapping.push({
        baseStationId: baseStation.id,
        shadeKey,
        deviceId,
      });
    });
  });

  return mapping;
}

function countEmptyFields(form: Form) {
  const values = form.getValues();

  const emptyRoomSwitch =
    values.configuration.roomSwitchIdToQRCodeMapping.filter(
      (item) => !item.qrCode.trim(),
    ).length;
  const emptySomeDimmer =
    values.configuration.roomDimmerIdToQRCodeMapping.filter(
      (item) => !item.qrCode.trim(),
    ).length;
  const emptyOutletDimmer =
    values.configuration.outletDimmerIdToQRCodeMapping.filter(
      (item) => !item.qrCode.trim(),
    ).length;
  const emptyCanBusController =
    values.configuration.canBusControllerIdToQRCodeMapping.filter(
      (item) => !item.qrCode.trim(),
    ).length;
  const emptyDoorSensor =
    values.configuration.doorSensorIdToQRCodeMapping.filter(
      (item) => !item.qrCode.trim(),
    ).length;
  const emptyPresenceSensor =
    values.configuration.presenceSensorIdToQRCodeMapping.filter(
      (item) => !item.qrCode.trim(),
    ).length;

  return (
    emptyRoomSwitch +
    emptySomeDimmer +
    emptyOutletDimmer +
    emptyCanBusController +
    emptyDoorSensor +
    emptyPresenceSensor
  );
}

function isQRCodeEmpty(
  form: Form,
  nodeId: string,
  deviceType:
    | "roomSwitch"
    | "roomDimmer"
    | "outletDimmer"
    | "canBusController"
    | "doorSensor"
    | "presenceSensor",
) {
  const { configuration } = form.getValues();
  const mapping = match(deviceType)
    .with("roomSwitch", () => configuration.roomSwitchIdToQRCodeMapping)
    .with("roomDimmer", () => configuration.roomDimmerIdToQRCodeMapping)
    .with("outletDimmer", () => configuration.outletDimmerIdToQRCodeMapping)
    .with(
      "canBusController",
      () => configuration.canBusControllerIdToQRCodeMapping,
    )
    .with("doorSensor", () => configuration.doorSensorIdToQRCodeMapping)
    .with("presenceSensor", () => configuration.presenceSensorIdToQRCodeMapping)
    .exhaustive();
  const entry = mapping.find(({ id }) => id === nodeId);
  return !entry?.qrCode?.trim();
}
