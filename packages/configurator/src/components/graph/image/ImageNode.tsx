import { useGetData } from "@/components/data/useData";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { cn } from "@/lib/classNames";
import { LayoutContainerNode } from "@somo/shared";
import {
  NodeProps,
  NodeResizer,
  useReactFlow,
  useUpdateNodeInternals,
} from "@xyflow/react";
import equal from "fast-deep-equal/react";
import { LockIcon, UnlockIcon } from "lucide-react";
import React, { useEffect } from "react";

function InternalImageNode(props: NodeProps<LayoutContainerNode>) {
  const { selected, id, draggable } = props;
  const data = useGetData(props.data.dataId, "image");

  const flow = useReactFlow();
  const updateNodeInternals = useUpdateNodeInternals();

  useEffect(() => {
    const timeout = setTimeout(() => {
      updateNodeInternals(id);
    }, 1);
    return () => clearTimeout(timeout);
  }, [updateNodeInternals, id]);

  if (!data) {
    return null;
  }

  return (
    <>
      {selected && draggable && (
        <NodeResizer minWidth={200} minHeight={200} keepAspectRatio />
      )}
      <div
        className={cn(
          "flex flex-col items-start justify-start flex-shrink-0 p-1",
          "overflow-hidden",
          "w-full h-full relative",
          "border-transparent",
          selected &&
            !draggable &&
            "outline outline-1 outline-offset-[0px] outline-gray-300",
        )}
      >
        <img
          src={data.imageUrl}
          className={cn("h-full w-full object-cover rounded-lg")}
          style={{
            opacity: data.opacity ?? 1,
          }}
          draggable={false}
        />
        {selected && (
          <>
            <div className="absolute top-4 right-4 flex flex-row items-center">
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => {
                      flow.updateNode(id, {
                        draggable: !draggable,
                        deletable: !draggable,
                      });
                    }}
                  >
                    {draggable ? (
                      <UnlockIcon className="size-4" />
                    ) : (
                      <LockIcon />
                    )}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  {!draggable ? "Unlock" : "Lock"}
                </TooltipContent>
              </Tooltip>
            </div>
          </>
        )}
      </div>
    </>
  );
}

export const ImageNode = React.memo(InternalImageNode, equal);
