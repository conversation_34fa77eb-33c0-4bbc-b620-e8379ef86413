import { useData } from "@/components/data/useData";
import { <PERSON>lide<PERSON> } from "@/components/ui/slider";
import { useAuth } from "@clerk/clerk-react";
import { LayoutContainerNode } from "@somo/shared";
import { useQuery } from "@tanstack/react-query";
import { generateUploadButton } from "@uploadthing/react";
import { useRef } from "react";
import {
  SettingsGroup,
  SettingsGroupContent,
  SettingsLabel,
  SettingsRow,
} from "../settings/SettingsGroup";

const UploadButton = generateUploadButton({
  url: `${import.meta.env.VITE_API_URL}/api/upload`,
});

export const ImageSettings = ({ node }: { node: LayoutContainerNode }) => {
  const lastImageSizeRef = useRef<{ width: number; height: number }>({
    width: 100,
    height: 100,
  });

  const { getToken } = useAuth();
  const { data: authToken } = useQuery({
    queryKey: ["authToken"],
    queryFn: async () => getToken({ template: "Configurator" }),
  });

  const { data, updateNestedData } = useData(node.data.dataId, "image");
  if (!data) {
    return null;
  }

  return (
    <SettingsGroup>
      <SettingsGroupContent>
        <SettingsRow>
          <SettingsLabel>Upload new Image</SettingsLabel>
          {authToken && (
            <div className="w-auto relative flex flex-start mt-2">
              <UploadButton
                className="w-full text-black ut-button:bg-white ut-button:h-9 ut-button:w-full ut-button:text-black ut-button:font-semibold ut-button:text-xs ut-button:rounded-md ut-button:border ut-button:border-gray-300 ut-button:hover:bg-gray-100 ut-button:hover:border-gray-400 ut-button:transition-all ut-button:duration-100 ut-button:ease-in-out ut-allowed-content:hidden"
                endpoint="imageUploader"
                headers={{
                  Authorization: `Bearer ${authToken}`,
                }}
                onClientUploadComplete={(res) => {
                  const firstFile = res?.[0];
                  if (!firstFile) {
                    return;
                  }

                  updateNestedData("imageUrl", firstFile.url);
                }}
                onBeforeUploadBegin={async (files) => {
                  const firstFile = files?.[0];
                  if (!firstFile) {
                    return files;
                  }
                  return new Promise((resolve) => {
                    const fileReader = new FileReader();

                    fileReader.onload = function () {
                      const img = new Image();

                      img.onload = function () {
                        lastImageSizeRef.current = {
                          width: img.width,
                          height: img.height,
                        };

                        resolve([firstFile]);
                      };

                      img.onerror = () => {
                        resolve([]);
                      };

                      img.src = String(fileReader.result);
                    };

                    fileReader.onerror = () => {
                      resolve([]);
                    };

                    fileReader.readAsDataURL(firstFile);
                  });
                }}
              />
            </div>
          )}
        </SettingsRow>

        <SettingsRow>
          <SettingsLabel>Opacity</SettingsLabel>
          <Slider
            className="mt-2"
            min={0}
            max={100}
            value={[(data.opacity ?? 1) * 100]}
            onValueChange={(value) => {
              updateNestedData("opacity", (value[0] ?? 100) / 100);
            }}
          />
        </SettingsRow>
      </SettingsGroupContent>
    </SettingsGroup>
  );
};
