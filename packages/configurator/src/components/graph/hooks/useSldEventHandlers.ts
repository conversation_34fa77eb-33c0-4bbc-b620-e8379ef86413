import { SldNodeData } from "@/components/devices/sld/types";
import { Connection, Edge, Node, ReactFlowInstance } from "@xyflow/react";
import { useCallback } from "react";
import * as Y from "yjs";
import { determineEdgeStyle } from "../config/sldComponentConfig";
import {
  ensureSldYDocStructure,
  getSldEdgesMap,
  getSldNodesMap,
  getSldSpecificNodes,
} from "../Graph";
import {
  createEdgeInYDoc,
  isHandleConnectedToGhost,
  updateEdgeConnection,
  validateEdgeConnection,
} from "../helpers/edgeHelpers";
import { SLD_EDITOR } from "../utils/editorConstants";
import { hasDataVisualizedInLayoutGraph } from "../helpers/nodeSyncHelpers";
import { deleteSLDNodes, deleteSLDEdges } from "../helpers/deleteSLDNodes";

interface UseSldEventHandlersProps {
  yDoc: Y.Doc;
  sldNodes: Node<SldNodeData>[];
  sldEdges: Edge[];
  reactFlowInstance: ReactFlowInstance | null;
  isFocused: boolean;
  isSyncingSelection: React.MutableRefObject<boolean>;
  mainGraphReactFlowInstanceRef:
    | React.MutableRefObject<ReactFlowInstance<any> | null>
    | undefined;
  showConnectionError: (message: string) => void;
  setFocusedEditor: (editor: any) => void;
  setSelectedNodes: React.Dispatch<React.SetStateAction<Node<SldNodeData>[]>>;
  setDeleteModalState: React.Dispatch<
    React.SetStateAction<{
      isOpen: boolean;
      elementsToDelete: { nodes: Node<any>[]; edges: Edge[] };
      mirroredNodeIds: string[];
      sldNodeIds: string[];
    }>
  >;
  updateCursor: (event?: React.MouseEvent) => void;
  isDraggingRef: React.MutableRefObject<boolean>;
}

export function useSldEventHandlers({
  yDoc,
  sldNodes,
  sldEdges,
  reactFlowInstance,
  isFocused,
  isSyncingSelection,
  mainGraphReactFlowInstanceRef,
  showConnectionError,
  setFocusedEditor,
  setSelectedNodes,
  setDeleteModalState,
  updateCursor,
  isDraggingRef,
}: UseSldEventHandlersProps) {
  // Helper function to update external drag state on connected ghost nodes
  const updateConnectedGhostDragState = useCallback(
    (nodeId: string, isActive: boolean) => {
      try {
        const connectedGhostIds = sldEdges
          .filter((e) => e.source === nodeId || e.target === nodeId)
          .map((e) => (e.source === nodeId ? e.target : e.source))
          .filter((otherId) => {
            const connectedNode = sldNodes.find((n) => n.id === otherId);
            return connectedNode?.type === "ghostSLD";
          });

        if (connectedGhostIds.length && yDoc) {
          const yNodes = getSldNodesMap(yDoc);
          if (yNodes) {
            connectedGhostIds.forEach((gid) => {
              const yGhost = yNodes.get(gid);
              const yData = yGhost?.get?.("data");
              if (yGhost && yData) {
                yData.set("externalDragActive", isActive);
              }
            });
          }
        }
      } catch (error) {
        console.error("Error in updateConnectedGhostDragState:", error);
      }
    },
    [sldEdges, sldNodes, yDoc],
  );

  // Connection handlers
  const handleConnect = useCallback(
    (connection: Connection) => {
      // Validate the connection first
      const sourceNode = sldNodes.find((node) => node.id === connection.source);
      const targetNode = sldNodes.find((node) => node.id === connection.target);

      const sldEdgesMapForValidation = getSldEdgesMap(yDoc);
      const sldNodesMapForValidation = getSldNodesMap(yDoc);
      const validation = validateEdgeConnection(
        sourceNode,
        targetNode,
        connection,
        {
          sldEdgesMap: sldEdgesMapForValidation ?? null,
          sldNodesMap: sldNodesMapForValidation ?? null,
        },
      );

      if (!validation.isValid) {
        showConnectionError(validation.errorMessage!);
        return; // Prevent the connection
      }

      // Sync the connection to SLD-specific edges map
      const { sldEdgesMap } = ensureSldYDocStructure(yDoc);

      // Prevent the same HANDLE from connecting multiple times to the same ghost node
      if (connection.source && connection.target) {
        const srcId = connection.source;
        const tgtId = connection.target;
        const srcHandle = connection.sourceHandle || "";
        const tgtHandle = connection.targetHandle || "";
        const isSrcGhost = srcId.startsWith("ghost-");
        const isTgtGhost = tgtId.startsWith("ghost-");
        if (isSrcGhost || isTgtGhost) {
          const ghostId = isSrcGhost ? srcId : tgtId;
          const otherId = isSrcGhost ? tgtId : srcId;
          const otherHandle = isSrcGhost ? tgtHandle : srcHandle;
          if (
            isHandleConnectedToGhost(sldEdgesMap, ghostId, otherId, otherHandle)
          ) {
            showConnectionError(
              "This handle is already connected to that branching node.",
            );
            return; // Block duplicate handle to same ghost
          }
        }
      }

      // Determine edge color based on source node type and handles
      const handleId = connection.sourceHandle || connection.targetHandle;

      const edgeStyle = determineEdgeStyle(sourceNode, targetNode, handleId);

      // Create edge using helper function
      createEdgeInYDoc(yDoc, connection, edgeStyle);
    },
    [yDoc, sldNodes, showConnectionError],
  );

  const handleReconnect = useCallback(
    (oldEdge: Edge, newConnection: Connection) => {
      // Validate the new connection first
      const sourceNode = sldNodes.find(
        (node) => node.id === newConnection.source,
      );
      const targetNode = sldNodes.find(
        (node) => node.id === newConnection.target,
      );

      // Validate that we have complete connection information
      if (!newConnection.source || !newConnection.target) {
        console.error(
          "Invalid reconnection: missing source or target",
          newConnection,
        );
        return false; // Prevent React Flow from creating its own edge
      }

      const sldEdgesMapForValidation2 = getSldEdgesMap(yDoc);
      const sldNodesMapForValidation2 = getSldNodesMap(yDoc);
      const validation = validateEdgeConnection(
        sourceNode,
        targetNode,
        newConnection,
        {
          sldEdgesMap: sldEdgesMapForValidation2 ?? null,
          sldNodesMap: sldNodesMapForValidation2 ?? null,
          oldEdgeIdToIgnore: oldEdge.id,
        },
      );

      if (!validation.isValid) {
        showConnectionError(validation.errorMessage!);
        return false; // Prevent React Flow from creating its own edge
      }

      // Handle edge reconnection by updating the edge in YDoc

      // Determine edge color for the new connection
      const handleId = newConnection.sourceHandle || newConnection.targetHandle;
      const edgeStyle = determineEdgeStyle(sourceNode, targetNode, handleId);

      // Update edge connection using helper function
      updateEdgeConnection(yDoc, oldEdge.id, newConnection, edgeStyle);

      // Let YDoc observer sync edges array; avoid double-adding locally
      return false; // Prevent React Flow from creating its own edge
    },
    [yDoc, sldNodes, showConnectionError],
  );

  // Node drag handlers
  const handleNodeDragStart = useCallback(
    (_event: React.MouseEvent, node: Node) => {
      isDraggingRef.current = true;
      updateConnectedGhostDragState(node.id, true);
    },
    [updateConnectedGhostDragState, isDraggingRef],
  );

  const handleNodeDrag = useCallback(
    (_event: React.MouseEvent, node: Node) => {
      // While dragging, keep connected ghosts marked active
      updateConnectedGhostDragState(node.id, true);
    },
    [updateConnectedGhostDragState],
  );

  const handleNodeDragStop = useCallback(
    (_event: React.MouseEvent, node: Node) => {
      isDraggingRef.current = false;
      updateConnectedGhostDragState(node.id, false);
    },
    [updateConnectedGhostDragState, isDraggingRef],
  );

  // Deletion handler
  const handleBeforeDelete = useCallback(
    async (elements: { nodes: Node<any>[]; edges: Edge[] }) => {
      // Only handle deletion if this SLD editor is focused
      if (!isFocused) {
        // If not focused, prevent deletion in this context
        return { nodes: [], edges: [] };
      }

      // Check if any of the nodes being deleted are visualized in the main layout graph
      const nodesWithMainGraphData: {
        node: Node<any>;
        mainGraphData: { nodeId: string; nodeData: any };
      }[] = [];

      elements.nodes.forEach((node) => {
        // Check if this SLD node has data that exists in the main graph
        if (node.data?.dataId) {
          const mainGraphData = hasDataVisualizedInLayoutGraph(
            yDoc,
            node.data.dataId,
          );
          if (mainGraphData) {
            nodesWithMainGraphData.push({ node, mainGraphData });
          }
        }
      });

      if (nodesWithMainGraphData.length > 0) {
        // Show confirmation modal for nodes that exist in main graph
        const mainGraphNodeIds = nodesWithMainGraphData.map(
          ({ mainGraphData }) => mainGraphData.nodeId,
        );
        const sldNodeIds = nodesWithMainGraphData.map(({ node }) => node.id);
        setDeleteModalState({
          isOpen: true,
          elementsToDelete: elements,
          mirroredNodeIds: mainGraphNodeIds,
          sldNodeIds: sldNodeIds,
        });

        // Return empty elements to prevent immediate deletion
        // The modal will handle the actual deletion
        return { nodes: [], edges: [] };
      }

      // For non-mirrored nodes, handle deletion ourselves
      const nodeIds = elements.nodes.map((node) => node.id);
      const edgeIds = elements.edges.map((edge) => edge.id);

      if (nodeIds.length > 0) {
        deleteSLDNodes(yDoc, nodeIds);
      }
      if (edgeIds.length > 0) {
        deleteSLDEdges(yDoc, edgeIds);
      }

      // Return empty to prevent React Flow from handling deletion
      return { nodes: [], edges: [] };
    },
    [isFocused, yDoc, setDeleteModalState],
  );

  // Focus handlers - inline simple handlers
  const handleMouseDown = useCallback(
    () => setFocusedEditor(SLD_EDITOR),
    [setFocusedEditor],
  );
  const handleMouseEnter = useCallback(
    () => setFocusedEditor(SLD_EDITOR),
    [setFocusedEditor],
  );
  const handlePaneClick = useCallback(
    () => setFocusedEditor(SLD_EDITOR),
    [setFocusedEditor],
  );
  const handleNodeClick = useCallback(
    () => setFocusedEditor(SLD_EDITOR),
    [setFocusedEditor],
  );
  const handleEdgeClick = useCallback(
    () => setFocusedEditor(SLD_EDITOR),
    [setFocusedEditor],
  );

  // Edge context menu handler
  const handleEdgeContextMenu = useCallback(
    (event: React.MouseEvent, edge: Edge) => {
      // Prevent edge deselection on right-click
      event.preventDefault();
      event.stopPropagation();

      // Ensure the edge stays selected
      if (edge.selected) {
        const edges = reactFlowInstance?.getEdges();
        if (edges) {
          const updatedEdges = edges.map((e) => ({
            ...e,
            selected: e.id === edge.id,
          }));
          reactFlowInstance?.setEdges(updatedEdges);
        }
      }
    },
    [reactFlowInstance],
  );

  // Edge deletion handler
  const handleEdgesDelete = useCallback(
    (deletedEdges: Edge[]) => {
      // Clean up ghost nodes when their associated edges are deleted
      if (yDoc) {
        const sldSpecificNodes = getSldSpecificNodes(yDoc);
        if (sldSpecificNodes) {
          const sldNodes = getSldNodesMap(yDoc);
          const sldEdges = getSldEdgesMap(yDoc);
          if (sldNodes && sldEdges) {
            // Get all point IDs from deleted edges
            const deletedPointIds = new Set<string>();
            deletedEdges.forEach((edge) => {
              if (edge.data?.points && Array.isArray(edge.data.points)) {
                edge.data.points.forEach((point: any) => {
                  deletedPointIds.add(point.id);
                });
              }
            });

            // Find all ghost nodes that need to be deleted (including cascading)
            const nodesToDelete = new Set<string>();
            const processedNodes = new Set<string>();

            const findConnectedGhostNodes = (pointId: string) => {
              if (processedNodes.has(pointId)) {
                return;
              }
              processedNodes.add(pointId);

              // Find ghost nodes directly connected to this point
              for (const [nodeId, node] of sldNodes.entries()) {
                const nodeData = node.get("data");
                if (nodeData && typeof nodeData.get === "function") {
                  const sourceHandleId = nodeData.get("sourceHandleId");
                  if (sourceHandleId === pointId) {
                    nodesToDelete.add(nodeId);

                    // Find edges that connect TO this ghost node (as target) - check both YDoc and React Flow
                    for (const [_edgeId, edge] of sldEdges.entries()) {
                      const edgeData = edge.get("data");
                      if (edgeData && typeof edgeData.get === "function") {
                        const target = edgeData.get("target");
                        if (target === nodeId) {
                          // This edge connects to the ghost node, find its points
                          const points = edgeData.get("points");
                          if (points) {
                            points.toArray().forEach((point: any) => {
                              if (point.id) {
                                findConnectedGhostNodes(point.id);
                              }
                            });
                          }
                        }
                      }
                    }

                    // Find edges that connect FROM this ghost node (as source) - check both YDoc and React Flow
                    for (const [_edgeId, edge] of sldEdges.entries()) {
                      const edgeData = edge.get("data");
                      if (edgeData && typeof edgeData.get === "function") {
                        const source = edgeData.get("source");
                        if (source === nodeId) {
                          // This edge connects from the ghost node, find its points
                          const points = edgeData.get("points");
                          if (points) {
                            points.toArray().forEach((point: any) => {
                              if (point.id) {
                                findConnectedGhostNodes(point.id);
                              }
                            });
                          }
                        }
                      }
                    }

                    // Also check React Flow edges for connections to/from this ghost node
                    const reactFlowEdges = reactFlowInstance?.getEdges();
                    if (reactFlowEdges && Array.isArray(reactFlowEdges)) {
                      reactFlowEdges.forEach((edge) => {
                        // Check if edge connects TO this ghost node
                        if (edge.target === nodeId) {
                          if (
                            edge.data?.points &&
                            Array.isArray(edge.data.points)
                          ) {
                            edge.data.points.forEach((point: any) => {
                              if (point.id) {
                                findConnectedGhostNodes(point.id);
                              }
                            });
                          }
                        }

                        // Check if edge connects FROM this ghost node
                        if (edge.source === nodeId) {
                          if (
                            edge.data?.points &&
                            Array.isArray(edge.data.points)
                          ) {
                            edge.data.points.forEach((point: any) => {
                              if (point.id) {
                                findConnectedGhostNodes(point.id);
                              }
                            });
                          }
                        }
                      });
                    }
                  }
                }
              }
            };

            // Process all deleted points and their connected ghost nodes
            deletedPointIds.forEach((pointId) => {
              findConnectedGhostNodes(pointId);
            });

            // Delete all the ghost nodes
            nodesToDelete.forEach((nodeId) => {
              sldNodes.delete(nodeId);
            });
          }
        }
      }
    },
    [yDoc, reactFlowInstance],
  );

  // Selection change handler
  const handleSelectionChange = useCallback(
    ({ nodes, edges: _edges }: { nodes: Node[]; edges: Edge[] }) => {
      // Track selected nodes for context menu (filter out background rect)
      const actualSelectedNodes = nodes.filter(
        (node) => node.id !== "bounds-background",
      );
      setSelectedNodes(actualSelectedNodes as Node<SldNodeData>[]);

      // Sync selection of nodes that are visualized in main graph to their anchor nodes
      // Always sync when in SLD editor context (don't rely on focus timing)
      if (yDoc && !isSyncingSelection.current && isFocused) {
        // Find nodes that have corresponding data in the main graph
        const nodesWithMainGraphData: {
          node: Node;
          mainGraphData: { nodeId: string; nodeData: any };
        }[] = [];

        nodes.forEach((selectedNode) => {
          if (
            selectedNode.data?.dataId &&
            typeof selectedNode.data.dataId === "string"
          ) {
            const mainGraphData = hasDataVisualizedInLayoutGraph(
              yDoc,
              selectedNode.data.dataId,
            );
            if (mainGraphData) {
              nodesWithMainGraphData.push({
                node: selectedNode,
                mainGraphData,
              });
            }
          }
        });

        if (nodesWithMainGraphData.length > 0) {
          const graph = yDoc.getMap("graph");
          if (graph) {
            const yNodes = graph.get("nodes") as Y.Map<any>;
            if (yNodes) {
              // Find anchor nodes that should be selected
              const anchorNodeIdsToSelect: string[] = [];

              nodesWithMainGraphData.forEach(({ mainGraphData }) => {
                const mainGraphNode = yNodes.get(mainGraphData.nodeId);
                if (mainGraphNode) {
                  // Get the parent anchor node
                  const parentId = mainGraphNode.get("parentId");
                  if (parentId && typeof parentId === "string") {
                    anchorNodeIdsToSelect.push(parentId);
                  }
                }
              });

              // Only update main graph selection if we have nodes to select
              if (
                anchorNodeIdsToSelect.length > 0 &&
                mainGraphReactFlowInstanceRef?.current
              ) {
                // Set flag to prevent infinite loop
                isSyncingSelection.current = true;

                try {
                  // Get all nodes and update their selection state in one operation
                  const allNodes =
                    mainGraphReactFlowInstanceRef?.current?.getNodes();
                  const updatedNodes = allNodes?.map((node: any) => ({
                    ...node,
                    selected: anchorNodeIdsToSelect.includes(node.id),
                  }));

                  // Update all nodes at once
                  if (updatedNodes) {
                    mainGraphReactFlowInstanceRef?.current?.setNodes(
                      updatedNodes,
                    );
                  }
                } finally {
                  // Reset flag after a short delay to allow the change to propagate
                  setTimeout(() => {
                    isSyncingSelection.current = false;
                  }, 10);
                }
              }
            }
          }
        }
      }
    },
    [
      yDoc,
      isFocused,
      isSyncingSelection,
      mainGraphReactFlowInstanceRef,
      setSelectedNodes,
    ],
  );

  return {
    onConnect: handleConnect,
    onReconnect: handleReconnect,
    onNodeDragStart: handleNodeDragStart,
    onNodeDrag: handleNodeDrag,
    onNodeDragStop: handleNodeDragStop,
    onBeforeDelete: handleBeforeDelete,
    onMouseDown: handleMouseDown,
    onMouseEnter: handleMouseEnter,
    onPaneClick: handlePaneClick,
    onNodeClick: handleNodeClick,
    onEdgeClick: handleEdgeClick,
    onEdgeContextMenu: handleEdgeContextMenu,
    onEdgesDelete: handleEdgesDelete,
    onSelectionChange: handleSelectionChange,
    onMouseMove: updateCursor,
    onMouseLeave: () => updateCursor(),
  };
}
