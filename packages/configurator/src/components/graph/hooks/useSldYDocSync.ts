import { SldNodeData } from "@/components/devices/sld/types";
import { YDOC_DATA_KEY, DataType } from "@/components/data/Data";
import { Edge, Node } from "@xyflow/react";
import { useCallback, useEffect, useRef } from "react";
import * as Y from "yjs";
import {
  determineEdgeStyle,
  getSldNodeTypeFromDataType,
} from "../config/sldComponentConfig";
import {
  ensureSldYDocStructure,
  getSldEdgesMap,
  getSldNodesMap,
} from "../Graph";
import {
  restoreReactFlowNode,
  createReactFlowNodeInYDoc,
  createSldNode,
} from "../helpers/nodeSyncHelpers";
import { restoreCommentNodeFromYMap } from "../helpers/commentHelpers";
import { isMirroredDataType } from "../config/sldComponentConfig";
// Generate React Flow nodes with appropriate defaults for mirrored nodes
const createReactFlowNodeInSldGraph = (
  dataId: string,
  deviceType: string,
  position: { x: number; y: number },
  width: number = 56,
  height: number = 56,
  shouldSelect: boolean = true,
): Node<SldNodeData> => {
  const node = createSldNode(deviceType, position, width, height);

  // Create a completely new data object instead of spreading the hardcoded one
  const updatedData: SldNodeData = {
    dataId,
  };

  node.data = updatedData;
  node.selected = shouldSelect;

  return node;
};

// Helper function to process main graph nodes and create mirrored SLD nodes
// const processMainGraphNodes = (
//   mainGraphNodes: Y.Map<any>,
//   sldStoredNodesMap: Y.Map<any> | null,
//   creatingMirroredNodes: React.MutableRefObject<Set<string>>,
// ): Node<SldNodeData>[] => {
//   const mainGraphSldNodes: Node<SldNodeData>[] = [];

//   // Iterate through all nodes in the main graph
//   mainGraphNodes.forEach((mainGraphNode) => {
//     const mainGraphNodeData = mainGraphNode.get("data");
//     const dataObject = mainGraphNodeData ? mainGraphNodeData.toJSON() : {};
//     const dataId = dataObject?.dataId;

//     if (dataId) {
//       // Check if we have a matching dataId in sldStoredNodesMap
//       const existingSldNode = sldStoredNodesMap
//         ? findExistingSldNode(sldStoredNodesMap, dataId)
//         : null;

//       if (existingSldNode) {
//         // Node already exists in SLD graph - restore and update it
//         const restoredNode = restoreReactFlowNode(
//           existingSldNode.nodeId,
//           existingSldNode.node,
//         );
//         mainGraphSldNodes.push(restoredNode);

//         // Remove from creating list since it now exists
//         creatingMirroredNodes.current.delete(dataId);
//       } else if (!creatingMirroredNodes.current.has(dataId)) {
//         // Node doesn't exist in SLD graph yet - create it
//         const mainGraphType = mainGraphNode.get("type");
//         //TODO - read from the DataType instead of the main graph type
//         const sldNodeType = getSldNodeTypeFromMainGraphType(mainGraphType);

//         // Skip creation if no valid SLD node type found
//         if (sldNodeType) {
//           const mirroredNode = createReactFlowNodeInSldGraph(
//             dataId,
//             sldNodeType,
//             { x: 0, y: 0 },
//             56,
//             56,
//           );

//           // Track that we're creating this mirrored node
//           creatingMirroredNodes.current.add(dataId);

//           if (sldStoredNodesMap) {
//             // Store the node directly in YDoc with the dataId
//             createReactFlowNodeInYDoc(
//               sldStoredNodesMap,
//               mirroredNode.id,
//               sldNodeType,
//               dataId,
//               { x: 0, y: 0 },
//             );
//             mainGraphSldNodes.push(mirroredNode);
//           }
//         }
//       }
//     }
//   });
//   return mainGraphSldNodes;
// };

interface UseSldYDocSyncProps {
  yDoc: Y.Doc;
  sldNodes: Node<SldNodeData>[];
  setSldNodes: React.Dispatch<React.SetStateAction<Node<SldNodeData>[]>>;
  setSldEdges: React.Dispatch<React.SetStateAction<Edge[]>>;
  reactFlowInstance: any;
}

export function useSldYDocSync({
  yDoc,
  sldNodes: _sldNodes,
  setSldNodes,
  setSldEdges,
  reactFlowInstance: reactFlowInstance,
}: UseSldYDocSyncProps) {
  // Initialize SLD-specific nodes YDoc map if it doesn't exist
  useEffect(() => {
    // Initialize SLD YDoc structure if it doesn't exist
    ensureSldYDocStructure(yDoc);
  }, [yDoc]);

  // // Clean up orphaned nodes (mirrored nodes whose originals no longer exist)
  // const cleanupOrphanedNodes = useCallback(() => {
  //   const sldNodesMap = getSldNodesMap(yDoc);
  //   if (!sldNodesMap) {
  //     return;
  //   }

  //   // Get all current nodes directly from the Y.Map (not converted to JSON)
  //   const nodeIds = Array.from(sldNodesMap.keys());

  //   // Find nodes that are orphaned (nodes with dataId that no longer exist in main graph)
  //   const nodesToDelete: string[] = [];
  //   nodeIds.forEach((nodeId) => {
  //     const yNode = sldNodesMap.get(nodeId);
  //     if (!yNode) {
  //       return;
  //     }

  //     const nodeData = yNode.get("data");
  //     let dataId: string | undefined;

  //     // Handle both Yjs Maps and plain objects
  //     if (nodeData) {
  //       dataId = nodeData.get("dataId");
  //     } else if (nodeData) {
  //       // Plain object
  //       dataId = nodeData.dataId;
  //     }

  //     // If the node has a dataId, check if it still exists in the main graph
  //     if (dataId) {
  //       const mainGraphData = hasDataVisualizedInLayoutGraph(yDoc, dataId);
  //       if (isSldSpecificNode(yNode.get("type"))) {
  //         // This SLD node is a sld specific node and should not be deleted
  //         return;
  //       }

  //       if (!mainGraphData) {
  //         // This SLD node's data no longer exists in the main graph - it's orphaned
  //         console.log("deleting node because it's orphaned:", nodeId);
  //         nodesToDelete.push(nodeId);
  //       }
  //       //
  //     }
  //   });

  //   // Delete the orphaned nodes
  //   nodesToDelete.forEach((nodeId) => {
  //     sldNodesMap.delete(nodeId);
  //   });
  // }, [yDoc]);

  // // Clean up orphaned edges that connect to non-existent nodes
  // const cleanupOrphanedEdges = useCallback(() => {
  //   const sldNodesMap = getSldNodesMap(yDoc);
  //   const sldEdgesMap = getSldEdgesMap(yDoc);

  //   if (!sldNodesMap || !sldEdgesMap) {
  //     return;
  //   }

  //   // Get all current nodes to check which ones exist
  //   const currentNodes = sldNodesMap.toJSON();
  //   const existingNodeIds = new Set(Object.keys(currentNodes));

  //   // Get all current edges
  //   const currentEdges = sldEdgesMap.toJSON();

  //   // Find edges that are connected to non-existent nodes or invalid mirrored nodes
  //   const edgesToDelete: string[] = [];
  //   Object.entries(currentEdges).forEach(([edgeId, edge]: [string, any]) => {
  //     const sourceExists = existingNodeIds.has(edge.source);
  //     const targetExists = existingNodeIds.has(edge.target);

  //     // Check if source and target nodes are valid (not orphaned)
  //     let sourceValid = sourceExists;
  //     let targetValid = targetExists;

  //     if (sourceExists) {
  //       const sourceNode = sldNodesMap.get(edge.source);
  //       if (sourceNode) {
  //         const sourceNodeData = sourceNode.get("data");
  //         if (sourceNodeData && typeof sourceNodeData.get === "function") {
  //           const isMirroredNode = sourceNodeData.get("isMirroredNode");
  //           const originalNodeId = sourceNodeData.get("originalNodeId");

  //           // If it's a mirrored node, check if the original node still exists in main graph
  //           if (isMirroredNode && originalNodeId) {
  //             const graph = yDoc.getMap("graph");
  //             const yNodes = graph?.get("nodes") as Y.Map<any>;
  //             sourceValid = yNodes ? yNodes.has(originalNodeId) : false;
  //           }
  //         }
  //       }
  //     }

  //     if (targetExists) {
  //       const targetNode = sldNodesMap.get(edge.target);
  //       if (targetNode) {
  //         const targetNodeData = targetNode.get("data");
  //         if (targetNodeData && typeof targetNodeData.get === "function") {
  //           const isMirroredNode = targetNodeData.get("isMirroredNode");
  //           const originalNodeId = targetNodeData.get("originalNodeId");

  //           // If it's a mirrored node, check if the original node still exists in main graph
  //           if (isMirroredNode && originalNodeId) {
  //             const graph = yDoc.getMap("graph");
  //             const yNodes = graph?.get("nodes") as Y.Map<any>;
  //             targetValid = yNodes ? yNodes.has(originalNodeId) : false;
  //           }
  //         }
  //       }
  //     }

  //     if (!sourceExists || !targetExists || !sourceValid || !targetValid) {
  //       edgesToDelete.push(edgeId);
  //     }
  //   });

  //   // Delete the orphaned edges
  //   edgesToDelete.forEach((edgeId) => {
  //     sldEdgesMap.delete(edgeId);
  //   });
  // }, [yDoc]);

  // // Clean up orphaned ghost nodes when their associated edges are deleted
  // const cleanupOrphanedGhostNodes = useCallback(() => {
  //   const sldNodesMap = getSldNodesMap(yDoc);
  //   const sldEdgesMap = getSldEdgesMap(yDoc);

  //   if (!sldNodesMap || !sldEdgesMap) {
  //     return;
  //   }

  //   // Get all current edges to check which ghost nodes are still connected
  //   const currentEdges = sldEdgesMap.toJSON();
  //   const connectedPointIds = new Set<string>();

  //   // Collect all point IDs that are still connected to edges
  //   Object.values(currentEdges).forEach((edge: any) => {
  //     if (edge.data?.points && Array.isArray(edge.data.points)) {
  //       edge.data.points.forEach((point: any) => {
  //         if (point.id) {
  //           connectedPointIds.add(point.id);
  //         }
  //       });
  //     }
  //   });

  //   // Find and remove ghost nodes that are no longer connected to any edges
  //   const nodesToDelete: string[] = [];

  //   sldNodesMap.forEach((node, nodeId) => {
  //     const nodeType = node.get("type");
  //     if (nodeType === "ghostSLD") {
  //       const nodeData = node.get("data");
  //       if (nodeData && typeof nodeData.get === "function") {
  //         const sourceHandleId = nodeData.get("sourceHandleId");
  //         if (sourceHandleId && !connectedPointIds.has(sourceHandleId)) {
  //           // This ghost node is no longer connected to any edge, mark it for deletion
  //           nodesToDelete.push(nodeId);
  //         }
  //       }
  //     }
  //   });

  //   // Delete the orphaned ghost nodes
  //   nodesToDelete.forEach((nodeId) => {
  //     sldNodesMap.delete(nodeId);
  //   });
  // }, [yDoc]);

  // Sync edges from YDoc
  const syncEdges = useCallback(() => {
    const sldEdgesMap = getSldEdgesMap(yDoc);
    if (!sldEdgesMap) {
      setSldEdges([]);
      return;
    }

    // Get current nodes directly from YDoc to avoid stale state
    const nodesToUse: any[] = [];

    const sldNodesMap = getSldNodesMap(yDoc);
    if (sldNodesMap) {
      sldNodesMap.forEach((node, id) => {
        const nodeType = node.get("type");
        nodesToUse.push({ id, type: nodeType });
      });
    }

    // Get all edges from SLD edges map
    const allEdges = Object.values(sldEdgesMap.toJSON()) as Edge[];

    // Filter out edges without proper IDs to prevent React key warnings
    const validEdges = allEdges.filter(
      (edge) =>
        edge && edge.id && typeof edge.id === "string" && edge.id.trim() !== "",
    );

    // Process all SLD edges and preserve their styles
    const sldEdges = validEdges.map((edge) => {
      // Ensure the edge has the proper style based on its handles and source node
      const sourceHandleId = edge.sourceHandle;
      const targetHandleId = edge.targetHandle;

      // Find nodes in provided nodes or current sldNodes state
      const sourceNode = nodesToUse.find((node) => node.id === edge.source);
      const targetNode = nodesToUse.find((node) => node.id === edge.target);

      // Check if either handle is a light handle (via1, via2, via3)
      const lightHandleId =
        sourceHandleId && ["via1", "via2", "via3"].includes(sourceHandleId)
          ? sourceHandleId
          : targetHandleId && ["via1", "via2", "via3"].includes(targetHandleId)
            ? targetHandleId
            : sourceHandleId || targetHandleId;

      const edgeStyle = determineEdgeStyle(
        sourceNode,
        targetNode,
        lightHandleId,
      );

      return {
        ...edge,
        type: "smoothstep",
        selected: edge.selected || false, // Preserve selection state
        animated: edge.selected || false, // Animate selected edges
        style: {
          ...edgeStyle,
          strokeWidth: edge.selected
            ? Math.max(edgeStyle.strokeWidth + 1, 4)
            : edgeStyle.strokeWidth, // Thicker when selected
        },
        // Data structure for CustomEdge with points array
        data: {
          ...edge.data,
          selected: edge.selected || false, // Include selection state in data
          points: edge.data?.points || [], // Initialize points array for CustomEdge
        },
      };
    });

    setSldEdges(sldEdges);
  }, [yDoc, setSldEdges]);

  // Create mirrored node if necessary
  const createMirroredNodeIfNecessary = (
    dataType: DataType,
    dataNodeId: string,
  ): Node<SldNodeData> | null => {
    // we're only monitoring the Data graph
    // to create mirrored nodes that should exist, but don't.
    if (!isMirroredDataType(dataType)) {
      // console.log("Node is not a mirrored node, skipping for type with id:", dataNodeId, "and dataType:", dataType);
      return null;
    }
    const sldNodeType = getSldNodeTypeFromDataType(dataType);
    if (!sldNodeType) {
      console.warn(
        "Cannot create SLD node: no sld node type found for dataType:",
        dataType,
      );
      return null;
    }

    const sldStoredNodesMap = getSldNodesMap(yDoc);
    if (!sldStoredNodesMap) {
      console.warn("SLD stored nodes map not found");
      return null;
    }

    // Check if SLD node already exists by dataId
    let existingSldNode = null;
    sldStoredNodesMap.forEach((node, nodeId) => {
      const nodeData = node.get("data");
      if (nodeData && nodeData.get("dataId") === dataNodeId) {
        existingSldNode = node;
      }
    });

    if (!existingSldNode) {
      const mirroredNode = createReactFlowNodeInSldGraph(
        dataNodeId,
        sldNodeType,
        { x: 0, y: 0 },
        56,
        56,
      );

      // Store the node in YDoc
      createReactFlowNodeInYDoc(
        sldStoredNodesMap,
        mirroredNode.id,
        sldNodeType,
        dataNodeId,
        mirroredNode.position,
      );

      // Deselect all existing nodes and select the new mirrored node
      setSldNodes((prev) =>
        prev.map((node) => ({
          ...node,
          selected: node.id === mirroredNode.id,
        })),
      );

      return mirroredNode;
    }
    return null;
  };

  // Create a ReactFlow node from a data nodeId
  const createReactFlowNodeFromDataId = (
    dataNodeId: string,
  ): Node<SldNodeData> | null => {
    const dataMap = yDoc.getMap(YDOC_DATA_KEY);
    if (!dataMap?.has(dataNodeId)) {
      console.warn("Data node not found:", dataNodeId);
      return null;
    }

    const nodeData = dataMap.get(dataNodeId) as Y.Map<any>;
    const dataType = nodeData.get("type") as DataType;

    if (!dataType) {
      console.warn("No dataType found for node:", dataNodeId);
      return null;
    }

    return createMirroredNodeIfNecessary(dataType, dataNodeId);
  };

  // Restore all nodes from Y.js document
  const restoreAllNodesFromYDoc = () => {
    const sldStoredNodesMap = getSldNodesMap(yDoc);
    if (!sldStoredNodesMap) {
      console.warn("SLD stored nodes map not found");
      return;
    }

    const restoredNodes: Node<SldNodeData>[] = [];

    // Iterate through all SLD graph nodes
    sldStoredNodesMap.forEach((node, nodeId) => {
      const nodeData = node.get("data");
      if (nodeData) {
        const dataId = nodeData.get("dataId");

        if (dataId) {
          // Node has a dataId - check if the data still exists
          const dataMap = yDoc.getMap(YDOC_DATA_KEY);
          if (dataMap?.has(dataId)) {
            // console.log("Restoring node:", nodeId, "dataId:", dataId);
            const reactFlowNode = restoreReactFlowNode(nodeId, node);
            restoredNodes.push(reactFlowNode);
          } else {
            // Data no longer exists, mark for cleanup
            console.log(
              "Cleaning up orphaned SLD node:",
              nodeId,
              "dataId:",
              dataId,
            );
            sldStoredNodesMap.delete(nodeId);
          }
        } else {
          // Node doesn't have a dataId (like ghost nodes, comment nodes) - restore directly
          const nodeType = node.get("type");
          if (nodeType === "ghostSLD" || nodeType === "comment") {
            const reactFlowNode = restoreReactFlowNode(nodeId, node);
            restoredNodes.push(reactFlowNode);
          }
        }
      }
    });

    // Check for mirrored nodes that should exist but don't
    const dataMap = yDoc.getMap(YDOC_DATA_KEY);
    if (dataMap) {
      dataMap.forEach((nodeData, dataNodeId) => {
        const dataType = (nodeData as Y.Map<any>).get("type") as DataType;
        if (dataType) {
          const mirroredNode = createMirroredNodeIfNecessary(
            dataType,
            dataNodeId,
          );
          if (mirroredNode) {
            console.log(
              "Created missing mirrored node:",
              dataNodeId,
              "type:",
              dataType,
            );
            restoredNodes.push(mirroredNode);
          }
        }
      });
    }

    setSldNodes(restoredNodes);
    syncEdges();
  };

  // Sync SLD nodes with YDoc changes
  useEffect(() => {
    const syncWithYDocData = (
      events?: Y.YEvent<any>[],
      transaction?: Y.Transaction,
    ) => {
      if (!events) {
        //if no events, we're in an initial sync
        restoreAllNodesFromYDoc();
        return;
      }
      // Check if this is an undo/redo operation
      if (
        transaction &&
        transaction.origin &&
        transaction.origin.constructor.name === "UndoManager"
      ) {
        console.log("Undo/Redo operation detected");
        // Handle undo/redo by restoring all nodes from YDoc
        restoreAllNodesFromYDoc();
        return;
      }

      events.forEach((event, _index) => {
        // Determine operation type based on changes
        if (event instanceof Y.YMapEvent) {
          const changes = event.changes;

          changes.keys.forEach((changeInfo, key) => {
            if (changeInfo.action === "add") {
              yDocNodeAdded(key);
            } else if (changeInfo.action === "delete") {
              yDocNodeRemoved(key);
            } else if (changeInfo.action === "update") {
              yDocNodeUpdated(key);
            }
          });
        }
      });
    };

    const dataMap = yDoc.getMap(YDOC_DATA_KEY);
    const sldEdgesMap = getSldEdgesMap(yDoc);

    if (dataMap) {
      const dataObserver = (
        events: Y.YEvent<any>[],
        transaction: Y.Transaction,
      ) => {
        syncWithYDocData(events, transaction);
      };

      dataMap.observeDeep(dataObserver);

      syncWithYDocData(); // Initial sync without event data

      // Also observe SLD edges map to sync edges when they change
      let edgesObserver:
        | ((events: Y.YEvent<any>[], transaction: Y.Transaction) => void)
        | null = null;
      if (sldEdgesMap) {
        edgesObserver = (
          events: Y.YEvent<any>[],
          transaction: Y.Transaction,
        ) => {
          syncEdges();
        };
        sldEdgesMap.observeDeep(edgesObserver);
      }

      return () => {
        dataMap.unobserveDeep(dataObserver);
        if (edgesObserver && sldEdgesMap) {
          sldEdgesMap.unobserveDeep(edgesObserver);
        }
      };
    }

    // Initial sync if no dataMap
    syncWithYDocData();
    return () => {};
  }, [
    yDoc,
    setSldNodes,
    syncEdges,
    reactFlowInstance,

    // cleanupOrphanedNodes,
    // cleanupOrphanedEdges,
    // cleanupOrphanedGhostNodes,
    // processMainGraphNodes,
  ]);

  // Stub methods for handling Y.js node events
  const yDocNodeAdded = (dataId: string) => {
    // console.log("Node added - dataId:", dataId);

    const reactFlowNode = createReactFlowNodeFromDataId(dataId);
    if (reactFlowNode) {
      setSldNodes((prev) => [...prev, reactFlowNode]);
    }
  };

  const yDocNodeRemoved = (dataId: string) => {
    console.log("SLD sync: Node removed from Data Map, dataId:", dataId);
    const sldStoredNodesMap = getSldNodesMap(yDoc);
    if (sldStoredNodesMap) {
      // Find the SLD node that has this dataId
      let nodeToDelete: string | null = null;
      sldStoredNodesMap.forEach((node, nodeId) => {
        const nodeData = node.get("data");
        if (nodeData && nodeData.get("dataId") === dataId) {
          nodeToDelete = nodeId;
        }
      });

      if (nodeToDelete) {
        sldStoredNodesMap.delete(nodeToDelete);
        setSldNodes((prev) =>
          prev.filter((sldNode) => sldNode.id !== nodeToDelete),
        );
      } else {
        console.log("SLD sync: No mirrored node found for dataId:", dataId);
      }
    } else {
      console.warn("Cannot delete node: SLD stored nodes map not found");
    }
  };

  const yDocNodeUpdated = (dataId: string) => {
    // TODO: Implement node updated logic
    // console.log("Node updated:", dataId);
  };

  const handleUndoRedo = () => {
    console.log("Handling undo/redo operation");
    // Restore all nodes from YDoc to ensure consistency
    restoreAllNodesFromYDoc();
  };

  return {
    yDocNodeAdded,
    yDocNodeRemoved,
    yDocNodeUpdated,
    handleUndoRedo,
  };
}
