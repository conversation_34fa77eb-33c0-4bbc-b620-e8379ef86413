import { SomoIrControllerIconAlt } from "@/components/devices/somo-ir-controller/SomoIrControllerIconAlt";
import { PaneContainer, PaneTitle } from "@/components/ui/Layout";
import { cn } from "@/lib/classNames";
import {
  DndContext,
  PointerSensor,
  closestCenter,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import { SortableContext, arrayMove } from "@dnd-kit/sortable";
import { GraphNode } from "@somo/shared";
import {
  Blinds,
  CirclePlayIcon,
  ConciergeBell,
  Disc2,
  DoorOpen,
  Fan,
  ImageIcon,
  LampIcon,
  LayoutPanelTop,
  MessageCircle,
  Microchip,
  Plug,
  PowerIcon,
  RadioIcon,
  Server,
  Sliders,
  Thermometer,
} from "lucide-react";
import React, { useEffect, useState } from "react";
import { match } from "ts-pattern";
import * as Y from "yjs";
import { SortableItem } from "../SortableItem";
import { useReactFlowContext } from "./ReactFlowContext";

function iconForNodeType(type: GraphNode["type"]) {
  return match(type)
    .with("image", () => <ImageIcon className="size-3 flex-shrink-0" />)
    .with("section", () => <LayoutPanelTop className="size-3 flex-shrink-0" />)
    .with("scene", () => <CirclePlayIcon className="size-3 flex-shrink-0" />)
    .with("light", "outletDimmerLight", () => (
      <LampIcon className="size-3 flex-shrink-0" />
    ))
    .with("virtualButtonAnchor", () => (
      <PowerIcon className="size-3 flex-shrink-0" />
    ))
    .with("baseStationAnchor", () => (
      <Server className="size-3 flex-shrink-0" />
    ))
    .with("canbusControllerAnchor", () => (
      <Microchip className="size-3 flex-shrink-0" />
    ))
    .with("canbusControllerContainer", () => (
      <LayoutPanelTop className="size-3 flex-shrink-0" />
    ))
    .with("roomSwitchAnchor", () => (
      <RadioIcon className="size-3 flex-shrink-0" />
    ))
    .with("roomDimmerAnchor", () => (
      <Sliders className="size-3 flex-shrink-0" />
    ))
    .with("outletDimmerAnchor", () => <Plug className="size-3 flex-shrink-0" />)
    .with("presenceSensorAnchor", () => (
      <Disc2 className="size-3 flex-shrink-0" />
    ))
    .with("doorSensorAnchor", () => (
      <DoorOpen className="size-3 flex-shrink-0" />
    ))
    .with("somoThermostatAnchor", () => (
      <Thermometer className="size-3 flex-shrink-0" />
    ))
    .with("somoIrControllerAnchor", () => (
      <SomoIrControllerIconAlt className="size-3 flex-shrink-0" />
    ))
    .with("somoShadesAnchor", () => <Blinds className="size-3 flex-shrink-0" />)
    .with("somoFanAnchor", () => <Fan className="size-3 flex-shrink-0" />)
    .with("servicePadAnchor", () => (
      <ConciergeBell className="size-3 flex-shrink-0" />
    ))
    .with("comment", () => <MessageCircle className="size-3 flex-shrink-0" />)
    .with(
      undefined,
      "virtualButtonContainer",
      "baseStationContainer",
      "doorSensorContainer",
      "presenceSensorContainer",
      "roomDimmerContainer",
      "outletDimmerContainer",
      "somoThermostatContainer",
      "somoIrControllerContainer",
      "roomSwitchContainer",
      "somoShadesContainer",
      "somoFanContainer",
      "servicePadContainer",
      () => null,
    )
    .exhaustive();
}

function nameForNodeType(type: GraphNode["type"]) {
  return match(type)
    .with("image", () => "Image")
    .with("section", () => "Section")
    .with("scene", () => "Scene")
    .with("light", () => "Light")
    .with("virtualButtonAnchor", () => "Virtual Button")
    .with("baseStationAnchor", () => "Base Station")
    .with("canbusControllerAnchor", () => "CAN Bus Controller")
    .with("roomSwitchAnchor", () => "Room Switch")
    .with("comment", () => "Comment")
    .with("doorSensorAnchor", () => "Door Sensor")
    .with("presenceSensorAnchor", () => "Occupancy Sensor")
    .with("roomDimmerAnchor", () => "Room Dimmer")
    .with("outletDimmerAnchor", () => "Plug-in Dimmer")
    .with("outletDimmerLight", () => "Plug-in Dimmer Light")
    .with("somoThermostatAnchor", () => "Room Thermostat")
    .with("somoIrControllerAnchor", () => "IR Controller")
    .with("somoShadesAnchor", () => "Shades & Curtains Controller")
    .with("somoFanAnchor", () => "Room Fan Controller")
    .with("servicePadAnchor", () => "Room Service Pad")
    .with(
      undefined,
      "virtualButtonContainer",
      "baseStationContainer",
      "somoShadesContainer",
      "servicePadContainer",
      "somoFanContainer",
      "somoThermostatContainer",
      "somoIrControllerContainer",
      "doorSensorContainer",
      "presenceSensorContainer",
      "roomDimmerContainer",
      "outletDimmerContainer",
      "roomSwitchContainer",
      "canbusControllerContainer",
      () => type,
    )
    .exhaustive();
}

interface Props {
  visibleNodes: GraphNode[];
  nodes: GraphNode[];
}

export const NodeList: React.FC<Props> = ({ visibleNodes, nodes }) => {
  const { getReactFlowInstance, yDoc } = useReactFlowContext();

  const [sortableNodes, setSortableNodes] = useState(
    visibleNodes.filter((node) => !node.parentId).reverse(),
  );
  useEffect(() => {
    setSortableNodes(visibleNodes.filter((node) => !node.parentId).reverse());
  }, [visibleNodes]);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
  );

  return (
    <PaneContainer className="flex flex-col flex-shrink-0">
      <PaneTitle>Items</PaneTitle>
      <div className="flex flex-col gap-0">
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragEnd={({ active, over }) => {
            if (over && active.id !== over.id) {
              const activeNode = sortableNodes.find((n) => n.id === active.id);
              const overNode = sortableNodes.find((n) => n.id === over.id);
              if (activeNode && overNode) {
                setSortableNodes((items) => {
                  if (!over || !active) {
                    return items;
                  }

                  const oldIndex = items.findIndex((n) => n.id === active.id);
                  const newIndex = items.findIndex((n) => n.id === over.id);
                  if (oldIndex === -1 || newIndex === -1) {
                    return items;
                  }

                  const newItems = arrayMove(items, oldIndex, newIndex);
                  const len = newItems.length;
                  for (const item of newItems) {
                    const graph = yDoc.getMap("graph");
                    const nodes = graph?.get("nodes") as Y.Map<any>;
                    const node = nodes?.get(item.id) as Y.Map<any>;
                    if (!node) {
                      continue;
                    }
                    node.set("zIndex", len - newItems.indexOf(item));
                  }
                  return newItems;
                });
              }
            }
          }}
        >
          <SortableContext items={sortableNodes}>
            {sortableNodes.map((node) => {
              const hasChildSelected = visibleNodes.some(
                (n) => n.parentId === node.id && n.selected,
              );
              const childNode = nodes.find((n) => n.parentId === node.id);

              const title =
                "title" in node.data && typeof node.data.title === "string"
                  ? node.data.title
                  : childNode &&
                      "title" in childNode.data &&
                      typeof childNode.data.title === "string"
                    ? childNode.data.title
                    : nameForNodeType(node.type);

              return (
                <SortableItem id={node.id} key={node.id}>
                  <div
                    className={cn(
                      "text-[10px] font-semibold px-3 py-2 flex flex-row items-center gap-1 min-w-0 text-gray-500",
                      (node.selected || hasChildSelected) && "bg-gray-100",
                    )}
                    onClick={(event) => {
                      // remove all node selections
                      if (!event.metaKey) {
                        for (const n of visibleNodes) {
                          if (n.id === node.id) {
                            continue;
                          }

                          getReactFlowInstance()?.updateNode(n.id, {
                            selected: false,
                          });
                        }
                      }
                      if (node.type === "image") {
                        // unlock image node
                        const isLocked = node.draggable;
                        getReactFlowInstance()?.updateNode(node.id, {
                          selectable: true,
                          selected: true,
                          draggable: isLocked ? true : false,
                        });
                      } else {
                        getReactFlowInstance()?.updateNode(node.id, {
                          selected: event.metaKey ? !node.selected : true,
                        });
                      }
                    }}
                  >
                    {iconForNodeType(node.type)}
                    <span className="truncate">{title}</span>
                  </div>
                </SortableItem>
              );
            })}
          </SortableContext>
        </DndContext>
      </div>
    </PaneContainer>
  );
};
