import { GraphNode, randomId } from "@somo/shared";
import { Node } from "@xyflow/react";
import * as Y from "yjs";
import { SldNodeData } from "../../devices/sld/types";
import { getMap, getRootMap } from "../../../lib/yjsUtils";

/**
 * Restores a React Flow node from YDoc node data
 */
export function restoreReactFlowNode(
  id: string,
  node: Y.Map<any>,
): Node<SldNodeData> {
  const nodeData = node.get("data");

  const dataObject = nodeData ? nodeData.toJSON() : {};

  // Extract all properties from the Y.Map and spread them
  const nodeProperties = node.toJSON();

  // Ensure position is properly formatted (Y.Map gets converted to plain object)
  const position = nodeProperties.position || { x: 0, y: 0 };

  const baseNode: Node<SldNodeData> = {
    id: id,
    position: { ...position }, // Create new object to ensure ReactFlow detects change
    data: dataObject,
    selected: false,
    zIndex: nodeProperties.type === "comment" ? 100 : 0,
    selectable: true,
    ...nodeProperties, // Spread all other properties from YDoc including type
  };

  return baseNode;
}

/**
 * Finds an existing SLD node by dataId
 */
export function findExistingSldNode(
  sldNodesMap: Y.Map<any>,
  dataId: string,
): { nodeId: string; node: Y.Map<any> } | null {
  let foundNode: Y.Map<any> | null = null;
  let foundKey: string | null = null;

  sldNodesMap.forEach((storedNode, key) => {
    const nodeData = storedNode.get("data");
    // Handle data - it should be a Y.Map now
    if (!nodeData) {
      console.warn(`Stored node ${key} has no data, skipping`);
      return;
    }
    const dataObject = nodeData.toJSON();

    if (dataObject && dataObject.dataId === dataId) {
      foundNode = storedNode;
      foundKey = key;
    }
  });

  if (foundNode && foundKey) {
    return { nodeId: foundKey, node: foundNode };
  }
  return null;
}

/**
 * Creates a new node in YDoc. this could be a main graph node or an sld node-
 * we just pass it the location where to store it
 */
export function createReactFlowNodeInYDoc(
  sldNodesMap: Y.Map<any>,
  nodeId: string,
  deviceType: string,
  dataId: string,
  position: { x: number; y: number } = { x: 0, y: 0 },
): void {
  // Wrap in transaction to prevent observer triggers during modification
  sldNodesMap.doc?.transact(() => {
    const yNode = new Y.Map();
    yNode.set("id", nodeId);
    yNode.set("type", deviceType);

    // Store position as Y.Map
    const yPosition = new Y.Map();
    yPosition.set("x", position.x);
    yPosition.set("y", position.y);
    yNode.set("position", yPosition);

    // Store dataId directly
    const yData = new Y.Map();
    yData.set("dataId", dataId); // Store the dataId reference for data fetching
    yNode.set("data", yData);
    yNode.set("selected", false);
    sldNodesMap.set(nodeId, yNode);
  });
}

/**
 * Calculates position for a new node based on previous nodes
 */
export function calculateNodePosition(
  previousNode: Node<SldNodeData> | undefined,
  margin = 50,
  spacing = 60,
): { x: number; y: number } {
  if (!previousNode) {
    return { x: margin, y: margin };
  }

  const previousNodeWidth = previousNode.width || 120;
  const xPosition = previousNode.position.x + previousNodeWidth + spacing;
  const yPosition = previousNode.position.y;

  return { x: xPosition, y: yPosition };
}

export function doesNodeExistInMainGraph(yDoc: Y.Doc, dataId: string): boolean {
  if (!dataId) {
    return false;
  }

  // Use getRootMap to get the graph map with proper error handling
  const graphResult = getRootMap(yDoc, "graph");
  if (!graphResult.success) {
    return false;
  }

  // Use getMap to get the nodes map with proper error handling
  const nodesResult = getMap(graphResult.value, "nodes");
  if (!nodesResult.success) {
    return false;
  }

  const yNodes = nodesResult.value;

  // Iterate through nodes and check for matching dataId
  for (const [_, node] of yNodes) {
    // Type check to ensure node is a Y.Map before using getMap
    if (!(node instanceof Y.Map)) {
      continue;
    }

    // Use getMap to get the data map with proper error handling
    const dataResult = getMap(node, "data");
    if (!dataResult.success) {
      continue;
    }

    const nodeData = dataResult.value;
    const nodeDataId = nodeData.get("dataId");
    if (nodeDataId === dataId) {
      return true;
    }
  }

  return false;
}

/**
 * Checks if a node with the given dataId exists in the main layout graph.
 * Returns the main graph node data if it exists, null otherwise.
 * This replaces the old isMirroredNode logic for delete confirmations.
 */
export function hasDataVisualizedInLayoutGraph(
  yDoc: Y.Doc,
  dataId: string,
): { nodeId: string; nodeData: any } | null {
  if (!dataId) {
    return null;
  }

  // Use getRootMap to get the graph map with proper error handling
  const graphResult = getRootMap(yDoc, "graph");
  if (!graphResult.success) {
    return null;
  }

  // Use getMap to get the nodes map with proper error handling
  const nodesResult = getMap(graphResult.value, "nodes");
  if (!nodesResult.success) {
    return null;
  }

  const yNodes = nodesResult.value;

  // Iterate through nodes and check for matching dataId
  for (const [nodeId, node] of yNodes) {
    // Type check to ensure node is a Y.Map before using getMap
    if (!(node instanceof Y.Map)) {
      continue;
    }

    // Use getMap to get the data map with proper error handling
    const dataResult = getMap(node, "data");
    if (!dataResult.success) {
      continue;
    }

    const nodeData = dataResult.value;
    const nodeDataId = nodeData.get("dataId");
    if (nodeDataId === dataId) {
      return {
        nodeId,
        nodeData: nodeData.toJSON(),
      };
    }
  }

  return null;
}

/**
 * Validates if a position is valid
 */
export function isValidPosition(position: any): boolean {
  return (
    position &&
    typeof position.x === "number" &&
    typeof position.y === "number" &&
    !isNaN(position.x) &&
    !isNaN(position.y) &&
    !(position.x === 0 && position.y === 0)
  );
}

/**
 * Gets device name from main graph node, with special handling for CanBo controllers
 */
export function getDeviceNameFromMainGraph(
  mainGraphNode: GraphNode | undefined,
  mirroredType: string,
  deviceConfig: any,
): string {
  if (!mainGraphNode) {
    return deviceConfig.deviceName || "";
  }

  const rawTitle = (mainGraphNode.data as any)?.title;
  let deviceName =
    (typeof rawTitle === "string" ? rawTitle : rawTitle?.toString?.() || "") ||
    "";

  // For CanBo controllers, use the component's default name instead of the mirrored title
  if (mirroredType === "canbusControllerContainer") {
    deviceName = deviceConfig.deviceName;
  }

  return deviceName;
}

/**
 * Creates an SLD node with proper typing and metadata
 */
export function createSldNode(
  type: string,
  position: { x: number; y: number },
  width: number,
  height: number,
  additionalProps: Partial<Node<SldNodeData>> = {},
): Node<SldNodeData> {
  return {
    id: randomId(),
    type,
    data: {
      dataId: "replaced at runtime", // This will be replaced by createDataFromNode
    },
    position,
    width,
    height,
    selected: true,
    draggable: true,
    selectable: true,
    ...additionalProps,
  };
}
