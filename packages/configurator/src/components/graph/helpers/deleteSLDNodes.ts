import * as Y from "yjs";
import { getSldNodesMap, getSldEdgesMap } from "../Graph";
/**
 * Deletes mirrored nodes from the main graph YDoc
 * This function handles the deletion of nodes that are mirrored from the main graph
 * and ensures proper cleanup of parent-child relationships
 */
export function deleteMirroredNodesFromMainGraph(
  yDoc: Y.Doc,
  mirroredNodeIds: string[],
): boolean {
  const graph = yDoc.getMap("graph");
  if (!graph) {
    console.warn("Cannot delete mirrored nodes: Graph map not found");
    return false;
  }

  const yNodes = graph.get("nodes") as Y.Map<any>;
  if (!yNodes) {
    console.warn("Cannot delete mirrored nodes: Nodes map not found");
    return false;
  }

  let success = true;

  mirroredNodeIds.forEach((nodeId) => {
    const node = yNodes.get(nodeId) as Y.Map<any> | undefined;
    if (!node) {
      console.warn(`Can't find node ${nodeId} for deletion`);
      success = false;
      return;
    }

    // Remove all children with the same parentId
    for (const child of yNodes.values()) {
      if (child.get("parentId") === nodeId) {
        yNodes.delete(child.get("id"));
      }
    }

    const json = node.toJSON();
    yNodes.delete(nodeId);
    if (json.parentId) {
      yNodes.delete(json.parentId);
    }
  });

  return success;
}

/**
 * Deletes SLD-specific nodes from the SLD YDoc
 * This function handles the deletion of nodes that are specific to the SLD
 */
export function deleteSLDNodes(yDoc: Y.Doc, nodeIds: string[]): boolean {
  const sldNodesMap = getSldNodesMap(yDoc);
  if (!sldNodesMap) {
    console.warn("Cannot delete SLD nodes: SLD nodes map not found");
    return false;
  }

  let success = true;

  nodeIds.forEach((nodeId) => {
    if (!sldNodesMap.has(nodeId)) {
      console.warn(`Can't find SLD node ${nodeId} for deletion`);
      success = false;
      return;
    }

    // Get the node data to find the dataId
    const nodeData = sldNodesMap.get(nodeId);
    const nodeDataMap = nodeData?.get("data");
    const dataId = nodeDataMap?.get("dataId");

    if (dataId) {
      // Delete from main data map
      const dataMap = yDoc.getMap("data");
      if (dataMap && dataMap.has(dataId)) {
        dataMap.delete(dataId);
      }
    }

    // Delete from SLD nodes map
    sldNodesMap.delete(nodeId);
  });

  return success;
}

/**
 * Deletes edges from the SLD YDoc
 * This function handles the deletion of edges in the SLD
 */
export function deleteSLDEdges(yDoc: Y.Doc, edgeIds: string[]): boolean {
  const sldEdgesMap = getSldEdgesMap(yDoc);
  if (!sldEdgesMap) {
    console.warn("Cannot delete SLD edges: SLD edges map not found");
    return false;
  }

  let success = true;

  edgeIds.forEach((edgeId) => {
    if (!sldEdgesMap.has(edgeId)) {
      console.warn(`Can't find SLD edge ${edgeId} for deletion`);
      success = false;
      return;
    }

    // Find ghost nodes attached to this edge before deleting it
    const ghostNodeIds: string[] = [];
    const sldNodesMap = getSldNodesMap(yDoc);
    if (sldNodesMap) {
      const edge = sldEdgesMap.get(edgeId);
      if (edge) {
        const edgeData = (edge as any).get("data");
        if (edgeData && typeof edgeData.get === "function") {
          const points = edgeData.get("points");
          if (points) {
            points.toArray().forEach((point: any) => {
              for (const [nodeId, node] of sldNodesMap.entries()) {
                const nodeData = (node as any).get("data");
                if (nodeData && typeof nodeData.get === "function") {
                  const sourceHandleId = nodeData.get("sourceHandleId");
                  if (sourceHandleId === point.id) {
                    ghostNodeIds.push(nodeId);
                  }
                }
              }
            });
          }
        }
      }
    }

    sldEdgesMap.delete(edgeId);
  });

  return success;
}
