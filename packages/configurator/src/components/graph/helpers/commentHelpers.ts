import { objectToYMap } from "@/lib/yjsUtils";
import { Node } from "@xyflow/react";
import * as Y from "yjs";
import { getSldNodesMap } from "../Graph";

/**
 * Creates a comment node in the SLD YDoc with proper Y.Map structure
 * @param yDoc - The Yjs document
 * @param node - The comment node to store
 * @returns void
 */
export function createCommentDataFromNode(yDoc: Y.Doc, node: Node<any>): void {
  const sldNodesMap = getSldNodesMap(yDoc);
  if (!sldNodesMap) {
    console.warn("SLD nodes map not found, cannot store comment node");
    return;
  }

  const nodeMap = new Y.Map();
  nodeMap.set("id", node.id);
  nodeMap.set("type", node.type);
  nodeMap.set("position", objectToYMap(node.position));
  nodeMap.set("data", objectToYMap(node.data));
  nodeMap.set("width", node.width || 16);
  nodeMap.set("height", node.height || 16);
  nodeMap.set("selected", node.selected || false);
  nodeMap.set("selectable", node.selectable !== false);
  nodeMap.set("draggable", node.draggable !== false);
  if (node.zIndex) {
    nodeMap.set("zIndex", node.zIndex);
  }

  sldNodesMap.set(node.id, nodeMap);
}

/**
 * Restores a comment node from Y.Map structure
 * @param id - The node ID
 * @param node - The Y.Map containing the node data
 * @returns The restored comment node
 */
export function restoreCommentNodeFromYMap(
  id: string,
  node: Y.Map<any>,
): Node<any> {
  const nodeData = node.get("data");
  const position = node.get("position");

  // Convert Y.Map objects back to plain objects
  const plainData =
    nodeData && typeof nodeData.toJSON === "function"
      ? nodeData.toJSON()
      : nodeData || {};
  const plainPosition =
    position && typeof position.toJSON === "function"
      ? position.toJSON()
      : position || { x: 0, y: 0 };

  return {
    id,
    type: "comment",
    position: plainPosition,
    data: plainData,
    width: node.get("width") || 16,
    height: node.get("height") || 16,
    selected: node.get("selected") || false,
    selectable: node.get("selectable") !== false,
    draggable: node.get("draggable") !== false,
    zIndex: node.get("zIndex") || 100,
  };
}
