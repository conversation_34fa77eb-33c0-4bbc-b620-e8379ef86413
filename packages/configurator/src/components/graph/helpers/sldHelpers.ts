import { randomId } from "@somo/shared";
import { Node } from "@xyflow/react";
import * as Y from "yjs";
import { getNodeTypeFromDataType } from "../../data/createData";
import { DataType, getAllDataTypes } from "../../data/Data";
import { getData } from "../../data/useData";
import {
  MAIN_GRAPH_TO_SLD_MAPPING,
  SLD_COMPONENTS,
} from "../config/sldComponentConfig";

/**
 * Maps data types to their corresponding SLD node types
 * Uses the existing NODES_TO_DATA_DEFAULTS mapping to find the right component
 */
function getDataTypeToSldType(dataType: DataType): string | null {
  // Get the node type from the existing mapping (e.g., "ground" -> "groundSLD")
  const nodeType = getNodeTypeFromDataType(dataType);
  if (!nodeType) {
    return null;
  }

  // Check if it's a direct SLD component
  if (SLD_COMPONENTS[nodeType]) {
    return nodeType; // Use the key directly as the ReactFlow type
  }

  // Check if it's in the main graph to SLD mapping
  if (MAIN_GRAPH_TO_SLD_MAPPING[nodeType]) {
    // Find the corresponding SLD component key
    for (const [sldKey, component] of Object.entries(SLD_COMPONENTS)) {
      if (component === MAIN_GRAPH_TO_SLD_MAPPING[nodeType]) {
        return sldKey;
      }
    }
  }

  return null;
}

/**
 * Creates an SLD node from shared data stored in the YDoc
 * @param yDoc - The YDoc containing the shared data
 * @param dataId - The ID of the data entry to create a node from
 * @param position - The position where the node should be placed
 * @returns A React Flow node for the SLD editor, or null if data not found
 */
export function createSLDNodeFromSharedData(
  yDoc: Y.Doc,
  dataId: string,
  position: { x: number; y: number },
): Node<{ dataId: string }> | null {
  // Try each validated type to find which one matches this data ID
  const allDataTypes = getAllDataTypes();

  for (const type of allDataTypes) {
    const data = getData(yDoc, dataId, type);
    if (data) {
      // We found the data! It's validated for this type
      return createSLDNodeFromTypedData(dataId, type, position);
    }
  }

  console.warn(
    `Data with ID ${dataId} not found or not supported in SLD editor`,
  );
  return null;
}

/**
 * Helper function that creates an SLD node with a dataId reference
 * The data is known to be valid for the given dataType since getDataMap succeeded
 */
export function createSLDNodeFromTypedData<T extends DataType>(
  dataId: string,
  dataType: T,
  position: { x: number; y: number },
): Node<{ dataId: string }> | null {
  // Map data type to SLD node type
  const sldNodeType = getDataTypeToSldType(dataType);
  if (!sldNodeType) {
    console.warn(
      `Data type '${dataType}' is not supported in SLD editor. Skipping node creation for data ID: ${dataType}`,
    );
    return null;
  }

  // Create the SLD node with dataId reference (following the same pattern as container nodes)
  const sldNode: Node<{ dataId: string }> = {
    id: randomId(),
    type: sldNodeType,
    position,
    data: {
      dataId: dataId,
    },
  };

  return sldNode;
}
