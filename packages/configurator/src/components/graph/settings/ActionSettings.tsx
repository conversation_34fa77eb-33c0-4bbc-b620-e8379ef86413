import { getData } from "@/components/data/useData";
import { DeviceWithSectionNode } from "@/components/devices/useConnectableDevices";
import { DeviceIcons } from "@/components/icons/DeviceIcons";
import { Button } from "@/components/ui/button";
import {
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Label } from "@/components/ui/label";
import { MultiSelect } from "@/components/ui/multi-select";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { TooltipDropdownMenu } from "@/components/ui/tooltip-dropdown-menu";
import { diff } from "@/lib/array";
import { clamp } from "@/lib/math";
import {
  DeviceControlSettings,
  FanControlSettings,
  filterNodes,
  GraphNode,
  SceneInput,
  ShadesControlSettings,
  SomoFanVia,
  ThermostatControlSettings,
  ThermostatFanSpeed,
  ThermostatMode,
} from "@somo/shared";
import {
  CircleAlertIcon,
  PlusIcon,
  SearchIcon,
  ThermometerIcon,
  Trash2Icon,
} from "lucide-react";
import { match } from "ts-pattern";
import { useReactFlowContext } from "../ReactFlowContext";
import {
  addInput,
  areInputsEqual,
  removeInput,
  wouldBeCircular,
} from "../scene/sceneOperations";
import { SettingsLabel, SettingsRow } from "./SettingsGroup";
import { SettingsInput } from "./SettingsInput";

type BaseProps = {
  node: GraphNode;
  label: string;
  sourceHandle: string;
  devices: DeviceWithSectionNode[];
  missingDeviceText?: string;
  onAddAction: (deviceId: string) => void;
  onDelete: (id: string) => void;
  onDeviceChange: (id: string, value: string) => void;
};
type Props = BaseProps &
  (
    | {
        actions: DeviceControlSettings[];
        type: "targetValue";
        onDelayChange: (id: string, value: number) => void;
        onDimSpeedChange: (id: string, value: number) => void;
        onTargetValueChange: (id: string, value: number) => void;
      }
    | {
        actions: DeviceControlSettings[];
        type: "onOff";
        onDelayChange: (id: string, value: number) => void;
        onDimSpeedChange: (id: string, value: number) => void;
        onOnValueChange: (id: string, value: number) => void;
        onOffValueChange: (id: string, value: number) => void;
      }
    | {
        actions: ThermostatControlSettings[];
        type: "thermostat";
        onSetPointChange: (id: string, value: number) => void;
        onModeChange: (id: string, value: ThermostatMode) => void;
        onFanSpeedChange: (id: string, value: ThermostatFanSpeed) => void;
      }
    | {
        actions: (ShadesControlSettings | FanControlSettings)[];
        type: "nothing";
      }
  );
export function ActionSettingsWithHeader(props: Props) {
  const { readOnly, nodes, yDoc } = useReactFlowContext();

  const { id, type, data } = props.node;
  const firstUnusedDeviceId = props.devices.find((d) => !d.isUsed)?.id;

  const scenes = filterNodes(nodes, "scene")
    .filter((scene) => id !== scene.id)
    .map((scene) => {
      const sceneData = getData(yDoc, scene.data.dataId, "scene");
      return {
        value: scene.data.dataId,
        label: sceneData?.title || "Scene",
        data: scene.data,
        inputs: sceneData?.inputs,
        disabled:
          type === "scene" &&
          wouldBeCircular(yDoc, scene.data.dataId, data.dataId),
      };
    });

  const sceneInput = {
    // @ts-expect-error - Should be removed once all nodes have been migrated
    source: data.dataId,
    sourceHandle: props.sourceHandle,
  } satisfies SceneInput;
  const selectedScenes = scenes
    .filter(({ inputs }) => inputs?.find((i) => areInputsEqual(i, sceneInput)))
    .map((scene) => scene.value);

  return (
    <div className="space-y-4 -mb-2">
      <SettingsRow className="flex flex-row justify-between items-center -mb-2">
        <SettingsLabel>{props.label}</SettingsLabel>

        {!readOnly && (
          <Tooltip>
            <TooltipTrigger asChild>
              <TooltipDropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="size-6 data-[state=open]:bg-gray-100"
                  >
                    <PlusIcon className="size-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="mr-5">
                  <DropdownMenuLabel>Add action</DropdownMenuLabel>
                  {firstUnusedDeviceId ? (
                    <DropdownMenuItem
                      onClick={() => props.onAddAction(firstUnusedDeviceId)}
                    >
                      Add action
                    </DropdownMenuItem>
                  ) : (
                    <DropdownMenuItem disabled>
                      <div className="flex items-center text-red-600">
                        <CircleAlertIcon className="mr-2 size-4" />
                        {props.missingDeviceText ?? "Create a device first"}
                      </div>
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </TooltipDropdownMenu>
            </TooltipTrigger>
            <TooltipContent>Add action</TooltipContent>
          </Tooltip>
        )}
      </SettingsRow>

      {scenes.length > 0 && (
        <MultiSelect
          className="w-full"
          placeholder="Select scenes"
          closeOnSelect
          hideSelectAll
          autoSize={false}
          variant="secondary"
          disabled={readOnly}
          maxCount={5}
          searchable={scenes.length > 5}
          options={scenes}
          defaultValue={selectedScenes}
          onValueChange={(newSelectedScenes) => {
            const { added, removed } = diff({
              before: selectedScenes,
              after: newSelectedScenes,
            });

            removed.forEach((sceneId) => {
              removeInput({
                yDoc,
                sceneId,
                input: sceneInput,
              });
            });
            added.forEach((sceneId) => {
              addInput({
                yDoc,
                sceneId,
                input: sceneInput,
              });
            });
          }}
          emptyIndicator={
            <div className="flex flex-col items-center p-4">
              <SearchIcon className="h-8 w-8 text-muted-foreground mb-2" />
              <p className="text-muted-foreground">No scenes found</p>
              <p className="text-xs text-muted-foreground">
                Try a different search term
              </p>
            </div>
          }
        />
      )}

      {props.actions.length === 0 && (
        <div className="text-gray-500 text-xs">No actions defined.</div>
      )}

      <SettingsRow>
        {match(props)
          .with({ type: "nothing" }, ({ actions }) =>
            actions.map((action) => (
              <div className="mx-3" key={action.id}>
                <ActionSettings
                  type="nothing"
                  action={action}
                  devices={props.devices}
                  onDelete={() => props.onDelete(action.id)}
                  onDeviceChange={(v) => props.onDeviceChange(action.id, v)}
                />
              </div>
            )),
          )
          .with(
            { type: "thermostat" },
            ({ actions, onSetPointChange, onModeChange, onFanSpeedChange }) =>
              actions.map((action) => (
                <div className="mx-3" key={action.id}>
                  <ActionSettings
                    type="thermostat"
                    action={action}
                    devices={props.devices}
                    onDelete={() => props.onDelete(action.id)}
                    onDeviceChange={(v) => props.onDeviceChange(action.id, v)}
                    onSetPointChange={(v) => onSetPointChange(action.id, v)}
                    onModeChange={(v) => onModeChange(action.id, v)}
                    onFanSpeedChange={(v) => onFanSpeedChange(action.id, v)}
                  />
                </div>
              )),
          )
          .with(
            { type: "targetValue" },
            ({
              actions,
              onDelayChange,
              onDimSpeedChange,
              onTargetValueChange,
            }) =>
              actions.map((action) => (
                <div className="mx-3" key={action.id}>
                  <ActionSettings
                    type="targetValue"
                    action={action}
                    devices={props.devices}
                    onDelete={() => props.onDelete(action.id)}
                    onDeviceChange={(v) => props.onDeviceChange(action.id, v)}
                    onDelayChange={(v) => onDelayChange(action.id, v)}
                    onDimSpeedChange={(v) => onDimSpeedChange(action.id, v)}
                    onTargetValueChange={(v) =>
                      onTargetValueChange(action.id, v)
                    }
                  />
                </div>
              )),
          )
          .with(
            { type: "onOff" },
            ({
              actions,
              onDelayChange,
              onDimSpeedChange,
              onOnValueChange,
              onOffValueChange,
            }) =>
              actions.map((action) => (
                <div className="mx-3" key={action.id}>
                  <ActionSettings
                    type="onOff"
                    action={action}
                    devices={props.devices}
                    onDelete={() => props.onDelete(action.id)}
                    onDeviceChange={(v) => props.onDeviceChange(action.id, v)}
                    onDelayChange={(v) => onDelayChange(action.id, v)}
                    onDimSpeedChange={(v) => onDimSpeedChange(action.id, v)}
                    onOnValueChange={(v) => onOnValueChange(action.id, v)}
                    onOffValueChange={(v) => onOffValueChange(action.id, v)}
                  />
                </div>
              )),
          )
          .exhaustive()}
      </SettingsRow>
    </div>
  );
}

type ActionSettingsProps = {
  devices: DeviceWithSectionNode[];
  onDelete: () => void;
  onDeviceChange: (value: string) => void;
} & (
  | {
      action: DeviceControlSettings;
      type: "targetValue";
      onDelayChange: (value: number) => void;
      onDimSpeedChange: (value: number) => void;
      onTargetValueChange: (value: number) => void;
    }
  | {
      action: DeviceControlSettings;
      type: "onOff";
      onDelayChange: (value: number) => void;
      onDimSpeedChange: (value: number) => void;
      onOnValueChange: (value: number) => void;
      onOffValueChange: (value: number) => void;
    }
  | {
      action: ThermostatControlSettings;
      type: "thermostat";
      onSetPointChange: (value: number) => void;
      onModeChange: (value: ThermostatMode) => void;
      onFanSpeedChange: (value: ThermostatFanSpeed) => void;
    }
  | {
      action: ShadesControlSettings | FanControlSettings;
      type: "nothing";
    }
);

export function ActionSettings(props: ActionSettingsProps) {
  const { nodes, readOnly, yDoc } = useReactFlowContext();

  return (
    <SettingsRow className="flex flex-col justify-between items-center gap-2 -mx-3">
      <div className="flex flex-row items-center gap-2 w-full">
        <Select
          onValueChange={props.onDeviceChange}
          value={props.action.deviceId}
          disabled={readOnly}
        >
          <SelectTrigger className="h-10 min-h-10 w-full">
            <SelectValue
              placeholder="Select a device"
              className="w-full h-10 min-h-10"
            />
          </SelectTrigger>
          <SelectContent className="w-full">
            {props.devices.map((device) =>
              match(device)
                .with({ type: "roomSwitch" }, (device) => (
                  <RoomSwitchSelectItem key={device.id} device={device} />
                ))
                .with({ type: "roomDimmer" }, (device) => (
                  <RoomDimmerSelectItem key={device.id} device={device} />
                ))
                .with({ type: "somoShades" }, (device) => (
                  <SomoShadesSelectItem key={device.id} device={device} />
                ))
                .with({ type: "somfyShades" }, (device) => (
                  <SomfyShadesSelectItem key={device.id} device={device} />
                ))
                .with({ type: "somoFan" }, (device) => (
                  <SomoFanSelectItem key={device.id} device={device} />
                ))
                .with({ type: "somoThermostat" }, (device) => (
                  <SomoThermostatSelectItem key={device.id} device={device} />
                ))
                .with({ type: "somoIrController" }, (device) => (
                  <SomoIrControllerSelectItem key={device.id} device={device} />
                ))
                .with({ type: "zeroToTenVoltDimmer" }, (device) => (
                  <ZeroToTenVoltDimmerSelectItem
                    key={device.id}
                    device={device}
                  />
                ))
                .with({ type: "relayOutput" }, (device) => (
                  <RelayOutputSelectItem key={device.id} device={device} />
                ))
                // Trying to perform an exhaustive check results in a type error:
                // `Type instantiation is excessively deep and possibly infinite`
                // => Use `otherwise` instead.
                .otherwise(() => null),
            )}
          </SelectContent>
        </Select>
        {!readOnly && <DeleteButton onDelete={props.onDelete} />}
      </div>

      {match(props)
        .with({ type: "nothing" }, () => (
          <div className="flex flex-row items-center gap-2 w-full flex-wrap">
            {/* No additional settings for shades and fan control actions */}
          </div>
        ))
        .with(
          { type: "targetValue" },
          ({
            action,
            onDelayChange,
            onDimSpeedChange,
            onTargetValueChange,
          }) => (
            <div className="flex flex-row items-center gap-2 w-full flex-wrap">
              <SettingsInput
                label="Dim Speed (sec)"
                value={`${action.dimSpeed ?? 0}`}
                disabled={readOnly}
                className="w-auto"
                inputClassName="w-[50px] text-center"
                onEndEdit={(value) => {
                  const dimSpeed = parseFloat(value);
                  if (isNaN(dimSpeed)) {
                    return;
                  }
                  onDimSpeedChange(dimSpeed);
                }}
              />
              <SettingsInput
                label="Target Value"
                value={`${action.targetValue ?? 0}`}
                disabled={readOnly}
                className="w-auto"
                inputClassName="w-[50px] text-center"
                onEndEdit={(value) => {
                  const targetValue = parseFloat(value);
                  if (isNaN(targetValue)) {
                    return;
                  }
                  onTargetValueChange(targetValue);
                }}
              />
              <SettingsInput
                label="Delay (ms)"
                value={`${action.delay ?? 0}`}
                disabled={readOnly}
                className="w-auto"
                inputClassName="w-[80px] text-center"
                onEndEdit={(value) => {
                  let numValue = parseInt(value, 10);
                  if (isNaN(numValue)) {
                    numValue = 0;
                  }
                  const clampedValue = clamp(numValue, {
                    min: 0,
                    max: 1_800_000,
                  });
                  onDelayChange(clampedValue);
                }}
              />
            </div>
          ),
        )
        .with(
          { type: "onOff" },
          ({
            action,
            onDelayChange,
            onDimSpeedChange,
            onOnValueChange,
            onOffValueChange,
          }) => (
            <div className="flex flex-row items-center gap-2 w-full flex-wrap">
              <SettingsInput
                label="Dim Speed (sec)"
                value={`${action.dimSpeed ?? 0}`}
                disabled={readOnly}
                className="w-auto"
                inputClassName="w-[50px] text-center"
                onEndEdit={(value) => {
                  const dimSpeed = parseFloat(value);
                  if (isNaN(dimSpeed)) {
                    return;
                  }
                  onDimSpeedChange(dimSpeed);
                }}
              />
              <SettingsInput
                label="On Value"
                value={`${action.onValue ?? 0}`}
                disabled={readOnly}
                className="w-auto"
                inputClassName="w-[50px] text-center"
                onEndEdit={(value) => {
                  const onValue = parseFloat(value);
                  if (isNaN(onValue)) {
                    return;
                  }
                  onOnValueChange(onValue);
                }}
              />
              <SettingsInput
                label="Off Value"
                value={`${action.offValue ?? 0}`}
                disabled={readOnly}
                className="w-auto"
                inputClassName="w-[50px] text-center"
                onEndEdit={(value) => {
                  const offValue = parseFloat(value);
                  if (isNaN(offValue)) {
                    return;
                  }
                  onOffValueChange(offValue);
                }}
              />
              <SettingsInput
                label="Delay (ms)"
                value={`${action.delay ?? 0}`}
                disabled={readOnly}
                className="w-auto"
                inputClassName="w-[80px] text-center"
                onEndEdit={(value) => {
                  let numValue = parseInt(value, 10);
                  if (isNaN(numValue)) {
                    numValue = 0;
                  }
                  const clampedValue = clamp(numValue, {
                    min: 0,
                    max: 1_800_000,
                  });
                  onDelayChange(clampedValue);
                }}
              />
            </div>
          ),
        )
        .with(
          { type: "thermostat" },
          ({ action, onSetPointChange, onModeChange, onFanSpeedChange }) => {
            const selectedDevice = props.devices.find(
              (device) => device.id === action.deviceId,
            );
            const thermostat = match(selectedDevice)
              .with({ type: "somoThermostat" }, ({ nodeId }) =>
                filterNodes(nodes, "somoThermostatContainer").find(
                  (n) => n.id === nodeId,
                ),
              )
              .otherwise(() => null);
            const irController = match(selectedDevice)
              .with({ type: "somoIrController" }, ({ nodeId }) =>
                filterNodes(nodes, "somoIrControllerContainer").find(
                  (n) => n.id === nodeId,
                ),
              )
              .otherwise(() => null);

            const thermostatData =
              thermostat &&
              getData(yDoc, thermostat.data.dataId, "somoThermostat");

            const irControllerData =
              irController &&
              getData(yDoc, irController.data.dataId, "somoIrController");

            const temperatureUnit =
              thermostatData?.temperatureUnit ??
              irControllerData?.temperatureUnit ??
              "C";
            const minTemp =
              thermostatData?.minTemp ?? irControllerData?.minTemp ?? 5;
            const maxTemp =
              thermostatData?.maxTemp ?? irControllerData?.maxTemp ?? 35;
            const allowedModes = thermostatData?.allowedModes ??
              irControllerData?.allowedModes ?? ["heat", "cool", "fan", "auto"];
            const allowedFanSpeeds = thermostatData?.allowedFanSpeeds ??
              irControllerData?.allowedFanSpeeds ?? [
                "low",
                "medium",
                "high",
                "auto",
              ];

            return (
              <div className="flex flex-row items-center gap-2 w-full flex-wrap">
                <SettingsInput
                  label={`Setpoint (°${temperatureUnit})`}
                  value={String(action.setpoint)}
                  className="w-auto"
                  inputClassName="w-[50px] text-center"
                  disabled={readOnly}
                  onEndEdit={(value) => {
                    const setpoint = parseFloat(value);
                    if (isNaN(setpoint)) {
                      return;
                    }

                    const clampedSetpoint = clamp(setpoint, {
                      min: minTemp ?? 5,
                      max: maxTemp ?? 35,
                    });
                    onSetPointChange(clampedSetpoint);
                  }}
                />

                <div className="flex flex-row gap-2 items-center">
                  <Label
                    htmlFor="mode"
                    className="text-xs text-gray-500 flex-grow"
                  >
                    Mode
                  </Label>
                  <Select
                    value={action.mode}
                    onValueChange={(value) => {
                      const mode = ThermostatMode.safeParse(value);
                      if (!mode.success) {
                        return;
                      }

                      onModeChange(mode.data);
                    }}
                    disabled={readOnly}
                  >
                    <SelectTrigger id="mode" className="h-8 w-32">
                      <SelectValue placeholder="Select mode" />
                    </SelectTrigger>
                    <SelectContent>
                      {allowedModes.map((mode) => (
                        <SelectItem key={mode} value={mode}>
                          {mode.charAt(0).toUpperCase() + mode.slice(1)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex flex-row gap-2 items-center">
                  <Label
                    htmlFor="fanSpeed"
                    className="text-xs text-gray-500 flex-grow"
                  >
                    Fan Speed
                  </Label>
                  <Select
                    value={action.fanSpeed}
                    onValueChange={(value) => {
                      const fanSpeed = ThermostatFanSpeed.safeParse(value);
                      if (!fanSpeed.success) {
                        return;
                      }

                      onFanSpeedChange(fanSpeed.data);
                    }}
                    disabled={readOnly}
                  >
                    <SelectTrigger id="fanSpeed" className="h-8 w-32">
                      <SelectValue placeholder="Select fan speed" />
                    </SelectTrigger>
                    <SelectContent>
                      {allowedFanSpeeds.map((speed) => (
                        <SelectItem key={speed} value={speed}>
                          {speed.charAt(0).toUpperCase() + speed.slice(1)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            );
          },
        )
        .exhaustive()}
    </SettingsRow>
  );
}

function RoomSwitchSelectItem({
  device,
}: {
  device: Extract<DeviceWithSectionNode, { type: "roomSwitch" }>;
}) {
  const { nodes, yDoc } = useReactFlowContext();

  const switchNodes = filterNodes(nodes, "roomSwitchContainer");
  const lights = [];
  for (const switchNode of switchNodes) {
    const data = getData(yDoc, switchNode.data.dataId, "roomSwitch");
    const via1 = data?.via1;
    const via2 = data?.via2;
    const via3 = data?.via3;
    if (via1?.hasLoad) {
      lights.push({
        nodeId: switchNode.id,
        id: "via1",
        ...via1,
      });
    }
    if (via2?.hasLoad) {
      lights.push({
        nodeId: switchNode.id,
        id: "via2",
        ...via2,
      });
    }
    if (via3?.hasLoad) {
      lights.push({
        nodeId: switchNode.id,
        id: "via3",
        ...via3,
      });
    }
  }

  const light = lights.find(
    (light) => light.id === device.viaId && light.nodeId === device.nodeId,
  );
  if (!light) {
    return null;
  }
  const IconComponent = light.lightIcon ? DeviceIcons[light.lightIcon] : null;
  return (
    <SelectItem
      key={device.id}
      value={device.id}
      className="flex flex-row w-full items-center "
    >
      <div className="flex flex-row items-center gap-1">
        {IconComponent && <IconComponent className="size-4" />}
        <div className="flex flex-col w-full items-start gap-0 p-1 space-y-0 h-10">
          <div className="text-[8px] text-gray-500 truncate mt-0 ml-0 text-left -mb-1.5">
            {device.sectionNode.data.title}
          </div>
          <div className="truncate mb-0 text-[10px] font-semibold">
            {light.lightName}
          </div>
        </div>
      </div>
    </SelectItem>
  );
}

function RoomDimmerSelectItem({
  device,
}: {
  device: Extract<DeviceWithSectionNode, { type: "roomDimmer" }>;
}) {
  const { nodes, yDoc } = useReactFlowContext();

  const dimmerNodes = filterNodes(nodes, "roomDimmerContainer");
  const lights = dimmerNodes
    .map((dimmerNode) => {
      const data = getData(yDoc, dimmerNode.data.dataId, "roomDimmer");
      return data
        ? {
            nodeId: dimmerNode.id,
            ...data.via,
          }
        : null;
    })
    .filter((light) => light !== null);

  const light = lights.find(
    (light) => device.viaId === "via" && light.nodeId === device.nodeId,
  );
  if (!light) {
    return null;
  }
  const IconComponent = light.lightIcon ? DeviceIcons[light.lightIcon] : null;
  return (
    <SelectItem
      key={device.id}
      value={device.id}
      className="flex flex-row w-full items-center "
    >
      <div className="flex flex-row items-center gap-1">
        {IconComponent && <IconComponent className="size-4" />}
        <div className="flex flex-col w-full items-start gap-0 p-1 space-y-0 h-10">
          <div className="text-[8px] text-gray-500 truncate mt-0 ml-0 text-left -mb-1.5">
            {device.sectionNode.data.title}
          </div>
          <div className="truncate mb-0 text-[10px] font-semibold">
            {light.lightName}
          </div>
        </div>
      </div>
    </SelectItem>
  );
}

// function OutletDimmerSelectItem({
//   device,
// }: {
//   device: Extract<DeviceWithSectionNode, { type: "outletDimmer" }>;
// }) {
//   const { nodes, yDoc } = useReactFlowContext();

//   const dimmerNodes = filterNodes(nodes, "outletDimmerContainer");
//   const lights = dimmerNodes
//     .map((dimmerNode) => {
//       const data = getData(yDoc, dimmerNode.data.dataId, "outletDimmer");
//       return data
//         ? {
//             nodeId: dimmerNode.id,
//             ...data.via,
//           }
//         : null;
//     })
//     .filter((light) => light !== null);

//   const light = lights.find(
//     (light) => device.viaId === "via" && light.nodeId === device.nodeId,
//   );
//   if (!light) {
//     return null;
//   }
//   const IconComponent = light.lightIcon ? DeviceIcons[light.lightIcon] : null;
//   return (
//     <SelectItem
//       key={device.id}
//       value={device.id}
//       className="flex flex-row w-full items-center "
//     >
//       <div className="flex flex-row items-center gap-1">
//         {IconComponent && <IconComponent className="size-4" />}
//         <div className="flex flex-col w-full items-start gap-0 p-1 space-y-0 h-10">
//           <div className="text-[8px] text-gray-500 truncate mt-0 ml-0 text-left -mb-1.5">
//             {device.sectionNode.data.title}
//           </div>
//           <div className="truncate mb-0 text-[10px] font-semibold">
//             {light.lightName}
//           </div>
//         </div>
//       </div>
//     </SelectItem>
//   );
// }

function SomoShadesSelectItem({
  device,
}: {
  device: Extract<DeviceWithSectionNode, { type: "somoShades" }>;
}) {
  const { yDoc, nodes } = useReactFlowContext();

  const shadesNodes = filterNodes(nodes, "somoShadesContainer");
  const allShades = shadesNodes
    .map((shadesNode) => {
      const data = getData(yDoc, shadesNode.data.dataId, "somoShades");
      return data
        ? {
            nodeId: shadesNode.id,
            ...data.via,
          }
        : null;
    })
    .filter((shade) => shade !== null);

  const shades = allShades.find(
    (light) => device.viaId === "via" && light.nodeId === device.nodeId,
  );
  if (!shades) {
    return null;
  }
  const IconComponent = shades.loadIcon ? DeviceIcons[shades.loadIcon] : null;
  return (
    <SelectItem
      key={device.id}
      value={device.id}
      className="flex flex-row w-full items-center "
    >
      <div className="flex flex-row items-center gap-1">
        {IconComponent && <IconComponent className="size-4" />}
        <div className="flex flex-col w-full items-start gap-0 p-1 space-y-0 h-10">
          <div className="text-[8px] text-gray-500 truncate mt-0 ml-0 text-left -mb-1.5">
            {device.sectionNode.data.title}
          </div>
          <div className="truncate mb-0 text-[10px] font-semibold">
            {shades.loadName}
          </div>
        </div>
      </div>
    </SelectItem>
  );
}

function SomfyShadesSelectItem({
  device,
}: {
  device: Extract<DeviceWithSectionNode, { type: "somfyShades" }>;
}) {
  const { yDoc, nodes } = useReactFlowContext();

  const baseStationNodes = filterNodes(nodes, "baseStationContainer");
  const allShades = [];
  for (const baseStationNode of baseStationNodes) {
    const data = getData(yDoc, baseStationNode.data.dataId, "baseStation");
    const somfyShades = data?.somfyShades || {};
    for (const [shadeId, shade] of Object.entries(somfyShades)) {
      allShades.push({
        nodeId: baseStationNode.id,
        ...shade,
        id: shadeId,
      });
    }
  }

  const shades = allShades.find(
    (shade) => device.viaId === shade.id && device.nodeId === shade.nodeId,
  );
  if (!shades) {
    return null;
  }
  const IconComponent = shades.icon ? DeviceIcons[shades.icon] : null;
  return (
    <SelectItem
      key={device.id}
      value={device.id}
      className="flex flex-row w-full items-center "
    >
      <div className="flex flex-row items-center gap-1">
        {IconComponent && <IconComponent className="size-4" />}
        <div className="flex flex-col w-full items-start gap-0 p-1 space-y-0 h-10">
          <div className="text-[8px] text-gray-500 truncate mt-0 ml-0 text-left -mb-1.5">
            {device.sectionNode.data.title}
          </div>
          <div className="truncate mb-0 text-[10px] font-semibold">
            {shades.name}
          </div>
        </div>
      </div>
    </SelectItem>
  );
}

function SomoFanSelectItem({
  device,
}: {
  device: Extract<DeviceWithSectionNode, { type: "somoFan" }>;
}) {
  const { yDoc, nodes } = useReactFlowContext();

  const fanNodes = filterNodes(nodes, "somoFanContainer");
  const allFan: Array<SomoFanVia & { nodeId: string }> = [];
  for (const fanNode of fanNodes) {
    const data = getData(yDoc, fanNode.data.dataId, "somoFan");
    if (data) {
      allFan.push({
        nodeId: fanNode.id,
        ...data.via,
      });
    }
  }

  const fan = allFan.find(
    (light) => device.viaId === "via" && light.nodeId === device.nodeId,
  );
  if (!fan) {
    return null;
  }
  const IconComponent = fan.loadIcon ? DeviceIcons[fan.loadIcon] : null;
  return (
    <SelectItem
      key={device.id}
      value={device.id}
      className="flex flex-row w-full items-center "
    >
      <div className="flex flex-row items-center gap-1">
        {IconComponent && <IconComponent className="size-4" />}
        <div className="flex flex-col w-full items-start gap-0 p-1 space-y-0 h-10">
          <div className="text-[8px] text-gray-500 truncate mt-0 ml-0 text-left -mb-1.5">
            {device.sectionNode.data.title}
          </div>
          <div className="truncate mb-0 text-[10px] font-semibold">
            {fan.loadName}
          </div>
        </div>
      </div>
    </SelectItem>
  );
}

function SomoThermostatSelectItem({
  device,
}: {
  device: Extract<DeviceWithSectionNode, { type: "somoThermostat" }>;
}) {
  const { yDoc, nodes } = useReactFlowContext();

  const containerNode = filterNodes(nodes, "somoThermostatContainer").find(
    (node) => node.id === device.nodeId,
  );

  if (!containerNode) {
    return null;
  }

  const data = getData(yDoc, containerNode?.data.dataId, "somoThermostat");
  if (!data) {
    return null;
  }

  const displayName = data.via.hvacName || "Thermostat";
  const iconKey = data.via.hvacIcon;
  const IconComponent = iconKey ? DeviceIcons[iconKey] : ThermometerIcon;

  return (
    <SelectItem
      key={device.id}
      value={device.id}
      className="flex flex-row w-full items-center"
    >
      <div className="flex flex-row items-center gap-1">
        <IconComponent className="size-4" />
        <div className="flex flex-col w-full items-start gap-0 p-1 space-y-0 h-10">
          <div className="text-[8px] text-gray-500 truncate mt-0 ml-0 text-left -mb-1.5">
            {device.sectionNode.data.title}
          </div>
          <div className="truncate mb-0 text-[10px] font-semibold">
            {displayName}
          </div>
        </div>
      </div>
    </SelectItem>
  );
}

function SomoIrControllerSelectItem({
  device,
}: {
  device: Extract<DeviceWithSectionNode, { type: "somoIrController" }>;
}) {
  const { yDoc, nodes } = useReactFlowContext();

  const containerNode = filterNodes(nodes, "somoIrControllerContainer").find(
    (node) => node.id === device.nodeId,
  );
  if (!containerNode) {
    return null;
  }

  const data = getData(yDoc, containerNode?.data.dataId, "somoIrController");
  if (!data) {
    return null;
  }

  const displayName = data.via.hvacName || "IR Controller";
  const iconKey = data.via.hvacIcon;
  const IconComponent = iconKey ? DeviceIcons[iconKey] : ThermometerIcon;

  return (
    <SelectItem
      key={device.id}
      value={device.id}
      className="flex flex-row w-full items-center"
    >
      <div className="flex flex-row items-center gap-1">
        <IconComponent className="size-4" />
        <div className="flex flex-col w-full items-start gap-0 p-1 space-y-0 h-10">
          <div className="text-[8px] text-gray-500 truncate mt-0 ml-0 text-left -mb-1.5">
            {device.sectionNode.data.title}
          </div>
          <div className="truncate mb-0 text-[10px] font-semibold">
            {displayName}
          </div>
        </div>
      </div>
    </SelectItem>
  );
}

function ZeroToTenVoltDimmerSelectItem({
  device,
}: {
  device: Extract<DeviceWithSectionNode, { type: "zeroToTenVoltDimmer" }>;
}) {
  const { yDoc, nodes } = useReactFlowContext();

  const canBusControllerNodes = filterNodes(nodes, "canbusControllerContainer");
  const allDimmers = [];
  for (const canBusControllerNode of canBusControllerNodes) {
    const data = getData(
      yDoc,
      canBusControllerNode.data.dataId,
      "canbusController",
    );
    const dimmers = Object.values(data?.zeroToTenVoltDimmers || {});
    for (const dimmer of dimmers) {
      allDimmers.push({
        nodeId: canBusControllerNode.id,
        ...dimmer,
      });
    }
  }

  const dimmer = allDimmers.find(
    (d) => d.id === device.viaId && d.nodeId === device.nodeId,
  );
  if (!dimmer) {
    return null;
  }
  const IconComponent = dimmer.icon ? DeviceIcons[dimmer.icon] : null;
  return (
    <SelectItem
      key={device.id}
      value={device.id}
      className="flex flex-row w-full items-center "
    >
      <div className="flex flex-row items-center gap-1">
        {IconComponent && <IconComponent className="size-4" />}
        <div className="flex flex-col w-full items-start gap-0 p-1 space-y-0 h-10">
          <div className="text-[8px] text-gray-500 truncate mt-0 ml-0 text-left -mb-1.5">
            {device.sectionNode.data.title}
          </div>
          <div className="truncate mb-0 text-[10px] font-semibold">
            {dimmer.name}
          </div>
        </div>
      </div>
    </SelectItem>
  );
}

function RelayOutputSelectItem({
  device,
}: {
  device: Extract<DeviceWithSectionNode, { type: "relayOutput" }>;
}) {
  const { nodes, yDoc } = useReactFlowContext();

  // Find the actual relay output from the CanBus controller to get current settings
  const canBusNodes = filterNodes(nodes, "canbusControllerContainer");
  const relayOutput = canBusNodes
    .flatMap((node) => {
      const data = getData(yDoc, node.data.dataId, "canbusController");
      return Object.values(data?.relayOutputs || {}).map((output) => ({
        ...output,
        nodeId: node.id,
      }));
    })
    .find(
      (output) => device.viaId === output.id && output.nodeId === device.nodeId,
    );

  if (!relayOutput) {
    return null;
  }

  const IconComponent = relayOutput.icon ? DeviceIcons[relayOutput.icon] : null;

  return (
    <SelectItem
      key={device.id}
      value={device.id}
      className="flex flex-row w-full items-center "
    >
      <div className="flex flex-row items-center gap-1">
        {IconComponent && <IconComponent className="size-4" />}
        <div className="flex flex-col w-full items-start gap-0 p-1 space-y-0 h-10">
          <div className="text-[8px] text-gray-500 truncate mt-0 ml-0 text-left -mb-1.5">
            {device.sectionNode.data.title}
          </div>
          <div className="truncate mb-0 text-[10px] font-semibold">
            {relayOutput.name}
          </div>
        </div>
      </div>
    </SelectItem>
  );
}

function DeleteButton({ onDelete }: { onDelete: () => void }) {
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="size-10 flex-shrink-0"
          onClick={onDelete}
        >
          <Trash2Icon className="size-4" />
        </Button>
      </TooltipTrigger>
      <TooltipContent>Delete</TooltipContent>
    </Tooltip>
  );
}
