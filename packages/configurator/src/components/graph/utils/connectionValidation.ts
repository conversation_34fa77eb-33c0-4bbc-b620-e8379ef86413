// Connection validation rules for SLD components
export interface ConnectionRule {
  sourceType: string;
  sourceHandle?: string;
  targetType: string;
  targetHandle?: string;
  allowed: boolean;
  errorMessage: string;
}

// Define validation rules
const CONNECTION_RULES: ConnectionRule[] = [
  // Power connection rules
  {
    sourceType: "lineSLD",
    targetType: "groundSLD",
    allowed: false,
    errorMessage:
      "Cannot connect 120V Line to Ground - this would create a short circuit",
  },
  {
    sourceType: "lineSLD",
    targetType: "neutralSLD",
    allowed: false,
    errorMessage:
      "Cannot connect 120V Line to Neutral - this would create a short circuit",
  },
  {
    sourceType: "neutralSLD",
    targetType: "groundSLD",
    allowed: false,
    errorMessage:
      "Cannot connect Neutral to Ground - this would create a short circuit",
  },
  {
    sourceType: "groundSLD",
    targetType: "lineSLD",
    allowed: false,
    errorMessage:
      "Cannot connect Ground to 120V Line - this would create a short circuit",
  },
  {
    sourceType: "groundSLD",
    targetType: "neutralSLD",
    allowed: false,
    errorMessage:
      "Cannot connect Ground to Neutral - this would create a short circuit",
  },
  {
    sourceType: "neutralSLD",
    targetType: "lineSLD",
    allowed: false,
    errorMessage:
      "Cannot connect Neutral to 120V Line - this would create a short circuit",
  },
];

// Handle-specific rules
const HANDLE_RULES: ConnectionRule[] = [
  // RF Switch specific rules
  {
    sourceType: "lineSLD",
    targetType: "roomSwitchSLD",
    targetHandle: "ground",
    allowed: false,
    errorMessage: "Cannot connect 120V Line to Ground handle on RF Switch",
  },
  {
    sourceType: "lineSLD",
    targetType: "roomSwitchSLD",
    targetHandle: "neutral",
    allowed: false,
    errorMessage: "Cannot connect 120V Line to Neutral handle on RF Switch",
  },
  {
    sourceType: "groundSLD",
    targetType: "roomSwitchSLD",
    targetHandle: "live",
    allowed: false,
    errorMessage: "Cannot connect Ground to Line handle on RF Switch",
  },
  {
    sourceType: "neutralSLD",
    targetType: "roomSwitchSLD",
    targetHandle: "live",
    allowed: false,
    errorMessage: "Cannot connect Neutral to Line handle on RF Switch",
  },
];

export interface ValidationResult {
  isValid: boolean;
  errorMessage?: string;
}

// Helper function to derive electrical role from node type and handle
function deriveElectricalRole(
  nodeType: string,
  handle?: string,
): string | null {
  const handleName = (handle || "").toLowerCase();
  const nodeTypeName = nodeType;

  // Handle names that imply electrical roles across devices
  if (["line", "live", "line-connection"].includes(handleName)) {
    return "line";
  }
  if (["neutral", "neutral-connection"].includes(handleName)) {
    return "neutral";
  }
  if (["ground", "ground-input", "ground-output"].includes(handleName)) {
    return "ground";
  }

  // Node types that imply electrical roles
  if (nodeTypeName === "lineSLD") {
    return "line";
  }
  if (nodeTypeName === "neutralSLD") {
    return "neutral";
  }
  if (nodeTypeName === "groundSLD") {
    return "ground";
  }

  return null;
}

// Helper function to check if two roles form a dangerous electrical pairing
function isDangerousElectricalPairing(
  role1?: string | null,
  role2?: string | null,
  dangerousRole1?: string,
  dangerousRole2?: string,
): boolean {
  return (
    (role1 === dangerousRole1 && role2 === dangerousRole2) ||
    (role1 === dangerousRole2 && role2 === dangerousRole1)
  );
}

// Helper function to check if a connection matches a rule (forward or reverse)
function connectionMatchesRule(
  rule: ConnectionRule,
  sourceType: string,
  sourceHandle: string | undefined,
  targetType: string,
  targetHandle: string | undefined,
): boolean {
  return (
    sourceType === rule.sourceType &&
    targetType === rule.targetType &&
    (!rule.sourceHandle || sourceHandle === rule.sourceHandle) &&
    (!rule.targetHandle || targetHandle === rule.targetHandle)
  );
}

// Helper function to safely convert Yjs or plain arrays to array
function toArray(value: any): any[] {
  if (!value) {
    return [];
  }
  if (Array.isArray(value)) {
    return value;
  }
  if (typeof value.toArray === "function") {
    return value.toArray();
  }
  return [];
}

// Helper function to safely get edge properties from Yjs or plain objects
function getEdgeProperty(edge: any, propertyName: string): any {
  return edge.get?.(propertyName) ?? edge[propertyName];
}

// Helper function to check for duplicate connections
function hasDuplicateConnection(
  edgesMap: { entries: () => IterableIterator<[string, any]> },
  sourceId: string,
  sourceHandle: string,
  targetId: string,
  targetHandle: string,
  ignoreEdgeId?: string,
): boolean {
  for (const [edgeId, edge] of edgesMap.entries()) {
    if (ignoreEdgeId && edgeId === ignoreEdgeId) {
      continue;
    }

    const edgeSource = getEdgeProperty(edge, "source");
    const edgeSourceHandle = getEdgeProperty(edge, "sourceHandle") || "";
    const edgeTarget = getEdgeProperty(edge, "target");
    const edgeTargetHandle = getEdgeProperty(edge, "targetHandle") || "";

    const sameDirection =
      edgeSource === sourceId &&
      edgeSourceHandle === sourceHandle &&
      edgeTarget === targetId &&
      edgeTargetHandle === targetHandle;

    const oppositeDirection =
      edgeSource === targetId &&
      edgeSourceHandle === targetHandle &&
      edgeTarget === sourceId &&
      edgeTargetHandle === sourceHandle;

    if (sameDirection || oppositeDirection) {
      return true;
    }
  }
  return false;
}

// Helper function to check if a node is a ghost node
function isGhostNode(nodeId: string): boolean {
  return nodeId?.startsWith?.("ghost-") || false;
}

// Helper function to validate ghost node connections
function validateGhostNodeConnection(
  edgesMap: { entries: () => IterableIterator<[string, any]> },
  nodesMap: { get: (id: string) => any },
  sourceNode: { type?: string; id?: string } | undefined,
  sourceHandle: string,
  targetNode: { type?: string; id?: string } | undefined,
  targetHandle: string,
  ignoreEdgeId?: string,
): ValidationResult {
  if (!sourceNode?.type || !targetNode?.type) {
    return { isValid: true };
  }

  const isSourceGhost = isGhostNode(sourceNode.id!);
  const isTargetGhost = isGhostNode(targetNode.id!);

  if (!isSourceGhost && !isTargetGhost) {
    return { isValid: true };
  }

  const ghostId = isSourceGhost ? sourceNode.id! : targetNode.id!;
  const otherNodeId = isSourceGhost ? targetNode.id! : sourceNode.id!;
  const otherNodeHandle = isSourceGhost ? targetHandle : sourceHandle;

  // Check for duplicate handle connections to the same ghost node
  for (const [edgeId, edge] of edgesMap.entries()) {
    if (ignoreEdgeId && edgeId === ignoreEdgeId) {
      continue;
    }

    const edgeSource = getEdgeProperty(edge, "source");
    const edgeSourceHandle = getEdgeProperty(edge, "sourceHandle") || "";
    const edgeTarget = getEdgeProperty(edge, "target");
    const edgeTargetHandle = getEdgeProperty(edge, "targetHandle") || "";

    const connectsSamePairSameHandle =
      (edgeSource === ghostId &&
        edgeTarget === otherNodeId &&
        edgeTargetHandle === otherNodeHandle) ||
      (edgeTarget === ghostId &&
        edgeSource === otherNodeId &&
        edgeSourceHandle === otherNodeHandle);

    if (connectsSamePairSameHandle) {
      return {
        isValid: false,
        errorMessage:
          "This handle is already connected to that branching node.",
      };
    }
  }

  // Check electrical role compatibility with the ghost node's edge
  const ghostNode = nodesMap.get?.(ghostId) ?? null;
  const ghostData = ghostNode?.get?.("data") ?? ghostNode?.data;
  const ghostPointId =
    ghostData?.get?.("sourceHandleId") ?? ghostData?.sourceHandleId;

  if (ghostPointId) {
    for (const [_edgeId, edge] of edgesMap.entries()) {
      const edgeData = getEdgeProperty(edge, "data");
      const edgePoints = toArray(edgeData?.get?.("points") ?? edgeData?.points);
      const ownsGhostPoint = edgePoints.some(
        (point: any) => point?.id === ghostPointId,
      );

      if (!ownsGhostPoint) {
        continue;
      }

      // This edge owns the ghost node - determine its electrical role
      const edgeSource = getEdgeProperty(edge, "source");
      const edgeSourceHandle = getEdgeProperty(edge, "sourceHandle") || "";
      const edgeSourceNode = nodesMap.get?.(edgeSource) ?? null;
      const edgeSourceNodeType =
        edgeSourceNode?.get?.("type") ?? edgeSourceNode?.type;
      const edgeRole = deriveElectricalRole(
        edgeSourceNodeType,
        edgeSourceHandle,
      );

      // Determine the role of the new connection
      const newConnectionRole = deriveElectricalRole(
        isSourceGhost ? targetNode.type : sourceNode.type,
        isSourceGhost ? targetHandle : sourceHandle,
      );

      // Check for dangerous electrical pairings
      if (edgeRole && newConnectionRole) {
        if (
          isDangerousElectricalPairing(
            edgeRole,
            newConnectionRole,
            "line",
            "ground",
          )
        ) {
          console.log("Blocking line->ground connection to ghost node");
          return {
            isValid: false,
            errorMessage:
              "Cannot connect 120V Line to Ground - this would create a short circuit",
          };
        }
        if (
          isDangerousElectricalPairing(
            edgeRole,
            newConnectionRole,
            "line",
            "neutral",
          )
        ) {
          console.log("Blocking line->neutral connection to ghost node");
          return {
            isValid: false,
            errorMessage:
              "Cannot connect 120V Line to Neutral - this would create a short circuit",
          };
        }
        if (
          isDangerousElectricalPairing(
            edgeRole,
            newConnectionRole,
            "neutral",
            "ground",
          )
        ) {
          console.log("Blocking neutral->ground connection to ghost node");
          return {
            isValid: false,
            errorMessage:
              "Cannot connect Neutral to Ground - this would create a short circuit",
          };
        }
      }
    }
  }

  // Prevent connecting handle->ghost when that handle already participates in the owning edge
  if (ghostPointId) {
    for (const [_edgeId, edge] of edgesMap.entries()) {
      const edgeData = getEdgeProperty(edge, "data");
      const edgePoints = toArray(edgeData?.get?.("points") ?? edgeData?.points);
      const ownsGhostPoint = edgePoints.some(
        (point: any) => point?.id === ghostPointId,
      );

      if (!ownsGhostPoint) {
        continue;
      }

      const edgeSource = getEdgeProperty(edge, "source");
      const edgeSourceHandle = getEdgeProperty(edge, "sourceHandle") || "";
      const edgeTarget = getEdgeProperty(edge, "target");
      const edgeTargetHandle = getEdgeProperty(edge, "targetHandle") || "";

      const handleAlreadyOnOwningEdge =
        (edgeSource === otherNodeId && edgeSourceHandle === otherNodeHandle) ||
        (edgeTarget === otherNodeId && edgeTargetHandle === otherNodeHandle);

      if (handleAlreadyOnOwningEdge) {
        return {
          isValid: false,
          errorMessage:
            "This handle already connects to the branching edge; connect a different handle or remove the existing connection.",
        };
      }
    }
  }

  return { isValid: true };
}

export function validateConnection(
  sourceNode: { type?: string; id?: string } | undefined,
  targetNode: { type?: string; id?: string } | undefined,
  sourceHandle?: string,
  targetHandle?: string,
  validationContext?: {
    sldEdgesMap?: { entries: () => IterableIterator<[string, any]> } | null;
    sldNodesMap?: { get: (id: string) => any } | null;
    oldEdgeIdToIgnore?: string;
  },
): ValidationResult {
  // Early return if we can't determine node types
  if (!sourceNode?.type || !targetNode?.type) {
    return { isValid: true };
  }

  const edgesMap = validationContext?.sldEdgesMap ?? null;
  const nodesMap = validationContext?.sldNodesMap ?? null;
  const ignoreEdgeId = validationContext?.oldEdgeIdToIgnore;

  // 1. Check electrical role safety (source/target-agnostic)
  const sourceRole = deriveElectricalRole(sourceNode.type, sourceHandle);
  const targetRole = deriveElectricalRole(targetNode.type, targetHandle);

  // Disallow unsafe electrical role pairings regardless of direction
  if (isDangerousElectricalPairing(sourceRole, targetRole, "line", "ground")) {
    return {
      isValid: false,
      errorMessage:
        "Cannot connect 120V Line to Ground - this would create a short circuit",
    };
  }
  if (isDangerousElectricalPairing(sourceRole, targetRole, "line", "neutral")) {
    return {
      isValid: false,
      errorMessage:
        "Cannot connect 120V Line to Neutral - this would create a short circuit",
    };
  }
  if (
    isDangerousElectricalPairing(sourceRole, targetRole, "neutral", "ground")
  ) {
    return {
      isValid: false,
      errorMessage:
        "Cannot connect Neutral to Ground - this would create a short circuit",
    };
  }

  // 2. Check general connection rules (forward and reverse)
  for (const rule of CONNECTION_RULES) {
    const forwardMatch = connectionMatchesRule(
      rule,
      sourceNode.type,
      sourceHandle,
      targetNode.type,
      targetHandle,
    );

    const reverseMatch = connectionMatchesRule(
      rule,
      targetNode.type,
      targetHandle,
      sourceNode.type,
      sourceHandle,
    );

    if (forwardMatch || reverseMatch) {
      if (!rule.allowed) {
        return { isValid: false, errorMessage: rule.errorMessage };
      }
    }
  }

  // 3. Check handle-specific rules (forward and reverse)
  for (const rule of HANDLE_RULES) {
    const forwardMatch = connectionMatchesRule(
      rule,
      sourceNode.type,
      sourceHandle,
      targetNode.type,
      targetHandle,
    );

    const reverseMatch = connectionMatchesRule(
      rule,
      targetNode.type,
      targetHandle,
      sourceNode.type,
      sourceHandle,
    );

    if (forwardMatch || reverseMatch) {
      if (!rule.allowed) {
        return { isValid: false, errorMessage: rule.errorMessage };
      }
    }
  }

  // 4. Check for duplicate connections using graph context (when available)
  if (edgesMap && sourceNode.id && targetNode.id) {
    const hasDuplicate = hasDuplicateConnection(
      edgesMap,
      sourceNode.id,
      sourceHandle || "",
      targetNode.id,
      targetHandle || "",
      ignoreEdgeId,
    );

    if (hasDuplicate) {
      return {
        isValid: false,
        errorMessage: "A connection between these handles already exists.",
      };
    }
  }

  // 5. Validate ghost node connections (when available)
  if (edgesMap && nodesMap && sourceNode.id && targetNode.id) {
    const ghostValidation = validateGhostNodeConnection(
      edgesMap,
      nodesMap,
      sourceNode,
      sourceHandle || "",
      targetNode,
      targetHandle || "",
      ignoreEdgeId,
    );

    if (!ghostValidation.isValid) {
      return ghostValidation;
    }
  }

  return { isValid: true };
}
