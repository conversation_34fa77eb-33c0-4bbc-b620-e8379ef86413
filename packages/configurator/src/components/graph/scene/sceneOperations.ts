import { getData, updateData } from "@/components/data/useData";
import { SceneInput } from "@somo/shared";
import * as Y from "yjs";

export function wouldBeCircular(
  yDoc: Y.Doc,
  targetSceneId: string,
  inputSceneId: string,
  visited: Set<string> = new Set<string>(),
): boolean {
  if (!targetSceneId || !inputSceneId) {
    return false;
  }
  // Found a path back to the target, targetSceneId would create a cycle.
  if (inputSceneId === targetSceneId) {
    return true;
  }
  // Already explored this node.
  if (visited.has(inputSceneId)) {
    return false;
  }
  visited.add(inputSceneId);
  const data = getData(yDoc, inputSceneId, "scene");
  if (!data) {
    return false;
  }
  const sceneInputs = data.inputs;
  if (!sceneInputs) {
    return false;
  }
  for (const input of sceneInputs) {
    if (wouldBeCircular(yDoc, targetSceneId, input.source, visited)) {
      return true;
    }
  }
  return false;
}

export function addInput({
  yDoc,
  sceneId,
  input,
}: {
  yDoc: Y.Doc;
  sceneId: string;
  input: SceneInput;
}) {
  const data = getData(yDoc, sceneId, "scene");
  if (!data) {
    return;
  }
  const inputsArray = data.inputs;
  const alreadyExists = inputsArray.some((i) => areInputsEqual(i, input));
  const circular = wouldBeCircular(yDoc, data?.id, input.source);

  if (!alreadyExists && !circular) {
    inputsArray?.push(input);
    updateData(yDoc, sceneId, "scene", { inputs: inputsArray });
  }
}

export function removeInput({
  yDoc,
  sceneId,
  input,
}: {
  yDoc: Y.Doc;
  sceneId: string;
  input: SceneInput;
}) {
  const data = getData(yDoc, sceneId, "scene");
  if (!data) {
    return;
  }
  const inputsArray = data.inputs;
  const updatedInputsArray = inputsArray.filter(
    (i) => !areInputsEqual(i, input),
  );
  updateData(yDoc, sceneId, "scene", { inputs: updatedInputsArray });
}

export function areInputsEqual(a: SceneInput, b: SceneInput) {
  // TODO: Rethink!!!
  return a.source === b.source && a.sourceHandle === b.sourceHandle;
}
