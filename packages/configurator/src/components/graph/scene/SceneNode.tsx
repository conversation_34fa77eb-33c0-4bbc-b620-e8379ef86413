import { cn } from "@/lib/classNames";
import { LayoutContainerNode } from "@somo/shared";
import {
  Handle,
  NodeProps,
  Position,
  useUpdateNodeInternals,
} from "@xyflow/react";
import equal from "fast-deep-equal/react";
import { CirclePlayIcon } from "lucide-react";
import React, { useEffect } from "react";
import { useReactFlowContext } from "../ReactFlowContext";

export const SceneNode = React.memo(RegularNode, equal);
export const SceneNodeSimulator = React.memo(SimulatorNode, equal);

function RegularNode({ selected, id }: NodeProps<LayoutContainerNode>) {
  const updateNodeInternals = useUpdateNodeInternals();
  const { readOnly } = useReactFlowContext();

  useEffect(() => {
    const timeout = setTimeout(() => {
      updateNodeInternals(id);
    }, 1);
    return () => clearTimeout(timeout);
  }, [updateNodeInternals, id]);

  return (
    <div className="w-full h-full flex items-center justify-center relative">
      <div
        className={cn(
          "size-4 bg-white rounded-[2px] mx-auto border border-white outline outline-[2px] outline-offset-[0px] outline-gray-400 shadow",
          "outline outline-1 outline-offset-[0px] outline-gray-300 bg-gray-600",
          selected && "outline-2 outline-blue-400 bg-black",
        )}
      />
      <Handle
        id={id}
        type="target"
        position={Position.Left}
        isConnectable={!readOnly}
        style={{
          background: "transparent",
          border: 0,
        }}
      />
      <Handle
        id={id}
        type="source"
        position={Position.Right}
        isConnectable={!readOnly}
        style={{
          background: "transparent",
          border: 0,
          top: "50%",
          left: "100%",
          transform: "translate(-50%, -50%)",
        }}
      />
      <div className="text-white absolute">
        <CirclePlayIcon className="size-2" />
      </div>
    </div>
  );
}

/**
 * We don't render scenes in the simulator.
 */
function SimulatorNode(_props: NodeProps<LayoutContainerNode>) {
  return <></>;
}
