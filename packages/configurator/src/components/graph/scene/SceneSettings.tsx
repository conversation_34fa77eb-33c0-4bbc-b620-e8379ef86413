import { useData } from "@/components/data/useData";
import { getDeviceDimmingSpeed } from "@/components/devices/deviceUtils";
import { useConnectableDevices } from "@/components/devices/useConnectableDevices";
import { useReactFlowContext } from "@/components/graph/ReactFlowContext";
import {
  SettingsGroup,
  SettingsGroupContent,
  SettingsGroupHeader,
  SettingsLabel,
  SettingsRow,
} from "@/components/graph/settings/SettingsGroup";
import { SettingsInput } from "@/components/graph/settings/SettingsInput";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { GraphNodesByType, randomId, SceneViaIds } from "@somo/shared";
import { ActionSettingsWithHeader } from "../settings/ActionSettings";

export function SceneSettings({ node }: { node: GraphNodesByType["scene"] }) {
  const { readOnly } = useReactFlowContext();
  const { data, updateNestedData } = useData(node.data.dataId, "scene");
  if (!data) {
    return <div>Loading...</div>;
  }

  return (
    <div>
      <SettingsGroup>
        <SettingsGroupContent>
          <SettingsRow>
            <div className="font-mono text-xs text-gray-500">ID: {node.id}</div>
          </SettingsRow>
          <SettingsRow>
            <SettingsLabel htmlFor="name">Name</SettingsLabel>
            <SettingsInput
              id="name"
              value={data.title}
              onEndEdit={(value) => {
                updateNestedData("title", value);
              }}
              disabled={readOnly}
            />
          </SettingsRow>
        </SettingsGroupContent>
      </SettingsGroup>

      <SettingsGroup>
        <SettingsGroupHeader className="flex flex-row justify-between items-center -mb-2 pt-4">
          <span className="flex flex-grow">Scene Actions</span>
        </SettingsGroupHeader>
        <SettingsGroupContent>
          <SettingsRow>
            <ConnectionRow node={node} viaId="onActivate" label="On Activate" />
          </SettingsRow>

          <SettingsRow className="mt-4">
            <ConnectionRow
              node={node}
              viaId="onDeactivate"
              label="On Deactivate"
            />
          </SettingsRow>
        </SettingsGroupContent>
      </SettingsGroup>
    </div>
  );
}

function ConnectionRow({
  node,
  viaId,
  label,
}: {
  node: GraphNodesByType["scene"];
  viaId: SceneViaIds;
  label: string;
}) {
  const { nodes, readOnly } = useReactFlowContext();
  const { data, updateNestedData, deleteNestedData } = useData(
    node.data.dataId,
    "scene",
  );
  if (!data) {
    return <></>;
  }

  const dataKey = `${viaId}.onUpClick` as const;
  const via = data[viaId];

  const actions = Object.values(via.onUpClick ?? {}).sort(
    (a, b) => a.sortIndex - b.sortIndex,
  );

  const connectableDevices = useConnectableDevices({
    nodes,
    nodeType: "scene",
    actions,
  });

  return (
    <div className="flex flex-col gap-2">
      <div className="flex flex-row gap-2 text-xs font-bold text-gray-800 mt-2 items-center justify-between">
        <Label className="text-gray-700">{label}</Label>
        <div className="flex flex-row items-center gap-1 w-[90px] flex-shrink-0">
          <Checkbox
            id={`${viaId}-enabled`}
            checked={via.enabled}
            onCheckedChange={(checked) => {
              updateNestedData(`${viaId}.enabled`, Boolean(checked));
            }}
            disabled={readOnly}
          />
          <Label
            htmlFor={`${viaId}-enabled`}
            className="text-gray-500 text-xs font-semibold truncate"
          >
            Enabled
          </Label>
        </div>
      </div>

      {via.enabled && (
        <ActionSettingsWithHeader
          type="targetValue"
          label="Execute"
          node={node}
          sourceHandle={node.id}
          devices={connectableDevices}
          actions={actions}
          onDelete={(id) => deleteNestedData(`${dataKey}.${id}`)}
          onDeviceChange={(id, deviceId) => {
            updateNestedData(`${dataKey}.${id}.deviceId`, deviceId);
          }}
          onDelayChange={(id, value) => {
            updateNestedData(`${dataKey}.${id}.delay`, value);
          }}
          onDimSpeedChange={(id, value) => {
            updateNestedData(`${dataKey}.${id}.dimSpeed`, value);
          }}
          onTargetValueChange={(id, value) => {
            updateNestedData(`${dataKey}.${id}.targetValue`, value);
          }}
          onAddAction={(deviceId) => {
            const actionId = `device-control-${randomId()}`;
            const mapSize = Object.values(data[viaId].onUpClick).length;
            updateNestedData(`${dataKey}.${actionId}`, {
              id: actionId,
              deviceId,
              sortIndex: mapSize,
              type: "lighting",
              dimSpeed: getDeviceDimmingSpeed(
                deviceId,
                connectableDevices,
                nodes,
              ),
              targetValue: 100,
              onValue: 100,
              offValue: 0,
              delay: 0,
            });
          }}
        />
      )}
    </div>
  );
}
