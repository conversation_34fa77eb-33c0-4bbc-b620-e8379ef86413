import { createDataFromNode } from "@/components/data/createData";
import { SceneInput } from "@somo/shared";
import { beforeEach, describe, expect, it } from "vitest";
import * as Y from "yjs";
import { wouldBeCircular } from "./sceneOperations";

describe("wouldBeCircular", () => {
  let yDoc: Y.Doc;

  beforeEach(() => {
    yDoc = new Y.Doc();
  });

  function createScene(sceneId: string, sceneInputs: Array<SceneInput> = []) {
    // Create a mock scene node with all required properties
    const sceneNode = {
      id: sceneId,
      type: "scene" as const,
      position: { x: 0, y: 0 },
      data: { dataId: sceneId },
      width: 100,
      height: 100,
      selected: false,
      dragging: false,
      dragHandle: undefined,
      resizing: false,
    };

    // Set up the main graph structure
    const graphMap = yDoc.getMap("graph");
    let nodesMap = graphMap.get("nodes") as Y.Map<any>;
    if (!nodesMap) {
      nodesMap = new Y.Map();
      graphMap.set("nodes", nodesMap);
    }

    // Use createDataFromNode to create the proper data structure
    const dataId = createDataFromNode(yDoc, sceneNode, {
      title: sceneId,
      inputs: sceneInputs,
    });
    assert(dataId);

    // Create scene node structure pointing to the data
    const sceneMap = new Y.Map();
    sceneMap.set("type", "scene");
    sceneMap.set("data", { dataId });
    nodesMap.set(sceneId, sceneMap);

    return dataId;
  }

  it("should return false for simple non-circular dependency", () => {
    const sceneA = createScene("sceneA");
    const sceneB = createScene("sceneB");

    expect(wouldBeCircular(yDoc, sceneA, sceneB)).toBe(false);
  });

  it("should detect direct circular dependency (A -> B, then B -> A)", () => {
    const sceneA = createScene("sceneA");
    const sceneB = createScene("sceneB", [
      { source: sceneA, sourceHandle: "onActivate" },
    ]);

    // This would create: sceneB -> sceneA, but sceneA already has sceneB as input
    expect(wouldBeCircular(yDoc, sceneA, sceneB)).toBe(true);
  });

  it("should detect indirect circular dependency (A -> B -> C, then C -> A)", () => {
    const sceneA = createScene("sceneA");
    const sceneB = createScene("sceneB", [
      { source: sceneA, sourceHandle: "onActivate" },
    ]);
    const sceneC = createScene("sceneC", [
      { source: sceneB, sourceHandle: "onActivate" },
    ]);

    // This would create: sceneC -> sceneA, but sceneA -> sceneB -> sceneC
    expect(wouldBeCircular(yDoc, sceneA, sceneC)).toBe(true);
  });

  it("should allow valid chain dependencies", () => {
    const sceneA = createScene("sceneA");
    const sceneB = createScene("sceneB", [
      { source: sceneA, sourceHandle: "onActivate" },
    ]);
    const sceneC = createScene("sceneC", [
      { source: sceneB, sourceHandle: "onActivate" },
    ]);

    // This creates: sceneA -> sceneB -> sceneC (linear chain, no cycle)
    expect(wouldBeCircular(yDoc, sceneC, sceneA)).toBe(false);
  });

  it("should handle complex circular dependency (A -> B -> C -> D -> B)", () => {
    const sceneA = createScene("sceneA");
    const sceneB = createScene("sceneB", [
      { source: sceneA, sourceHandle: "onActivate" },
    ]);
    const sceneC = createScene("sceneC", [
      { source: sceneB, sourceHandle: "onActivate" },
    ]);
    const sceneD = createScene("sceneD", [
      { source: sceneC, sourceHandle: "onActivate" },
    ]);

    // This would create: sceneD -> sceneB, but sceneB -> sceneC -> sceneD
    expect(wouldBeCircular(yDoc, sceneB, sceneD)).toBe(true);
  });

  it("should handle multiple inputs without creating false positives", () => {
    const sceneA = createScene("sceneA");
    const sceneB = createScene("sceneB");
    const sceneC = createScene("sceneC", [
      { source: sceneA, sourceHandle: "onActivate" },
      { source: sceneB, sourceHandle: "onDeactivate" },
    ]);

    // sceneC has inputs from both sceneA and sceneB, adding sceneD -> sceneC should be fine
    const sceneD = createScene("sceneD");
    expect(wouldBeCircular(yDoc, sceneC, sceneD)).toBe(false);
  });

  it("should detect circular dependency with multiple inputs", () => {
    const sceneA = createScene("sceneA");
    const sceneB = createScene("sceneB", [
      { source: sceneA, sourceHandle: "onActivate" },
    ]);
    const sceneC = createScene("sceneC", [
      { source: sceneA, sourceHandle: "onActivate" },
      { source: sceneB, sourceHandle: "onDeactivate" },
    ]);

    // This would create: sceneC -> sceneA, but sceneA already depends on sceneC
    expect(wouldBeCircular(yDoc, sceneA, sceneC)).toBe(true);
    // This would create: sceneC -> sceneB, but sceneA already depends on sceneB
    expect(wouldBeCircular(yDoc, sceneB, sceneC)).toBe(true);
  });

  it("should handle self-referencing scenario", () => {
    const sceneA = createScene("sceneA");

    // Scene trying to add itself as input
    expect(wouldBeCircular(yDoc, sceneA, sceneA)).toBe(true);
  });

  it("should handle empty scenes", () => {
    const sceneA = createScene("sceneA", []);
    const sceneB = createScene("sceneB", []);

    expect(wouldBeCircular(yDoc, sceneA, sceneB)).toBe(false);
  });
});
