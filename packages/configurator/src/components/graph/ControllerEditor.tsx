import { But<PERSON> } from "@/components/ui/button";
import { pick } from "@/lib/object";
import { areEqual } from "@/lib/Set";
import { MainModule } from "@/types/basestation";
import { useAuth, useUser } from "@clerk/clerk-react";
import { HocuspocusProvider } from "@hocuspocus/provider";
import {
  GraphNode,
  isNodeOrParentSelected,
  reactFlowKeysToPersist,
} from "@somo/shared";
import { Edge, ReactFlowInstance } from "@xyflow/react";
import "@xyflow/react/dist/style.css";
import { Allotment } from "allotment";
import "allotment/dist/style.css";
import deepEqual from "fast-deep-equal/react";
import { useFlags } from "launchdarkly-react-client-sdk";
import {
  EthernetPortIcon,
  EyeIcon,
  EyeOffIcon,
  FileWarning,
} from "lucide-react";
import { useEffect, useMemo, useRef, useState } from "react";
import { match, P } from "ts-pattern";
import { IndexeddbPersistence } from "y-indexeddb";
import * as Y from "yjs";
import { BluetoothProvisioningButton } from "../bluetooth/BluetoothProvisioningButton";
import { ControllerEditorMenu } from "./ControllerEditorMenu";
import { LiveUser } from "./DocumentGraphCollaborationCursors";
import { GraphEditor } from "./GraphEditor";
import { LiveUsers } from "./LiveUsers";
import { ReactFlowContext, type FocusedEditor } from "./ReactFlowContext";
import { Simulator } from "./Simulator";
import { SldEditor } from "./SldEditor";
import { Room, useRoom } from "./useRoom";
import { useVisibleNodesAndEdges } from "./useVisibleNodesAndEdges";
import { SLD_EDITOR } from "./utils/editorConstants";

type Mode =
  | { state: "configurator" }
  | { state: "configurator and sld editor" }
  | { state: "simulator"; basestation: MainModule; room: Room };

export function ControllerEditor({
  controllerId,
  orgId,
}: {
  controllerId: string;
  orgId: string;
}) {
  const { getToken } = useAuth();
  const { data: room } = useRoom({ orgId, controllerId });

  const [mode, setMode] = useState<Mode>({ state: "configurator" });
  const [nodes, setNodes] = useState<GraphNode[]>([]);
  const [edges, setEdges] = useState<Edge[]>([]);
  const [graphUndoManager, setGraphUndoManager] = useState<Y.UndoManager>();
  const [liveUsers, setLiveUsers] = useState<LiveUser[]>([]);
  const [trackedLiveUserId, setTrackedLiveUserId] = useState<string | null>(
    null,
  );

  const [basestation, setBasestation] = useState<MainModule | undefined>(
    undefined,
  );

  // Global focus management for ReactFlow editors
  const [focusedEditor, setFocusedEditor] = useState<FocusedEditor>(null);

  // SLD ReactFlow instance management
  const [sldReactFlowInstance, setSldReactFlowInstance] =
    useState<ReactFlowInstance | null>(null);

  const { simulator, sldEditor } = useFlags();

  // Initialize WASM module on component mount
  useEffect(() => {
    window
      .createBasestationModule()
      .then((module) => setBasestation(module))
      .catch((error) => {
        console.error("Failed to initialize WASM module:", error);
      });
  }, []);

  const toggleSimulation = () => {
    const newMode = match([mode, basestation, room])
      .with(
        [{ state: "configurator" }, P.nonNullable, P.nonNullable],
        [
          { state: "configurator and sld editor" },
          P.nonNullable,
          P.nonNullable,
        ],
        ([_, basestation, room]) =>
          ({
            state: "simulator",
            basestation,
            room,
          }) as const,
      )
      .otherwise(
        () =>
          ({
            state: "configurator",
          }) as const,
      );

    setMode(newMode);
  };

  const showSldEditorButton = sldEditor && mode.state !== "simulator";
  const showSldEditor = mode.state === "configurator and sld editor";
  const [sldEditorHeight, setSldEditorHeight] = useState(600);

  const toggleSldEditor = () => {
    setMode((prev) => {
      return prev.state === "configurator"
        ? { state: "configurator and sld editor" }
        : { state: "configurator" };
    });
  };

  const reactFlowInstance = useRef<ReactFlowInstance<GraphNode> | null>(null);
  const yDoc = useMemo(() => new Y.Doc({ autoLoad: true }), [controllerId]);
  const [authenticationFailed, setAuthenticationFailed] = useState(false);

  const provider = useMemo(
    () =>
      new HocuspocusProvider({
        url: `${import.meta.env.VITE_WS_URL}/ws/sync`,
        name: `rooms:${orgId}:${controllerId}`,
        document: yDoc,
        token: async () => {
          const token = await getToken({ template: "Configurator" });
          return token ?? "";
        },
        onOpen: () => {
          setAuthenticationFailed(false);
        },
        onAuthenticationFailed: () => {
          setAuthenticationFailed(true);
        },
        onSynced: () => {
          const graph = yDoc.getMap("graph");

          if (!graph.has("nodes")) {
            graph.set("nodes", new Y.Map());
          }
          if (!graph.has("edges")) {
            graph.set("edges", new Y.Map());
          }

          const yNodes = graph.get("nodes") as Y.Map<any>;
          const yEdges = graph.get("edges") as Y.Map<any>;

          const newNodes: GraphNode[] = [];
          yNodes.forEach((value) => {
            newNodes.push(pick(value.toJSON(), reactFlowKeysToPersist));
          });
          newNodes.sort((a, b) => (a.zIndex ?? 0) - (b.zIndex ?? 0));
          setNodes(newNodes);
          setEdges(Object.values(yEdges.toJSON()) as Edge[]);

          // Create undo manager that includes main graph, SLD-specific nodes, and data map
          const sldSpecificNodes = yDoc.getMap("SldGraph");
          const dataMap = yDoc.getMap("data");
          const undoManager = new Y.UndoManager(
            [graph, sldSpecificNodes, dataMap],
            {
              ignoreRemoteMapChanges: true,
              captureTimeout: 100, // Force near-immediate capture of operations
            },
          );
          setGraphUndoManager(undoManager);
        },
      }),
    [controllerId, orgId, yDoc],
  );

  const [indexDbSupported, setIndexDbSupported] = useState(false);

  useEffect(() => {
    async function supportsIndexedDB() {
      try {
        const db = await indexedDB.open("test");
        // @ts-expect-error - this is just not yet defined in the types
        db.close();
        return true;
      } catch {
        return false;
      }
    }
    supportsIndexedDB().then(setIndexDbSupported);
  }, []);

  const db = useMemo(() => {
    if (!indexDbSupported) {
      return undefined;
    }
    const db = new IndexeddbPersistence(`rooms:${orgId}:${controllerId}`, yDoc);

    db.on("synced", () => {
      const graph = yDoc.getMap("graph");

      if (!graph.has("nodes")) {
        graph.set("nodes", new Y.Map());
      }
      if (!graph.has("edges")) {
        graph.set("edges", new Y.Map());
      }

      const yNodes = graph.get("nodes") as Y.Map<any>;
      const yEdges = graph.get("edges") as Y.Map<any>;

      const newNodes: GraphNode[] = [];
      yNodes.forEach((value) => {
        newNodes.push(value.toJSON());
      });
      newNodes.sort((a, b) => (a.zIndex ?? 0) - (b.zIndex ?? 0));
      setNodes(newNodes);
      setEdges(Object.values(yEdges.toJSON()) as Edge[]);
    });
    return db;
  }, [indexDbSupported, orgId, controllerId]);

  useEffect(() => {
    console.log(db?.synced ? "[Remote] Synched" : "[Remote] not synched");
  }, [db?.synced]);

  const { visibleNodes, visibleEdges } = useVisibleNodesAndEdges({
    yDoc,
    nodes,
    edges,
  });

  const selectedNodes = visibleNodes.filter((node) =>
    isNodeOrParentSelected(visibleNodes, node),
  );

  const selectedEdges = visibleEdges.filter((e) => e.selected);

  useEffect(() => {
    const graph = yDoc.getMap("graph");

    if (!graph || !graph.has("nodes") || !graph.has("edges")) {
      return;
    }

    const yNodes = graph.get("nodes") as Y.Map<any>;
    const yEdges = graph.get("edges") as Y.Map<any>;

    const nodesChangeHandler = () => {
      setNodes((oldNodes) => {
        let newNodes: GraphNode[] = [];
        yNodes.forEach((value) => {
          newNodes.push(value.toJSON());
        });
        newNodes = newNodes.map((node) => {
          const oldNode = oldNodes.find((n) => n.id === node.id);
          if (oldNode) {
            return {
              ...oldNode,
              ...node,
            } as GraphNode;
          }
          return node;
        });

        if (deepEqual(oldNodes, newNodes)) {
          return oldNodes;
        }

        // sort nodes by zIndex
        newNodes.sort((a, b) => (a.zIndex ?? 0) - (b.zIndex ?? 0));

        return newNodes;
      });
    };
    const edgesChangeHandler = () => {
      setEdges((oldEdges) => {
        const newEdges = Object.values(yEdges.toJSON()) as Edge[];
        const updatedEdges = newEdges.map((edge) => {
          const oldEdge = oldEdges.find((e) => e.id === edge.id);
          if (oldEdge) {
            return {
              ...oldEdge,
              ...edge,
            };
          }
          return edge;
        });
        if (deepEqual(oldEdges, updatedEdges)) {
          return oldEdges;
        }
        return updatedEdges;
      });
    };

    yNodes.observeDeep(nodesChangeHandler);
    yEdges.observeDeep(edgesChangeHandler);

    return () => {
      yNodes.unobserveDeep(nodesChangeHandler);
      yEdges.unobserveDeep(edgesChangeHandler);
    };
  }, [provider, yDoc, provider.synced]);

  // handle undo/redo
  useEffect(() => {
    if (!graphUndoManager) {
      return;
    }

    const onKeyDown = (event: KeyboardEvent) => {
      const isUndoRedoModifier = event.metaKey || event.ctrlKey;

      if (isUndoRedoModifier && event.code === "KeyZ") {
        event.preventDefault();
        event.stopPropagation();
        if (event.shiftKey) {
          graphUndoManager.redo();
        } else {
          graphUndoManager.undo();
        }
      }

      // Also handle Ctrl+Y as redo (common on Windows)
      if (isUndoRedoModifier && event.code === "KeyY") {
        event.preventDefault();
        event.stopPropagation();
        graphUndoManager.redo();
      }
    };

    window.addEventListener("keydown", onKeyDown);
    return () => {
      window.removeEventListener("keydown", onKeyDown);
    };
  }, [graphUndoManager]);

  const { user } = useUser();

  useEffect(() => {
    if (!provider.awareness) {
      return;
    }
    const awarenessChangeHandler = () => {
      const awarenessStates = provider.awareness?.getStates();
      if (!awarenessStates) {
        return;
      }

      // Only add one cursor per user, keeping only the most recent cursor (by timestamp)
      const userMap: Map<string, LiveUser> = new Map();
      for (const [currentUser, awareness] of awarenessStates) {
        if (!awareness["somo-user"]) {
          continue;
        }
        const userId = awareness["somo-user"].id;
        const cursor = awareness["somo-cursor"];
        // dont add ourselves to the list of live users
        if (currentUser === provider.awareness?.clientID) {
          continue;
        }
        if (userId.toString() === user?.id) {
          continue;
        }
        const previousUserState = userMap.get(userId);
        if (
          !previousUserState ||
          (cursor?.timestamp ?? 0) > (previousUserState.cursor?.timestamp ?? 0)
        ) {
          userMap.set(userId, {
            id: userId,
            name: awareness["somo-user"].name,
            picture: awareness["somo-user"]?.picture,
            clientId: currentUser,
            cursor,
          });
        }
      }
      const newLiveUsers = Array.from(userMap.values());
      setLiveUsers((oldLiveUsers) => {
        if (deepEqual(oldLiveUsers, newLiveUsers)) {
          return oldLiveUsers;
        }
        return newLiveUsers;
      });
    };
    provider.awareness.on("change", awarenessChangeHandler);
    return () => {
      provider.awareness?.off("change", awarenessChangeHandler);
    };
  }, [user?.id]);

  useEffect(() => {
    if (!provider.isSynced) {
      return;
    }
    if (user?.id && provider.awareness) {
      provider.awareness.setLocalStateField("somo-user", {
        id: user.id,
        name: user.fullName ?? user.emailAddresses[0].emailAddress,
        picture: user.imageUrl,
      });
    }
  }, [
    provider.isSynced,
    user?.id,
    user?.fullName,
    user?.emailAddresses,
    user?.imageUrl,
    provider.awareness,
  ]);

  const trackedLiveUser = liveUsers.find(
    (user) => user.id === trackedLiveUserId,
  );

  useEffect(() => {
    if (!trackedLiveUser) {
      return;
    }
    if (trackedLiveUser.cursor?.viewport) {
      reactFlowInstance.current?.setViewport(trackedLiveUser.cursor.viewport);
    }
    const selectedNodeIds = selectedNodes.map((n) => n.id);
    const selectedEdgeIds = selectedEdges.map((e) => e.id);
    const selectedNodeIdSet = new Set(selectedNodeIds);
    const selectedEdgeIdSet = new Set(selectedEdgeIds);

    if (
      trackedLiveUser.cursor?.selectedNodes &&
      trackedLiveUser.cursor.selectedNodes.length > 0
    ) {
      const remoteNodeIdSet = new Set(trackedLiveUser.cursor.selectedNodes);
      if (!areEqual(remoteNodeIdSet, selectedNodeIdSet)) {
        // deselect all selected nodes
        for (const node of selectedNodes) {
          reactFlowInstance.current?.updateNode(node.id, {
            selected: false,
          });
        }
        // select all remote selected nodes
        for (const nodeId of trackedLiveUser.cursor.selectedNodes) {
          reactFlowInstance.current?.updateNode(nodeId, {
            selected: true,
          });
        }
      }
    }
    if (
      trackedLiveUser.cursor?.selectedEdges &&
      trackedLiveUser.cursor.selectedEdges.length > 0
    ) {
      const remoteEdgeIdSet = new Set(trackedLiveUser.cursor.selectedEdges);
      if (!areEqual(remoteEdgeIdSet, selectedEdgeIdSet)) {
        // deselect all selected edges
        for (const edge of selectedEdges) {
          reactFlowInstance.current?.updateEdge(edge.id, {
            selected: false,
          });
        }
        // select all remote selected edges
        for (const edgeId of trackedLiveUser.cursor.selectedEdges) {
          reactFlowInstance.current?.updateEdge(edgeId, {
            selected: true,
          });
        }
      }
    }
  }, [
    selectedNodes,
    selectedEdges,
    trackedLiveUser?.cursor?.selectedNodes,
    trackedLiveUser?.cursor?.selectedEdges,
  ]);

  const graph = { edges, nodes };

  if (authenticationFailed) {
    return (
      <div className="absolute inset-0 bg-white shadow-lg dark:bg-black text-gray-900 dark:text-gray-400 flex items-center justify-center gap-2">
        <FileWarning className="size-6" />
        <div className="text-sm">Authentication failed</div>
      </div>
    );
  }
  return (
    <ReactFlowContext.Provider
      value={{
        reactFlowInstance: reactFlowInstance.current,
        sldReactFlowInstance,
        setSldReactFlowInstance,
        yDoc,
        nodes,
        edges,
        orgId,
        controllerId,
        readOnly: room?.readOnly ?? false,
        focusedEditor,
        setFocusedEditor,
        getReactFlowInstance: (context?: FocusedEditor) => {
          const targetContext = context ?? focusedEditor;
          return targetContext === SLD_EDITOR
            ? sldReactFlowInstance
            : reactFlowInstance.current;
        },
      }}
    >
      <div className="absolute inset-0 bg-gray-50">
        <div className="absolute inset-x-1 top-1 h-10 flex flex-row justify-start items-center gap-2 p-1">
          <ControllerEditorMenu
            orgId={orgId}
            controllerId={controllerId}
            reactFlowInstance={reactFlowInstance.current}
            graph={graph}
          />
          <div className="absolute pointer-events-none top-3 left-0 right-0 flex flex-row justify-center items-center">
            <div className="text-gray-500 text-sm ml-2 flex items-center gap-2">
              {room?.name ?? "Controller Editor"}
              {room?.readOnly && <> [READ ONLY]</>}
            </div>
          </div>

          <Button
            variant="outline"
            onClick={() => {
              for (const node of selectedNodes) {
                reactFlowInstance.current?.updateNode(node.id, {
                  selected: false,
                });
              }
              for (const edge of selectedEdges) {
                reactFlowInstance.current?.updateEdge(edge.id, {
                  selected: false,
                });
              }
            }}
            className="ml-auto"
          >
            <EthernetPortIcon className="size-4" />
            Connected Controllers
          </Button>

          {showSldEditorButton && (
            <Button
              variant="outline"
              onClick={toggleSldEditor}
              className="ml-2"
            >
              {mode.state === "configurator" ? (
                <>
                  <EyeIcon className="size-4" /> Show SLD Editor
                </>
              ) : (
                <>
                  <EyeOffIcon className="size-4" /> Hide SLD Editor
                </>
              )}
            </Button>
          )}

          {simulator && (
            <Button
              variant="outline"
              disabled={!basestation}
              onClick={toggleSimulation}
              className="ml-2"
            >
              {mode.state === "simulator" ? "End Simulation" : "Simulate"}
            </Button>
          )}

          <BluetoothProvisioningButton variant="outline" className="ml-2" />

          {!provider.isSynced && (
            <div className="bg-black px-2 py-1 rounded-md text-white text-xs shadow-md">
              {provider.unsyncedChanges} unsyched changes
            </div>
          )}

          <LiveUsers
            users={liveUsers}
            trackedUserId={trackedLiveUserId}
            onSelect={(user) => {
              setTrackedLiveUserId(
                trackedLiveUserId === user.id ? null : user.id,
              );
            }}
          />
        </div>
        <div className="absolute inset-x-0 top-10 bottom-0">
          {mode.state === "simulator" ? (
            <Simulator
              basestation={mode.basestation}
              room={mode.room}
              graph={graph}
              yDoc={yDoc}
            />
          ) : (
            <Allotment
              separator={true}
              vertical
              onDragEnd={(sizes) => {
                if (showSldEditor) {
                  setSldEditorHeight(sizes[1]);
                }
              }}
              className="h-full w-full"
            >
              <Allotment.Pane minSize={200} className="h-full w-full">
                <div
                  className="h-full w-full"
                  style={{ minHeight: "400px", minWidth: "600px" }}
                >
                  <GraphEditor
                    reactFlowInstance={reactFlowInstance}
                    provider={provider}
                    yDoc={yDoc}
                    setNodes={setNodes}
                    setEdges={setEdges}
                    graph={graph}
                    liveUsers={liveUsers}
                    trackedLiveUser={trackedLiveUser}
                    orgId={orgId}
                    controllerId={controllerId}
                  />
                </div>
              </Allotment.Pane>
              {showSldEditor && (
                <Allotment.Pane
                  minSize={200}
                  maxSize={800}
                  preferredSize={sldEditorHeight}
                  className="h-full w-full"
                >
                  <div className="h-full w-full">
                    <SldEditor
                      yDoc={yDoc}
                      roomName={room?.name}
                      liveUsers={liveUsers}
                      provider={provider}
                      reactFlowInstance={reactFlowInstance}
                    />
                  </div>
                </Allotment.Pane>
              )}
            </Allotment>
          )}
        </div>
      </div>
      <style>{`
        [data-panel-group-direction="vertical"] > [data-panel-group-separator] {
          background-color: #d1d5db !important;
          position: relative !important;
          cursor: row-resize !important;
        }
        [data-panel-group-direction="vertical"] > [data-panel-group-separator]:hover {
          background-color: #9ca3af !important;
        }
        [data-panel-group-direction="vertical"] > [data-panel-group-separator]::after {
          content: '';
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
          width: 32px;
          height: 4px;
          background-color: #6b7280;
          border-radius: 2px;
        }
      `}</style>
    </ReactFlowContext.Provider>
  );
}
