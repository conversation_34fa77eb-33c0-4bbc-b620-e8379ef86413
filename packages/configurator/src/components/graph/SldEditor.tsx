import { GhostNodeSLD } from "@/components/devices/sld/GhostNodeSLD";
import { SldNodeData } from "@/components/devices/sld/types";
import { CommentNode } from "./comment/CommentNode";
import { HocuspocusProvider } from "@hocuspocus/provider";
import { GraphNode } from "@somo/shared";
import {
  ConnectionMode,
  Edge,
  Node,
  Panel,
  ReactFlow,
  ReactFlowInstance,
  ReactFlowProvider,
  useEdgesState,
  useNodesState,
} from "@xyflow/react";
import "@xyflow/react/dist/style.css";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import * as Y from "yjs";
import {
  DocumentGraphCollaborationCursors,
  LiveUser,
} from "./DocumentGraphCollaborationCursors";
import { SLDContextMenu } from "./SldContextMenu";

import { fitToScreenWithFallback } from "@/lib/fitToScreen";
import { GridConfig } from "@/lib/gridConfig";
import { objectToYMap } from "@/lib/yjsUtils";
import { Allotment } from "allotment";
import "allotment/dist/style.css";
import { SLD_COMPONENTS } from "./config/sldComponentConfig";
import { ConnectionErrorToast } from "./ConnectionErrorToast";
import { DeleteConfirmationModal } from "./DeleteConfirmationModal";
import { DownloadButton } from "./DownloadButton";
import type { EdgeHandleContextMenuState } from "./EdgeHandleContextMenu";
import { EdgeHandleContextMenu } from "./EdgeHandleContextMenu";
import { getSldEdgesMap, getSldNodesMap } from "./Graph";
import {
  deleteMirroredNodesFromMainGraph,
  deleteSLDNodes,
} from "./helpers/deleteSLDNodes";
import { reconnectEdgeInYDoc } from "./helpers/reconnectEdgeHelper";
import { useSldEventHandlers } from "./hooks/useSldEventHandlers";
import { useSldYDocSync } from "./hooks/useSldYDocSync";
import { SLDCustomEdge } from "./PositionableEdge/CustomEdge/SLDCustomEdge";
import { useReactFlowContext } from "./ReactFlowContext";
import { SLDSettingsEditor } from "./SLDSettingsEditor";
import { SldToolbar } from "./SldToolbar";
import { SLD_EDITOR } from "./utils/editorConstants";
import { createSLDNodeFromSharedData } from "./helpers/sldHelpers";

export function SldEditor({
  yDoc,
  roomName,
  liveUsers,
  provider,
  reactFlowInstance: mainGraphReactFlowInstanceRef,
}: {
  yDoc: Y.Doc;
  roomName?: string;
  liveUsers?: LiveUser[];
  provider?: HocuspocusProvider;
  reactFlowInstance?: React.MutableRefObject<ReactFlowInstance<GraphNode> | null>;
}) {
  const [sldNodes, setSldNodes, onNodesChange] = useNodesState<
    Node<SldNodeData>
  >([]);
  const [sldEdges, setSldEdges, onEdgesChange] = useEdgesState<Edge>([]);
  const [selectedNodes, setSelectedNodes] = useState<Node<SldNodeData>[]>([]);
  const containerRef = useRef<HTMLDivElement>(null);
  const [currentContextMenuPosition, setCurrentContextMenuPosition] = useState<{
    x: number;
    y: number;
  } | null>(null);
  const [edgeHandleContextMenu, setEdgeHandleContextMenu] =
    useState<EdgeHandleContextMenuState>(null);
  const [reactFlowInstance, setReactFlowInstance] =
    useState<ReactFlowInstance | null>(null);
  const [showGrid, setShowGrid] = useState(true);
  const [snapToGrid, setSnapToGrid] = useState(true);
  const [_rightDrawerWidth, _setRightDrawerWidth] = useState(300);
  const [deleteModalState, setDeleteModalState] = useState<{
    isOpen: boolean;
    elementsToDelete: { nodes: Node<any>[]; edges: Edge[] };
    mirroredNodeIds: string[];
    sldNodeIds: string[];
  }>({
    isOpen: false,
    elementsToDelete: { nodes: [], edges: [] },
    mirroredNodeIds: [],
    sldNodeIds: [],
  });

  // Use global focus management
  const {
    focusedEditor,
    setFocusedEditor,
    reactFlowInstance: _contextReactFlowInstance,
    setSldReactFlowInstance,
  } = useReactFlowContext();
  const isFocused = focusedEditor === SLD_EDITOR;

  // Flag to prevent infinite loop during selection sync
  const isSyncingSelection = useRef(false);

  // Cleanup SLD ReactFlow instance on unmount
  useEffect(() => {
    return () => {
      setSldReactFlowInstance(null);
    };
  }, [setSldReactFlowInstance]);

  const isDraggingRef = useRef(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const [showErrorToast, setShowErrorToast] = useState(false);

  // Function to show connection error toast
  const showConnectionError = useCallback((message: string) => {
    setConnectionError(message);
    setShowErrorToast(true);
  }, []);

  // Memoize the onClose function to prevent unnecessary re-renders
  const handleCloseErrorToast = useCallback(() => {
    setShowErrorToast(false);
  }, []);

  // Cursor tracking for collaboration
  const updateCursor = (event?: React.MouseEvent) => {
    if (!provider?.awareness) {
      return;
    }
    if (!reactFlowInstance) {
      return;
    }

    const oldCursor = provider.awareness.getLocalState()?.["somo-cursor"] ?? {};
    const position = event
      ? reactFlowInstance.screenToFlowPosition(
          {
            x: event.clientX,
            y: event.clientY,
          },
          { snapToGrid: false },
        )
      : oldCursor.position;

    const cursorData = {
      ...oldCursor,
      ...(position ?? {}),
      dragging: isDraggingRef.current,
      viewport: reactFlowInstance.getViewport(),
      timestamp: Date.now(),
      context: SLD_EDITOR, // Add context information
    };
    provider.awareness.setLocalStateField("somo-cursor", cursorData);
  };

  // Custom node change handler to sync SLD-specific nodes
  const handleNodesChange = useCallback(
    (changes: any[]) => {
      // Apply changes to local state first (ReactFlow handles snapping automatically)
      onNodesChange(changes);

      // Process each change for additional logic
      yDoc.transact(() => {
        // Log current nodes before processing changes
        const sldNodesMap = getSldNodesMap(yDoc);
        if (sldNodesMap) {
          changes.forEach((change) => {
            if (change.type === "remove") {
              // Remove from SLD YDoc
              const yNode = sldNodesMap.get(change.id);
              if (yNode) {
                sldNodesMap.delete(change.id);
              }
            } else if (change.type === "position") {
              // Update position in SLD YDoc for collaborative position updates
              const sldNodesMap = getSldNodesMap(yDoc);
              if (sldNodesMap) {
                const yNode = sldNodesMap.get(change.id);
                if (yNode) {
                  // Ensure position is stored as a Y.Map
                  let position = yNode.get("position");
                  if (!position || typeof position.set !== "function") {
                    // If position doesn't exist or is not a Y.Map, create a new one
                    position = new Y.Map();
                    yNode.set("position", position);
                  }
                  position.set("x", change.position.x);
                  position.set("y", change.position.y);
                }
              }
            } else if (change.type === "add") {
              const node = change.item;

              // Store in SLD YDoc
              const sldNodesMap = getSldNodesMap(yDoc);
              if (sldNodesMap) {
                // Check if this is a comment node and preserve its original data
                if (node.type === "comment") {
                  // For comment nodes, don't overwrite the existing data
                  const existingYNode = sldNodesMap.get(node.id);
                  if (existingYNode) {
                    // Just update the selection state, preserve the original data
                    existingYNode.set("selected", false);
                    existingYNode.set("zIndex", node.zIndex || 100);
                  } else {
                    // If it doesn't exist, create it with the full data
                    const yNode = objectToYMap(node);
                    yNode.set("selected", false);
                    yNode.set("zIndex", node.zIndex || 100);
                    sldNodesMap.set(node.id, yNode);
                  }
                } else {
                  // For other nodes, use the standard approach
                  const yNode = objectToYMap(node);
                  yNode.set("selected", false);
                  yNode.set("zIndex", 0);
                  sldNodesMap.set(node.id, yNode);
                }
              }

              // Auto-fit to screen, deselect other nodes, and select the new node
              if (reactFlowInstance) {
                setSldNodes((prev) =>
                  prev.map((n) => ({
                    ...n,
                    data: {
                      ...n.data, // Preserve all existing data including dataId
                    },
                    selected: n.id === node.id, // Only select the new node
                  })),
                );
              }
            }
          });
        }
      });
    },
    [onNodesChange, yDoc, reactFlowInstance],
  );

  const handleEdgesChange = useCallback(
    (changes: any[]) => {
      // Apply changes to local state first
      onEdgesChange(changes);

      // Check for deletions and sync to SLD-specific edges map
      changes.forEach((change) => {
        if (change.type === "remove") {
          yDoc.transact(() => {
            const sldNodes = getSldNodesMap(yDoc);
            const sldEdges = getSldEdgesMap(yDoc);

            if (!sldNodes || !sldEdges) {
              return;
            }

            if (sldEdges.has(change.id)) {
              const edgeBeforeDeletion = sldEdges.get(change.id);

              const edgeDataBefore = edgeBeforeDeletion.get("data");
              if (edgeDataBefore && typeof edgeDataBefore.get === "function") {
                const pointsBefore = edgeDataBefore.get("points");

                if (pointsBefore) {
                  const points = pointsBefore.toArray();

                  const ghostNodesToDelete: string[] = [];
                  points.forEach((point: any) => {
                    for (const [nodeId, node] of sldNodes.entries()) {
                      const nodeData = node.get("data");
                      if (nodeData && typeof nodeData.get === "function") {
                        const sourceHandleId = nodeData.get("sourceHandleId");
                        if (sourceHandleId === point.id) {
                          ghostNodesToDelete.push(nodeId);
                        }
                      }
                    }
                  });

                  if (ghostNodesToDelete.length > 0) {
                    ghostNodesToDelete.forEach((nodeId) => {
                      sldNodes.delete(nodeId);
                    });
                  }
                }
              }
            }

            if (sldEdges.has(change.id)) {
              sldEdges.delete(change.id);
            }
          });
        }
      });
    },
    [onEdgesChange, yDoc],
  );

  // Calculate background bounds based on node positions
  const lastBoundsRef = useRef<{
    min: [number, number];
    max: [number, number];
  } | null>(null);

  const backgroundBounds = useMemo(() => {
    if (sldNodes.length === 0) {
      // Default bounds when no nodes exist
      return {
        min: [-500, -500],
        max: [500, 500],
      };
    }

    // Calculate the bounds of all nodes
    const positions = sldNodes
      .map((node) => node.position)
      .filter(
        (pos) => pos && typeof pos.x === "number" && typeof pos.y === "number",
      );

    if (positions.length === 0) {
      // Default bounds when no valid positions exist
      return {
        min: [-500, -500],
        max: [500, 500],
      };
    }

    const minX = Math.min(...positions.map((p) => p.x));
    const maxX = Math.max(...positions.map((p) => p.x));
    const minY = Math.min(...positions.map((p) => p.y));
    const maxY = Math.max(...positions.map((p) => p.y));

    // Add padding around the content (100px on each side)
    const padding = 100;
    const newBounds = {
      min: [minX - padding, minY - padding] as [number, number],
      max: [maxX + padding + 120, maxY + padding + 80] as [number, number],
    };

    // Only update if bounds changed significantly
    const lastBounds = lastBoundsRef.current;
    if (lastBounds) {
      const xDiff =
        Math.abs(newBounds.min[0] - lastBounds.min[0]) +
        Math.abs(newBounds.max[0] - lastBounds.max[0]);
      const yDiff =
        Math.abs(newBounds.min[1] - lastBounds.min[1]) +
        Math.abs(newBounds.max[1] - lastBounds.max[1]);

      if (xDiff < 1 && yDiff < 1) {
        return lastBounds; // Return cached bounds if change is minimal
      }
    }

    // Update cached bounds
    lastBoundsRef.current = newBounds;
    return newBounds;
  }, [
    sldNodes.length,
    sldNodes
      .map(
        (n) =>
          `${n.id}-${Math.round((n.position?.x || 0) / 10)}-${Math.round((n.position?.y || 0) / 10)}`,
      )
      .join(","),
  ]);

  // Create a background rectangle to visualize the bounds
  const backgroundRect: Node<any> = useMemo(
    () => ({
      id: "bounds-background",
      type: "default",
      position: { x: backgroundBounds.min[0], y: backgroundBounds.min[1] },
      data: {},
      style: {
        width: backgroundBounds.max[0] - backgroundBounds.min[0],
        height: backgroundBounds.max[1] - backgroundBounds.min[1],
        backgroundColor: "#f5f5f5", // Light grey background
        border: "3px solid #000000", // Black border
        borderRadius: "12px",
        opacity: 0.8,
        zIndex: -1,
        pointerEvents: "none", // Make it completely non-interactive
      },
      draggable: false,
      selectable: false,
      deletable: false,
      connectable: false,
    }),
    [backgroundBounds],
  );

  // Auto-fit state management
  const [hasAutoFitted, setHasAutoFitted] = useState(false);

  // Trigger auto-fit when SLD nodes are first loaded
  useEffect(() => {
    if (sldNodes.length > 0 && reactFlowInstance && !hasAutoFitted) {
      setHasAutoFitted(true);
      setTimeout(() => {
        fitToScreenWithFallback({
          reactFlowInstance,
          containerRef,
          backgroundRect,
          padding: 0.1,
          duration: 500,
          maxZoom: 1,
        });
      }, 100);
    }
  }, [sldNodes.length, reactFlowInstance, hasAutoFitted]);

  // Use the YDoc sync hook
  useSldYDocSync({
    yDoc,
    sldNodes,
    setSldNodes,
    setSldEdges,
    reactFlowInstance,
  });

  // Wrapper for SLDCustomEdge that passes required props
  const SLDCustomEdgeWrapper = (props: any) => {
    return (
      <SLDCustomEdge
        {...props}
        yDoc={yDoc}
        onShowContextMenu={(x, y, pointId, hasBranchingNode) => {
          setEdgeHandleContextMenu({ x, y, pointId, hasBranchingNode });
        }}
      />
    );
  };

  const sldEdgeTypes = useMemo(
    () => ({
      smoothstep: SLDCustomEdgeWrapper,
    }),
    [],
  );

  // Handle confirmed deletion of mirrored nodes
  const handleConfirmedDeletion = useCallback(() => {
    if (!deleteModalState.elementsToDelete) {
      return;
    }

    if (yDoc) {
      // Delete the mirrored nodes from the main graph YDoc
      deleteMirroredNodesFromMainGraph(yDoc, deleteModalState.mirroredNodeIds);
      // Delete the corresponding SLD nodes
      deleteSLDNodes(yDoc, deleteModalState.sldNodeIds);
    }

    // Close the modal
    setDeleteModalState((prev) => ({ ...prev, isOpen: false }));
  }, [deleteModalState, yDoc]);

  // Method to reconnect an edge to a different handle
  const reconnectEdge = useCallback(
    (edgeId: string, newSourceHandle?: string, newTargetHandle?: string) => {
      return reconnectEdgeInYDoc(
        yDoc,
        edgeId,
        newSourceHandle,
        newTargetHandle,
      );
    },
    [yDoc],
  );

  const sldNodeTypes = useMemo(
    () => ({
      ...SLD_COMPONENTS,
      // Add comment node type
      comment: CommentNode,
      // Override ghostSLD to inject reconnectEdge callback
      ghostSLD: (props: any) => {
        const enhancedProps = {
          ...props,
          data: {
            ...props.data,
            reconnectEdge,
          },
        };
        return <GhostNodeSLD {...enhancedProps} />;
      },
      default: ({ data }: { data: any }) => <div style={data.style} />,
    }),
    [reconnectEdge],
  );

  // Use the event handlers hook
  const eventHandlers = useSldEventHandlers({
    yDoc,
    sldNodes,
    sldEdges,
    reactFlowInstance,
    isFocused,
    isSyncingSelection,
    mainGraphReactFlowInstanceRef,
    showConnectionError,
    setFocusedEditor,
    setSelectedNodes,
    setDeleteModalState,
    updateCursor,
    isDraggingRef,
  });

  return (
    <div className="h-full w-full flex flex-col">
      <SldToolbar
        nodeCount={sldNodes.length}
        reactFlowInstance={reactFlowInstance}
        containerRef={containerRef}
        backgroundRect={backgroundRect}
        showGrid={showGrid}
        setShowGrid={setShowGrid}
        snapToGrid={snapToGrid}
        setSnapToGrid={setSnapToGrid}
      />
      <Allotment separator={false}>
        <Allotment.Pane
          className="absolute inset-0 flex flex-row py-2 pr-1 pl-2"
          preferredSize={600}
        >
          <div ref={containerRef} className="h-full w-full">
            <SLDContextMenu
              reactFlowInstance={reactFlowInstance as any}
              currentContextMenuPosition={currentContextMenuPosition}
              setCurrentContextMenuPosition={setCurrentContextMenuPosition}
              selectedNodes={selectedNodes}
            >
              <ReactFlowProvider>
                <ReactFlow
                  nodes={[backgroundRect, ...sldNodes]}
                  edges={sldEdges}
                  nodeTypes={sldNodeTypes}
                  edgeTypes={sldEdgeTypes}
                  onInit={(instance) => {
                    setReactFlowInstance(instance);
                    setSldReactFlowInstance(instance);
                    // Initial auto-fit is now handled by direct fitToScreenWithFallback calls
                  }}
                  proOptions={{ hideAttribution: true }}
                  className={`${showGrid ? "bg-grid" : "bg-gray-50"}`}
                  panOnDrag={[1]}
                  selectionOnDrag
                  panOnScroll
                  zoomOnDoubleClick={false}
                  edgesFocusable={true}
                  elevateEdgesOnSelect={true}
                  edgesReconnectable={true}
                  multiSelectionKeyCode="Shift"
                  nodesDraggable={true}
                  nodesConnectable={true}
                  elementsSelectable={true}
                  selectNodesOnDrag={true}
                  nodeDragThreshold={0}
                  snapToGrid={snapToGrid}
                  snapGrid={[GridConfig.GRID_SIZE, GridConfig.GRID_SIZE]}
                  nodeExtent={[
                    [-1000, -1000], // Min bounds (x, y)
                    [2000, 2000], // Max bounds (x, y)
                  ]}
                  connectionLineStyle={{
                    stroke: "#4D9FC9", // Default blue color for dragging
                    strokeWidth: 3,
                  }}
                  connectionMode={ConnectionMode.Loose}
                  {...eventHandlers}
                  onNodesChange={handleNodesChange}
                  onEdgesChange={handleEdgesChange}
                  style={{
                    WebkitUserSelect: "none",
                    userSelect: "none",
                    height: "100%",
                    width: "100%",
                  }}
                >
                  <Panel position="top-right" className="mt-2 mr-2">
                    <DownloadButton roomName={roomName} />
                  </Panel>
                  {liveUsers && (
                    <DocumentGraphCollaborationCursors
                      liveUsers={liveUsers}
                      graphContainerRef={containerRef}
                      reactFlowInstance={reactFlowInstance as any}
                      currentEditor={SLD_EDITOR}
                    />
                  )}
                </ReactFlow>
              </ReactFlowProvider>
            </SLDContextMenu>
          </div>
        </Allotment.Pane>
        <Allotment.Pane
          maxSize={500}
          minSize={300}
          className="absolute inset-0 flex flex-row py-2 pr-2 pl-1"
          preferredSize={_rightDrawerWidth}
        >
          <div className="flex flex-col flex-shrink-0 divide-y-8 divide-gray-300 dark:divide-gray-800">
            <SLDSettingsEditor selectedNodes={selectedNodes} />
          </div>
        </Allotment.Pane>
      </Allotment>
      <style>{`
        @keyframes dash {
          to {
            stroke-dashoffset: -20px;
          }
        }

        .react-flow__edge.selected .react-flow__edge-path {
          stroke-width: 4px !important;
          stroke-dasharray: 5px 5px !important;
          animation: dash 1s linear infinite !important;
          filter: brightness(0.7) !important; /* Make the original color darker */
        }

        .react-flow__edge:hover .react-flow__edge-path {
          stroke-width: 4px !important;
        }

        .react-flow__edge.selected:hover .react-flow__edge-path {
          stroke-width: 4px !important;
          stroke-dasharray: 5px 5px !important;
          animation: dash 1s linear infinite !important;
          filter: brightness(0.7) !important; /* Make the original color darker */
        }

        .bg-grid {
          background-image:
            linear-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(0, 0, 0, 0.1) 1px, transparent 1px);
          background-size: ${GridConfig.GRID_SIZE}px ${GridConfig.GRID_SIZE}px;
          background-position: -1px -1px;
        }
      `}</style>

      <DeleteConfirmationModal
        isOpen={deleteModalState.isOpen}
        onClose={() =>
          setDeleteModalState((prev) => ({ ...prev, isOpen: false }))
        }
        onConfirm={handleConfirmedDeletion}
        mirroredNodeIds={deleteModalState.mirroredNodeIds}
        sldNodeIds={deleteModalState.sldNodeIds}
      />

      <ConnectionErrorToast
        message={connectionError || ""}
        isVisible={showErrorToast}
        onClose={handleCloseErrorToast}
      />

      {/* Global Edge Handle Context Menu */}
      {edgeHandleContextMenu && (
        <EdgeHandleContextMenu
          edgeHandleContextMenu={edgeHandleContextMenu}
          setEdgeHandleContextMenu={setEdgeHandleContextMenu}
          reactFlowInstance={reactFlowInstance}
          yDoc={yDoc}
        />
      )}
    </div>
  );
}
