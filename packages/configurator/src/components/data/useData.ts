import { DeepP<PERSON>s, DeepPathValue } from "@/lib/path";
import { getMap, getRootMap, toYjsType } from "@/lib/yjsUtils";
import { useEffect, useState } from "react";
import * as Y from "yjs";
import z from "zod";
import { useReactFlowContext } from "../graph/ReactFlowContext";
import {
  Data,
  DataSchemaByType,
  DataType,
  getSchemaForType,
  YDOC_DATA_KEY,
} from "./Data";
export function useData<Type extends DataType>(
  dataId: string,
  type: Type,
): {
  data: ReturnType<typeof useGetData<Type>>;
  updateData: ReturnType<typeof useUpdateData<Type>>;
  updateNestedData: ReturnType<typeof useUpdateNestedData<Type>>;
  deleteNestedData: ReturnType<typeof useDeleteNestedData<Type>>;
} {
  const data = useGetData(dataId, type);
  const updateData = useUpdateData(dataId, type);
  const updateNestedData = useUpdateNestedData(dataId, type);
  const deleteNestedData = useDeleteNestedData(dataId, type);

  return {
    data,
    updateData,
    updateNestedData,
    deleteNestedData,
  };
}

// #region GET

export function useGetData<
  Type extends DataType,
  Schema = DataSchemaByType[Type],
>(dataId: string, type: Type) {
  const [data, setData] = useState<z.infer<Schema> | null>(null);

  const { yDoc } = useReactFlowContext();

  useEffect(() => {
    const dataMap = getDataMap(yDoc, dataId, type);
    if (!dataMap) {
      return;
    }

    // Initialize value immediately, then watch for changes
    setData(dataMap.toJSON() as z.infer<Schema>);

    const observer = () => {
      // `.toJSON()` looses the type information => we need to cast
      // The data was validated against the Schema by `getDataMap()`.
      setData(dataMap.toJSON() as z.infer<Schema>);
    };

    dataMap.observeDeep(observer);

    return () => {
      dataMap.unobserveDeep(observer);
    };
  }, [yDoc, dataId, type]);

  return data;
}

export function getData<Type extends DataType, Schema = DataSchemaByType[Type]>(
  yDoc: Y.Doc,
  dataId: string,
  type: Type,
): z.infer<Schema> | null {
  const dataMap = getDataMap(yDoc, dataId, type);
  return dataMap ? (dataMap.toJSON() as z.infer<Schema>) : null;
}

function getDataMap<Type extends DataType, Schema = DataSchemaByType[Type]>(
  yDoc: Y.Doc,
  dataId: string,
  type: Type,
): Y.Map<Schema> | null {
  const dataMap = getRootMap(yDoc, YDOC_DATA_KEY, Data);
  if (!dataMap.success) {
    logErrorDetails(
      `Could not get \`${YDOC_DATA_KEY}\` map from Y.js doc`,
      dataMap.error.message,
      dataMap.error.received,
    );
    return null;
  }

  const mapResult = getMap(dataMap.value, dataId, getSchemaForType(type));
  if (!mapResult.success) {
    logErrorDetails(
      `Could not get valid \`${dataId}\` from Y.js \`${YDOC_DATA_KEY}\` map`,
      mapResult.error.message,
      mapResult.error.received,
    );
    return null;
  }

  // Because `Schema` is generic, TS can't infer `mapResult.value` type itself.
  // It's safe because the data was validated against the Schema by `getMap()`.
  return mapResult.value as Y.Map<Schema>;
}

// #endregion

// #region UPDATE

// Don't allow to update the `id` or `type`
type NewData<Type extends DataType> = Partial<
  Omit<Data[string] & { type: Type }, "id" | "type">
>;

export function useUpdateData<Type extends DataType>(
  dataId: string,
  type: Type,
): (newData: NewData<Type>) => void {
  const { yDoc } = useReactFlowContext();

  return (newData: NewData<DataType>) => {
    if (yDoc) {
      return updateData(yDoc, dataId, type, newData);
    }
  };
}

export function updateData<Type extends DataType>(
  yDoc: Y.Doc,
  dataId: string,
  type: Type,
  newData: NewData<Type>,
) {
  const dataMap = getRootMap(yDoc, YDOC_DATA_KEY, Data);
  if (!dataMap.success) {
    logErrorDetails(
      `Could not get \`data\` map from Y.js doc`,
      dataMap.error.message,
      dataMap.error.received,
    );
    return;
  }

  const mapResult = getMap(dataMap.value, dataId, getSchemaForType(type));
  if (!mapResult.success) {
    logErrorDetails(
      `Could not get \`${dataId}\` from Y.js \`data\` map`,
      mapResult.error.message,
      mapResult.error.received,
    );
    return;
  }

  Object.entries(newData).forEach(([key, value]) => {
    const yValue = toYjsType(value);
    mapResult.value.set(key, yValue);
  });
}

// Get the full data structure without Partial
type FullDataType<Type extends DataType> = z.infer<DataSchemaByType[Type]>;

export function useUpdateNestedData<Type extends DataType>(
  dataId: string,
  type: Type,
): <Path extends DeepPaths<FullDataType<Type>>>(
  path: Path,
  newValue: DeepPathValue<FullDataType<Type>, Path>,
) => void {
  const { yDoc } = useReactFlowContext();

  return (path, newValue) => {
    if (yDoc) {
      return updateNestedData(yDoc, dataId, type, path, newValue);
    }
  };
}

export function updateNestedData<
  Type extends DataType,
  Path extends DeepPaths<FullDataType<Type>>,
>(
  yDoc: Y.Doc,
  dataId: string,
  type: Type,
  path: Path,
  newValue: DeepPathValue<FullDataType<Type>, Path>,
) {
  const dataMap = getRootMap(yDoc, YDOC_DATA_KEY, Data);
  if (!dataMap.success) {
    logErrorDetails(
      `Could not get \`data\` map from Y.js doc`,
      dataMap.error.message,
      dataMap.error.received,
    );
    return;
  }

  const mapResult = getMap(dataMap.value, dataId, getSchemaForType(type));
  if (!mapResult.success) {
    logErrorDetails(
      `Could not get \`${dataId}\` from Y.js \`data\` map`,
      mapResult.error.message,
      mapResult.error.received,
    );
    return;
  }

  const pathParts = path.split(".");
  let currentMap = mapResult.value;

  // Navigate to the parent of the target property
  for (let i = 0; i < pathParts.length - 1; i++) {
    const key = pathParts[i];
    let nestedMap = currentMap.get(key);
    if (!nestedMap) {
      nestedMap = new Y.Map();
      currentMap.set(key, nestedMap);
    }
    if (!(nestedMap instanceof Y.Map)) {
      console.error(
        `Expected Y.Map for \`${dataId}\` at \`${key}\`, got ${nestedMap}`,
      );
      return;
    }

    currentMap = nestedMap;
  }

  const finalKey = pathParts[pathParts.length - 1];
  const yValue = toYjsType(newValue);
  currentMap.set(finalKey, yValue);
}

// #endregion

// #region DELETE

export function deleteData(yDoc: Y.Doc, dataId: string) {
  const dataMap = getRootMap(yDoc, YDOC_DATA_KEY, Data);
  if (!dataMap.success) {
    logErrorDetails(
      `Could not get \`data\` map from Y.js doc`,
      dataMap.error.message,
      dataMap.error.received,
    );
    return;
  }

  dataMap.value.delete(dataId);
}

export function useDeleteNestedData<Type extends DataType>(
  dataId: string,
  type: Type,
): <Path extends DeepPaths<FullDataType<Type>>>(path: Path) => void {
  const { yDoc } = useReactFlowContext();

  return (path) => {
    if (yDoc) {
      return deleteNestedData(yDoc, dataId, type, path);
    }
  };
}

export function deleteNestedData<
  Type extends DataType,
  Path extends DeepPaths<FullDataType<Type>>,
>(yDoc: Y.Doc, dataId: string, type: Type, path: Path) {
  const dataMap = getRootMap(yDoc, YDOC_DATA_KEY, Data);
  if (!dataMap.success) {
    logErrorDetails(
      `Could not get \`data\` map from Y.js doc`,
      dataMap.error.message,
      dataMap.error.received,
    );
    return;
  }

  const mapResult = getMap(dataMap.value, dataId, getSchemaForType(type));
  if (!mapResult.success) {
    logErrorDetails(
      `Could not get \`${dataId}\` from Y.js \`data\` map`,
      mapResult.error.message,
      mapResult.error.received,
    );
    return;
  }

  const pathParts = path.split(".");
  let currentMap = mapResult.value;

  // Navigate to the parent of the target property
  for (let i = 0; i < pathParts.length - 1; i++) {
    const key = pathParts[i];
    let nestedMap = currentMap.get(key);
    if (!nestedMap) {
      nestedMap = new Y.Map();
      currentMap.set(key, nestedMap);
    }
    if (!(nestedMap instanceof Y.Map)) {
      console.error(
        `Expected Y.Map for \`${dataId}\` at \`${key}\`, got ${nestedMap}`,
      );
      return;
    }

    currentMap = nestedMap;
  }

  const finalKey = pathParts[pathParts.length - 1];
  currentMap.delete(finalKey);
}

// #endregion

function logErrorDetails(message: string, details: string, received: unknown) {
  console.error(`${message}\n\n${details}\n\n`, { received }, "\n\n");
}
