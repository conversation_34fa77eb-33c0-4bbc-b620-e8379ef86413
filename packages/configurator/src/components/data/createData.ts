import { objectToYMap } from "@/lib/yjsUtils";
import {
  createDefaultAdcPorts,
  createDefaultRelayConnectors,
  createDefaultThreePinPorts,
  createDefaultTwoPinPorts,
  createDefaultZeroToTenVoltPorts,
  GraphNode,
  randomId,
} from "@somo/shared";
import { Node } from "@xyflow/react";
import * as Y from "yjs";
import z from "zod";
import { SldNodeData } from "../devices/sld/types";
import { DataSchemaByType, DataType, YDOC_DATA_KEY } from "./Data";

export type DataDefaults = {
  [K in DataType]: Omit<z.infer<DataSchemaByType[K]>, "id">;
}[DataType];

/**
 * Map graph nodes types (configurator, SLD editor) to default values for
 * creating the Data entry. Excludes the ID, it should be set dynamically.
 */
const NODES_TO_DATA_DEFAULTS: { [K in string]: DataDefaults } = {
  baseStationContainer: {
    type: "baseStation",
    title: "Base Station",
    somfyShades: {},
  },
  canbusControllerContainer: {
    type: "canbusController",
    title: "CAN Bus Controller",
    controllers: {},
    reedSwitchSensors: {},
    pirSensors: {},
    zeroToTenVoltDimmers: {},
    relayOutputs: {},
    threePinPorts: createDefaultThreePinPorts(),
    twoPinPorts: createDefaultTwoPinPorts(),
    adcPorts: createDefaultAdcPorts(),
    zeroToTenVoltPorts: createDefaultZeroToTenVoltPorts(),
    relayConnectors: createDefaultRelayConnectors(),
  },
  doorSensorContainer: {
    type: "doorSensor",
    title: "Door Sensor",
    onClose: {
      icon: undefined,
      enabled: false,
      name: "On Close",
      onUpClick: {},
      showLabel: true,
      offDelay: 0.0,
    },
    onOpen: {
      icon: undefined,
      enabled: true,
      name: "On Open",
      onUpClick: {},
      showLabel: true,
      offDelay: 0.0,
    },
  },
  groundSLD: {
    type: "ground",
  },
  image: {
    type: "image",
    opacity: 0.3,
    imageUrl: "/<EMAIL>",
  },
  light: {
    type: "light",
    name: "Light",
    showLabel: true,
    icon: "ceilingLamp",
    fixtures: {},
    isDimmable: true,
    defaultDimmingSpeed: 0.2,
    defaultDimmingCurve: {
      type: "linear",
      points: [
        { x: 0, y: 0 },
        { x: 1, y: 1 },
      ],
    },
  },
  lineSLD: {
    type: "line",
  },
  neutralSLD: {
    type: "neutral",
  },
  outletDimmerContainer: {
    type: "outletDimmer",
    name: "Plug-in Dimmer",
    via: {
      icon: undefined,
      name: "Last/Off",
      showLabel: true,
      onUpClick: {},
    },
    dimSpeed: 1.0,
    dimmingCurve: {
      type: "linear",
      points: [
        { x: 0, y: 0 },
        { x: 1, y: 1 },
      ],
    },
    viaDown: {
      icon: undefined,
      enabled: true,
      name: "Down",
      onUpClick: {},
      showLabel: true,
    },
    viaUp: {
      icon: undefined,
      enabled: true,
      name: "Up",
      onUpClick: {},
      showLabel: true,
    },
  },
  outletDimmerLight: {
    type: "outletDimmerLight",
    name: "Outlet Dimmer Light",
    showLabel: true,
    icon: "ceilingLamp",
    controllerId: "",
    defaultDimmingSpeed: 1.0,
    defaultDimmingCurve: {
      type: "linear",
      points: [
        { x: 0, y: 0 },
        { x: 1, y: 1 },
      ],
    },
  },
  presenceSensorContainer: {
    type: "presenceSensor",
    title: "Occupancy Sensor",
    onDeactivate: {
      icon: undefined,
      enabled: false,
      name: "On Deactivate",
      onUpClick: {},
      showLabel: true,
      offDelay: 0.0,
      cancelOnActivityDuringDelay: false,
    },
    onActivate: {
      icon: undefined,
      enabled: true,
      name: "On Activate",
      onUpClick: {},
      showLabel: true,
      offDelay: 0.0,
      cancelOnActivityDuringDelay: false,
    },
  },
  roomDimmerContainer: {
    type: "roomDimmer",
    title: "Room Dimmer",
    via: {
      name: "Dimmer",
      icon: undefined,
      lightName: "Light",
      lightIcon: undefined,
      showLabel: true,
      onUpClick: {},
      onUpHold: {},
    },
    viaUp: {
      name: "Up",
      enabled: true,
      icon: undefined,
      showLabel: true,
      onUpClick: {},
      onUpHold: {},
    },
    viaDown: {
      name: "Down",
      enabled: true,
      icon: undefined,
      showLabel: true,
      onUpClick: {},
      onUpHold: {},
    },
    dimSpeed: 3,
    dimmingCurve: {
      points: [
        { x: 0, y: 0 },
        { x: 1, y: 1 },
      ],
      type: "linear",
    },
  },
  servicePadContainer: {
    type: "servicePad",
    title: "Service Pad",
    mode: "servicePad",
    makeUpRoomButton: {
      icon: "brush",
      enabled: true,
      name: "Make Up Room",
      showLabel: true,
    },
    doorbellButton: {
      icon: "bell",
      enabled: true,
      name: "Doorbell",
      showLabel: true,
    },
    doNotDisturbButton: {
      icon: "doNotDisturb",
      enabled: true,
      name: "Do Not Disturb",
      showLabel: true,
    },
  },
  somoFanContainer: {
    type: "somoFan",
    title: "Room Fan Controller",
    via: {
      icon: undefined,
      loadIcon: undefined,
      name: "Fan",
      loadName: "Fan",
      showLabel: true,
      onUpClick: {},
    },
    fanType: "onoff",
    viaLow: {
      icon: undefined,
      enabled: true,
      name: "Fan Low",
      onUpClick: {},
      showLabel: true,
    },
    viaMed: {
      icon: undefined,
      enabled: true,
      name: "Fan Medium",
      onUpClick: {},
      showLabel: true,
    },
    viaHigh: {
      icon: undefined,
      enabled: true,
      name: "Fan High",
      onUpClick: {},
      showLabel: true,
    },
  },
  somoIrControllerContainer: {
    title: "IR HVAC Controller",
    temperatureUnit: "C",
    minTemp: 5,
    maxTemp: 35,
    allowedModes: ["heat", "cool", "fan", "auto"],
    allowedFanSpeeds: ["low", "medium", "high", "auto"],
    stepSize: 1,
    via: {
      name: "IR Controller",
      icon: undefined,
      hvacName: "IR Controller",
      hvacIcon: undefined,
      showLabel: true,
      onUpClick: {},
    },
    viaUp: {
      enabled: true,
      name: "Temp Up",
      icon: undefined,
      showLabel: true,
      onUpClick: {},
    },
    viaDown: {
      enabled: true,
      name: "Temp Down",
      icon: undefined,
      showLabel: true,
      onUpClick: {},
    },
    modeCool: {
      enabled: true,
      name: "Cool",
      icon: undefined,
      showLabel: true,
      onUpClick: {},
    },
    modeHeat: {
      enabled: true,
      name: "Heat",
      icon: undefined,
      showLabel: true,
      onUpClick: {},
    },
    modeAuto: {
      enabled: true,
      name: "Auto",
      icon: undefined,
      showLabel: true,
      onUpClick: {},
    },
    modeFan: {
      enabled: true,
      name: "Fan",
      icon: undefined,
      showLabel: true,
      onUpClick: {},
    },
    fanAuto: {
      enabled: true,
      name: "Fan Auto",
      icon: undefined,
      showLabel: true,
      onUpClick: {},
    },
    fanLow: {
      enabled: true,
      name: "Fan Low",
      icon: undefined,
      showLabel: true,
      onUpClick: {},
    },
    fanMedium: {
      enabled: true,
      name: "Fan Medium",
      icon: undefined,
      showLabel: true,
      onUpClick: {},
    },
    fanHigh: {
      enabled: true,
      name: "Fan High",
      icon: undefined,
      showLabel: true,
      onUpClick: {},
    },
    type: "somoIrController",
  },
  roomSwitchContainer: {
    type: "roomSwitch",
    title: "Room Switch",
    via1: {
      icon: undefined,
      lightIcon: undefined,
      name: "Button 1",
      lightName: "Light 1",
      hasLoad: false,
      showLabel: true,
      enabled: true,
      onUpClick: {},
    },
    via2: {
      icon: undefined,
      lightIcon: undefined,
      name: "Button 2",
      lightName: "Light 2",
      hasLoad: false,
      showLabel: true,
      enabled: true,
      onUpClick: {},
    },
    via3: {
      icon: undefined,
      lightIcon: undefined,
      name: "Button 3",
      lightName: "Light 3",
      hasLoad: false,
      showLabel: true,
      enabled: true,
      onUpClick: {},
    },
  },
  scene: {
    type: "scene",
    title: "Scene",
    inputs: [],
    onActivate: {
      enabled: true,
      onUpClick: {},
    },
    onDeactivate: {
      enabled: false,
      onUpClick: {},
    },
  },
  somoShadesContainer: {
    type: "somoShades",
    title: "Shades",
    via: {
      icon: undefined,
      loadIcon: undefined,
      name: "Shades",
      loadName: "Shades",
      showLabel: true,
      onUpClick: {},
    },
    raceTime: 30,
    viaDown: {
      icon: undefined,
      enabled: true,
      name: "Shades Close",
      onUpClick: {},
      showLabel: true,
    },
    viaUp: {
      icon: undefined,
      enabled: true,
      name: "Shades Open",
      onUpClick: {},
      showLabel: true,
    },
  },
  somoThermostatContainer: {
    type: "somoThermostat",
    title: "Thermostat",
    temperatureUnit: "C",
    minTemp: 5,
    maxTemp: 35,
    allowedModes: ["heat", "cool", "fan", "auto"],
    allowedFanSpeeds: ["low", "medium", "high", "auto"],
    stepSize: 1,
    via: {
      icon: undefined,
      hvacIcon: undefined,
      name: "Via",
      hvacName: "Thermostat",
      showLabel: true,
      onUpClick: {},
    },
    viaDown: {
      icon: undefined,
      enabled: true,
      name: "Lower Setpoint",
      onUpClick: {},
      showLabel: true,
    },
    viaUp: {
      icon: undefined,
      enabled: true,
      name: "Increase Setpoint",
      onUpClick: {},
      showLabel: true,
    },
    modeCool: {
      icon: undefined,
      enabled: true,
      name: "Cool",
      onUpClick: {},
      showLabel: true,
    },
    modeHeat: {
      icon: undefined,
      enabled: true,
      name: "Heat",
      onUpClick: {},
      showLabel: true,
    },
    modeAuto: {
      icon: undefined,
      enabled: true,
      name: "Auto",
      onUpClick: {},
      showLabel: true,
    },
    modeFan: {
      icon: undefined,
      enabled: true,
      name: "Fan",
      onUpClick: {},
      showLabel: true,
    },
    fanAuto: {
      icon: undefined,
      enabled: true,
      name: "Fan Auto",
      onUpClick: {},
      showLabel: true,
    },
    fanLow: {
      icon: undefined,
      enabled: true,
      name: "Fan Low",
      onUpClick: {},
      showLabel: true,
    },
    fanMedium: {
      icon: undefined,
      enabled: true,
      name: "Fan Medium",
      onUpClick: {},
      showLabel: true,
    },
    fanHigh: {
      icon: undefined,
      enabled: true,
      name: "Fan High",
      onUpClick: {},
      showLabel: true,
    },
  },
  virtualButtonContainer: {
    title: "Virtual Button",
    type: "virtualButton",
    via: {
      icon: undefined,
      lightIcon: undefined,
      name: "Button",
      lightName: "Light",
      showLabel: true,
      enabled: true,
      onUpClick: {},
    },
  },
};

/**
 * Get the node type that corresponds to a given data type
 * This reverses the NODES_TO_DATA_DEFAULTS mapping
 */
export function getNodeTypeFromDataType(dataType: DataType): string | null {
  for (const [nodeType, defaults] of Object.entries(NODES_TO_DATA_DEFAULTS)) {
    if (defaults.type === dataType) {
      return nodeType;
    }
  }
  return null;
}

export function createDataFromNode(
  yDoc: Y.Doc,
  node: GraphNode | Node<SldNodeData>,
  additionalData?: Partial<DataDefaults>,
): string | null {
  if (!node.type) {
    return null;
  }

  const dataWithoutId = NODES_TO_DATA_DEFAULTS[node.type];
  if (!dataWithoutId) {
    return null;
  }

  const data = {
    ...dataWithoutId,
    ...additionalData,
    id: randomId(),
  };

  yDoc.getMap(YDOC_DATA_KEY).set(data.id, objectToYMap(data));

  return data.id;
}
