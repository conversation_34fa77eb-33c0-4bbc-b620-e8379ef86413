import {
  BaseStation,
  CanBusController,
  DoorSensor,
  Ground,
  Image,
  Light,
  Line,
  Neutral,
  OutletDimmer,
  OutletDimmerLight,
  PresenceSensor,
  RoomDimmer,
  RoomSwitch,
  Scene,
  ServicePad,
  SomoFan,
  SomoIrController,
  SomoShades,
  SomoThermostat,
  VirtualButton,
} from "@somo/shared";
import z from "zod";
export const YDOC_DATA_KEY = "data";

const DataSchema = z.discriminatedUnion("type", [
  BaseStation,
  CanBusController,
  Image,
  SomoFan,
  Ground,
  Line,
  Light,
  Neutral,
  RoomSwitch,
  RoomDimmer,
  OutletDimmer,
  OutletDimmerLight,
  PresenceSensor,
  Scene,
  ServicePad,
  SomoIrController,
  SomoShades,
  SomoThermostat,
  DoorSensor,
  VirtualButton,
]);

export const Data = z.record(z.string(), DataSchema);
export type Data = z.infer<typeof Data>;

export type DataType = Data[string]["type"];

type AnyDataSchema = (typeof DataSchema)["options"][number];
export type DataSchemaByType = {
  [S in AnyDataSchema as z.infer<S>["type"]]: S;
};
export type DataByType = {
  [S in AnyDataSchema as z.infer<S>["type"]]: z.infer<S>;
};

// Create a map that associates each Data `type` with its related schema.
const typeToSchemaMap = Object.fromEntries(
  DataSchema.options.map((opt) => [opt.shape.type.value, opt]),
) as {
  [Type in DataType]: Extract<
    (typeof DataSchema)["options"][number],
    { shape: { type: z.ZodLiteral<Type> } }
  >;
};

export function getSchemaForType<Type extends DataType>(
  type: Type,
): DataSchemaByType[Type] {
  return typeToSchemaMap[type];
}

export function getAllDataTypes(): DataType[] {
  return Object.keys(typeToSchemaMap) as DataType[];
}
