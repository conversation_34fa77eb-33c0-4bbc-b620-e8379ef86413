import { getData } from "@/components/data/useData";
import { Graph } from "@/components/graph/Graph";
import { LogEntry } from "@/components/graph/simulator/LogEntry";
import { Room } from "@/components/graph/useRoom";
import { useInterval } from "@/hooks/useInterval";
import { MainModule } from "@/types/basestation";
import {
  ActiveConfiguration,
  Commands,
  filterNodes,
  getConnectorId,
  GraphNode,
  serializeNodesForBasestation,
} from "@somo/shared";
import { useEffect, useMemo, useState } from "react";
import { match } from "ts-pattern";
import * as Y from "yjs";
import { MessageType } from "./BasestationEvents";
import { ExecutionContext } from "./ExecutionContext";

export function useSimulationExecutionContextProvider({
  yDoc,
  basestation,
  room,
  graph,
  captureLog,
}: {
  yDoc: Y.Doc;
  basestation: MainModule;
  room: Room;
  graph: Graph;
  captureLog: (entry: LogEntry) => void;
}): ExecutionContext & { lastModificationTime: number } {
  const { nodes } = graph;

  const { data, lightsIds } = useMemo(
    () => createBasestationData(nodes, room),
    [room?.id, nodes.length],
  );

  useEffect(() => {
    const success = basestation.setActiveConfigurationFromProtobuf(data);
    if (success) {
      console.log("✅ Successfully updated basestation configuration");
      basestation.prettyPrintActiveConfiguration();
    } else {
      console.error("Failed to update basestation configuration");
    }
  }, [data]);

  const [basestationState, setBasestationState] = useState({
    state: "",
    lastModifiedTime: Date.now(),
  });

  // Simulate the basestation's main loop
  // Update the last modification time to trigger a re-render if necessary
  const refreshFrequencyInHz = 20;
  useInterval(() => {
    basestation.processAllLights();
    const config = getActiveConfig(basestation);
    const newState = JSON.stringify(config);
    if (basestationState.state !== newState) {
      setBasestationState({ state: newState, lastModifiedTime: Date.now() });
    }
  }, 1000 / refreshFrequencyInHz);

  const isLightOn: ExecutionContext["isLightOn"] = (id) => {
    return getBrightness(id) > 0;
  };

  const getBrightness: ExecutionContext["getBrightness"] = (id) => {
    const lightId = lightsIds.find((l) => l.deviceId === id)?.basestationId;
    const activeConfig = getActiveConfig(basestation);
    const state = activeConfig.state?.lights.find((l) => l.id === lightId);
    if (!state) {
      console.warn("Can't find light state", { id, lightId, activeConfig });
      return 0;
    }

    return Math.round(state.brightness);
  };

  const sendMomentaryCommand: ExecutionContext["sendMomentaryCommand"] = (
    containerId,
    controllerId,
    viaId,
  ) => {
    captureLog({
      timestamp: Date.now(),
      device: { type: "momentaryCanBusController", id: containerId },
      action: match(viaId)
        .with("onUpHold", () => "mouseDown" as const)
        .with("onUpClick", () => "mouseUp" as const)
        .with("onUpPress", () => "mouseDown" as const)
        .with("onUpRelease", "onUpHoldRelease", () => "mouseUp" as const)
        .exhaustive(),
      target: viaId,
    });

    const connectorId = findCanbusControllerConnectorId(
      yDoc,
      nodes,
      containerId,
      controllerId,
    );
    if (!connectorId) {
      console.warn("Can't find connector ID to message the basestation");
      return;
    }

    const message = Commands.MomentaryButtonCommand.encode({
      connectorId,
      state: match(viaId)
        .with(
          "onUpHold",
          "onUpPress",
          () => Commands.MomentaryButtonCommand_State.Pressed,
        )
        .with(
          "onUpClick",
          "onUpRelease",
          "onUpHoldRelease",
          () => Commands.MomentaryButtonCommand_State.Released,
        )
        .exhaustive(),
      timestamp: Math.floor(Date.now() / 1000),
    }).finish();

    sendCommandToDevices(
      basestation,
      containerId,
      MessageType.MESSAGE_CANBO_MOMENTARY_BUTTON,
      message,
    );
  };

  const sendToggleCommand: ExecutionContext["sendToggleCommand"] = (
    containerId,
    controllerId,
    viaId,
  ) => {
    captureLog({
      timestamp: Date.now(),
      device: { type: "toggleCanBusController", id: containerId },
      action: match(viaId)
        .with(
          "onUpHold",
          "onDownHold",
          "onUpPress",
          "onDownPress",
          () => "mouseDown" as const,
        )
        .with(
          "onUpClick",
          "onDownClick",
          "onUpRelease",
          "onDownRelease",
          "onUpHoldRelease",
          "onDownHoldRelease",
          () => "mouseUp" as const,
        )
        .exhaustive(),
      target: viaId,
    });

    const connectorId = findCanbusControllerConnectorId(
      yDoc,
      nodes,
      containerId,
      controllerId,
    );
    if (!connectorId) {
      console.warn("Can't find connector ID to message the basestation");
      return;
    }

    const message = Commands.ToggleButtonCommand.encode({
      connectorId,
      state: match(viaId)
        .with(
          "onUpHold",
          "onUpPress",
          () => Commands.ToggleButtonCommand_State.Up,
        )
        .with(
          "onDownHold",
          "onDownPress",
          () => Commands.ToggleButtonCommand_State.Down,
        )
        .with(
          "onUpClick",
          "onDownClick",
          "onUpRelease",
          "onDownRelease",
          "onUpHoldRelease",
          "onDownHoldRelease",
          () => Commands.ToggleButtonCommand_State.Released,
        )
        .exhaustive(),
      timestamp: Math.floor(Date.now() / 1000),
    }).finish();

    sendCommandToDevices(
      basestation,
      containerId,
      MessageType.MESSAGE_CANBO_TOGGLE_BUTTON,
      message,
    );
  };

  const isDimmerOn: ExecutionContext["isDimmerOn"] = (dimmerId, viaId) => {
    const container = filterNodes(nodes, "roomDimmerContainer").find(
      (n) => n.id === dimmerId,
    );
    if (!container) {
      console.warn("Can't find dimmer container", { dimmerId, viaId, nodes });
      return false;
    }

    const data = getData(yDoc, container.data.dataId, "roomDimmer");
    if (!data) {
      console.warn("Can't find dimmer data", { dimmerId, viaId, nodes });
      return false;
    }
    const dimmer = data[viaId];
    const actions = [
      ...Object.values(dimmer.onUpClick ?? {}),
      ...Object.values(dimmer.onUpHold ?? {}),
    ];
    const lights = actions.map((action) => action.deviceId);

    return lights.some((l) => isLightOn(l));
  };

  const sendDimmerCommand: ExecutionContext["sendDimmerCommand"] = (
    dimmerId,
    viaId,
    action,
  ) => {
    captureLog({
      timestamp: Date.now(),
      device: { type: "roomDimmer", id: dimmerId },
      action: match(action)
        .with("onUpHold", () => "mouseDown" as const)
        .with("onUpClick", () => "mouseUp" as const)
        .exhaustive(),
      target: viaId,
    });

    const message = Commands.MomentaryButtonCommand.encode({
      connectorId: match(viaId)
        .with("viaUp", () => 0)
        .with("via", () => 1)
        .with("viaDown", () => 2)
        .exhaustive(),
      state: match(action)
        .with("onUpHold", () => Commands.MomentaryButtonCommand_State.Pressed)
        .with("onUpClick", () => Commands.MomentaryButtonCommand_State.Released)
        .exhaustive(),
      timestamp: Math.floor(Date.now() / 1000),
    }).finish();

    sendCommandToDevices(
      basestation,
      dimmerId,
      MessageType.MESSAGE_RF_MOMENTARY_BUTTON,
      message,
    );
  };

  return {
    status: { value: "offline" },
    lastModificationTime: basestationState.lastModifiedTime,

    sendVirtualButtonCommand: () => {
      captureLog({
        timestamp: Date.now(),
        device: { type: "virtualButton", id: "stub" },
        action: "click",
        target: "via",
      });
    },

    isLightOn,
    getBrightness,

    isControllerOn: () => false,
    sendMomentaryCommand,
    sendToggleCommand,

    isSwitchOn: () => false,
    sendSwitchCommand: () => {
      captureLog({
        timestamp: Date.now(),
        device: { type: "roomSwitch", id: "stub" },
        action: "click",
        target: "via",
      });
    },

    isDimmerOn,
    sendDimmerCommand,

    isOutletDimmerOn: () => false,
    sendOutletDimmerCommand: () => {
      captureLog({
        timestamp: Date.now(),
        device: { type: "outletDimmer", id: "stub" },
        action: "click",
        target: "via",
      });
    },

    sendPresenceSensorCommand: () => {
      captureLog({
        timestamp: Date.now(),
        device: { type: "presenceSensor", id: "stub" },
        action: "click",
        target: "via",
      });
    },

    sendDoorSensorCommand: () => {
      captureLog({
        timestamp: Date.now(),
        device: { type: "doorSensor", id: "stub" },
        action: "click",
        target: "via",
      });
    },

    isReedSwitchSensorOpen: () => false,
    openReedSwitchSensor: () => {
      captureLog({
        timestamp: Date.now(),
        device: { type: "reedSwitchSensor", id: "stub" },
        action: "emit",
        event: "REED_SWITCH_OPEN",
      });
    },
    closeReedSwitchSensor: () => {
      captureLog({
        timestamp: Date.now(),
        device: { type: "reedSwitchSensor", id: "stub" },
        action: "emit",
        event: "REED_SWITCH_CLOSED",
      });
    },

    isPirSensorActive: () => false,
    activatePirSensor: () => {
      captureLog({
        timestamp: Date.now(),
        device: { type: "pirSensor", id: "stub" },
        action: "emit",
        event: "PIR_SENSOR_ACTIVATED",
      });
    },
    deactivatePirSensor: () => {
      captureLog({
        timestamp: Date.now(),
        device: { type: "pirSensor", id: "stub" },
        action: "emit",
        event: "PIR_SENSOR_DEACTIVATED",
      });
    },

    isShadesOpen: () => false,
    getShadesProgress: () => 0,
    sendShadesCommand: () => {
      captureLog({
        timestamp: Date.now(),
        device: { type: "somoShades", id: "stub" },
        action: "click",
        target: "via",
      });
    },

    isSomfyShadesOpen: () => false,
    getSomfyShadesProgress: () => 0,
    sendSomfyShadesCommand: () => {
      captureLog({
        timestamp: Date.now(),
        device: { type: "somfyShades", id: "stub" },
        action: "click",
        target: "via",
      });
    },

    isFanOn: () => false,
    getFanLevel: () => 0,
    sendFanCommand: () => {
      captureLog({
        timestamp: Date.now(),
        device: { type: "somoFan", id: "stub" },
        action: "click",
        target: "via",
      });
    },

    servicePadStatus: { value: "none" },
    sendServicePadCommand: () => {
      captureLog({
        timestamp: Date.now(),
        device: { type: "servicePad", id: "stub" },
        action: "servicePadStatus",
        status: "none",
      });
    },

    thermostatStates: {},
    getThermostatSetpoint: () => 0,
    getThermostatMode: () => "auto",
    getThermostatFanSpeed: () => "auto",
    sendThermostatCommand: () => {
      captureLog({
        timestamp: Date.now(),
        device: { type: "somoThermostat", id: "stub" },
        action: "click",
        target: "via",
      });
    },

    irControllerStates: {},
    getIrControllerSetpoint: () => 0,
    getIrControllerMode: () => "auto",
    getIrControllerFanSpeed: () => "auto",
    sendIrControllerCommand: () => {
      captureLog({
        timestamp: Date.now(),
        device: { type: "somoIrController", id: "stub" },
        action: "click",
        target: "via",
      });
    },
  };
}

function findCanbusControllerConnectorId(
  yDoc: Y.Doc,
  nodes: GraphNode[],
  containerId: string,
  controllerId: string,
) {
  const container = filterNodes(nodes, "canbusControllerContainer").find(
    (n) => n.id === containerId,
  );
  if (!container) {
    console.warn("Can't find canbus controller container");
    return;
  }
  const data = getData(yDoc, container.data.dataId, "canbusController");
  if (!data) {
    console.warn("Can't find canbus controller data", {
      containerId,
      controllerId,
      nodes,
    });
    return false;
  }

  const controller = Object.values(data.controllers).find(
    (controller) => controller.id === controllerId,
  );
  if (!controller) {
    console.warn("Can't find controller");
    return;
  }

  return getConnectorId(controller);
}

export function sendCommandToDevices(
  basestation: MainModule,
  containerId: string,
  messageType: number,
  message: Uint8Array,
) {
  const activeConfig = getActiveConfig(basestation);
  const nodes =
    activeConfig.config?.nodeQrMappings.filter(
      (i) => i.qrCode === containerId,
    ) ?? [];

  const command = new Uint8Array([messageType, ...message]);
  nodes.forEach(({ nodeId }) => {
    basestation.handleCommand(command, nodeId);
  });
}

export function getActiveConfig(basestation: MainModule) {
  const rawConfig = basestation.getActiveConfiguration();
  if (!rawConfig) {
    throw new Error("Can't get active configuration from basestation");
  }

  return ActiveConfiguration.decode(rawConfig);
}

export function createBasestationData(nodes: GraphNode[], room?: Room) {
  return serializeNodesForBasestation({
    nodes,
    name: room?.name ?? "Room name",
    roomId: room?.id ?? "room-id",
    version: "000000",
    serverAddress: "wss://somo-server.fly.dev/ws/service",
    nodeQrMappings: nodes
      .filter((n) => n.type?.endsWith("Container"))
      .map((n) => ({ deviceId: n.id, qrCode: n.id })),
  });
}
